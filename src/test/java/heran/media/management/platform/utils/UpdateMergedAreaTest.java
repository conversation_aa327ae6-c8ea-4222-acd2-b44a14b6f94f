package heran.media.management.platform.utils;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public class UpdateMergedAreaTest {

    private static final String DB_URL = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    private static final String DB_USER = "heran_app";
    private static final String DB_PASSWORD = "5G5ZVlDjWE=";
    private static final int PAGE_SIZE = 10000; // 每页查询10000条数据
    private static final int THREAD_POOL_SIZE = 50; // 线程池大小50个
    private static final int BATCH_SIZE = 200; // 每个线程处理的数据批次大小

    // 数据库连接池配置
    private static final HikariDataSource dataSource;

    // 线程池
    private static final ExecutorService executorService;

    // 统计计数器
    private static final AtomicLong totalProcessed = new AtomicLong(0);
    private static final AtomicLong totalSuccess = new AtomicLong(0);
    private static final AtomicLong totalFail = new AtomicLong(0);

    static {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(DB_URL);
        config.setUsername(DB_USER);
        config.setPassword(DB_PASSWORD);

        // 优化连接池配置
        config.setMaximumPoolSize(200);
        config.setMinimumIdle(10);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        config.setLeakDetectionThreshold(300000);
        config.setValidationTimeout(10000);
        config.setConnectionTestQuery("SELECT 1");
        config.setPoolName("GridMergerPool");

        // 连接保活配置
        config.addDataSourceProperty("autoReconnect", "true");
        config.addDataSourceProperty("failOverReadOnly", "false");
        config.addDataSourceProperty("maxReconnects", "10");
        config.addDataSourceProperty("initialTimeout", "10");
        config.addDataSourceProperty("socketTimeout", "0");
        config.addDataSourceProperty("connectTimeout", "30000");

        // 新增：连接保活配置
        config.addDataSourceProperty("useKeepAlive", "true");
        config.addDataSourceProperty("keepAliveTime", "300");
        config.addDataSourceProperty("tcpKeepAlive", "true");

        dataSource = new HikariDataSource(config);

        // 初始化线程池
        executorService = new ThreadPoolExecutor(
                THREAD_POOL_SIZE,
                THREAD_POOL_SIZE,
                0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(2000), // 增加队列大小
                new ThreadFactory() {
                    private final AtomicInteger threadNumber = new AtomicInteger(1);

                    @Override
                    public Thread newThread(Runnable r) {
                        Thread t = new Thread(r, "DataProcessor-" + threadNumber.getAndIncrement());
                        t.setDaemon(true);
                        return t;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

//    @Test
//    public void testProcessExcelDataWithDatabaseQuery() {
//        processAllDataWithPaginationAndThreadPool();
//    }

    public static void main(String[] args) {
        UpdateMergedAreaTest updateMergedAreaTest = new UpdateMergedAreaTest();
        updateMergedAreaTest.processAllDataWithPaginationAndThreadPool();
    }

    /**
     * 使用线程池分页处理所有数据
     */
    public void processAllDataWithPaginationAndThreadPool() {
        resetCounters();
        int pageNum = 1;

        log.info("开始使用线程池分页处理数据，每页大小: {}, 线程池大小: {}, 批次大小: {}", PAGE_SIZE, THREAD_POOL_SIZE, BATCH_SIZE);

        long startTime = System.currentTimeMillis();

        try {
            while (true) {
                List<Coordinate> pageData = getMergedResidentMapCellResourceList(pageNum, PAGE_SIZE);

                if (CollectionUtils.isEmpty(pageData)) {
                    log.info("第 {} 页没有数据，处理完成", pageNum);
                    break;
                }

                log.info("处理第 {} 页数据，共 {} 条", pageNum, pageData.size());

                // 使用线程池处理当前页数据
                processPageWithThreadPool(pageData, pageNum);

                pageNum++;
            }
        } finally {
            // 关闭线程池
            shutdownThreadPool();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        log.info("所有数据处理完成 - 总处理: {}, 总成功: {}, 总失败: {}, 耗时: {} 秒",
                totalProcessed.get(), totalSuccess.get(), totalFail.get(), duration / 1000.0);
    }

    /**
     * 使用线程池处理单页数据
     */
    private void processPageWithThreadPool(List<Coordinate> pageData, int pageNum) {
        List<Future<int[]>> futures = new ArrayList<>();
        List<List<Coordinate>> batches = new ArrayList<>();

        // 将数据分批
        for (int i = 0; i < pageData.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, pageData.size());
            List<Coordinate> batch = pageData.subList(i, endIndex);
            batches.add(batch);

            // 提交任务到线程池
            final List<Coordinate> finalBatch = batch;
            Future<int[]> future = executorService.submit(() -> processBatch(finalBatch));
            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        int pageSuccess = 0;
        int pageFail = 0;

        for (int i = 0; i < futures.size(); i++) {
            Future<int[]> future = futures.get(i);
            List<Coordinate> batch = batches.get(i);

            try {
                int[] result = future.get(180, TimeUnit.SECONDS); // 60秒超时
                pageSuccess += result[0];
                pageFail += result[1];
            } catch (TimeoutException e) {
                log.error("处理批次超时", e);
                pageFail += batch.size();
            } catch (Exception e) {
                log.error("处理批次失败", e);
                pageFail += batch.size();
            }
        }

        totalSuccess.addAndGet(pageSuccess);
        totalFail.addAndGet(pageFail);
        totalProcessed.addAndGet(pageData.size());

        log.info("第 {} 页处理完成 - 成功: {}, 失败: {}, 累计处理: {}",
                pageNum, pageSuccess, pageFail, totalProcessed.get());
    }

    private Set<String> specialCodes = new HashSet<>(Arrays.asList(
            "419001", "429004", "429005", "429006", "429021",
            "469001", "469002", "469005", "469006", "469007", "469021",
            "469022", "469023", "469024", "469025", "469026",
            "469027", "469028", "469029", "469030",
            "659001", "659002", "659003", "659004", "659005",
            "659006", "659007", "659008", "659009", "659010",
            "659011", "659012"));


    private String convertToCity(String areaCode) {
        // 如果是特殊的区域代码，直接返回 因为这些code 都没有下一级 这个就是城市code
        if (specialCodes.contains(areaCode)) {
            return null;
        }
        // 获取省级代码（前2位）
        String provinceCode = areaCode.substring(0, 2);
        // 直辖市列表：北京(11), 天津(12), 上海(31), 重庆(50)
        if ("11".equals(provinceCode) || "12".equals(provinceCode) || "31".equals(provinceCode) || "50".equals(provinceCode)) {
            // 直辖市返回省级代码 (例如: 310112 -> 310000)
            if (provinceCode.equals("50")) {
                //特殊处理重庆郊区
                String code = areaCode.substring(0, 4);
                if (code.equals("5002")) {
                    return "500200";
                }
            }
            return provinceCode + "0000";
        } else {
            // 非直辖市返回市级代码 (例如: 440103 -> 440100)
            return areaCode.substring(0, 4) + "00";
        }
    }

    /**
     * 处理单个批次的数据
     */
    private int[] processBatch(List<Coordinate> batch) {
        int successCount = 0;
        int failCount = 0;

        try (Connection conn = dataSource.getConnection()) {
            for (Coordinate coordinate : batch) {
                String center = coordinate.toString();
                try {
                    String code = null;
                    // 执行SQL查询获取area_code
                    String areaCode = getAreaCodeByCenter(conn, center, "wkt_lower_left", coordinate.getId());
                    if (StringUtils.isNotBlank(areaCode) && coordinate.getCityCode().equals(convertToCity(areaCode))) {
                        code = areaCode;
                    }

                    if (StringUtils.isBlank(code)) {
                        areaCode = getAreaCodeByCenter(conn, center, "wkt_up_right", coordinate.getId());
                        if (StringUtils.isNotBlank(areaCode) && coordinate.getCityCode().equals(convertToCity(areaCode))) {
                            code = areaCode;
                        }
                    }

                    if (StringUtils.isBlank(code)) {
                        areaCode = getAreaCodeByCenter(conn, center, "wkt_lower_right", coordinate.getId());
                        if (StringUtils.isNotBlank(areaCode) && coordinate.getCityCode().equals(convertToCity(areaCode))) {
                            code = areaCode;
                        }
                    }

                    if (StringUtils.isBlank(code)) {
                        areaCode = getAreaCodeByCenter(conn, center, "wkt_up_left", coordinate.getId());
                        if (StringUtils.isNotBlank(areaCode) && coordinate.getCityCode().equals(convertToCity(areaCode))) {
                            code = areaCode;
                        }
                    }

                    if (StringUtils.isEmpty(code) && specialCodes.contains(coordinate.getCityCode())) {
                        code = "-1";
                    }

                    if (StringUtils.isNotBlank(code)) {
                        updateCenterAreaCode(conn, code, coordinate.getId());
                        successCount++;
                        log.debug("更新成功 - ID: {}, 中心点: {}, 区域代码: {}", coordinate.getId(), center, code);
                    } else {
                        failCount++;
                        log.warn("未找到匹配的区域代码 - ID: {}, 中心点: {}", coordinate.getId(), center);
                    }
                } catch (SQLException e) {
                    failCount++;
                    log.error("处理失败 - ID: {}, 中心点: {}, 错误: {}", coordinate.getId(), center, e.getMessage());
                }
            }
        } catch (SQLException e) {
            log.error("处理批次失败", e);
            failCount += batch.size();
        }

        return new int[]{successCount, failCount};
    }

    /**
     * 重置计数器
     */
    private void resetCounters() {
        totalProcessed.set(0);
        totalSuccess.set(0);
        totalFail.set(0);
    }

    /**
     * 关闭线程池
     */
    private void shutdownThreadPool() {
        log.info("开始关闭线程池...");
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(120, TimeUnit.SECONDS)) {
                log.warn("线程池未能在120秒内正常关闭，强制关闭");
                executorService.shutdownNow();
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.error("线程池无法正常关闭");
                }
            }
            log.info("线程池已关闭");
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
            log.warn("关闭线程池时被中断");
        }
    }

    /**
     * 分页查询数据
     */
    public List<Coordinate> getMergedResidentMapCellResourceList(int pageNum, int pageSize) {
        List<Coordinate> cellResources = new ArrayList<>();
        String sql = "SELECT id, center_lng, center_lat,city_code FROM merged_resident_map_cell_resource WHERE area_code IS NULL ORDER BY id LIMIT ? OFFSET ?";

        int offset = (pageNum - 1) * pageSize;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, pageSize);
            stmt.setInt(2, offset);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    double centerLng = rs.getDouble("center_lng");
                    double centerLat = rs.getDouble("center_lat");
                    Long id = rs.getLong("id");
                    String cityCode = rs.getString("city_code");
                    Coordinate coordinate = new Coordinate(centerLng, centerLat, id, cityCode);
                    cellResources.add(coordinate);
                }
            }

            log.debug("第 {} 页查询到 {} 条数据", pageNum, cellResources.size());

        } catch (SQLException e) {
            log.error("查询第 {} 页数据失败", pageNum, e);
        }
        return cellResources;
    }

    /**
     * 获取总数据量
     */
    public long getTotalCount() {
        String sql = "SELECT COUNT(*) FROM merged_resident_map_cell_resource WHERE area_code IS NULL OR area_code = ''";
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getLong(1);
            }
        } catch (SQLException e) {
            log.error("获取总数据量失败", e);
        }
        return 0;
    }

    @Data
    static class Coordinate {
        final double lng, lat;
        final Long id;
        final String cityCode;

        Coordinate(double lng, double lat, Long id, String cityCode) {
            this.lng = lng;
            this.lat = lat;
            this.id = id;
            this.cityCode = cityCode;
        }

        public String toString() {
            return lng + "," + lat;
        }
    }

    /**
     * 根据中心点查询area_code
     */
    private static String getAreaCodeByCenter(Connection conn, String center, String field, Long mergeId) throws SQLException {
        String sql = "SELECT area_code FROM resident_map_cell_resource WHERE merge_id = ? AND " + field + " = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, mergeId);
            pstmt.setString(2, center);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("area_code");
                }
            }
        }
        return null;
    }

    private void updateCenterAreaCode(Connection conn, String areaCode, Long id) {
        String sql = "UPDATE merged_resident_map_cell_resource SET area_code = ? WHERE id = ?";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, areaCode);
            stmt.setLong(2, id);
            int rows = stmt.executeUpdate();
            if (rows > 0) {
                log.debug("更新成功 - ID: {}, 区域代码: {}", id, areaCode);
            } else {
                log.warn("更新失败，未找到匹配记录 - ID: {}", id);
            }
        } catch (SQLException e) {
            log.error("更新区域code状态失败: areaCode={}, id={}", areaCode, id, e);
        }
    }

    // @Test
    public void testGetTotalCount() {
        long totalCount = getTotalCount();
        log.info("需要处理的总数据量: {}", totalCount);

        if (totalCount > 0) {
            int totalPages = (int) Math.ceil((double) totalCount / PAGE_SIZE);
            log.info("预计需要处理 {} 页数据", totalPages);
        }
    }

    //@Test
    public void testPaginationQuery() {
        // 测试分页查询
        int pageNum = 1;
        List<Coordinate> pageData = getMergedResidentMapCellResourceList(pageNum, PAGE_SIZE);

        if (!CollectionUtils.isEmpty(pageData)) {
            log.info("第 {} 页查询到 {} 条数据", pageNum, pageData.size());
            for (int i = 0; i < Math.min(5, pageData.size()); i++) {
                Coordinate coord = pageData.get(i);
                log.info("示例数据 {}: ID={}, 中心点=({}, {})", i + 1, coord.getId(), coord.getLng(), coord.getLat());
            }
        } else {
            log.info("第 {} 页没有查询到数据", pageNum);
        }
    }

    //@Test
    public void testDatabaseConnection() {
        try (Connection conn = dataSource.getConnection()) {
            log.info("数据库连接测试成功");
        } catch (SQLException e) {
            log.error("数据库连接测试失败", e);
        }
    }

    //@Test
    public void testThreadPoolPerformance() {
        // 测试线程池性能
        List<Coordinate> testData = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            testData.add(new Coordinate(116.397128 + i * 0.001, 39.916527 + i * 0.001, (long) i, "440100"));
        }

        long startTime = System.currentTimeMillis();
        processPageWithThreadPool(testData, 1);
        long endTime = System.currentTimeMillis();

        log.info("线程池性能测试完成，处理1000条数据耗时: {} 毫秒", endTime - startTime);
    }

    // @Test
    public void testThreadPoolStatus() {
        // 测试线程池状态
        ThreadPoolExecutor executor = (ThreadPoolExecutor) executorService;
        log.info("线程池状态 - 活跃线程数: {}, 队列大小: {}, 已完成任务数: {}",
                executor.getActiveCount(),
                executor.getQueue().size(),
                executor.getCompletedTaskCount());
    }
}