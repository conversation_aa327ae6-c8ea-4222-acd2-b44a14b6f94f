package heran.media.management.platform.utils;

import heran.media.management.platform.main.subdomain.bo.XlsTemporaryTableData;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;

public class ExcelFileReaderTest {

    private static final String DB_URL = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    private static final String DB_USER = "heran_app";
    private static final String DB_PASSWORD = "5G5ZVlDjWE=";

    @Test
    public void testProcessExcelDataWithDatabaseQuery() throws IOException, SQLException {
        String filePath = "C:/Users/<USER>/Desktop/测试导出热力值数据_20250806121327.xlsx";
        File file = new File(filePath);

        if (!file.exists()) {
            System.out.println("文件不存在，跳过测试: " + filePath);
            return;
        }

        List<XlsTemporaryTableData> dataList = testReadExcelToObjectList(file);

        // 处理数据并查询数据库
        processDataWithDatabaseQuery(dataList);

    }


    public List<XlsTemporaryTableData> testReadExcelToObjectList(File file) throws IOException {

        List<XlsTemporaryTableData> dataList = new ArrayList<>();

        try (FileInputStream inputStream = new FileInputStream(file);
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);

            // 跳过表头，从第二行开始读取数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    XlsTemporaryTableData data = new XlsTemporaryTableData();
                    // 读取每列数据并设置到对象中
                    data.setCenterLng(getCellValueAsString(row.getCell(0)));
                    data.setCenterLat(getCellValueAsString(row.getCell(1)));
                    data.setStatus(getCellValueAsString(row.getCell(2)));
                    data.setAreaCode(getCellValueAsString(row.getCell(3)));

                    dataList.add(data);
                }
            }
        }

        // 验证数据
        assertFalse(dataList.isEmpty(), "数据列表不能为空");
        System.out.println("总共读取到 " + dataList.size() + " 条数据");

        return dataList;
    }

    private void processDataWithDatabaseQuery(List<XlsTemporaryTableData> dataList) throws SQLException {
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            for (XlsTemporaryTableData data : dataList) {
                // 组合经纬度
                String center = data.getCenterLng() + "," + data.getCenterLat();
                try {
                    // 执行SQL查询获取area_code
                    String areaCode = getAreaCodeByCenter(conn, center, "wkt_lower_left");
                    if (StringUtils.isBlank(areaCode)) {
                        areaCode = getAreaCodeByCenter(conn, center, "wkt_lower_right");
                        if (StringUtils.isBlank(areaCode)) {
                            areaCode = getAreaCodeByCenter(conn, center, "wkt_up_left");
                            if (StringUtils.isBlank(areaCode)) {
                                areaCode = getAreaCodeByCenter(conn, center, "wkt_up_right");
                            }
                        }
                    }
                    // 设置查询结果
                    if (areaCode != null && !areaCode.trim().isEmpty()) {
                        data.setAreaCode(areaCode);
                        System.out.println("查询成功 - 中心点: " + center + ", 区域代码: " + areaCode);
                    } else {
                        System.out.println("未找到匹配数据 - 中心点: " + center);
                    }

                    // 保存到数据库
                    saveToTemporaryTable(conn, data.getCenterLng(), data.getCenterLat(), areaCode);

                } catch (SQLException e) {
                    System.err.println("查询失败 - 中心点: " + center + ", 错误: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 根据中心点查询area_code
     */
    private static String getAreaCodeByCenter(Connection conn, String center, String field) throws SQLException {
        String sql = "SELECT area_code FROM resident_map_cell_resource WHERE " + field + " = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, center);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("area_code");
                }
            }
        }
        return null;
    }

    /**
     * 保存数据到temporary_table
     */
    private static boolean saveToTemporaryTable(Connection conn, String centerLng, String centerLat, String areaCode) throws SQLException {
        String insertSql = "INSERT IGNORE INTO temporary_table (center_lng, center_lat, area_code, status, update_time) VALUES (?, ?, ?, 'INITIALIZE', NOW())";
        try (PreparedStatement insertStmt = conn.prepareStatement(insertSql)) {
            insertStmt.setString(1, centerLng);
            insertStmt.setString(2, centerLat);
            insertStmt.setString(3, areaCode);
            int rows = insertStmt.executeUpdate();
            return rows > 0;
        }
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }


}