package heran.media.management.platform.utils;

import heran.media.management.platform.common.utils.DownloadFileUtils;
import org.apache.catalina.connector.ClientAbortException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.file.Path;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

/**
 * DownloadFileUtils 测试类
 * 主要测试客户端断开连接异常的处理
 */
public class DownloadFileUtilsTest {

    @TempDir
    Path tempDir;

    @Test
    public void testIsClientAbortException_WithClientAbortException() throws Exception {
        // 使用反射调用私有方法
        Method method = DownloadFileUtils.class.getDeclaredMethod("isClientAbortException", Throwable.class);
        method.setAccessible(true);

        // 测试 ClientAbortException
        ClientAbortException clientAbortException = new ClientAbortException("Connection aborted");
        boolean result = (boolean) method.invoke(null, clientAbortException);
        assertTrue(result);
    }

    @Test
    public void testIsClientAbortException_WithIOExceptionContainingKeywords() throws Exception {
        Method method = DownloadFileUtils.class.getDeclaredMethod("isClientAbortException", Throwable.class);
        method.setAccessible(true);

        // 测试包含关键词的 IOException
        IOException ioException1 = new IOException("你的主机中的软件中止了一个已建立的连接");
        boolean result1 = (boolean) method.invoke(null, ioException1);
        assertTrue(result1);

        IOException ioException2 = new IOException("Connection reset by peer");
        boolean result2 = (boolean) method.invoke(null, ioException2);
        assertTrue(result2);

        IOException ioException3 = new IOException("Broken pipe");
        boolean result3 = (boolean) method.invoke(null, ioException3);
        assertTrue(result3);
    }

    @Test
    public void testIsClientAbortException_WithNormalIOException() throws Exception {
        Method method = DownloadFileUtils.class.getDeclaredMethod("isClientAbortException", Throwable.class);
        method.setAccessible(true);

        // 测试普通的 IOException
        IOException normalException = new IOException("File not found");
        boolean result = (boolean) method.invoke(null, normalException);
        assertFalse(result);
    }

    @Test
    public void testIsClientAbortException_WithNullException() throws Exception {
        Method method = DownloadFileUtils.class.getDeclaredMethod("isClientAbortException", Throwable.class);
        method.setAccessible(true);

        // 测试 null 异常
        boolean result = (boolean) method.invoke(null, (Throwable) null);
        assertFalse(result);
    }

    @Test
    public void testSetFileResponse_WithClientAbortException() throws Exception {
        // 创建一个临时文件
        File tempFile = tempDir.resolve("test.xlsx").toFile();
        tempFile.createNewFile();

        // 创建 mock response
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        ServletOutputStream mockOutputStream = mock(ServletOutputStream.class);
        
        when(mockResponse.getOutputStream()).thenReturn(mockOutputStream);
        
        // 模拟客户端断开连接异常
        doThrow(new ClientAbortException("Connection aborted"))
            .when(mockOutputStream).write(any(byte[].class));

        // 使用 MockedStatic 来模拟 FileUtils.compressFile
        try (MockedStatic<heran.media.management.platform.common.utils.FileUtils> mockedFileUtils = 
             Mockito.mockStatic(heran.media.management.platform.common.utils.FileUtils.class)) {
            
            mockedFileUtils.when(() -> heran.media.management.platform.common.utils.FileUtils.compressFile(any()))
                          .thenReturn(tempFile);

            // 执行测试 - 应该不抛出异常
            assertDoesNotThrow(() -> {
                DownloadFileUtils.setFileResponse(mockResponse, Collections.singletonList(tempFile), "test");
            });
        }

        // 验证方法被调用
        verify(mockResponse).getOutputStream();
        verify(mockOutputStream).write(any(byte[].class));
    }

    @Test
    public void testSetLongFileResponse_WithClientAbortException() throws Exception {
        // 创建一个临时文件
        File tempFile = tempDir.resolve("test.xlsx").toFile();
        tempFile.createNewFile();

        // 创建 mock response
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        ServletOutputStream mockOutputStream = mock(ServletOutputStream.class);
        
        when(mockResponse.getOutputStream()).thenReturn(mockOutputStream);
        
        // 模拟客户端断开连接异常
        doThrow(new ClientAbortException("Connection aborted"))
            .when(mockOutputStream).write(any(byte[].class), anyInt(), anyInt());

        // 使用 MockedStatic 来模拟 FileUtils.compressFile
        try (MockedStatic<heran.media.management.platform.common.utils.FileUtils> mockedFileUtils = 
             Mockito.mockStatic(heran.media.management.platform.common.utils.FileUtils.class)) {
            
            mockedFileUtils.when(() -> heran.media.management.platform.common.utils.FileUtils.compressFile(any()))
                          .thenReturn(tempFile);

            // 执行测试 - 应该不抛出异常
            assertDoesNotThrow(() -> {
                DownloadFileUtils.setLongFileResponse(mockResponse, Collections.singletonList(tempFile), "test");
            });
        }

        // 验证方法被调用
        verify(mockResponse).getOutputStream();
    }

    @Test
    public void testDownloadFile_WithClientAbortException() throws Exception {
        // 创建 mock response
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        ServletOutputStream mockOutputStream = mock(ServletOutputStream.class);
        
        when(mockResponse.getOutputStream()).thenReturn(mockOutputStream);
        
        // 模拟客户端断开连接异常
        doThrow(new ClientAbortException("Connection aborted"))
            .when(mockOutputStream).write(any(byte[].class), anyInt(), anyInt());

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            DownloadFileUtils.downloadFile(mockResponse, "nonexistent", "test");
        });
    }
}
