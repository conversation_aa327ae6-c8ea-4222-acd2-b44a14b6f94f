package heran.media.management.platform.utils;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.sql.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
public class GridMergerTest {

    private static final String DB_URL = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    private static final String USER = "heran_app";
    private static final String PASSWORD = "5G5ZVlDjWE=";


//    private static final String DB_URL = "*****************************************************************************";
//    private static final String USER = "opworker";
//    private static final String PASSWORD = "Tango$#@!222";

    // 数据库连接池配置
    private static final HikariDataSource dataSource;

    static {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(DB_URL);
        config.setUsername(USER);
        config.setPassword(PASSWORD);

        // 优化连接池配置
        config.setMaximumPoolSize(200); // 减少连接数，避免资源竞争
        config.setMinimumIdle(10);
        config.setConnectionTimeout(30000); // 30秒连接超时
        config.setIdleTimeout(600000); // 10分钟空闲超时
        config.setMaxLifetime(1800000); // 30分钟最大生命周期（定期刷新）
        config.setLeakDetectionThreshold(300000); // 5分钟泄漏检测
        config.setValidationTimeout(10000); // 10秒验证超时
        config.setConnectionTestQuery("SELECT 1");
        config.setPoolName("GridMergerPool");

        // 连接保活配置
        config.addDataSourceProperty("autoReconnect", "true");
        config.addDataSourceProperty("failOverReadOnly", "false");
        config.addDataSourceProperty("maxReconnects", "10");
        config.addDataSourceProperty("initialTimeout", "10");
        config.addDataSourceProperty("socketTimeout", "0"); // 禁用socket超时
        config.addDataSourceProperty("connectTimeout", "30000");

        // 新增：连接保活配置
        config.addDataSourceProperty("useKeepAlive", "true");
        config.addDataSourceProperty("keepAliveTime", "300"); // 5分钟保活
        config.addDataSourceProperty("tcpKeepAlive", "true");

        dataSource = new HikariDataSource(config);
    }

    private static final int CORE_POOL_SIZE = 50; // 进一步降低线程数
    private static final int MAX_POOL_SIZE = 100; // 降低最大线程数
    private static final int QUEUE_CAPACITY = 1000; // 降低队列容量

    // 添加监控计数器
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private final AtomicLong timeoutCount = new AtomicLong(0);

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(QUEUE_CAPACITY), new ThreadFactory() {
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, "grid-merger-" + threadNumber.getAndIncrement());
            thread.setDaemon(false);
            return thread;
        }
    }, new BlockWhenFullPolicy() // 自定义拒绝策略
    );

    // 自定义拒绝策略 - 阻塞提交
    private static class BlockWhenFullPolicy implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            try {
                executor.getQueue().put(r);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RejectedExecutionException("Task rejected while waiting", e);
            }
        }
    }

    // 添加内存级别的去重集合
    private final Set<String> processingCenters = ConcurrentHashMap.newKeySet();

    // 添加监控线程
    private final ScheduledExecutorService monitorExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "monitor-thread");
        t.setDaemon(true); // 设置为守护线程
        return t;
    });

    enum Quadrant {
        // 右下
        NW("wkt_lower_right", "wkt_lower_left"), // 左下
        NE("wkt_lower_left", "wkt_up_left"), // 右上
        SW("wkt_up_right", "wkt_lower_right"), // 左上
        SE("wkt_up_left", "wkt_lower_left");

        final String horizontalVertex;
        final String verticalVertex;

        Quadrant(String horizontalVertex, String verticalVertex) {
            this.horizontalVertex = horizontalVertex;
            this.verticalVertex = verticalVertex;
        }
    }

    private boolean markPointAsProcessed(Connection conn, String centerLng, String centerLat, String areaCode) throws SQLException {
        // 使用独立连接进行状态管理
        try (Connection statusConn = dataSource.getConnection()) {
            statusConn.setAutoCommit(true); // 状态管理用自动提交
            // 1. 尝试原子更新
            String updateSql = "UPDATE temporary_table SET status = 'PROCESSING', update_time = NOW() " +
                    "WHERE center_lng = ? AND center_lat = ? AND area_code = ? AND status != 'DONE'";
            try (PreparedStatement updateStmt = statusConn.prepareStatement(updateSql)) {
                updateStmt.setString(1, centerLng);
                updateStmt.setString(2, centerLat);
                updateStmt.setString(3, areaCode);
                int rows = updateStmt.executeUpdate();
                if (rows > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean saveTemporaryTable(String centerLng, String centerLat, String areaCode) throws SQLException {
        // 使用独立连接进行状态管理
        try (Connection statusConn = dataSource.getConnection()) {
            statusConn.setAutoCommit(true); // 状态管理用自动提交
            // 2. 如果UPDATE没有生效，尝试INSERT
            String insertSql = "INSERT IGNORE INTO temporary_table (center_lng, center_lat, area_code, status, update_time) VALUES (?, ?, ?, 'INITIALIZE', NOW())";
            try (PreparedStatement insertStmt = statusConn.prepareStatement(insertSql)) {
                insertStmt.setString(1, centerLng);
                insertStmt.setString(2, centerLat);
                insertStmt.setString(3, areaCode);
                int rows = insertStmt.executeUpdate();
                return rows > 0;
            }
        }
    }

    /**
     * @param conn
     * @param centerLng
     * @param centerLat
     * @param areaCode
     * @return
     * @throws SQLException
     */
    private boolean insertNewCenterIfNotExists(Connection conn, String centerLng, String centerLat, String areaCode) throws SQLException {
        String sql = "INSERT IGNORE INTO temporary_table (center_lng, center_lat, area_code, status, update_time) VALUES (?, ?, ?, 'PENDING', NOW())";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, centerLng);
            stmt.setString(2, centerLat);
            stmt.setString(3, areaCode);
            int rows = stmt.executeUpdate();
            return rows > 0;
        }
    }

    // 检查点是否已处理
    private static boolean isPointProcessed(Connection conn, String centerLng, String centerLat) throws SQLException {
        String sql = "SELECT 1 FROM temporary_table WHERE center_lng = ? AND center_lat = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, centerLng);
            pstmt.setString(2, centerLat);
            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    // 添加监控方法
    private void startMonitoring() {
        monitorExecutor.scheduleAtFixedRate(() -> {
            try {
                Runtime runtime = Runtime.getRuntime();
                long totalMemory = runtime.totalMemory();
                long freeMemory = runtime.freeMemory();
                long usedMemory = totalMemory - freeMemory;

                log.info("=== 系统监控状态 ===");
                log.info("内存使用: {}/{} MB ({}%)", usedMemory / 1024 / 1024, totalMemory / 1024 / 1024, (usedMemory * 100) / totalMemory);
                log.info("线程池状态: 活跃={}, 队列={}, 已完成={}", executor.getActiveCount(), executor.getQueue().size(), executor.getCompletedTaskCount());
                log.info("数据库连接池: 活跃={}, 空闲={}, 总计={}", dataSource.getHikariPoolMXBean().getActiveConnections(), dataSource.getHikariPoolMXBean().getIdleConnections(), dataSource.getHikariPoolMXBean().getTotalConnections());
                log.info("处理统计: 成功={}, 错误={}, 超时={}", processedCount.get(), errorCount.get(), timeoutCount.get());
                log.info("正在处理的中心点数量: {}", processingCenters.size());


                // 如果内存使用超过80%，清理一些数据
                if ((usedMemory * 100) / totalMemory > 80) {
                    log.warn("内存使用率过高，建议重启程序");
                    // 清理一些内存
                    System.gc();
                }

                // 如果队列积压过多，记录警告
                if (executor.getQueue().size() > QUEUE_CAPACITY * 0.6) {
                    log.warn("任务队列积压严重: {}", executor.getQueue().size());
                }

                // 如果数据库连接池接近满载，记录警告
                if (dataSource.getHikariPoolMXBean().getActiveConnections() > 40) {
                    log.warn("数据库连接池使用率过高: {}", dataSource.getHikariPoolMXBean().getActiveConnections());
                }

                // 连接保活：定期执行简单查询
                try (Connection conn = dataSource.getConnection()) {
                    try (PreparedStatement stmt = conn.prepareStatement("SELECT 1")) {
                        stmt.executeQuery();
                    }
                    log.debug("数据库连接保活成功");
                } catch (SQLException e) {
                    log.error("数据库连接保活失败: {}", e.getMessage());
                }

            } catch (Exception e) {
                log.error("监控过程中发生错误: ", e);
            }
        }, 30, 30, TimeUnit.SECONDS); // 每30秒监控一次
    }


    public static void main(String[] args) throws SQLException {
        GridMergerTest merger = new GridMergerTest();
        merger.processMerge();
    }


    // 初始化处理队列
    private void initializeProcessingQueue(Queue<String> queue) throws SQLException {
        // 添加初始中心点
        //String centerStr = "121.927498,30.942517,310115";
        String centerStr = "121.855498,31.150707,310115";

        saveTemporaryTable("121.855498", "31.150707", "310115");

        queue.add(centerStr);
        log.info("添加初始中心点: {}", centerStr);
    }


    /**
     * 恢复未完成的中心点到队列
     */
    private void restoreUnfinishedCenters(Queue<String> queue) {
        String sql = "SELECT center_lng, center_lat, area_code FROM temporary_table WHERE status !='DONE'";
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            int restoredCount = 0;
            while (rs.next()) {
                String centerLng = rs.getString("center_lng");
                String centerLat = rs.getString("center_lat");
                String areaCode = rs.getString("area_code");

                String centerStr = centerLng + "," + centerLat + "," + areaCode;
                queue.add(centerStr);
                restoredCount++;

                log.info("恢复未完成中心点: {}", centerStr);
            }

            log.info("总共恢复 {} 个未完成的中心点", restoredCount);

        } catch (SQLException e) {
            log.error("恢复未完成中心点失败", e);
        }
    }

    /**
     * 更新中心点状态
     */
    private void updateCenterStatus(Connection conn, String centerLng, String centerLat, String status) {
        String sql = "UPDATE temporary_table SET status = ?, update_time = NOW() WHERE center_lng = ? AND center_lat = ?";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, status);
            stmt.setString(2, centerLng);
            stmt.setString(3, centerLat);
            stmt.executeUpdate();
        } catch (SQLException e) {
            log.error("更新中心点状态失败: {},{}", centerLng, centerLat, e);
        }
    }

    //@Test
    public void processMerge() throws SQLException {
        log.info("=== 开始网格合并处理 ===");
        long startTime = System.currentTimeMillis();

        // 启动监控
        startMonitoring();

        Queue<String> queue = new LinkedBlockingQueue<>(50000);

        // 恢复未完成的中心点
        restoreUnfinishedCenters(queue);

        if (queue.isEmpty()) {
            initializeProcessingQueue(queue);
        }

        try {
            List<Future<?>> futures = new ArrayList<>();
            int submittedTasks = 0;
            int completedTasks = 0;
            long lastProgressTime = System.currentTimeMillis();

            while (!queue.isEmpty() || !futures.isEmpty()) {
                // 提交新的任务  如果超过队列百分之八十 进行背压模式外层一直循环 等待线队列任务处理 处理低于线程数百分之八十 接着提交任务  无进展超过三十分钟就会超时
                while (!queue.isEmpty() && executor.getQueue().size() < QUEUE_CAPACITY * 0.6) {
                    log.info("queue size:{}", queue.size());
                    log.info("线程池队列大小：{}", executor.getQueue().size());
                    String center = queue.poll();
                    if (center != null) {
                        Future<?> future = executor.submit(() -> {
                            try {
                                processCenter(center, queue);
                            } catch (Exception e) {
                                log.error("处理中心点失败: {}", center, e);
                                errorCount.incrementAndGet();
                                throw e; // 重新抛出异常
                            }
                        });
                        futures.add(future);
                        submittedTasks++;
                        if (submittedTasks % 10 == 0) {
                            log.info("已提交任务数: {}, 队列剩余: {}, 线程池活跃线程: {}", submittedTasks, queue.size(), executor.getActiveCount());
                        }
                    }
                }

                // 检查任务完成情况
                Iterator<Future<?>> iterator = futures.iterator();
                while (iterator.hasNext()) {
                    Future<?> future = iterator.next();
                    if (future.isDone()) {
                        try {
                            future.get(1, TimeUnit.SECONDS); // 1秒超时
                            completedTasks++;
                            // 任务成功完成，更新进度时间
                            lastProgressTime = System.currentTimeMillis();
                        } catch (TimeoutException e) {
                            timeoutCount.incrementAndGet();
                            log.warn("任务执行超时");
                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                            log.error("任务执行失败: ", e);
                        }
                        iterator.remove();
                    }
                }
                // 检查是否长时间没有进展
                long currentTime = System.currentTimeMillis();
                long noProgressDuration = currentTime - lastProgressTime;
                if (currentTime - lastProgressTime > 300000) { // 5分钟没有进展
                    log.warn("程序可能卡住，当前状态: 队列={}, 活跃线程={}, 已完成={}", queue.size(), executor.getActiveCount(), completedTasks);

                    if (noProgressDuration > 1800000) {
                        log.error("程序运行30分钟仍无进展，建议手动停止");
                        break;
                    }

                    // 重置进度时间，给程序更多时间
                    lastProgressTime = currentTime;
                    log.info("重置进度时间，继续等待任务完成");
                }
            }

            // 等待所有任务完成
            log.info("所有任务已提交，等待线程池关闭...");
            executor.shutdown();
            boolean terminated = executor.awaitTermination(30, TimeUnit.MINUTES); // 增加超时时间
            if (terminated) {
                log.info("线程池正常关闭");
            } else {
                log.warn("线程池超时关闭，强制终止");
                executor.shutdownNow();
            }

            // 关闭监控
            monitorExecutor.shutdown();

            long endTime = System.currentTimeMillis();
            log.info("=== 网格合并处理完成 ===");
            log.info("总耗时: {} ms ({} 分钟)", endTime - startTime, (endTime - startTime) / 60000);
            log.info("总提交任务数: {}, 总完成任务数: {}", submittedTasks, completedTasks);

        } catch (Exception e) {
            log.error("处理过程中发生错误: ", e);
        } finally {
            // 确保资源清理
            cleanup();
            Set<Thread> threadSet = Thread.getAllStackTraces().keySet();
            for (Thread t : threadSet) {
                System.out.println("Thread: " + t.getName() + ", daemon=" + t.isDaemon() + ", state=" + t.getState());
            }
        }
    }

    // 资源清理方法
    private void cleanup() {
        try {
            if (!executor.isShutdown()) {
                executor.shutdownNow();
            }
            if (!monitorExecutor.isShutdown()) {
                monitorExecutor.shutdownNow();
            }
            if (dataSource != null && !dataSource.isClosed()) {
                dataSource.close();
            }
            log.info("资源清理完成");
        } catch (Exception e) {
            log.error("资源清理过程中发生错误: ", e);
        }
    }

    // 添加连接重试机制
    private Connection getConnectionWithRetry(int maxRetries) throws SQLException {
        SQLException lastException = null;
        for (int i = 0; i < maxRetries; i++) {
            try {
                Connection conn = dataSource.getConnection();

                // 验证连接是否有效
                if (isConnectionValid(conn)) {
                    return conn;
                } else {
                    log.warn("获取到无效连接，重试 {}/{}", i + 1, maxRetries);
                    conn.close();
                }
            } catch (SQLException e) {
                lastException = e;
                log.warn("获取数据库连接失败，重试 {}/{}: {}", i + 1, maxRetries, e.getMessage());
                if (i < maxRetries - 1) {
                    try {
                        Thread.sleep(1000L * (i + 1)); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new SQLException("连接重试被中断", ie);
                    }
                }
            }
        }
        throw new SQLException("无法获取数据库连接，已重试" + maxRetries + "次", lastException);
    }

    // 验证连接有效性
    private boolean isConnectionValid(Connection conn) {
        try {
            return conn != null && !conn.isClosed() && conn.isValid(5);
        } catch (SQLException e) {
            return false;
        }
    }

    // 添加连接超时检测和处理
    private boolean isConnectionTimeout(SQLException e) {
        String message = e.getMessage().toLowerCase();
        return message.contains("packet") || message.contains("timeout") || message.contains("connection") || message.contains("communications link failure");
    }


    private Map<String, List<String>> regionCodeCityMap = null;
    private Map<String, List<String>> regionCodeProvinceMap = null;

    private void processCenter(String center, Queue<String> queue) {
        String threadName = Thread.currentThread().getName();
        // 关键：内存级别去重检查
        if (!processingCenters.add(center)) {
            log.info("[{}] 中心点 {} 正在被其他线程处理，跳过", threadName, center);
            return;
        }
        long startTime = System.currentTimeMillis();

        log.info("[{}] 开始处理中心点: {}", threadName, center);

        Connection conn = null;
        try {
            conn = getConnectionWithRetry(3); // 最多重试3次
            // 设置连接超时和保活
            conn.setNetworkTimeout(Executors.newSingleThreadExecutor(), 3600000); // 60分钟网络超时
            conn.setAutoCommit(false);

            // 设置会话变量，支持长时间查询
            try (PreparedStatement stmt = conn.prepareStatement(
                    "SET SESSION wait_timeout=3600, " +
                            "interactive_timeout=3600, " +
                            "net_read_timeout=3600, " +
                            "net_write_timeout=3600, " +
                            "lock_wait_timeout=3600, " +
                            "innodb_lock_wait_timeout=3600")) {
                stmt.executeUpdate();
            }

            String[] coords = center.split(",");

            // 关键修改：提前执行数据库标记并检查
            if (!markPointAsProcessed(conn, coords[0], coords[1], coords[2])) {
                log.info("[{}] 中心点 {} 已处理过（数据库标记），跳过", threadName, center);
                return;
            }

            // 加载区域代码映射（如果为空）
//            if (regionCodeCityMap == null || regionCodeCityMap.isEmpty()) {
//                regionCodeCityMap = getRegionCodeCityMap(conn);
//            }
//
//            if (regionCodeProvinceMap == null || regionCodeProvinceMap.isEmpty()) {
//                regionCodeProvinceMap = getRegionCodeProvinceMap(conn);
//            }

            Coordinate centerCord = new Coordinate(Double.parseDouble(coords[0]), Double.parseDouble(coords[1]), coords[2]);
            log.debug("[{}] 解析中心点坐标: lng={}, lat={}", threadName, centerCord.lng, centerCord.lat);

            log.info("----------开始收集象限网格数据-----");
            long timeMillis = System.currentTimeMillis();
            // 第一步：收集网格数据（可能耗时十来分钟）
            Map<Quadrant, List<OriginalGrid>> allGrids = collectGrids(conn, centerCord);
            log.info("收集网格数据成功 耗费时间为:{}", System.currentTimeMillis() - timeMillis);

            // 第二步：创建合并记录
            log.info("[{}] 开始创建中心点 {} 的合并记录", threadName, center);
            long mergeStartTime = System.currentTimeMillis();
            long mergedId = createMergedRecord(conn, allGrids, centerCord, centerCord.getCode());
            long mergeEndTime = System.currentTimeMillis();
            log.info("[{}] 中心点 {} 创建合并记录成功, 耗时: {} ms", threadName, center, mergeEndTime - mergeStartTime);

            // 第三步：更新原始网格
            log.info("[{}] 开始更新中心点 {} 关联的原始网格", threadName, center);
            long updateStartTime = System.currentTimeMillis();
            updateOriginalGrids(conn, allGrids, mergedId, centerCord.getCode());
            long updateEndTime = System.currentTimeMillis();
            log.info("[{}] 中心点 {} 更新原始网格完成，耗时: {} ms", threadName, center, updateEndTime - updateStartTime);

            // 第四步：查找下一个中心点
            log.info("[{}] 开始查找中心点 {} 的相邻中心点", threadName, center);
            long findStartTime = System.currentTimeMillis();
            findAndAddNextCenters(conn, allGrids, center, queue, centerCord);
            long findEndTime = System.currentTimeMillis();
            log.info("[{}] 中心点 {} 查找相邻中心点完成，耗时: {} ms", threadName, center, findEndTime - findStartTime);

            // 标记为完成
            updateCenterStatus(conn, coords[0], coords[1], "DONE");
            // 第五步：提交事务
            conn.commit();
            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;
            log.info("[{}] 成功处理中心点: {}，总耗时: {} ms", threadName, center, totalTime);
            processedCount.incrementAndGet();
        } catch (SQLException e) {
            log.error("[{}] 处理中心点 {} 时发生SQL错误: {}", threadName, center, e.getMessage(), e);
            errorCount.incrementAndGet();
            // 如果是连接超时错误，记录详细信息
            if (isConnectionTimeout(e)) {
                log.error("[{}] 数据库连接超时，当前连接池状态: 活跃={}, 空闲={}, 总计={}", threadName, dataSource.getHikariPoolMXBean().getActiveConnections(), dataSource.getHikariPoolMXBean().getIdleConnections(), dataSource.getHikariPoolMXBean().getTotalConnections());
            }
            // 尝试回滚事务
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException rollbackEx) {
                    log.error("[{}] 回滚事务失败: {}", threadName, rollbackEx.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("[{}] 处理中心点 {} 时发生未知错误: ", threadName, center, e);
            errorCount.incrementAndGet();

            // 尝试回滚事务
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException rollbackEx) {
                    log.error("[{}] 回滚事务失败: {}", threadName, rollbackEx.getMessage());
                }
            }
        } finally {
            // 处理完成后从集合中移除
            processingCenters.remove(center);
            // 关闭连接
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    log.error("[{}] 关闭数据库连接失败: {}", threadName, e.getMessage());
                }
            }
        }
    }

    private void findAndAddNextCenters(Connection conn, Map<Quadrant, List<OriginalGrid>> allGrids, String centerPoint, Queue<String> queue, Coordinate centerCord) {
        try {

            List<OriginalGrid> gridList = allGrids.values().stream().flatMap(List::stream).collect(Collectors.toList());
            //起始网格
            Map<Quadrant, OriginalGrid> originGridsMap = findOriginGridsMap(gridList, centerCord);

            // 找右边的中心点：从SE象限的25个网格中移动5次找到边缘网格，然后向右延伸5格
            findNextCenterInDirection(conn, gridList, centerPoint, "right", queue, originGridsMap);

            // 找左边的中心点：从NW象限的25个网格中移动5次找到边缘网格，然后向左延伸5格
            findNextCenterInDirection(conn, gridList, centerPoint, "left", queue, originGridsMap);

            // 找上边的中心点：从NE象限的25个网格中移动5次找到边缘网格，然后向上延伸5格
            findNextCenterInDirection(conn, gridList, centerPoint, "up", queue, originGridsMap);

            // 找下边的中心点：从SW象限的25个网格中移动5次找到边缘网格，然后向下延伸5格
            findNextCenterInDirection(conn, gridList, centerPoint, "down", queue, originGridsMap);

        } catch (Exception e) {
            log.error("[{}] 查找下一个中心点时发生未知错误: {}", Thread.currentThread().getName(), e.getMessage(), e);
        }
    }

    private void findNextCenterInDirection(Connection conn, List<OriginalGrid> gridList, String centerPoint, String direction, Queue<String> queue, Map<Quadrant, OriginalGrid> originGridsMap) throws Exception {
//        // 根据方向找到对应的边缘网格（在25个网格中移动5次）
//        OriginalGrid edgeGrid = findEdgeGridByMovingInGrids(allGrids, centerPoint, direction);
//        if (edgeGrid == null) {
//            log.warn("[{}] 在 {} 方向未找到边缘网格", Thread.currentThread().getName(), direction);
//            return;
//        }
//        // 从边缘网格开始向指定方向延伸5格，找到下一个大网格的中心点
//        findNextCenterFromEdge(conn, edgeGrid, direction, queue);
        OriginalGrid edgeGridByMovingInGrids = findEdgeGridByMovingInGrids(direction, gridList, originGridsMap);
        if (edgeGridByMovingInGrids == null) {
            log.info("中心点 :{} 在根据起始网格找边缘网格没有找到", centerPoint);
            return;
        }
        findNextCenterFromEdge(conn, edgeGridByMovingInGrids, direction, queue);
        //findNextBigGridCenter(conn, centerPoint, direction, queue);
    }

    private OriginalGrid findEdgeGridByMovingInGrids(String direction, List<OriginalGrid> gridList, Map<Quadrant, OriginalGrid> originGridsMap) {
        switch (direction) {
            case "right":
                // 从SE象限的25个网格中找左下顶点等于中心点的网格，然后向右移动5次
                return moveInGridsToFindEdge(direction, originGridsMap.get(Quadrant.NE), gridList);
            case "left":
                // 从NW象限的25个网格中找右上顶点等于中心点的网格，然后向左移动5次
                return moveInGridsToFindEdge(direction, originGridsMap.get(Quadrant.SW), gridList);
            case "up":
                // 从NE象限的25个网格中找右下顶点等于中心点的网格，然后向上移动5次
                return moveInGridsToFindEdge(direction, originGridsMap.get(Quadrant.NW), gridList);
            case "down":
                // 从SW象限的25个网格中找左上顶点等于中心点的网格，然后向下移动5次
                return moveInGridsToFindEdge(direction, originGridsMap.get(Quadrant.SE), gridList);
            default:
                return null;
        }
    }

    private OriginalGrid moveInGridsToFindEdge(String direction, OriginalGrid originalGrid, List<OriginalGrid> gridList) {
        if (originalGrid == null) {
            return null;
        }

        if (gridList == null || gridList.isEmpty()) {
            return null;
        }
        // 2. 在25个网格中移动5次找到边缘网格
        for (int i = 0; i < 4; i++) {
            OriginalGrid nextGrid = findNextGridInDirection(gridList, originalGrid, direction);
            if (nextGrid == null) {
                return null;
            }
            originalGrid = nextGrid;
        }

        return originalGrid;
    }

    private OriginalGrid findEdgeGridByMovingInGrids(Map<Quadrant, List<OriginalGrid>> allGrids, String centerPoint, String direction) {
        String[] centerCoords = centerPoint.split(",");
        double centerLng = Double.parseDouble(centerCoords[0]);
        double centerLat = Double.parseDouble(centerCoords[1]);

        switch (direction) {
            case "right":
                // 从SE象限的25个网格中找左下顶点等于中心点的网格，然后向右移动5次
                return moveInGridsToFindEdge(allGrids.get(Quadrant.NE), centerLng, centerLat, "lowerLeft", "right");
            case "left":
                // 从NW象限的25个网格中找右上顶点等于中心点的网格，然后向左移动5次
                return moveInGridsToFindEdge(allGrids.get(Quadrant.SW), centerLng, centerLat, "upperRight", "left");
            case "up":
                // 从NE象限的25个网格中找右下顶点等于中心点的网格，然后向上移动5次
                return moveInGridsToFindEdge(allGrids.get(Quadrant.NW), centerLng, centerLat, "lowerRight", "up");
            case "down":
                // 从SW象限的25个网格中找左上顶点等于中心点的网格，然后向下移动5次
                return moveInGridsToFindEdge(allGrids.get(Quadrant.SE), centerLng, centerLat, "upperLeft", "down");
            default:
                return null;
        }
    }

    private OriginalGrid moveInGridsToFindEdge(List<OriginalGrid> grids, double centerLng, double centerLat, String startVertexType, String direction) {
        if (grids == null || grids.isEmpty()) return null;

        // 1. 找到起始网格（顶点等于中心点的网格）
        OriginalGrid currentGrid = findGridWithMatchingVertex(grids, centerLng, centerLat, startVertexType);
        if (currentGrid == null) return null;

        // 2. 在25个网格中移动5次找到边缘网格
        for (int i = 0; i < 5; i++) {
            OriginalGrid nextGrid = findNextGridInDirection(grids, currentGrid, direction);
            if (nextGrid == null) {
                // 如果找不到下一个网格，说明已经到达边缘
                return currentGrid;
            }
            currentGrid = nextGrid;
        }

        return currentGrid;
    }

    private OriginalGrid findGridWithMatchingVertex(List<OriginalGrid> grids, double centerLng, double centerLat, String vertexType) {
        for (OriginalGrid grid : grids) {
            double vertexLng, vertexLat;

            switch (vertexType) {
                case "upperLeft": // 左上
                    vertexLng = grid.upperLeftLng;
                    vertexLat = grid.upperLeftLat;
                    break;
                case "upperRight": // 右上
                    vertexLng = grid.upperRightLng;
                    vertexLat = grid.upperRightLat;
                    break;
                case "lowerLeft": // 左下
                    vertexLng = grid.lowerLeftLng;
                    vertexLat = grid.lowerLeftLat;
                    break;
                case "lowerRight": // 右下
                    vertexLng = grid.lowerRightLng;
                    vertexLat = grid.lowerRightLat;
                    break;
                default:
                    continue;
            }

            // 精确匹配坐标
            if (vertexLng == centerLng && vertexLat == centerLat) {
                return grid;
            }
        }
        return null;
    }

    private OriginalGrid findNextGridInDirection(List<OriginalGrid> grids, OriginalGrid currentGrid, String direction) {
        String targetVertex = null;
        String searchVertex = null;

        switch (direction) {
            case "right":
                // 当前网格的右下顶点匹配下一个网格的左下顶点
                targetVertex = formatCoordinate(new Coordinate(currentGrid.lowerRightLng, currentGrid.lowerRightLat, null));
                searchVertex = "lowerLeft";
                break;
            case "left":
                // 当前网格的左上顶点匹配下一个网格的右上顶点
                targetVertex = formatCoordinate(new Coordinate(currentGrid.upperLeftLng, currentGrid.upperLeftLat, null));
                searchVertex = "upperRight";
                break;
            case "up":
                // 当前网格的右上顶点匹配下一个网格的右下顶点
                targetVertex = formatCoordinate(new Coordinate(currentGrid.upperRightLng, currentGrid.upperRightLat, null));
                searchVertex = "lowerRight";
                break;
            case "down":
                // 当前网格的左下顶点匹配下一个网格的左上顶点
                targetVertex = formatCoordinate(new Coordinate(currentGrid.lowerLeftLng, currentGrid.lowerLeftLat, null));
                searchVertex = "upperLeft";
                break;
        }

        // 在25个网格中查找匹配的网格
        for (OriginalGrid grid : grids) {
            double vertexLng, vertexLat;

            switch (Objects.requireNonNull(searchVertex)) {
                case "upperLeft":
                    vertexLng = grid.upperLeftLng;
                    vertexLat = grid.upperLeftLat;
                    break;
                case "upperRight":
                    vertexLng = grid.upperRightLng;
                    vertexLat = grid.upperRightLat;
                    break;
                case "lowerLeft":
                    vertexLng = grid.lowerLeftLng;
                    vertexLat = grid.lowerLeftLat;
                    break;
                case "lowerRight":
                    vertexLng = grid.lowerRightLng;
                    vertexLat = grid.lowerRightLat;
                    break;
                default:
                    continue;
            }

            String gridVertex = formatCoordinate(new Coordinate(vertexLng, vertexLat, null));
            if (targetVertex.equals(gridVertex)) {
                return grid;
            }
        }
        return null;
    }

    private void findNextCenterFromEdge(Connection conn, OriginalGrid startGrid, String direction, Queue<String> queue) throws SQLException {
        String nextCornerField = null;
        String moveField = null;
        String startPoint = null;

        switch (direction) {
            case "right":
                nextCornerField = "wkt_lower_left";
                moveField = "wkt_lower_right";
                startPoint = formatCoordinate(new Coordinate(startGrid.lowerRightLng, startGrid.lowerRightLat, null));
                break;
            case "left":
                nextCornerField = "wkt_up_right";
                moveField = "wkt_up_left";
                startPoint = formatCoordinate(new Coordinate(startGrid.upperLeftLng, startGrid.upperLeftLat, null));
                break;
            case "up":
                nextCornerField = "wkt_lower_right";
                moveField = "wkt_up_right";
                startPoint = formatCoordinate(new Coordinate(startGrid.upperRightLng, startGrid.upperRightLat, null));
                break;
            case "down":
                nextCornerField = "wkt_up_left";
                moveField = "wkt_lower_left";
                startPoint = formatCoordinate(new Coordinate(startGrid.lowerLeftLng, startGrid.lowerLeftLat, null));
                break;
        }

        String currCorner = startPoint;
        String areaCode = null;

        for (int i = 0; i < 5; i++) {
            String sql = "SELECT " + moveField + ",area_code FROM resident_map_cell_resource WHERE " + nextCornerField + " = ?";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, currCorner);
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    currCorner = rs.getString(moveField);
                    areaCode = rs.getString("area_code");
                } else {
                    return; // 找不到下一个格子，直接返回
                }
            }
        }

        // 添加新的中心点
        String nextCenterStr = currCorner;

        if (StringUtils.isNotEmpty(nextCenterStr)) {
            String[] split = nextCenterStr.split(",");

            // 构建完整的中心点字符串（包含areaCode）
            String fullCenterStr = StringUtils.isNotEmpty(areaCode) ? nextCenterStr + "," + areaCode : nextCenterStr;

            // 一次性检查：数据库级别和内存级别
            if (isPointProcessed(conn, split[0], split[1]) || processingCenters.contains(fullCenterStr)) {
                log.info("[{}] 中心点已处理过或正在处理: {}", direction, fullCenterStr);
                return;
            }

            // 添加到队列（使用同步块确保线程安全）
            synchronized (queue) {
                // 双重检查：防止在等待同步锁期间状态发生变化
                if (isPointProcessed(conn, split[0], split[1]) || processingCenters.contains(fullCenterStr)) {
                    log.info("[{}] 中心点在同步块内检查时已被处理: {}", direction, fullCenterStr);
                    return;
                }
                boolean b = saveTemporaryTable(split[0], split[1], areaCode);
                if (b) {
                    log.info("数据库插入新的中心点成功！");
                }
                queue.add(fullCenterStr);
                log.info("找到新的中心点: {} 方向: {}", fullCenterStr, direction);
            }
        }
    }


    public void findNextBigGridCenter(Connection conn, String centerStr, String direction, Queue<String> queue) throws Exception {
        String startSql = null;
        String nextCornerField = null;
        String moveField = null;

        // 修复SQL语句中的字段重复问题
        if ("right".equals(direction)) {
            startSql = "SELECT wkt_lower_right,area_code FROM resident_map_cell_resource WHERE wkt_lower_left = ?";
            nextCornerField = "wkt_lower_left";
            moveField = "wkt_lower_right";
        } else if ("up".equals(direction)) {
            startSql = "SELECT wkt_up_right,area_code FROM resident_map_cell_resource WHERE wkt_lower_right = ? ";
            nextCornerField = "wkt_lower_right";
            moveField = "wkt_up_right";
        } else if ("left".equals(direction)) {
            startSql = "SELECT wkt_up_left,area_code FROM resident_map_cell_resource WHERE wkt_up_right = ? ";
            nextCornerField = "wkt_up_right";
            moveField = "wkt_up_left";
        } else if ("down".equals(direction)) {
            startSql = "SELECT wkt_lower_left,area_code FROM resident_map_cell_resource WHERE wkt_up_left = ? ";
            nextCornerField = "wkt_up_left";
            moveField = "wkt_lower_left";
        } else {
            throw new IllegalArgumentException("方向只支持 right/up/left/down");
        }

        String currCorner = centerStr;
        String areaCode = null;

        // 移动10格找到下一个大网格的中心点
        for (int i = 0; i < 10; i++) {
            String sql = (i == 0) ? startSql : "SELECT " + moveField + ",area_code FROM resident_map_cell_resource WHERE " + nextCornerField + " = ? ";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                String[] split = currCorner.split(",");
                String s = split[0] + "," + split[1];
                stmt.setString(1, s);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        currCorner = rs.getString(moveField);
                        areaCode = rs.getString("area_code");
                    } else {
                        // 找不到下一个网格，直接返回
                        return;
                    }
                }
            }
        }

        if (StringUtils.isNotEmpty(currCorner)) {
            String[] split = currCorner.split(",");

            // 构建完整的中心点字符串（包含areaCode）
            String fullCenterStr = StringUtils.isNotEmpty(areaCode) ? currCorner + "," + areaCode : currCorner;

            // 一次性检查：数据库级别和内存级别
            if (isPointProcessed(conn, split[0], split[1]) || processingCenters.contains(fullCenterStr)) {
                log.info("[{}] 中心点已处理过或正在处理: {}", direction, fullCenterStr);
                return;
            }

            // 添加到队列（使用同步块确保线程安全）
            synchronized (queue) {
                // 双重检查：防止在等待同步锁期间状态发生变化
                if (isPointProcessed(conn, split[0], split[1]) || processingCenters.contains(fullCenterStr)) {
                    log.info("[{}] 中心点在同步块内检查时已被处理: {}", direction, fullCenterStr);
                    return;
                }
                // 添加到内存处理集合和队列
                processingCenters.add(fullCenterStr);
                queue.add(fullCenterStr);
                log.info("找到新的中心点: {} 方向: {}", fullCenterStr, direction);
            }
        }
    }

    private Map<Quadrant, OriginalGrid> findOriginGrids(Connection conn, Coordinate center) throws SQLException {
        Map<Quadrant, OriginalGrid> origins = new EnumMap<>(Quadrant.class);
        for (Quadrant q : Quadrant.values()) {
            String sql;
            switch (q) {
                case NW:
                    sql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_lower_right = ?";
                    break;
                case NE:
                    sql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_lower_left = ?";
                    break;
                case SW:
                    sql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_up_right = ?";
                    break;
                case SE:
                    sql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_up_left = ?";
                    break;
                default:
                    throw new IllegalArgumentException("未知象限: " + q);
            }
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, formatCoordinate(center));
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    origins.put(q, mapToGrid(rs));
                }
            }
        }
        return origins;
    }

    // 新的网格收集方法 - 返回 Map<Quadrant, List<OriginalGrid>>
    private Map<Quadrant, List<OriginalGrid>> collectGrids(Connection conn, Coordinate centerPoint) throws Exception {
        long start = System.currentTimeMillis();

        // 设置查询超时
//        try (PreparedStatement stmt = conn.prepareStatement("SET SESSION max_execution_time=1800")) {
//            stmt.executeUpdate(); // 30分钟查询超时
//        }

        //区域范围处理
        List<OriginalGrid> grids = getNearestGrids(conn, centerPoint, 150, "area");
        Map<Quadrant, OriginalGrid> originGridsMap = findOriginGridsMap(grids, centerPoint);
        Map<Quadrant, List<OriginalGrid>> map = filterConnectedGridsByQuadrant(originGridsMap, grids, centerPoint);
        log.info("中心点为：{} 区域聚合数据耗时：{}", centerPoint, System.currentTimeMillis() - start);

        //如果缺少的格子过于少 低于五十个进行 城市 省区范围查找 否则过少网格查找 进行递归查询
//        if (map.values().stream().mapToInt(List::size).sum() <= 50) {
//            long cityTime = System.currentTimeMillis();
//            log.info("中心点为：{} 进入到城市区域查询数据", centerPoint.getLng() + centerPoint.getLat());
//            grids = getNearestGrids(conn, centerPoint, 150, "city");
//            originGridsMap = findOriginGridsMap(grids, centerPoint);
//            map = filterConnectedGridsByQuadrant(originGridsMap, grids, centerPoint);
//            log.info("中心点为：{} 城市聚合数据耗时：{}ms", centerPoint, System.currentTimeMillis() - cityTime);
//            //判断是不是够100个 不够 就要进行省维度查找
//            if (!isGridMapComplete(map)) {
//                long provinceTime = System.currentTimeMillis();
//                log.info("中心点为：{} 进入到省份查询数据", centerPoint.getLng() + centerPoint.getLat());
//                grids = getNearestGrids(conn, centerPoint, 150, "province");
//                originGridsMap = findOriginGridsMap(grids, centerPoint);
//                map = filterConnectedGridsByQuadrant(originGridsMap, grids, centerPoint);
//                log.info("中心点为：{} 省份聚合数据耗时：{}ms", centerPoint, System.currentTimeMillis() - provinceTime);
//            }
//        }
        //在不够100个进行递归查找
        if (!isGridMapComplete(map)) {
            long l = System.currentTimeMillis();
            //获取到中心点周围的起始网格数据
            if (originGridsMap.size() != 4) {
                log.info("中心点：{} 未能找到四个起始格子 跨省情况，根据中心点获取起始网格数据", centerPoint);
                originGridsMap = findInitialOriginGrids(conn, centerPoint);
            }
            log.info("中心点为：{} 进入到递归查询模式进行查询数据,", centerPoint.getLng() + "," + centerPoint.getLat());
            for (Map.Entry<Quadrant, OriginalGrid> entry : originGridsMap.entrySet()) {
                Quadrant q = entry.getKey();
                OriginalGrid seed = entry.getValue();
                //判断该象限中的网格是不满足需求
                List<OriginalGrid> list = map.computeIfAbsent(q, k -> new ArrayList<>());
                if (list.size() != 25) {
                    NetGridLocation loc = calcGridStart(q);
                    Map<Long, OriginalGrid> visited = new HashMap<>();
                    visited.put(seed.getId(), seed);
                    collectNearby(conn, seed, loc, q, visited, list);
                    map.put(q, new ArrayList<>(visited.values()));
                }
            }
            log.info("[{}]递归补齐消耗时间：{}ms", Thread.currentThread().getName(), System.currentTimeMillis() - l);
        }
        log.info("[{}]中心点为：{},整合大网格数据总耗时为：{}ms", Thread.currentThread().getName(), centerPoint, System.currentTimeMillis() - start);
        return map;
    }


//    private Map<Quadrant, List<OriginalGrid>> tryGetQuadrantGrids(Connection conn, Coordinate centerPoint, String scopeType) throws SQLException {
//        List<OriginalGrid> grids = getNearestGrids(conn, centerPoint, 150, scopeType);
//        Map<Quadrant, OriginalGrid> originGridsMap = findOriginGridsMap(grids, centerPoint);
//        return filterConnectedGridsByQuadrant(originGridsMap, grids, centerPoint);
//    }

    private boolean isGridMapComplete(Map<Quadrant, List<OriginalGrid>> map) {
        int sum = map.values().stream().mapToInt(List::size).sum();
        log.info("检索出来的条数为：{}", sum);
        return sum == 100;
    }

    private NetGridLocation calcGridStart(Quadrant q) {
        NetGridLocation currGridLocation = new NetGridLocation(0, 0);
        if (q == Quadrant.NW) {
            currGridLocation.setX(4);
            currGridLocation.setY(4);
        } else if (q == Quadrant.NE) {
            currGridLocation.setX(0);
            currGridLocation.setY(4);
        } else if (q == Quadrant.SW) {
            currGridLocation.setX(4);
            currGridLocation.setY(0);
        } else if (q == Quadrant.SE) {
            currGridLocation.setX(0);
            currGridLocation.setY(0);
        }
        return currGridLocation;
    }


    public static String convertDistrictCodeToCityCode(String districtCode) {
        if (districtCode == null || districtCode.length() != 6) {
            throw new IllegalArgumentException("区域 code 必须是 6 位字符串");
        }
        return districtCode.substring(0, 4) + "00";
    }

    public static String convertDistrictCodeToProvinceCode(String districtCode) {
        if (districtCode == null || districtCode.length() != 6) {
            throw new IllegalArgumentException("区域 code 必须是 6 位字符串");
        }
        return districtCode.substring(0, 2) + "0000";
    }


    // 查询距离最近的网格
    private List<OriginalGrid> getNearestGrids(Connection conn, Coordinate centerPoint, int limit, String type) throws SQLException {
        if (StringUtils.isEmpty(centerPoint.getCode())) {
            return Collections.emptyList();
        }

        final String baseSql = "SELECT id, up_left_lng, up_left_lat, lower_left_lng, lower_left_lat, "
                + "up_right_lng, up_right_lat, lower_right_lng, lower_right_lat, "
                + "people_count, area_code "
                + "FROM resident_map_cell_resource ";

        String whereClause;
        List<Double> params = new ArrayList<>();

        switch (type) {
            case "area":
                //whereClause = "WHERE area_code = ? AND merge_id IS NULl";
                whereClause = "WHERE center_lng BETWEEN ? AND ? AND center_lat BETWEEN ? AND ?";

                // 经纬度范围参数
                params.add(centerPoint.lng - 0.00505);
                params.add(centerPoint.lng + 0.00505);
                params.add(centerPoint.lat - 0.01);
                params.add(centerPoint.lat + 0.01);
                break;
//            case "city":
//                String cityCode = convertDistrictCodeToCityCode(centerPoint.getCode());
//                log.info("[{}]转完之后的城市code为：{}", centerPoint.getCode(), cityCode);
//                List<String> cityList = regionCodeCityMap.get(cityCode);
//                if (CollectionUtils.isNotEmpty(cityList)) {
//                    // 动态构建IN子句
//                    String placeholders = cityList.stream().map(c -> "?").collect(Collectors.joining(","));
//                    whereClause = "WHERE area_code IN(" + placeholders + ")";
//                    params.addAll(cityList);
//                } else {
//                    whereClause = "WHERE area_code LIKE ?";
//                    params.add(centerPoint.getCode().substring(0, 4) + "%");
//                }
//                break;
//            case "province":
//                String provinceCode = convertDistrictCodeToProvinceCode(centerPoint.getCode());
//                log.info("[{}]转完之后的省份code为：{}", centerPoint.getCode(), provinceCode);
//                List<String> provinceList = regionCodeProvinceMap.get(provinceCode);
//                if (CollectionUtils.isNotEmpty(provinceList)) {
//                    String placeholders = provinceList.stream().map(c -> "?").collect(Collectors.joining(","));
//                    whereClause = "WHERE area_code IN(" + placeholders + ")";
//                    params.addAll(provinceList);
//                } else {
//                    whereClause = "WHERE area_code LIKE ?";
//                    params.add(centerPoint.getCode().substring(0, 2) + "%");
//                }
//                break;
            default:
                throw new IllegalArgumentException("未知类型: " + type);
        }

        String orderLimitClause = " ORDER BY ST_Distance_Sphere(POINT(center_lng, center_lat), POINT(?, ?)) LIMIT ?";
        String sql = baseSql + whereClause + orderLimitClause;

        List<OriginalGrid> grids = new ArrayList<>(limit);

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            int paramIndex = 1;

            // 设置WHERE条件参数
            for (Double param : params) {
                stmt.setDouble(paramIndex++, param);
            }

            // 设置ORDER BY参数
            stmt.setDouble(paramIndex++, centerPoint.lng);
            stmt.setDouble(paramIndex++, centerPoint.lat);
            stmt.setInt(paramIndex, limit);

            log.info("执行SQL: {}", sql);
            log.info("参数: {}", params);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    grids.add(mapToGrid(rs));
                }
            }
        }
        log.info("查询结果数量: {}", grids.size());
        return grids;
    }


    // 在内存中进行连通性判断和筛选，按象限划分
    private Map<Quadrant, List<OriginalGrid>> filterConnectedGridsByQuadrant(Map<Quadrant, OriginalGrid> originGridsMap, List<OriginalGrid> allGrids, Coordinate centerPoint) {
        return Arrays.stream(Quadrant.values()).filter(originGridsMap::containsKey).collect(Collectors.toMap(q -> q, q -> {
            OriginalGrid originalGrid = originGridsMap.get(q);
            NetGridLocation currGridLocation = getInitialGridLocation(q);

            Map<Long, OriginalGrid> visitedIds = new HashMap<>();
            visitedIds.put(originalGrid.getId(), originalGrid);
            collectNearbyInMemory(allGrids, originalGrid, currGridLocation, q, visitedIds);
            return new ArrayList<>(visitedIds.values());
        }, (a, b) -> b, () -> new EnumMap<>(Quadrant.class)));
    }


    private NetGridLocation getInitialGridLocation(Quadrant q) {
        NetGridLocation location = new NetGridLocation(0, 0);
        switch (q) {
            case NW:
                location.setX(4);
                location.setY(4);
                break;
            case NE:
                location.setX(0);
                location.setY(4);
                break;
            case SW:
                location.setX(4);
                location.setY(0);
                break;
            case SE:
                location.setX(0);
                location.setY(0);
                break;
        }
        return location;
    }


    private Map<Quadrant, OriginalGrid> findOriginGridsMap(List<OriginalGrid> originalGrids, Coordinate center) {
        Map<Quadrant, OriginalGrid> origins = new EnumMap<>(Quadrant.class);
        for (Quadrant q : Quadrant.values()) {
            switch (q) {
                case NW:
                    List<OriginalGrid> nw = originalGrids.stream().filter(d -> Double.compare(d.getLowerRightLat(), center.getLat()) == 0 && Double.compare(d.getLowerRightLng(), center.getLng()) == 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(nw)) {
                        origins.put(q, nw.get(0));
                    } else {
                        log.error("中心点 :{} 在 :{} 象限没有找到起始网格", center, q);
                    }
                    break;
                case NE:
                    List<OriginalGrid> ne = originalGrids.stream().filter(d -> Double.compare(d.getLowerLeftLat(), center.getLat()) == 0 && Double.compare(d.getLowerLeftLng(), center.getLng()) == 0).collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(ne)) {
                        origins.put(q, ne.get(0));
                    } else {
                        log.error("中心点 :{} 在 :{} 象限没有找到起始网格", center, q);
                    }
                    break;
                case SW:
                    List<OriginalGrid> sw = originalGrids.stream().filter(d -> Double.compare(d.getUpperRightLat(), center.getLat()) == 0 && Double.compare(d.getUpperRightLng(), center.getLng()) == 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(sw)) {
                        origins.put(q, sw.get(0));
                    } else {
                        log.error("中心点 :{} 在 :{} 象限没有找到起始网格", center, q);
                    }
                    break;
                case SE:
                    List<OriginalGrid> se = originalGrids.stream().filter(d -> Double.compare(d.getUpperLeftLat(), center.getLat()) == 0 && Double.compare(d.getUpperLeftLng(), center.getLng()) == 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(se)) {
                        origins.put(q, se.get(0));
                    } else {
                        log.error("中心点 :{} 在 :{} 象限没有找到起始网格", center, q);
                    }
                    break;
                default:
                    throw new IllegalArgumentException("未知象限: " + q);
            }
        }
        return origins;
    }

    // 在内存中收集相邻网格（模拟原来的递归逻辑）
    private void collectNearbyInMemory(List<OriginalGrid> allGrids, OriginalGrid current, NetGridLocation currGridLocation, Quadrant q, Map<Long, OriginalGrid> visitedIds) {
        if (q == Quadrant.NW) {
            String baseVertex = formatCoordinate(new Coordinate(current.upperLeftLng, current.upperLeftLat, null));
            int nextX = currGridLocation.getX() - 1;
            int nextY = currGridLocation.getY() - 1;

            if (nextX >= 0) {
                OriginalGrid upperRightGrid = findGridByVertex(allGrids, baseVertex, "upperRight");
                if (upperRightGrid != null && !visitedIds.containsKey(upperRightGrid.getId())) {
                    visitedIds.put(upperRightGrid.getId(), upperRightGrid);
                    collectNearbyInMemory(allGrids, upperRightGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds);
                }
            }

            if (nextY >= 0) {
                OriginalGrid lowerLeftGrid = findGridByVertex(allGrids, baseVertex, "lowerLeft");
                if (lowerLeftGrid != null && !visitedIds.containsKey(lowerLeftGrid.getId())) {
                    visitedIds.put(lowerLeftGrid.getId(), lowerLeftGrid);
                    collectNearbyInMemory(allGrids, lowerLeftGrid, new NetGridLocation(currGridLocation.getX(), nextY), q, visitedIds);
                }
            }

            if (nextX >= 0 && nextY >= 0) {
                OriginalGrid lowerRightGrid = findGridByVertex(allGrids, baseVertex, "lowerRight");
                if (lowerRightGrid != null && !visitedIds.containsKey(lowerRightGrid.getId())) {
                    visitedIds.put(lowerRightGrid.getId(), lowerRightGrid);
                    collectNearbyInMemory(allGrids, lowerRightGrid, new NetGridLocation(nextX, nextY), q, visitedIds);
                }
            }
        }

        if (q == Quadrant.NE) {
            String baseVertex = formatCoordinate(new Coordinate(current.upperRightLng, current.upperRightLat, null));
            int nextX = currGridLocation.getX() + 1;
            int nextY = currGridLocation.getY() - 1;

            if (nextX <= 4) {
                OriginalGrid upperLeftGrid = findGridByVertex(allGrids, baseVertex, "upperLeft");
                if (upperLeftGrid != null && !visitedIds.containsKey(upperLeftGrid.getId())) {
                    visitedIds.put(upperLeftGrid.getId(), upperLeftGrid);
                    collectNearbyInMemory(allGrids, upperLeftGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds);
                }
            }

            if (nextY >= 0) {
                OriginalGrid lowerRightGrid = findGridByVertex(allGrids, baseVertex, "lowerRight");
                if (lowerRightGrid != null && !visitedIds.containsKey(lowerRightGrid.getId())) {
                    visitedIds.put(lowerRightGrid.getId(), lowerRightGrid);
                    collectNearbyInMemory(allGrids, lowerRightGrid, new NetGridLocation(currGridLocation.getX(), nextY), q, visitedIds);
                }
            }

            if (nextX <= 4 && nextY >= 0) {
                OriginalGrid lowerLeftGrid = findGridByVertex(allGrids, baseVertex, "lowerLeft");
                if (lowerLeftGrid != null && !visitedIds.containsKey(lowerLeftGrid.getId())) {
                    visitedIds.put(lowerLeftGrid.getId(), lowerLeftGrid);
                    collectNearbyInMemory(allGrids, lowerLeftGrid, new NetGridLocation(nextX, nextY), q, visitedIds);
                }
            }
        }

        if (q == Quadrant.SW) {
            String baseVertex = formatCoordinate(new Coordinate(current.lowerLeftLng, current.lowerLeftLat, null));
            int nextX = currGridLocation.getX() - 1;
            int nextY = currGridLocation.getY() + 1;

            if (nextX >= 0) {
                OriginalGrid lowerRightGrid = findGridByVertex(allGrids, baseVertex, "lowerRight");
                if (lowerRightGrid != null && !visitedIds.containsKey(lowerRightGrid.getId())) {
                    visitedIds.put(lowerRightGrid.getId(), lowerRightGrid);
                    collectNearbyInMemory(allGrids, lowerRightGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds);
                }
            }

            if (nextY <= 4) {
                OriginalGrid upperLeftGrid = findGridByVertex(allGrids, baseVertex, "upperLeft");
                if (upperLeftGrid != null && !visitedIds.containsKey(upperLeftGrid.getId())) {
                    visitedIds.put(upperLeftGrid.getId(), upperLeftGrid);
                    collectNearbyInMemory(allGrids, upperLeftGrid, new NetGridLocation(currGridLocation.getX(), nextY), q, visitedIds);
                }
            }

            if (nextX >= 0 && nextY <= 4) {
                OriginalGrid upperRightGrid = findGridByVertex(allGrids, baseVertex, "upperRight");
                if (upperRightGrid != null && !visitedIds.containsKey(upperRightGrid.getId())) {
                    visitedIds.put(upperRightGrid.getId(), upperRightGrid);
                    collectNearbyInMemory(allGrids, upperRightGrid, new NetGridLocation(nextX, nextY), q, visitedIds);
                }
            }
        }

        if (q == Quadrant.SE) {
            String baseVertex = formatCoordinate(new Coordinate(current.lowerRightLng, current.lowerRightLat, null));
            int nextX = currGridLocation.getX() + 1;
            int nextY = currGridLocation.getY() + 1;

            if (nextX <= 4) {
                OriginalGrid lowerLeftGrid = findGridByVertex(allGrids, baseVertex, "lowerLeft");
                if (lowerLeftGrid != null && !visitedIds.containsKey(lowerLeftGrid.getId())) {
                    visitedIds.put(lowerLeftGrid.getId(), lowerLeftGrid);
                    collectNearbyInMemory(allGrids, lowerLeftGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds);
                }
            }

            if (nextY <= 4) {
                OriginalGrid upperRightGrid = findGridByVertex(allGrids, baseVertex, "upperRight");
                if (upperRightGrid != null && !visitedIds.containsKey(upperRightGrid.getId())) {
                    visitedIds.put(upperRightGrid.getId(), upperRightGrid);
                    collectNearbyInMemory(allGrids, upperRightGrid, new NetGridLocation(nextX, nextY), q, visitedIds);
                }
            }

            if (nextX <= 4 && nextY <= 4) {
                OriginalGrid upperLeftGrid = findGridByVertex(allGrids, baseVertex, "upperLeft");
                if (upperLeftGrid != null && !visitedIds.containsKey(upperLeftGrid.getId())) {
                    visitedIds.put(upperLeftGrid.getId(), upperLeftGrid);
                    collectNearbyInMemory(allGrids, upperLeftGrid, new NetGridLocation(nextX, nextY), q, visitedIds);
                }
            }
        }
    }

    // 在内存中根据顶点查找网格
    private OriginalGrid findGridByVertex(List<OriginalGrid> allGrids, String baseVertex, String vertexType) {
        for (OriginalGrid grid : allGrids) {
            String gridVertex = null;
            switch (vertexType) {
                case "upperLeft":
                    gridVertex = formatCoordinate(new Coordinate(grid.upperLeftLng, grid.upperLeftLat, null));
                    break;
                case "upperRight":
                    gridVertex = formatCoordinate(new Coordinate(grid.upperRightLng, grid.upperRightLat, null));
                    break;
                case "lowerLeft":
                    gridVertex = formatCoordinate(new Coordinate(grid.lowerLeftLng, grid.lowerLeftLat, null));
                    break;
                case "lowerRight":
                    gridVertex = formatCoordinate(new Coordinate(grid.lowerRightLng, grid.lowerRightLat, null));
                    break;
            }

            if (baseVertex.equals(gridVertex)) {
                return grid;
            }
        }
        return null;
    }

    @AllArgsConstructor
    @Data
    public class NetGridLocation {
        int x;
        int y;
    }


    private void updateOriginalGrids(Connection conn, Map<Quadrant, List<OriginalGrid>> allGrids, long mergedId, String cityCode) throws SQLException {
        // 直接收集所有网格ID
        List<Long> gridIds = allGrids.values().stream().flatMap(List::stream).map(OriginalGrid::getId).collect(Collectors.toList());
        if (gridIds.isEmpty()) {
            return;
        }
        // 构建IN语句
        String placeholders = gridIds.stream().map(id -> "?").collect(Collectors.joining(","));

        String sql = "UPDATE resident_map_cell_resource SET merge_id = ?, city_code = ? WHERE id IN (" + placeholders + ")";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setLong(1, mergedId);
            stmt.setString(2, convertToCity(cityCode));

            // 设置ID参数
            for (int i = 0; i < gridIds.size(); i++) {
                stmt.setLong(i + 3, gridIds.get(i));
            }

            int updated = stmt.executeUpdate();
            log.info("批量更新完成，总共更新: {} 条记录", updated);
        }
    }


    private Set<String> specialCodes = new HashSet<>(Arrays.asList(
            "419001", "429004", "429005", "429006", "429021",
            "469001", "469002", "469005", "469006", "469007", "469021",
            "469022", "469023", "469024", "469025", "469026",
            "469027", "469028", "469029", "469030",
            "659001", "659002", "659003", "659004", "659005",
            "659006", "659007", "659008", "659009", "659010",
            "659011", "659012"));

    private String convertToCity(String areaCode) {

        // 如果是特殊的区域代码，直接返回
        if (specialCodes.contains(areaCode)) {
            return areaCode;
        }

        // 获取省级代码（前2位）
        String provinceCode = areaCode.substring(0, 2);
        // 直辖市列表：北京(11), 天津(12), 上海(31), 重庆(50)
        if ("11".equals(provinceCode) || "12".equals(provinceCode) || "31".equals(provinceCode) || "50".equals(provinceCode)) {
            // 直辖市返回省级代码 (例如: 310112 -> 310000)
            return provinceCode + "0000";
        } else {
            // 非直辖市返回市级代码 (例如: 440103 -> 440100)
            return areaCode.substring(0, 4) + "00";
        }
    }

    private long createMergedRecord(Connection conn, Map<Quadrant, List<OriginalGrid>> allGrids, Coordinate originalCenter, String cityCode) throws SQLException {
        String sql = "INSERT IGNORE INTO `merged_resident_map_cell_resource` " + "(center_lng, center_lat, people_count,lower_left_lng,lower_left_lat,lower_right_lng,lower_right_lat,up_left_lng,up_left_lat,up_right_lng,up_right_lat,city_code) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";

        int totalPeople = calculateTotalPeople(allGrids);
        int totalSize = allGrids.values().stream().mapToInt(List::size).sum();

        OriginalGrid lowerLeftGrid = new OriginalGrid();
        OriginalGrid lowerRightGrid = new OriginalGrid();
        OriginalGrid upperLeftGrid = new OriginalGrid();
        OriginalGrid upperRightGrid = new OriginalGrid();

        if (totalSize == 100) {
            lowerLeftGrid = findExtremeGrid(allGrids.get(Quadrant.SW), Quadrant.SW, true, true);
            lowerRightGrid = findExtremeGrid(allGrids.get(Quadrant.SE), Quadrant.SE, false, true);
            upperLeftGrid = findExtremeGrid(allGrids.get(Quadrant.NW), Quadrant.NW, true, false);
            upperRightGrid = findExtremeGrid(allGrids.get(Quadrant.NE), Quadrant.NE, false, false);
        }

        try (PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            stmt.setBigDecimal(1, BigDecimal.valueOf(originalCenter.lng));
            stmt.setBigDecimal(2, BigDecimal.valueOf(originalCenter.lat));
            stmt.setInt(3, totalPeople);

            stmt.setBigDecimal(4, BigDecimal.valueOf(lowerLeftGrid.lowerLeftLng));
            stmt.setBigDecimal(5, BigDecimal.valueOf(lowerLeftGrid.lowerLeftLat));

            stmt.setBigDecimal(6, BigDecimal.valueOf(lowerRightGrid.lowerRightLng));
            stmt.setBigDecimal(7, BigDecimal.valueOf(lowerRightGrid.lowerRightLat));

            stmt.setBigDecimal(8, BigDecimal.valueOf(upperLeftGrid.upperLeftLng));
            stmt.setBigDecimal(9, BigDecimal.valueOf(upperLeftGrid.upperLeftLat));

            stmt.setBigDecimal(10, BigDecimal.valueOf(upperRightGrid.upperRightLng));
            stmt.setBigDecimal(11, BigDecimal.valueOf(upperRightGrid.upperRightLat));

            // 因为网格的都是区县级别的 这里要转为城市
            String code = convertToCity(cityCode);
            stmt.setString(12, code);
            stmt.executeUpdate();

            try (ResultSet rs = stmt.getGeneratedKeys()) {
                return rs.next() ? rs.getLong(1) : -1;
            }
        }
    }

    private OriginalGrid findExtremeGrid(List<OriginalGrid> grids, Quadrant quadrant, boolean findMinLng, boolean findMinLat) {
        if (grids == null || grids.isEmpty()) {
            return new OriginalGrid();
        }

        Comparator<OriginalGrid> comparator = (g1, g2) -> {
            double lng1 = getLngByQuadrant(g1, quadrant);
            double lng2 = getLngByQuadrant(g2, quadrant);
            double lat1 = getLatByQuadrant(g1, quadrant);
            double lat2 = getLatByQuadrant(g2, quadrant);

            int latCompare = findMinLat ? Double.compare(lat1, lat2) : Double.compare(lat2, lat1);
            if (latCompare != 0) {
                return latCompare;
            }
            return findMinLng ? Double.compare(lng1, lng2) : Double.compare(lng2, lng1);
        };

        return grids.stream().min(comparator).orElseThrow(() -> new IllegalStateException("未找到极值格子"));
    }

    private double getLngByQuadrant(OriginalGrid grid, Quadrant quadrant) {
        switch (quadrant) {
            case SW:
                return grid.lowerLeftLng;
            case SE:
                return grid.lowerRightLng;
            case NW:
                return grid.upperLeftLng;
            case NE:
                return grid.upperRightLng;
            default:
                throw new IllegalArgumentException("未知象限: " + quadrant);
        }
    }

    private double getLatByQuadrant(OriginalGrid grid, Quadrant quadrant) {
        switch (quadrant) {
            case SW:
                return grid.lowerLeftLat;
            case SE:
                return grid.lowerRightLat;
            case NW:
                return grid.upperLeftLat;
            case NE:
                return grid.upperRightLat;
            default:
                throw new IllegalArgumentException("未知象限: " + quadrant);
        }
    }

    private int calculateTotalPeople(Map<Quadrant, List<OriginalGrid>> allGrids) {
        return allGrids.values().stream().flatMap(List::stream).mapToInt(g -> Integer.parseInt(g.peopleCount)).sum();
    }

    @Data
    @ToString
    static class OriginalGrid {
        long id;
        double upperLeftLng, upperLeftLat;
        double lowerLeftLng, lowerLeftLat;
        double upperRightLng, upperRightLat;
        double lowerRightLng, lowerRightLat;
        String areaCode;
        String peopleCount;
    }

    @Data
    static class Coordinate {
        final double lng, lat;
        final String code;

        Coordinate(double lng, double lat, String code) {
            this.lng = lng;
            this.lat = lat;
            this.code = code;
        }

        public String toString() {
            return lng + "," + lat;
        }
    }

    private OriginalGrid mapToGrid(ResultSet rs) throws SQLException {
        OriginalGrid g = new OriginalGrid();
        g.id = rs.getInt("id");
        g.upperLeftLng = rs.getDouble("up_left_lng");
        g.upperLeftLat = rs.getDouble("up_left_lat");
        g.lowerLeftLng = rs.getDouble("lower_left_lng");
        g.lowerLeftLat = rs.getDouble("lower_left_lat");
        g.upperRightLng = rs.getDouble("up_right_lng");
        g.upperRightLat = rs.getDouble("up_right_lat");
        g.lowerRightLat = rs.getDouble("lower_right_lat");
        g.lowerRightLng = rs.getDouble("lower_right_lng");
        g.peopleCount = rs.getString("people_count");
        g.areaCode = rs.getString("area_code");
        return g;
    }

    private String formatCoordinate(Coordinate cord) {
        return cord.toString();
    }


    private void collectNearby(Connection conn, OriginalGrid current, NetGridLocation currGridLocation, Quadrant q,
                               Map<Long, OriginalGrid> visitedIds, List<OriginalGrid> allGrids) throws SQLException {
        if (q == Quadrant.NW) {
            String baseVertex = formatCoordinate(new Coordinate(current.upperLeftLng, current.upperLeftLat, null));
            int nextX = currGridLocation.getX() - 1;
            int nextY = currGridLocation.getY() - 1;
            if (nextX >= 0) {
                //可以先从区域里面获取到的NW象限中获取改网格 网格没有则查询数据库 没有则递归
                OriginalGrid upperRightGrid = findGridByVertex(allGrids, baseVertex, "upperRight");
                if (upperRightGrid != null) {
                    // 在内存中找到，直接使用
                    if (!visitedIds.containsKey(upperRightGrid.getId())) {
                        visitedIds.put(upperRightGrid.getId(), upperRightGrid);
                        collectNearby(conn, upperRightGrid, new NetGridLocation(nextX, currGridLocation.getY()), q,
                                visitedIds, allGrids);
                    }
                } else {
                    //内存中没找到从数据库中查找数据
                    String upperRightSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_up_right = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(upperRightSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet upperRightRs = stmt.executeQuery();
                        if (upperRightRs.next()) {
                            upperRightGrid = mapToGrid(upperRightRs);
                            if (!visitedIds.containsKey(upperRightGrid.getId())) {
                                visitedIds.put(upperRightGrid.getId(), upperRightGrid);
                                collectNearby(conn, upperRightGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds, allGrids);
                            }
                        }
                    } catch (SQLException e) {
                        log.error("查询右上顶点失败: {}", e.getMessage());
                    }
                }
            }
            if (nextY >= 0) {
                OriginalGrid lowerLeftGrid = findGridByVertex(allGrids, baseVertex, "lowerLeft");
                if (lowerLeftGrid != null) {
                    if (!visitedIds.containsKey(lowerLeftGrid.getId())) {
                        visitedIds.put(lowerLeftGrid.getId(), lowerLeftGrid);
                        collectNearby(conn, lowerLeftGrid, new NetGridLocation(currGridLocation.getX(), nextY), q, visitedIds, allGrids);
                    }
                } else {
                    String lowerLeftSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_lower_left = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(lowerLeftSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet lowerLeftRs = stmt.executeQuery();
                        if (lowerLeftRs.next()) {
                            lowerLeftGrid = mapToGrid(lowerLeftRs);
                            if (!visitedIds.containsKey(lowerLeftGrid.getId())) {
                                visitedIds.put(lowerLeftGrid.getId(), lowerLeftGrid);
                                collectNearby(conn, lowerLeftGrid, new NetGridLocation(currGridLocation.getX(), nextY), q,
                                        visitedIds, allGrids);
                            }
                        }
                    } catch (SQLException e) {
                        log.error("查询左下顶点失败: {}", e.getMessage());
                    }
                }
            }
            if (nextX >= 0 && nextY >= 0) {
                OriginalGrid lowerRightGrid = findGridByVertex(allGrids, baseVertex, "lowerRight");
                if (lowerRightGrid != null) {
                    if (!visitedIds.containsKey(lowerRightGrid.getId())) {
                        visitedIds.put(lowerRightGrid.getId(), lowerRightGrid);
                        collectNearby(conn, lowerRightGrid, new NetGridLocation(nextX, nextY), q, visitedIds, allGrids);
                    }
                } else {
                    String lowerRightSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_lower_right = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(lowerRightSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet lowerRightRs = stmt.executeQuery();
                        if (lowerRightRs.next()) {
                            lowerRightGrid = mapToGrid(lowerRightRs);
                            if (!visitedIds.containsKey(lowerRightGrid.getId())) {
                                visitedIds.put(lowerRightGrid.getId(), lowerRightGrid);
                                collectNearby(conn, lowerRightGrid, new NetGridLocation(nextX, nextY), q, visitedIds, allGrids);
                            }
                        }
                    } catch (SQLException e) {
                        log.error("查询右下顶点失败: {}", e.getMessage());
                    }
                }
            }
        }
        if (q == Quadrant.NE) {
            String baseVertex = formatCoordinate(new Coordinate(current.upperRightLng, current.upperRightLat, null));
            int nextX = currGridLocation.getX() + 1;
            int nextY = currGridLocation.getY() - 1;
            if (nextX <= 4) {
                OriginalGrid upperLeftGrid = findGridByVertex(allGrids, baseVertex, "upperLeft");
                if (upperLeftGrid != null) {
                    if (!visitedIds.containsKey(upperLeftGrid.getId())) {
                        visitedIds.put(upperLeftGrid.getId(), upperLeftGrid);
                        collectNearby(conn, upperLeftGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds, allGrids);
                    }
                } else {
                    String upperLeftSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_up_left = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(upperLeftSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet upperLeftRs = stmt.executeQuery();
                        if (upperLeftRs.next()) {
                            upperLeftGrid = mapToGrid(upperLeftRs);
                            if (!visitedIds.containsKey(upperLeftGrid.getId())) {
                                visitedIds.put(upperLeftGrid.getId(), upperLeftGrid);
                                collectNearby(conn, upperLeftGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds, allGrids);
                            }
                        }
                    }
                }
            }
            if (nextY >= 0) {
                OriginalGrid lowerRightGrid = findGridByVertex(allGrids, baseVertex, "lowerRight");
                if (lowerRightGrid != null) {
                    if (!visitedIds.containsKey(lowerRightGrid.getId())) {
                        visitedIds.put(lowerRightGrid.getId(), lowerRightGrid);
                        collectNearby(conn, lowerRightGrid, new NetGridLocation(currGridLocation.getX(), nextY), q, visitedIds, allGrids);
                    }
                } else {
                    String lowerRightSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_lower_right = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(lowerRightSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet lowerRightRs = stmt.executeQuery();
                        if (lowerRightRs.next()) {
                            lowerRightGrid = mapToGrid(lowerRightRs);
                            if (!visitedIds.containsKey(lowerRightGrid.getId())) {
                                visitedIds.put(lowerRightGrid.getId(), lowerRightGrid);
                                collectNearby(conn, lowerRightGrid, new NetGridLocation(currGridLocation.getX(), nextY), q, visitedIds, allGrids);
                            }
                        }
                    }
                }
            }
            if (nextX <= 4 && nextY >= 0) {
                OriginalGrid lowerLeftGrid = findGridByVertex(allGrids, baseVertex, "lowerLeft");
                if (lowerLeftGrid != null) {
                    if (!visitedIds.containsKey(lowerLeftGrid.getId())) {
                        visitedIds.put(lowerLeftGrid.getId(), lowerLeftGrid);
                        collectNearby(conn, lowerLeftGrid, new NetGridLocation(nextX, nextY), q, visitedIds, allGrids);
                    }
                } else {
                    String lowerLeftSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_lower_left = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(lowerLeftSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet lowerLeftRs = stmt.executeQuery();
                        if (lowerLeftRs.next()) {
                            lowerLeftGrid = mapToGrid(lowerLeftRs);
                            if (!visitedIds.containsKey(lowerLeftGrid.getId())) {
                                visitedIds.put(lowerLeftGrid.getId(), lowerLeftGrid);
                                collectNearby(conn, lowerLeftGrid, new NetGridLocation(nextX, nextY), q, visitedIds, allGrids);
                            }
                        }
                    }
                }
            }
        }
        if (q == Quadrant.SW) {
            String baseVertex = formatCoordinate(new Coordinate(current.lowerLeftLng, current.lowerLeftLat, null));
            int nextX = currGridLocation.getX() - 1;
            int nextY = currGridLocation.getY() + 1;
            if (nextX >= 0) {
                OriginalGrid lowerRightGrid = findGridByVertex(allGrids, baseVertex, "lowerRight");
                if (lowerRightGrid != null) {
                    if (!visitedIds.containsKey(lowerRightGrid.getId())) {
                        visitedIds.put(lowerRightGrid.getId(), lowerRightGrid);
                        collectNearby(conn, lowerRightGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds, allGrids);
                    }
                } else {
                    String lowerRightSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_lower_right = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(lowerRightSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet lowerRightRs = stmt.executeQuery();
                        if (lowerRightRs.next()) {
                            lowerRightGrid = mapToGrid(lowerRightRs);
                            if (!visitedIds.containsKey(lowerRightGrid.getId())) {
                                visitedIds.put(lowerRightGrid.getId(), lowerRightGrid);
                                collectNearby(conn, lowerRightGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds, allGrids);
                            }
                        }
                    }
                }
            }
            if (nextY <= 4) {
                OriginalGrid upperLeftGrid = findGridByVertex(allGrids, baseVertex, "upperLeft");
                if (upperLeftGrid != null) {
                    if (!visitedIds.containsKey(upperLeftGrid.getId())) {
                        visitedIds.put(upperLeftGrid.getId(), upperLeftGrid);
                        collectNearby(conn, upperLeftGrid, new NetGridLocation(currGridLocation.getX(), nextY), q, visitedIds, allGrids);
                    }
                } else {
                    String upperLeftSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_up_left = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(upperLeftSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet upperLeftRs = stmt.executeQuery();
                        if (upperLeftRs.next()) {
                            upperLeftGrid = mapToGrid(upperLeftRs);
                            if (!visitedIds.containsKey(upperLeftGrid.getId())) {
                                visitedIds.put(upperLeftGrid.getId(), upperLeftGrid);
                                collectNearby(conn, upperLeftGrid, new NetGridLocation(currGridLocation.getX(), nextY), q, visitedIds, allGrids);
                            }
                        }
                    }
                }
            }
            if (nextX >= 0 && nextY <= 4) {
                OriginalGrid upperRightGrid = findGridByVertex(allGrids, baseVertex, "upperRight");
                if (upperRightGrid != null) {
                    if (!visitedIds.containsKey(upperRightGrid.getId())) {
                        visitedIds.put(upperRightGrid.getId(), upperRightGrid);
                        collectNearby(conn, upperRightGrid, new NetGridLocation(nextX, nextY), q, visitedIds, allGrids);
                    }
                } else {
                    String upperRightSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_up_right = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(upperRightSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet upperRightRs = stmt.executeQuery();
                        if (upperRightRs.next()) {
                            upperRightGrid = mapToGrid(upperRightRs);
                            if (!visitedIds.containsKey(upperRightGrid.getId())) {
                                visitedIds.put(upperRightGrid.getId(), upperRightGrid);
                                collectNearby(conn, upperRightGrid, new NetGridLocation(nextX, nextY), q, visitedIds, allGrids);
                            }
                        }
                    }
                }
            }
        }
        if (q == Quadrant.SE) {
            String baseVertex = formatCoordinate(new Coordinate(current.lowerRightLng, current.lowerRightLat, null));
            int nextX = currGridLocation.getX() + 1;
            int nextY = currGridLocation.getY() + 1;
            if (nextX <= 4) {
                OriginalGrid lowerLeftGrid = findGridByVertex(allGrids, baseVertex, "lowerLeft");
                if (lowerLeftGrid != null) {
                    if (!visitedIds.containsKey(lowerLeftGrid.getId())) {
                        visitedIds.put(lowerLeftGrid.getId(), lowerLeftGrid);
                        collectNearby(conn, lowerLeftGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds, allGrids);
                    }
                } else {
                    String lowerLeftSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_lower_left = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(lowerLeftSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet lowerLeftRs = stmt.executeQuery();
                        if (lowerLeftRs.next()) {
                            lowerLeftGrid = mapToGrid(lowerLeftRs);
                            if (!visitedIds.containsKey(lowerLeftGrid.getId())) {
                                visitedIds.put(lowerLeftGrid.getId(), lowerLeftGrid);
                                collectNearby(conn, lowerLeftGrid, new NetGridLocation(nextX, currGridLocation.getY()), q, visitedIds, allGrids);
                            }
                        }
                    }
                }
            }
            if (nextY <= 4) {
                OriginalGrid upperRightGrid = findGridByVertex(allGrids, baseVertex, "upperRight");
                if (upperRightGrid != null) {
                    if (!visitedIds.containsKey(upperRightGrid.getId())) {
                        visitedIds.put(upperRightGrid.getId(), upperRightGrid);
                        collectNearby(conn, upperRightGrid, new NetGridLocation(nextX, nextY), q, visitedIds, allGrids);
                    }
                } else {
                    String upperRightSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_up_right = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(upperRightSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet upperRightRs = stmt.executeQuery();
                        if (upperRightRs.next()) {
                            upperRightGrid = mapToGrid(upperRightRs);
                            if (!visitedIds.containsKey(upperRightGrid.getId())) {
                                visitedIds.put(upperRightGrid.getId(), upperRightGrid);
                                collectNearby(conn, upperRightGrid, new NetGridLocation(nextX, nextY), q, visitedIds, allGrids);
                            }
                        }
                    }
                }
            }
            if (nextX <= 4 && nextY <= 4) {
                OriginalGrid upperLeftGrid = findGridByVertex(allGrids, baseVertex, "upperLeft");
                if (upperLeftGrid != null) {
                    if (!visitedIds.containsKey(upperLeftGrid.getId())) {
                        visitedIds.put(upperLeftGrid.getId(), upperLeftGrid);
                        collectNearby(conn, upperLeftGrid, new NetGridLocation(nextX, nextY), q, visitedIds, allGrids);
                    }
                } else {
                    String upperLeftSql = "SELECT id,up_left_lng,up_left_lat,lower_left_lng,lower_left_lat,up_right_lng,up_right_lat,lower_right_lat,lower_right_lng,people_count,area_code FROM resident_map_cell_resource WHERE wkt_up_left = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(upperLeftSql)) {
                        stmt.setString(1, baseVertex);
                        ResultSet upperLeftRs = stmt.executeQuery();
                        if (upperLeftRs.next()) {
                            upperLeftGrid = mapToGrid(upperLeftRs);
                            if (!visitedIds.containsKey(upperLeftGrid.getId())) {
                                visitedIds.put(upperLeftGrid.getId(), upperLeftGrid);
                                collectNearby(conn, upperLeftGrid, new NetGridLocation(nextX, nextY), q, visitedIds, allGrids);
                            }
                        }
                    }
                }
            }
        }
    }


    private Map<Quadrant, OriginalGrid> findInitialOriginGrids(Connection conn, Coordinate center) throws SQLException {
        Map<Quadrant, OriginalGrid> origins = new EnumMap<>(Quadrant.class);
        String sql = "SELECT * FROM resident_map_cell_resource " +
                "WHERE wkt_lower_right = ? OR wkt_lower_left = ? " +
                "OR wkt_up_right = ? OR wkt_up_left = ?";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            String centerStr = formatCoordinate(center);
            for (int i = 1; i <= 4; i++) {
                stmt.setString(i, centerStr);
            }
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                OriginalGrid grid = mapToGrid(rs);
                // 下面根据匹配到的字段判断象限
                if (centerStr.equals(rs.getString("wkt_lower_right"))) {
                    origins.put(Quadrant.NW, grid);
                }
                if (centerStr.equals(rs.getString("wkt_lower_left"))) {
                    origins.put(Quadrant.NE, grid);
                }
                if (centerStr.equals(rs.getString("wkt_up_right"))) {
                    origins.put(Quadrant.SW, grid);
                }
                if (centerStr.equals(rs.getString("wkt_up_left"))) {
                    origins.put(Quadrant.SE, grid);
                }
            }
        }
        return origins;
    }

    public Map<String, List<String>> getRegionCodeCityMap(Connection conn) throws SQLException {
        Map<String, List<String>> result = new HashMap<>();
        String sql = "SELECT parent_code,`code` FROM main_dict_region WHERE `code` IN ('110101','110102','110105','110106','110107','110108','110109','110111','110112','110113','110114','110115','110116','110117','110118','110119','120101','120102','120103','120104','120105','120106','120110','120111','120112','120113','120114','120115','120116','120117','120118','120119','130102','130104','130105','130107','130108','130109','130110','130111','130121','130123','130125','130126','130127','130128','130129','130130','130131','130132','130133','130181','130183','130184','130202','130203','130204','130205','130207','130208','130209','130224','130225','130227','130229','130281','130283','130284','130302','130303','130304','130306','130321','130322','130324','130402','130403','130404','130406','130407','130408','130423','130424','130425','130426','130427','130430','130431','130432','130433','130434','130435','130481','130502','130503','130505','130506','130522','130523','130524','130525','130528','130529','130530','130531','130532','130533','130534','130535','130581','130582','130602','130606','130607','130608','130609','130623','130624','130626','130627','130628','130629','130630','130631','130632','130633','130634','130635','130636','130637','130638','130681','130682','130683','130684','130702','130703','130705','130706','130708','130709','130722','130723','130724','130725','130726','130727','130728','130730','130731','130732','130802','130803','130804','130821','130822','130824','130825','130826','130827','130828','130881','130902','130903','130921','130922','130923','130924','130925','130926','130927','130928','130929','130930','130981','130982','130983','130984','131002','131003','131022','131023','131024','131025','131026','131028','131081','131082','131102','131103','131121','131122','131123','131124','131125','131126','131127','131128','131182','140105','140106','140107','140108','140109','140110','140121','140122','140123','140181','140212','140213','140214','140215','140221','140222','140223','140224','140225','140226','140302','140303','140311','140321','140322','140403','140404','140405','140406','140423','140425','140426','140427','140428','140429','140430','140431','140502','140521','140522','140524','140525','140581','140602','140603','140621','140622','140623','140681','140702','140703','140721','140722','140723','140724','140725','140727','140728','140729','140781','140802','140821','140822','140823','140824','140825','140826','140827','140828','140829','140830','140881','140882','140902','140921','140922','140923','140924','140925','140926','140927','140928','140929','140930','140931','140932','140981','141002','141021','141022','141023','141024','141025','141026','141027','141028','141029','141030','141031','141032','141033','141034','141081','141082','141102','141121','141122','141123','141124','141125','141126','141127','141128','141129','141130','141181','141182','150102','150103','150104','150105','150121','150122','150123','150124','150125','150202','150203','150204','150205','150206','150207','150221','150222','150223','150302','150303','150304','150402','150403','150404','150421','150422','150423','150424','150425','150426','150428','150429','150430','150502','150521','150522','150523','150524','150525','150526','150581','150602','150603','150621','150622','150623','150624','150625','150626','150627','150702','150703','150721','150722','150723','150724','150725','150726','150727','150781','150782','150783','150784','150785','150802','150821','150822','150823','150824','150825','150826','150902','150921','150922','150923','150924','150925','150926','150927','150928','150929','150981','152201','152202','152221','152222','152223','152224','152501','152502','152522','152523','152524','152525','152526','152527','152528','152529','152530','152531','152921','152922','152923','210102','210103','210104','210105','210106','210111','210112','210113','210114','210115','210123','210124','210181','210202','210203','210204','210211','210212','210213','210214','210224','210281','210283','210302','210303','210304','210311','210321','210323','210381','210402','210403','210404','210411','210421','210422','210423','210502','210503','210504','210505','210521','210522','210602','210603','210604','210624','210681','210682','210702','210703','210711','210726','210727','210781','210782','210802','210803','210804','210811','210881','210882','210902','210903','210904','210905','210911','210921','210922','211002','211003','211004','211005','211011','211021','211081','211102','211103','211104','211122','211202','211204','211221','211223','211224','211281','211282','211302','211303','211321','211322','211324','211381','211382','211402','211403','211404','211421','211422','211481','220102','220103','220104','220105','220106','220112','220113','220122','220182','220183','220184','220202','220203','220204','220211','220221','220281','220282','220283','220284','220302','220303','220322','220323','220382','220402','220403','220421','220422','220502','220503','220521','220523','220524','220581','220582','220602','220605','220621','220622','220623','220681','220702','220721','220722','220723','220781','220802','220821','220822','220881','220882','222401','222402','222403','222404','222405','222406','222424','222426','230102','230103','230104','230108','230109','230110','230111','230112','230113','230123','230124','230125','230126','230127','230128','230129','230183','230184','230202','230203','230204','230205','230206','230207','230208','230221','230223','230224','230225','230227','230229','230230','230231','230281','230302','230303','230304','230305','230306','230307','230321','230381','230382','230402','230403','230404','230405','230406','230407','230421','230422','230502','230503','230505','230506','230521','230522','230523','230524','230602','230603','230604','230605','230606','230621','230622','230623','230624','230717','230718','230719','230722','230723','230724','230725','230726','230751','230781','230803','230804','230805','230811','230822','230826','230828','230881','230882','230883','230902','230903','230904','230921','231002','231003','231004','231005','231025','231081','231083','231084','231085','231086','231102','231123','231124','231181','231182','231183','231202','231221','231222','231223','231224','231225','231226','231281','231282','231283','232701','232718','232721','232722','310101','310104','310105','310106','310107','310109','310110','310112','310113','310114','310115','310116','310117','310118','310120','310151','320102','320104','320105','320106','320111','320113','320114','320115','320116','320117','320118','320205','320206','320211','320213','320214','320281','320282','320302','320303','320305','320311','320312','320321','320322','320324','320381','320382','320402','320404','320411','320412','320413','320481','320505','320506','320507','320508','320509','320581','320582','320583','320585','320612','320613','320614','320623','320681','320682','320685','320703','320706','320707','320722','320723','320724','320803','320804','320812','320813','320826','320830','320831','320902','320903','320904','320921','320922','320923','320924','320925','320981','321002','321003','321012','321023','321081','321084','321102','321111','321112','321181','321182','321183','321202','321203','321204','321281','321282','321283','321302','321311','321322','321323','321324','330102','330105','330106','330108','330109','330110','330111','330112','330113','330114','330122','330127','330182','330203','330205','330206','330211','330212','330213','330225','330226','330281','330282','330302','330303','330304','330305','330324','330326','330327','330328','330329','330381','330382','330383','330402','330411','330421','330424','330481','330482','330483','330502','330503','330521','330522','330523','330602','330603','330604','330624','330681','330683','330702','330703','330723','330726','330727','330781','330782','330783','330784','330802','330803','330822','330824','330825','330881','330902','330903','330921','330922','331002','331003','331004','331022','331023','331024','331081','331082','331083','331102','331121','331122','331123','331124','331125','331126','331127','331181','340102','340103','340104','340111','340121','340122','340123','340124','340181','340202','340207','340209','340210','340212','340223','340281','340302','340303','340304','340311','340321','340322','340323','340402','340403','340404','340405','340406','340421','340422','340503','340504','340506','340521','340522','340523','340602','340603','340604','340621','340705','340706','340711','340722','340802','340803','340811','340822','340825','340826','340827','340828','340881','340882','341002','341003','341004','341021','341022','341023','341024','341102','341103','341122','341124','341125','341126','341181','341182','341202','341203','341204','341221','341222','341225','341226','341282','341302','341321','341322','341323','341324','341502','341503','341504','341522','341523','341524','341525','341602','341621','341622','341623','341702','341721','341722','341723','341802','341821','341823','341824','341825','341881','341882','350102','350103','350104','350105','350111','350112','350121','350122','350123','350124','350125','350128','350181','350203','350205','350206','350211','350212','350213','350302','350303','350304','350305','350322','350404','350405','350421','350423','350424','350425','350426','350428','350429','350430','350481','350502','350503','350504','350505','350521','350524','350525','350526','350581','350582','350583','350602','350603','350604','350605','350622','350623','350624','350626','350627','350628','350629','350702','350703','350721','350722','350723','350724','350725','350781','350782','350783','350802','350803','350821','350823','350824','350825','350881','350902','350921','350922','350923','350924','350925','350926','350981','350982','360102','360103','360104','360111','360112','360113','360121','360123','360124','360202','360203','360222','360281','360302','360313','360321','360322','360323','360402','360403','360404','360423','360424','360425','360426','360428','360429','360430','360481','360482','360483','360502','360521','360602','360603','360681','360702','360703','360704','360722','360723','360724','360725','360726','360728','360729','360730','360731','360732','360733','360734','360735','360781','360783','360802','360803','360821','360822','360823','360824','360825','360826','360827','360828','360829','360830','360881','360902','360921','360922','360923','360924','360925','360926','360981','360982','360983','361002','361003','361021','361022','361023','361024','361025','361026','361027','361028','361030','361102','361103','361104','361123','361124','361125','361126','361127','361128','361129','361130','361181','370102','370103','370104','370105','370112','370113','370114','370115','370116','370117','370124','370126','370202','370203','370211','370212','370213','370214','370215','370281','370283','370285','370302','370303','370304','370305','370306','370321','370322','370323','370402','370403','370404','370405','370406','370481','370502','370503','370505','370522','370523','370602','370611','370612','370613','370614','370681','370682','370683','370685','370686','370687','370702','370703','370704','370705','370724','370725','370781','370782','370783','370784','370785','370786','370811','370812','370826','370827','370828','370829','370830','370831','370832','370881','370883','370902','370911','370921','370923','370982','370983','371002','371003','371082','371083','371102','371103','371121','371122','371302','371311','371312','371321','371322','371323','371324','371325','371326','371327','371328','371329','371402','371403','371422','371423','371424','371425','371426','371427','371428','371481','371482','371502','371503','371521','371522','371524','371525','371526','371581','371602','371603','371621','371622','371623','371625','371681','371702','371703','371721','371722','371723','371724','371725','371726','371728','410102','410103','410104','410105','410106','410108','410122','410181','410182','410183','410184','410185','410202','410203','410204','410205','410212','410221','410222','410223','410225','410302','410303','410304','410305','410307','410308','410311','410323','410324','410325','410326','410327','410328','410329','410402','410403','410404','410411','410421','410422','410423','410425','410481','410482','410502','410503','410505','410506','410522','410523','410526','410527','410581','410602','410603','410611','410621','410622','410702','410703','410704','410711','410721','410724','410725','410726','410727','410781','410782','410783','410802','410803','410804','410811','410821','410822','410823','410825','410882','410883','410902','410922','410923','410926','410927','410928','411002','411003','411024','411025','411081','411082','411102','411103','411104','411121','411122','411202','411203','411221','411224','411281','411282','411302','411303','411321','411322','411323','411324','411325','411326','411327','411328','411329','411330','411381','411402','411403','411421','411422','411423','411424','411425','411426','411481','411502','411503','411521','411522','411523','411524','411525','411526','411527','411528','411602','411603','411621','411622','411623','411624','411625','411627','411628','411681','411702','411721','411722','411723','411724','411725','411726','411727','411728','411729','419001','420102','420103','420104','420105','420106','420107','420111','420112','420113','420114','420115','420116','420117','420202','420203','420204','420205','420222','420281','420302','420303','420304','420322','420323','420324','420325','420381','420502','420503','420504','420505','420506','420525','420526','420527','420528','420529','420581','420582','420583','420602','420606','420607','420624','420625','420626','420682','420683','420684','420702','420703','420704','420802','420804','420822','420881','420882','420902','420921','420922','420923','420981','420982','420984','421002','421003','421022','421024','421081','421083','421087','421088','421102','421121','421122','421123','421124','421125','421126','421127','421181','421182','421202','421221','421222','421223','421224','421281','421303','421321','421381','422801','422802','422822','422823','422825','422826','422827','422828','429004','429005','429006','429021','430102','430103','430104','430105','430111','430112','430121','430181','430182','430202','430203','430204','430211','430212','430223','430224','430225','430281','430302','430304','430321','430381','430382','430405','430406','430407','430408','430412','430421','430422','430423','430424','430426','430481','430482','430502','430503','430511','430522','430523','430524','430525','430527','430528','430529','430581','430582','430602','430603','430611','430621','430623','430624','430626','430681','430682','430702','430703','430721','430722','430723','430724','430725','430726','430781','430802','430811','430821','430822','430902','430903','430921','430922','430923','430981','431002','431003','431021','431022','431023','431024','431025','431026','431027','431028','431081','431102','431103','431122','431123','431124','431125','431126','431127','431128','431129','431181','431202','431221','431222','431223','431224','431225','431226','431227','431228','431229','431230','431281','431302','431321','431322','431381','431382','433101','433122','433123','433124','433125','433126','433127','433130','440103','440104','440105','440106','440111','440112','440113','440114','440115','440117','440118','440203','440204','440205','440222','440224','440229','440232','440233','440281','440282','440303','440304','440305','440306','440307','440308','440309','440310','440311','440402','440403','440404','440507','440511','440512','440513','440514','440515','440523','440604','440605','440606','440607','440608','440703','440704','440705','440781','440783','440784','440785','440802','440803','440804','440811','440823','440825','440881','440882','440883','440902','440904','440981','440982','440983','441202','441203','441204','441223','441224','441225','441226','441284','441302','441303','441322','441323','441324','441402','441403','441422','441423','441424','441426','441427','441481','441502','441521','441523','441581','441602','441621','441622','441623','441624','441625','441702','441704','441721','441781','441802','441803','441821','441823','441825','441826','441881','441882','441900','442000','445102','445103','445122','445202','445203','445222','445224','445281','445302','445303','445321','445322','445381','450102','450103','450105','450107','450108','450109','450110','450123','450124','450125','450126','450181','450202','450203','450204','450205','450206','450222','450223','450224','450225','450226','450302','450303','450304','450305','450311','450312','450321','450323','450324','450325','450326','450327','450328','450329','450330','450332','450381','450403','450405','450406','450421','450422','450423','450481','450502','450503','450512','450521','450602','450603','450621','450681','450702','450703','450721','450722','450802','450803','450804','450821','450881','450902','450903','450921','450922','450923','450924','450981','451002','451003','451022','451024','451026','451027','451028','451029','451030','451031','451081','451082','451102','451103','451121','451122','451123','451202','451203','451221','451222','451223','451224','451225','451226','451227','451228','451229','451302','451321','451322','451323','451324','451381','451402','451421','451422','451423','451424','451425','451481','460105','460106','460107','460108','460202','460203','460204','460205','460301','460400','469001','469002','469005','469006','469007','469021','469022','469023','469024','469025','469026','469027','469028','469029','469030','500101','500102','500103','500104','500105','500106','500107','500108','500109','500110','500111','500112','500113','500114','500115','500116','500117','500118','500119','500120','500151','500152','500153','500154','500155','500156','500229','500230','500231','500233','500235','500236','500237','500238','500240','500241','500242','500243','510104','510105','510106','510107','510108','510112','510113','510114','510115','510116','510117','510118','510121','510129','510131','510181','510182','510183','510184','510185','510302','510303','510304','510311','510321','510322','510402','510403','510411','510421','510422','510502','510503','510504','510521','510522','510524','510525','510603','510604','510623','510681','510682','510683','510703','510704','510705','510722','510723','510725','510726','510727','510781','510802','510811','510812','510821','510822','510823','510824','510903','510904','510921','510923','510981','511002','511011','511024','511025','511083','511102','511111','511112','511113','511123','511124','511126','511129','511132','511133','511181','511302','511303','511304','511321','511322','511323','511324','511325','511381','511402','511403','511421','511423','511424','511425','511502','511503','511504','511523','511524','511525','511526','511527','511528','511529','511602','511603','511621','511622','511623','511681','511702','511703','511722','511723','511724','511725','511781','511802','511803','511822','511823','511824','511825','511826','511827','511902','511903','511921','511922','511923','512002','512021','512022','513201','513221','513222','513223','513224','513225','513226','513227','513228','513230','513231','513232','513233','513301','513322','513323','513324','513325','513326','513327','513328','513329','513330','513331','513332','513333','513334','513335','513336','513337','513338','513401','513402','513422','513423','513424','513426','513427','513428','513429','513430','513431','513432','513433','513434','513435','513436','513437','520102','520103','520111','520112','520113','520115','520121','520122','520123','520181','520201','520203','520204','520281','520302','520303','520304','520322','520323','520324','520325','520326','520327','520328','520329','520330','520381','520382','520402','520403','520422','520423','520424','520425','520502','520521','520523','520524','520525','520526','520527','520581','520602','520603','520621','520622','520623','520624','520625','520626','520627','520628','522301','522302','522323','522324','522325','522326','522327','522328','522601','522622','522623','522624','522625','522626','522627','522628','522629','522630','522631','522632','522633','522634','522635','522636','522701','522702','522722','522723','522725','522726','522727','522728','522729','522730','522731','522732','530102','530103','530111','530112','530113','530114','530115','530124','530125','530126','530127','530128','530129','530181','530302','530303','530304','530322','530323','530324','530325','530326','530381','530402','530403','530423','530424','530425','530426','530427','530428','530481','530502','530521','530523','530524','530581','530602','530621','530622','530623','530624','530625','530626','530627','530628','530629','530681','530702','530721','530722','530723','530724','530802','530821','530822','530823','530824','530825','530826','530827','530828','530829','530902','530921','530922','530923','530924','530925','530926','530927','532301','532302','532322','532323','532324','532325','532326','532327','532328','532329','532501','532502','532503','532504','532523','532524','532525','532527','532528','532529','532530','532531','532532','532601','532622','532623','532624','532625','532626','532627','532628','532801','532822','532823','532901','532922','532923','532924','532925','532926','532927','532928','532929','532930','532931','532932','533102','533103','533122','533123','533124','533301','533323','533324','533325','533401','533422','533423','540102','540103','540104','540121','540122','540123','540124','540127','540202','540221','540222','540223','540224','540225','540226','540227','540228','540229','540230','540231','540232','540233','540234','540235','540236','540237','540302','540321','540322','540323','540324','540325','540326','540327','540328','540329','540330','540402','540421','540423','540424','540425','540426','540481','540502','540521','540522','540523','540524','540525','540526','540527','540528','540529','540531','540581','540602','540621','540622','540623','540624','540625','540626','540627','540628','540629','540630','542521','542522','542523','542524','542525','542526','542527','610102','610103','610104','610111','610112','610113','610114','610115','610116','610117','610118','610122','610124','610202','610203','610204','610222','610302','610303','610304','610305','610323','610324','610326','610327','610328','610329','610330','610331','610402','610403','610404','610422','610423','610424','610425','610426','610428','610429','610430','610431','610481','610482','610502','610503','610522','610523','610524','610525','610526','610527','610528','610581','610582','610602','610603','610621','610622','610625','610626','610627','610628','610629','610630','610631','610632','610681','610702','610703','610722','610723','610724','610725','610726','610727','610728','610729','610730','610802','610803','610822','610824','610825','610826','610827','610828','610829','610830','610831','610881','610902','610921','610922','610923','610924','610925','610926','610927','610929','610981','611002','611021','611022','611023','611024','611025','611026','620102','620103','620104','620105','620111','620121','620122','620123','620200','620302','620321','620402','620403','620421','620422','620423','620502','620503','620521','620522','620523','620524','620525','620602','620621','620622','620623','620702','620721','620722','620723','620724','620725','620802','620821','620822','620823','620825','620826','620881','620902','620921','620922','620923','620924','620981','620982','621002','621021','621022','621023','621024','621025','621026','621027','621102','621121','621122','621123','621124','621125','621126','621202','621221','621222','621223','621224','621225','621226','621227','621228','622901','622921','622922','622923','622924','622925','622926','622927','623001','623021','623022','623023','623024','623025','623026','623027','630102','630103','630104','630105','630106','630121','630123','630202','630203','630222','630223','630224','630225','632221','632222','632223','632224','632301','632322','632323','632324','632521','632522','632523','632524','632525','632621','632622','632623','632624','632625','632626','632701','632722','632723','632724','632725','632726','632801','632802','632803','632821','632822','632823','632825','640104','640105','640106','640121','640122','640181','640202','640205','640221','640302','640303','640323','640324','640381','640402','640422','640423','640424','640425','640502','640521','640522','650102','650103','650104','650105','650106','650107','650109','650121','650202','650203','650204','650205','650402','650421','650422','650502','650521','650522','652301','652302','652323','652324','652325','652327','652328','652701','652702','652722','652723','652801','652822','652823','652824','652825','652826','652827','652828','652829','652901','652902','652922','652924','652925','652926','652927','652928','652929','653001','653022','653023','653024','653101','653121','653122','653123','653124','653125','653126','653127','653128','653129','653130','653131','653201','653221','653222','653223','653224','653225','653226','653227','654002','654003','654004','654021','654022','654023','654024','654025','654026','654027','654028','654201','654202','654203','654221','654224','654225','654226','654301','654321','654322','654323','654324','654325','654326','659001','659002','659003','659004','659005','659006','659007','659008','659009','659010','659011','659012')";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            while (rs.next()) {
                String parentCode = rs.getString("parent_code");
                String code = rs.getString("code");
                result.computeIfAbsent(parentCode, k -> new ArrayList<>()).add(code);
            }
        }
        return result;
    }


    public Map<String, List<String>> getRegionCodeProvinceMap(Connection conn) throws SQLException {
        Map<String, List<String>> result = new HashMap<>();
        String sql = "SELECT t2.parent_code,t1.`code` FROM main_dict_region t1 JOIN main_dict_region t2 ON t1.parent_code = t2.`code` WHERE t1.code IN ('110101','110102','110105','110106','110107','110108','110109','110111','110112','110113','110114','110115','110116','110117','110118','110119','120101','120102','120103','120104','120105','120106','120110','120111','120112','120113','120114','120115','120116','120117','120118','120119','130102','130104','130105','130107','130108','130109','130110','130111','130121','130123','130125','130126','130127','130128','130129','130130','130131','130132','130133','130181','130183','130184','130202','130203','130204','130205','130207','130208','130209','130224','130225','130227','130229','130281','130283','130284','130302','130303','130304','130306','130321','130322','130324','130402','130403','130404','130406','130407','130408','130423','130424','130425','130426','130427','130430','130431','130432','130433','130434','130435','130481','130502','130503','130505','130506','130522','130523','130524','130525','130528','130529','130530','130531','130532','130533','130534','130535','130581','130582','130602','130606','130607','130608','130609','130623','130624','130626','130627','130628','130629','130630','130631','130632','130633','130634','130635','130636','130637','130638','130681','130682','130683','130684','130702','130703','130705','130706','130708','130709','130722','130723','130724','130725','130726','130727','130728','130730','130731','130732','130802','130803','130804','130821','130822','130824','130825','130826','130827','130828','130881','130902','130903','130921','130922','130923','130924','130925','130926','130927','130928','130929','130930','130981','130982','130983','130984','131002','131003','131022','131023','131024','131025','131026','131028','131081','131082','131102','131103','131121','131122','131123','131124','131125','131126','131127','131128','131182','140105','140106','140107','140108','140109','140110','140121','140122','140123','140181','140212','140213','140214','140215','140221','140222','140223','140224','140225','140226','140302','140303','140311','140321','140322','140403','140404','140405','140406','140423','140425','140426','140427','140428','140429','140430','140431','140502','140521','140522','140524','140525','140581','140602','140603','140621','140622','140623','140681','140702','140703','140721','140722','140723','140724','140725','140727','140728','140729','140781','140802','140821','140822','140823','140824','140825','140826','140827','140828','140829','140830','140881','140882','140902','140921','140922','140923','140924','140925','140926','140927','140928','140929','140930','140931','140932','140981','141002','141021','141022','141023','141024','141025','141026','141027','141028','141029','141030','141031','141032','141033','141034','141081','141082','141102','141121','141122','141123','141124','141125','141126','141127','141128','141129','141130','141181','141182','150102','150103','150104','150105','150121','150122','150123','150124','150125','150202','150203','150204','150205','150206','150207','150221','150222','150223','150302','150303','150304','150402','150403','150404','150421','150422','150423','150424','150425','150426','150428','150429','150430','150502','150521','150522','150523','150524','150525','150526','150581','150602','150603','150621','150622','150623','150624','150625','150626','150627','150702','150703','150721','150722','150723','150724','150725','150726','150727','150781','150782','150783','150784','150785','150802','150821','150822','150823','150824','150825','150826','150902','150921','150922','150923','150924','150925','150926','150927','150928','150929','150981','152201','152202','152221','152222','152223','152224','152501','152502','152522','152523','152524','152525','152526','152527','152528','152529','152530','152531','152921','152922','152923','210102','210103','210104','210105','210106','210111','210112','210113','210114','210115','210123','210124','210181','210202','210203','210204','210211','210212','210213','210214','210224','210281','210283','210302','210303','210304','210311','210321','210323','210381','210402','210403','210404','210411','210421','210422','210423','210502','210503','210504','210505','210521','210522','210602','210603','210604','210624','210681','210682','210702','210703','210711','210726','210727','210781','210782','210802','210803','210804','210811','210881','210882','210902','210903','210904','210905','210911','210921','210922','211002','211003','211004','211005','211011','211021','211081','211102','211103','211104','211122','211202','211204','211221','211223','211224','211281','211282','211302','211303','211321','211322','211324','211381','211382','211402','211403','211404','211421','211422','211481','220102','220103','220104','220105','220106','220112','220113','220122','220182','220183','220184','220202','220203','220204','220211','220221','220281','220282','220283','220284','220302','220303','220322','220323','220382','220402','220403','220421','220422','220502','220503','220521','220523','220524','220581','220582','220602','220605','220621','220622','220623','220681','220702','220721','220722','220723','220781','220802','220821','220822','220881','220882','222401','222402','222403','222404','222405','222406','222424','222426','230102','230103','230104','230108','230109','230110','230111','230112','230113','230123','230124','230125','230126','230127','230128','230129','230183','230184','230202','230203','230204','230205','230206','230207','230208','230221','230223','230224','230225','230227','230229','230230','230231','230281','230302','230303','230304','230305','230306','230307','230321','230381','230382','230402','230403','230404','230405','230406','230407','230421','230422','230502','230503','230505','230506','230521','230522','230523','230524','230602','230603','230604','230605','230606','230621','230622','230623','230624','230717','230718','230719','230722','230723','230724','230725','230726','230751','230781','230803','230804','230805','230811','230822','230826','230828','230881','230882','230883','230902','230903','230904','230921','231002','231003','231004','231005','231025','231081','231083','231084','231085','231086','231102','231123','231124','231181','231182','231183','231202','231221','231222','231223','231224','231225','231226','231281','231282','231283','232701','232718','232721','232722','310101','310104','310105','310106','310107','310109','310110','310112','310113','310114','310115','310116','310117','310118','310120','310151','320102','320104','320105','320106','320111','320113','320114','320115','320116','320117','320118','320205','320206','320211','320213','320214','320281','320282','320302','320303','320305','320311','320312','320321','320322','320324','320381','320382','320402','320404','320411','320412','320413','320481','320505','320506','320507','320508','320509','320581','320582','320583','320585','320612','320613','320614','320623','320681','320682','320685','320703','320706','320707','320722','320723','320724','320803','320804','320812','320813','320826','320830','320831','320902','320903','320904','320921','320922','320923','320924','320925','320981','321002','321003','321012','321023','321081','321084','321102','321111','321112','321181','321182','321183','321202','321203','321204','321281','321282','321283','321302','321311','321322','321323','321324','330102','330105','330106','330108','330109','330110','330111','330112','330113','330114','330122','330127','330182','330203','330205','330206','330211','330212','330213','330225','330226','330281','330282','330302','330303','330304','330305','330324','330326','330327','330328','330329','330381','330382','330383','330402','330411','330421','330424','330481','330482','330483','330502','330503','330521','330522','330523','330602','330603','330604','330624','330681','330683','330702','330703','330723','330726','330727','330781','330782','330783','330784','330802','330803','330822','330824','330825','330881','330902','330903','330921','330922','331002','331003','331004','331022','331023','331024','331081','331082','331083','331102','331121','331122','331123','331124','331125','331126','331127','331181','340102','340103','340104','340111','340121','340122','340123','340124','340181','340202','340207','340209','340210','340212','340223','340281','340302','340303','340304','340311','340321','340322','340323','340402','340403','340404','340405','340406','340421','340422','340503','340504','340506','340521','340522','340523','340602','340603','340604','340621','340705','340706','340711','340722','340802','340803','340811','340822','340825','340826','340827','340828','340881','340882','341002','341003','341004','341021','341022','341023','341024','341102','341103','341122','341124','341125','341126','341181','341182','341202','341203','341204','341221','341222','341225','341226','341282','341302','341321','341322','341323','341324','341502','341503','341504','341522','341523','341524','341525','341602','341621','341622','341623','341702','341721','341722','341723','341802','341821','341823','341824','341825','341881','341882','350102','350103','350104','350105','350111','350112','350121','350122','350123','350124','350125','350128','350181','350203','350205','350206','350211','350212','350213','350302','350303','350304','350305','350322','350404','350405','350421','350423','350424','350425','350426','350428','350429','350430','350481','350502','350503','350504','350505','350521','350524','350525','350526','350581','350582','350583','350602','350603','350604','350605','350622','350623','350624','350626','350627','350628','350629','350702','350703','350721','350722','350723','350724','350725','350781','350782','350783','350802','350803','350821','350823','350824','350825','350881','350902','350921','350922','350923','350924','350925','350926','350981','350982','360102','360103','360104','360111','360112','360113','360121','360123','360124','360202','360203','360222','360281','360302','360313','360321','360322','360323','360402','360403','360404','360423','360424','360425','360426','360428','360429','360430','360481','360482','360483','360502','360521','360602','360603','360681','360702','360703','360704','360722','360723','360724','360725','360726','360728','360729','360730','360731','360732','360733','360734','360735','360781','360783','360802','360803','360821','360822','360823','360824','360825','360826','360827','360828','360829','360830','360881','360902','360921','360922','360923','360924','360925','360926','360981','360982','360983','361002','361003','361021','361022','361023','361024','361025','361026','361027','361028','361030','361102','361103','361104','361123','361124','361125','361126','361127','361128','361129','361130','361181','370102','370103','370104','370105','370112','370113','370114','370115','370116','370117','370124','370126','370202','370203','370211','370212','370213','370214','370215','370281','370283','370285','370302','370303','370304','370305','370306','370321','370322','370323','370402','370403','370404','370405','370406','370481','370502','370503','370505','370522','370523','370602','370611','370612','370613','370614','370681','370682','370683','370685','370686','370687','370702','370703','370704','370705','370724','370725','370781','370782','370783','370784','370785','370786','370811','370812','370826','370827','370828','370829','370830','370831','370832','370881','370883','370902','370911','370921','370923','370982','370983','371002','371003','371082','371083','371102','371103','371121','371122','371302','371311','371312','371321','371322','371323','371324','371325','371326','371327','371328','371329','371402','371403','371422','371423','371424','371425','371426','371427','371428','371481','371482','371502','371503','371521','371522','371524','371525','371526','371581','371602','371603','371621','371622','371623','371625','371681','371702','371703','371721','371722','371723','371724','371725','371726','371728','410102','410103','410104','410105','410106','410108','410122','410181','410182','410183','410184','410185','410202','410203','410204','410205','410212','410221','410222','410223','410225','410302','410303','410304','410305','410307','410308','410311','410323','410324','410325','410326','410327','410328','410329','410402','410403','410404','410411','410421','410422','410423','410425','410481','410482','410502','410503','410505','410506','410522','410523','410526','410527','410581','410602','410603','410611','410621','410622','410702','410703','410704','410711','410721','410724','410725','410726','410727','410781','410782','410783','410802','410803','410804','410811','410821','410822','410823','410825','410882','410883','410902','410922','410923','410926','410927','410928','411002','411003','411024','411025','411081','411082','411102','411103','411104','411121','411122','411202','411203','411221','411224','411281','411282','411302','411303','411321','411322','411323','411324','411325','411326','411327','411328','411329','411330','411381','411402','411403','411421','411422','411423','411424','411425','411426','411481','411502','411503','411521','411522','411523','411524','411525','411526','411527','411528','411602','411603','411621','411622','411623','411624','411625','411627','411628','411681','411702','411721','411722','411723','411724','411725','411726','411727','411728','411729','419001','420102','420103','420104','420105','420106','420107','420111','420112','420113','420114','420115','420116','420117','420202','420203','420204','420205','420222','420281','420302','420303','420304','420322','420323','420324','420325','420381','420502','420503','420504','420505','420506','420525','420526','420527','420528','420529','420581','420582','420583','420602','420606','420607','420624','420625','420626','420682','420683','420684','420702','420703','420704','420802','420804','420822','420881','420882','420902','420921','420922','420923','420981','420982','420984','421002','421003','421022','421024','421081','421083','421087','421088','421102','421121','421122','421123','421124','421125','421126','421127','421181','421182','421202','421221','421222','421223','421224','421281','421303','421321','421381','422801','422802','422822','422823','422825','422826','422827','422828','429004','429005','429006','429021','430102','430103','430104','430105','430111','430112','430121','430181','430182','430202','430203','430204','430211','430212','430223','430224','430225','430281','430302','430304','430321','430381','430382','430405','430406','430407','430408','430412','430421','430422','430423','430424','430426','430481','430482','430502','430503','430511','430522','430523','430524','430525','430527','430528','430529','430581','430582','430602','430603','430611','430621','430623','430624','430626','430681','430682','430702','430703','430721','430722','430723','430724','430725','430726','430781','430802','430811','430821','430822','430902','430903','430921','430922','430923','430981','431002','431003','431021','431022','431023','431024','431025','431026','431027','431028','431081','431102','431103','431122','431123','431124','431125','431126','431127','431128','431129','431181','431202','431221','431222','431223','431224','431225','431226','431227','431228','431229','431230','431281','431302','431321','431322','431381','431382','433101','433122','433123','433124','433125','433126','433127','433130','440103','440104','440105','440106','440111','440112','440113','440114','440115','440117','440118','440203','440204','440205','440222','440224','440229','440232','440233','440281','440282','440303','440304','440305','440306','440307','440308','440309','440310','440311','440402','440403','440404','440507','440511','440512','440513','440514','440515','440523','440604','440605','440606','440607','440608','440703','440704','440705','440781','440783','440784','440785','440802','440803','440804','440811','440823','440825','440881','440882','440883','440902','440904','440981','440982','440983','441202','441203','441204','441223','441224','441225','441226','441284','441302','441303','441322','441323','441324','441402','441403','441422','441423','441424','441426','441427','441481','441502','441521','441523','441581','441602','441621','441622','441623','441624','441625','441702','441704','441721','441781','441802','441803','441821','441823','441825','441826','441881','441882','441900','442000','445102','445103','445122','445202','445203','445222','445224','445281','445302','445303','445321','445322','445381','450102','450103','450105','450107','450108','450109','450110','450123','450124','450125','450126','450181','450202','450203','450204','450205','450206','450222','450223','450224','450225','450226','450302','450303','450304','450305','450311','450312','450321','450323','450324','450325','450326','450327','450328','450329','450330','450332','450381','450403','450405','450406','450421','450422','450423','450481','450502','450503','450512','450521','450602','450603','450621','450681','450702','450703','450721','450722','450802','450803','450804','450821','450881','450902','450903','450921','450922','450923','450924','450981','451002','451003','451022','451024','451026','451027','451028','451029','451030','451031','451081','451082','451102','451103','451121','451122','451123','451202','451203','451221','451222','451223','451224','451225','451226','451227','451228','451229','451302','451321','451322','451323','451324','451381','451402','451421','451422','451423','451424','451425','451481','460105','460106','460107','460108','460202','460203','460204','460205','460301','460400','469001','469002','469005','469006','469007','469021','469022','469023','469024','469025','469026','469027','469028','469029','469030','500101','500102','500103','500104','500105','500106','500107','500108','500109','500110','500111','500112','500113','500114','500115','500116','500117','500118','500119','500120','500151','500152','500153','500154','500155','500156','500229','500230','500231','500233','500235','500236','500237','500238','500240','500241','500242','500243','510104','510105','510106','510107','510108','510112','510113','510114','510115','510116','510117','510118','510121','510129','510131','510181','510182','510183','510184','510185','510302','510303','510304','510311','510321','510322','510402','510403','510411','510421','510422','510502','510503','510504','510521','510522','510524','510525','510603','510604','510623','510681','510682','510683','510703','510704','510705','510722','510723','510725','510726','510727','510781','510802','510811','510812','510821','510822','510823','510824','510903','510904','510921','510923','510981','511002','511011','511024','511025','511083','511102','511111','511112','511113','511123','511124','511126','511129','511132','511133','511181','511302','511303','511304','511321','511322','511323','511324','511325','511381','511402','511403','511421','511423','511424','511425','511502','511503','511504','511523','511524','511525','511526','511527','511528','511529','511602','511603','511621','511622','511623','511681','511702','511703','511722','511723','511724','511725','511781','511802','511803','511822','511823','511824','511825','511826','511827','511902','511903','511921','511922','511923','512002','512021','512022','513201','513221','513222','513223','513224','513225','513226','513227','513228','513230','513231','513232','513233','513301','513322','513323','513324','513325','513326','513327','513328','513329','513330','513331','513332','513333','513334','513335','513336','513337','513338','513401','513402','513422','513423','513424','513426','513427','513428','513429','513430','513431','513432','513433','513434','513435','513436','513437','520102','520103','520111','520112','520113','520115','520121','520122','520123','520181','520201','520203','520204','520281','520302','520303','520304','520322','520323','520324','520325','520326','520327','520328','520329','520330','520381','520382','520402','520403','520422','520423','520424','520425','520502','520521','520523','520524','520525','520526','520527','520581','520602','520603','520621','520622','520623','520624','520625','520626','520627','520628','522301','522302','522323','522324','522325','522326','522327','522328','522601','522622','522623','522624','522625','522626','522627','522628','522629','522630','522631','522632','522633','522634','522635','522636','522701','522702','522722','522723','522725','522726','522727','522728','522729','522730','522731','522732','530102','530103','530111','530112','530113','530114','530115','530124','530125','530126','530127','530128','530129','530181','530302','530303','530304','530322','530323','530324','530325','530326','530381','530402','530403','530423','530424','530425','530426','530427','530428','530481','530502','530521','530523','530524','530581','530602','530621','530622','530623','530624','530625','530626','530627','530628','530629','530681','530702','530721','530722','530723','530724','530802','530821','530822','530823','530824','530825','530826','530827','530828','530829','530902','530921','530922','530923','530924','530925','530926','530927','532301','532302','532322','532323','532324','532325','532326','532327','532328','532329','532501','532502','532503','532504','532523','532524','532525','532527','532528','532529','532530','532531','532532','532601','532622','532623','532624','532625','532626','532627','532628','532801','532822','532823','532901','532922','532923','532924','532925','532926','532927','532928','532929','532930','532931','532932','533102','533103','533122','533123','533124','533301','533323','533324','533325','533401','533422','533423','540102','540103','540104','540121','540122','540123','540124','540127','540202','540221','540222','540223','540224','540225','540226','540227','540228','540229','540230','540231','540232','540233','540234','540235','540236','540237','540302','540321','540322','540323','540324','540325','540326','540327','540328','540329','540330','540402','540421','540423','540424','540425','540426','540481','540502','540521','540522','540523','540524','540525','540526','540527','540528','540529','540531','540581','540602','540621','540622','540623','540624','540625','540626','540627','540628','540629','540630','542521','542522','542523','542524','542525','542526','542527','610102','610103','610104','610111','610112','610113','610114','610115','610116','610117','610118','610122','610124','610202','610203','610204','610222','610302','610303','610304','610305','610323','610324','610326','610327','610328','610329','610330','610331','610402','610403','610404','610422','610423','610424','610425','610426','610428','610429','610430','610431','610481','610482','610502','610503','610522','610523','610524','610525','610526','610527','610528','610581','610582','610602','610603','610621','610622','610625','610626','610627','610628','610629','610630','610631','610632','610681','610702','610703','610722','610723','610724','610725','610726','610727','610728','610729','610730','610802','610803','610822','610824','610825','610826','610827','610828','610829','610830','610831','610881','610902','610921','610922','610923','610924','610925','610926','610927','610929','610981','611002','611021','611022','611023','611024','611025','611026','620102','620103','620104','620105','620111','620121','620122','620123','620200','620302','620321','620402','620403','620421','620422','620423','620502','620503','620521','620522','620523','620524','620525','620602','620621','620622','620623','620702','620721','620722','620723','620724','620725','620802','620821','620822','620823','620825','620826','620881','620902','620921','620922','620923','620924','620981','620982','621002','621021','621022','621023','621024','621025','621026','621027','621102','621121','621122','621123','621124','621125','621126','621202','621221','621222','621223','621224','621225','621226','621227','621228','622901','622921','622922','622923','622924','622925','622926','622927','623001','623021','623022','623023','623024','623025','623026','623027','630102','630103','630104','630105','630106','630121','630123','630202','630203','630222','630223','630224','630225','632221','632222','632223','632224','632301','632322','632323','632324','632521','632522','632523','632524','632525','632621','632622','632623','632624','632625','632626','632701','632722','632723','632724','632725','632726','632801','632802','632803','632821','632822','632823','632825','640104','640105','640106','640121','640122','640181','640202','640205','640221','640302','640303','640323','640324','640381','640402','640422','640423','640424','640425','640502','640521','640522','650102','650103','650104','650105','650106','650107','650109','650121','650202','650203','650204','650205','650402','650421','650422','650502','650521','650522','652301','652302','652323','652324','652325','652327','652328','652701','652702','652722','652723','652801','652822','652823','652824','652825','652826','652827','652828','652829','652901','652902','652922','652924','652925','652926','652927','652928','652929','653001','653022','653023','653024','653101','653121','653122','653123','653124','653125','653126','653127','653128','653129','653130','653131','653201','653221','653222','653223','653224','653225','653226','653227','654002','654003','654004','654021','654022','654023','654024','654025','654026','654027','654028','654201','654202','654203','654221','654224','654225','654226','654301','654321','654322','654323','654324','654325','654326','659001','659002','659003','659004','659005','659006','659007','659008','659009','659010','659011','659012') ";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            while (rs.next()) {
                String parentCode = rs.getString("parent_code");
                String code = rs.getString("code");

                result.computeIfAbsent(parentCode, k -> new ArrayList<>()).add(code);
            }
        }
        return result;
    }

}