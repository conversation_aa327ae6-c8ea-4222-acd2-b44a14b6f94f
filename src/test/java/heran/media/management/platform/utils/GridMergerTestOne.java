package heran.media.management.platform.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class GridMergerTestOne {

    private static final String DB_URL = "*****************************************************************************";
    private static final String USER = "opworker";
    private static final String PASSWORD = "Tango$#@!222";
    private static final String CENTER_POINT_JSON_FILE_PATH = "C:\\Users\\<USER>\\Desktop\\上海常驻人口热力值202501.txt";


//    1， 文件读取中心点列表（这个中心点就是merge表里的中心点），这个中心点列表里的元素是resident_map_cell_resource的网格（一行数据为一个网格）顶点，如果匹配不到则写个日志，跳过这个中心点处理
//    2， 基于该中心点，找到原始表里的4个网格（左上、左下、右上、右下），得2*2的4个格子， 再往外找，得3*3，一直到10*10，它们组成 一条merged_resident_map_cell_resource的数据，并且原始数据的merge_id都填这行数据的id
//    3， 进一步解析，相当于先给出一个点位， 以它为中心（同时也是merge元素的中心点） 划分出4个象限的扩展方向， 左上|右上|左下|右下， 每个象限包含不大于5*5=25的网格数量，
//    以左上的象限扩散为例，已知 中心点 x0,y0, 查找右下角为x0,y0的元素（只有一个格子）； 然后一路往左，额外多找4格，第一上限横排的
//    4， 如果某一个方向某有匹配到网格，这个方向可以停止，然后这一横排5个格例再分别网上延伸，也是再找到4个，这样总共就找出了5*5， 寻找方式也是顶点相邻法，如果没找到相邻的又不够5个，额外写个日志
//    5， 最终4个上限各找25，就得出了需要merge的100个格子


    enum Quadrant {
        NW("wkt_lower_right", "wkt_lower_left"),  // 左上象限：水平 新格子右下=当前格子左下， 垂直 新格子左下=当前各自左上
        NE("wkt_lower_left", "wkt_up_left"),     // 右上象限
        SW("wkt_up_right", "wkt_lower_right"),// 左下象限
        SE("wkt_up_left", "wkt_lower_left");  // 右下象限

        final String horizontalVertex;  // 水平扩展方向要匹配的顶点字段
        final String verticalVertex;    // 垂直扩展方向要匹配的顶点字段

        Quadrant(String horizontalVertex, String verticalVertex) {
            this.horizontalVertex = horizontalVertex;
            this.verticalVertex = verticalVertex;
        }
    }

    @Test
    public void processMerge() {
        //获取1000*1000的网格中心点数据
        List<Coordinate> centers = readInputPoints();
        try (Connection conn = DriverManager.getConnection(DB_URL, USER, PASSWORD)) {
            for (Coordinate center : centers) {
                try {
                    processCenter(conn, center);
                } catch (SQLException e) {
                    log.error("处理中心点 ：{} 时发生错误:{}", center, e.getMessage());
                    try {
                        conn.rollback();
                    } catch (SQLException ex) {
                        log.error("回滚事务失败 ：{}", ex.getMessage());
                    }
                }
            }
        } catch (SQLException e) {
            log.error("数据库连接错误: {}", e.getMessage());
        }
    }


    private void processCenter(Connection conn, Coordinate center) throws SQLException {
        conn.setAutoCommit(false);
        try {
            // 1. 查找四个起始网格
            Map<Quadrant, OriginalGrid> origins = findOriginGrids(conn, center);
            if (origins.isEmpty()) {
                log.error("中心点:{} 没有找到任何起始网格，跳过处理", center);
                conn.commit();
                return;
            }
            // 2. 为每个象限收集网格
            Map<Quadrant, List<OriginalGrid>> allGrids = new EnumMap<>(Quadrant.class);
            for (Quadrant q : Quadrant.values()) {
                OriginalGrid origin = origins.get(q);
                if (origin == null) {
                    log.error("中心点 :{} 在 :{} 象限没有找到起始网格", center, q);
                    continue;
                }
                List<OriginalGrid> grids = collectGrids(conn, q, origin);
                allGrids.put(q, grids);
                if (grids.size() != 25) {
                    log.error("中心点 ：{} 在 {} 象限网格数不足 {}", center, q, grids.size());
                }
            }
            if (allGrids.isEmpty()) {
                log.error("中心点 {} 没有收集到任何网格，跳过处理", center);
                conn.commit();
                return;
            }
            // 3. 创建合并记录，使用原始中心点
            //long mergedId = createMergedRecord(conn, allGrids, center);
            // 4. 更新原始表
            //updateOriginalGrids(conn, allGrids, mergedId);
            conn.commit();
            System.out.println("成功处理中心点: " + center);
        } catch (SQLException e) {
            conn.rollback();
            throw e;
        } finally {
            conn.setAutoCommit(true);
        }
    }

    // 查找四个起始网格（每个象限的起始网格）
    private Map<Quadrant, OriginalGrid> findOriginGrids(Connection conn, Coordinate center) throws SQLException {
        Map<Quadrant, OriginalGrid> origins = new EnumMap<>(Quadrant.class);
        for (Quadrant q : Quadrant.values()) {
            String sql;
            switch (q) {
                case NW:
                    sql = "SELECT * FROM resident_map_cell_resource WHERE wkt_lower_right = ?";
                    break;
                case NE:
                    sql = "SELECT * FROM resident_map_cell_resource WHERE wkt_lower_left = ?";
                    break;
                case SW:
                    sql = "SELECT * FROM resident_map_cell_resource WHERE wkt_up_right = ?";
                    break;
                case SE:
                    sql = "SELECT * FROM resident_map_cell_resource WHERE wkt_up_left = ?";
                    break;
                default:
                    throw new IllegalArgumentException("未知象限: " + q);
            }
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, formatCoordinate(center));
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    origins.put(q, mapToGrid(rs));
                }
            }
        }
        return origins;
    }

    // 按方向收集网格（5x5）
    private List<OriginalGrid> collectGrids(Connection conn, Quadrant q, OriginalGrid startGrid) throws SQLException {
        List<OriginalGrid> grids = new ArrayList<>();
        grids.add(startGrid);

        // 横向扩展（例如：左上象限先向左扩展）
        List<OriginalGrid> horizontalLine = new ArrayList<>();
        horizontalLine.add(startGrid);

        // 横向收集（最多4个）
        OriginalGrid current = startGrid;
        for (int i = 0; i < 4; i++) {
            current = findAdjacentGrid(conn, current, q, true);
            if (current == null) break;
            horizontalLine.add(current);
        }

        // 纵向扩展（从每列的横向基线向上/下扩展）
        for (OriginalGrid base : horizontalLine) {
            OriginalGrid columnCurrent = base;
            grids.add(columnCurrent);

            // 纵向收集（最多4个）
            for (int i = 0; i < 4; i++) {
                columnCurrent = findAdjacentGrid(conn, columnCurrent, q, false);
                if (columnCurrent == null) break;
                grids.add(columnCurrent);
            }
        }

        return grids.stream().distinct().limit(25).collect(Collectors.toList());
    }


    private OriginalGrid findAdjacentGrid(Connection conn, OriginalGrid current, Quadrant q, boolean isHorizontal) throws SQLException {
        String currentVertex;
        String targetVertexField;
        switch (q) {
            case NW:
                // 左上象限
                if (isHorizontal) {
                    //右下顶点 匹配 左下顶点
                    targetVertexField = "wkt_lower_right";
                    currentVertex = formatCoordinate(new Coordinate(current.lowerLeftLng, current.lowerLeftLat, null));
                } else {
                    //左下顶点 匹配 左上顶点
                    targetVertexField = "wkt_lower_left";
                    currentVertex = formatCoordinate(new Coordinate(current.upperLeftLng, current.upperLeftLat, null));
                }
                break;
            case NE:
                // 右上象限
                if (isHorizontal) {
                    //左下顶点 匹配 右下顶点
                    targetVertexField = "wkt_lower_left";
                    currentVertex = formatCoordinate(new Coordinate(current.lowerRightLng, current.lowerRightLat, null));
                } else {
                    //左下顶点 匹配 左上顶点
                    targetVertexField = "wkt_lower_left";
                    currentVertex = formatCoordinate(new Coordinate(current.upperLeftLng, current.upperLeftLat, null));
                }
                break;
            case SW:
                // 左下象限
                if (isHorizontal) {
                    //右下顶点 匹配 左下顶点
                    targetVertexField = "wkt_lower_right";
                    currentVertex = formatCoordinate(new Coordinate(current.lowerLeftLng, current.lowerLeftLat, null));
                } else {
                    //右上顶点 匹配 右下顶点
                    targetVertexField = "wkt_up_right";
                    currentVertex = formatCoordinate(new Coordinate(current.lowerRightLng, current.lowerRightLat, null));
                }
                break;
            case SE:
               // 右下象限
                if (isHorizontal) {
                    //左下顶点 匹配 右下顶点
                    targetVertexField = "wkt_lower_left";
                    currentVertex = formatCoordinate(new Coordinate(current.lowerRightLng, current.lowerRightLat, null));
                } else {
                    //右上顶点 匹配 右下顶点
                    targetVertexField = "wkt_up_right";
                    currentVertex = formatCoordinate(new Coordinate(current.lowerRightLng, current.lowerRightLat, null));
                }
                break;
            default:
                throw new IllegalArgumentException("Invalid vertex field");
        }

        String sql = "SELECT * FROM resident_map_cell_resource WHERE " + targetVertexField + " = ? LIMIT 1";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, currentVertex);
            ResultSet rs = stmt.executeQuery();
            return rs.next() ? mapToGrid(rs) : null;
        }
    }

    private long createMergedRecord(Connection conn, Map<Quadrant, List<OriginalGrid>> allGrids, Coordinate originalCenter) throws SQLException {

        //左下顶点纬度  右下顶点经度 右下顶点纬度  左上顶点经度 左上顶点纬度 右上顶点经度 右上顶点纬度

        String sql = "INSERT INTO merged_resident_map_cell_resource " +
                "(center_lng, center_lat, people_count,lower_left_lng,lower_left_lat,lower_right_lng,lower_right_lat,up_left_lng,up_left_lat,up_right_lng,up_right_lat,city_code) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";

        //聚合人口数
        int totalPeople = calculateTotalPeople(allGrids);
        //获取1000*1000的网格经纬度
        OriginalGrid lowerLeftGrid = findExtremeGrid(allGrids.get(Quadrant.SW), true, true);   // 左下：经度小、纬度小
        OriginalGrid lowerRightGrid = findExtremeGrid(allGrids.get(Quadrant.SE), false, true); // 右下：经度大、纬度小
        OriginalGrid upperLeftGrid = findExtremeGrid(allGrids.get(Quadrant.NW), true, false);  // 左上：经度小、纬度大
        OriginalGrid upperRightGrid = findExtremeGrid(allGrids.get(Quadrant.NE), false, false);// 右上：经度大、纬度大

        try (PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            stmt.setBigDecimal(1, BigDecimal.valueOf(originalCenter.lng));
            stmt.setBigDecimal(2, BigDecimal.valueOf(originalCenter.lat));
            stmt.setInt(3, totalPeople);

            stmt.setBigDecimal(4, BigDecimal.valueOf(lowerLeftGrid.lowerLeftLng));
            stmt.setBigDecimal(5, BigDecimal.valueOf(lowerLeftGrid.lowerLeftLat));

            stmt.setBigDecimal(6, BigDecimal.valueOf(lowerRightGrid.lowerRightLng));
            stmt.setBigDecimal(7, BigDecimal.valueOf(lowerRightGrid.lowerRightLat));

            stmt.setBigDecimal(8, BigDecimal.valueOf(upperLeftGrid.upperLeftLng));
            stmt.setBigDecimal(9, BigDecimal.valueOf(upperLeftGrid.upperLeftLat));

            stmt.setBigDecimal(10, BigDecimal.valueOf(upperRightGrid.upperRightLng));
            stmt.setBigDecimal(11, BigDecimal.valueOf(upperRightGrid.upperRightLat));

            stmt.setString(12, "310000");
            stmt.executeUpdate();

            try (ResultSet rs = stmt.getGeneratedKeys()) {
                return rs.next() ? rs.getLong(1) : -1;
            }
        }
    }

    private OriginalGrid findExtremeGrid(List<OriginalGrid> grids, boolean findMinLng, boolean findMinLat) {
        if (grids == null || grids.isEmpty()) {
            return new OriginalGrid();
        }
        return grids.stream().min((g1, g2) -> {
            double lng1 = findMinLng ? g1.lowerLeftLng : g1.lowerRightLng;
            double lng2 = findMinLng ? g2.lowerLeftLng : g2.lowerRightLng;
            double lat1 = findMinLat ? g1.lowerLeftLat : g1.upperLeftLat;
            double lat2 = findMinLat ? g2.lowerLeftLat : g2.upperLeftLat;

            if (Double.compare(lat1, lat2) != 0) {
                return Double.compare(lat1, lat2);
            } else {
                return Double.compare(lng1, lng2);
            }
        }).orElseThrow(() -> new IllegalStateException("没有找到合适的格子"));
    }


    // 更新原始表merge_id
    private void updateOriginalGrids(Connection conn, Map<Quadrant, List<OriginalGrid>> allGrids, long mergedId) throws SQLException {
        String sql = "UPDATE resident_map_cell_resource SET merge_id = ? WHERE id = ?";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            for (List<OriginalGrid> grids : allGrids.values()) {
                for (OriginalGrid grid : grids) {
                    stmt.setLong(1, mergedId);
                    stmt.setInt(2, grid.id);
                    stmt.addBatch();
                }
            }
            stmt.executeBatch();
        }
    }

    // 辅助方法
    private int calculateTotalPeople(Map<Quadrant, List<OriginalGrid>> allGrids) {
        return allGrids.values().stream()
                .flatMap(List::stream)
                .mapToInt(g -> Integer.parseInt(g.peopleCount))
                .sum();
    }

    // 数据结构
    static class OriginalGrid {
        int id;
        double upperLeftLng, upperLeftLat;
        double lowerLeftLng, lowerLeftLat;
        double upperRightLng, upperRightLat;
        double lowerRightLng, lowerRightLat;
        String peopleCount;

        Coordinate getCenter() {
            return new Coordinate(
                    (upperLeftLng + upperRightLng) / 2,
                    (upperLeftLat + lowerLeftLat) / 2,
                    null
            );
        }
    }

    @Data
    static class Coordinate {
        final double lng, lat;
        final String code;

        Coordinate(double lng, double lat, String code) {
            this.lng = lng;
            this.lat = lat;
            this.code = code;
        }

        public String toString() {
            //return String.format("%.6f,%.6f", lng, lat);
            return lng + "," + lat;
        }
    }


    private OriginalGrid mapToGrid(ResultSet rs) throws SQLException {
        OriginalGrid g = new OriginalGrid();
        g.id = rs.getInt("id");
        g.upperLeftLng = rs.getDouble("up_left_lng");
        g.upperLeftLat = rs.getDouble("up_left_lat");
        g.lowerLeftLng = rs.getDouble("lower_left_lng");
        g.lowerLeftLat = rs.getDouble("lower_left_lat");
        g.upperRightLng = rs.getDouble("up_right_lng");
        g.upperRightLat = rs.getDouble("up_right_lat");
        g.lowerRightLat = rs.getDouble("lower_right_lat");
        g.lowerRightLng = rs.getDouble("lower_right_lng");
        g.peopleCount = rs.getString("people_count");
        return g;
    }

    private String formatCoordinate(Coordinate coord) {
        return coord.toString();
    }

    private static List<Coordinate> readInputPoints() {
        List<Coordinate> points = new ArrayList<>();
//        Connection conn = null;
//        PreparedStatement ps = null;
//        ResultSet rs = null;
//        try {
//            conn = DriverManager.getConnection(DB_URL, USER, PASSWORD);
//            String sql = "SELECT type_info ,longitude, latitude FROM grid_hot_data WHERE type_info = '310000' AND stat_month = '202501'";
//            ps = conn.prepareStatement(sql);
//            rs = ps.executeQuery();
//            while (rs.next()) {
//                double longitude = rs.getDouble("longitude");
//                double latitude = rs.getDouble("latitude");
//                String code = rs.getString("type_info");
//                points.add(new Coordinate(longitude, latitude, code));
//            }
//        } catch (Exception e) {
//            log.error("读取数据库失败", e);
//        } finally {
//            try {
//                if (rs != null) rs.close();
//            } catch (Exception ignored) {
//            }
//            try {
//                if (ps != null) ps.close();
//            } catch (Exception ignored) {
//            }
//            try {
//                if (conn != null) conn.close();
//            } catch (Exception ignored) {
//            }
//        }
        points.add(new Coordinate(121.495498, 31.227698, "310000"));
        return points;
    }


/*    private static List<Coordinate> readInputPoints() {
        List<Coordinate> points = new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(new File(CENTER_POINT_JSON_FILE_PATH));
            JsonNode dataHotNode = rootNode.path("data").path("query").path("index_data").path("data_hot");

            if (dataHotNode.isObject()) {
                dataHotNode.fields().forEachRemaining(entry -> {
                    String[] coords = entry.getKey().split(",");
                    try {
                        points.add(new Coordinate(
                                Double.parseDouble(coords[0]),
                                Double.parseDouble(coords[1])
                        ));
                    } catch (Exception e) {
                        log.error("解析坐标点失败: " + entry.getKey(), e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("读取文件失败: " + CENTER_POINT_JSON_FILE_PATH, e);
        }
        return points;
    }*/


}