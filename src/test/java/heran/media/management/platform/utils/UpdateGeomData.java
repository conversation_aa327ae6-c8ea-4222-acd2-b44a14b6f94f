package heran.media.management.platform.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Select;
import org.junit.Test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class UpdateGeomData {
    private static final String DB_URL = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    private static final String USER = "heran_app";
    private static final String PASSWORD = "5G5ZVlDjWE=";

    @Test
    public void execute() throws SQLException {
        //String code = "150100,150200,150300,150400,150500,150600,150700,150800,150900,152200,152500,152900,210100,210200,210300,210400,210500,210600,210700,210800,210900,211000,211100,211200,211300,211400,220100,220200,220300,220400,220500,220600,220700,220800,222400,230100,230200,230300,230400,230500,230600,230700,230800,230900,231000,231100,231200,232700,310000,320100,320200,320300,320400,320500,320600,320700,320800,320900,321000,321100,321200,321300,330100,330200,330300,330400,330500,330600,330700,330800,330900,331000,331100,340100,340200,340300,340400,340500,340600,340700,340800,341000,341100,341200,341300,341500,341600,341700,341800,350100,350200,350300,350400,350500,350600,350700,350800,350900,360100,360200,360300,360400,360500,360600,360700,360800,360900,361000,361100,370100,370200,370300,370400,370500,370600,370700,370800,370900,371000,371100,371300,371400,371500,371600,371700,410100,410200,410300,410400,410500,410600,410700,410800,410900,411000,411100,411200,411300,411400,411500,411600,411700,419001,420100,420200,420300,420500,420600,420700,420800,420900,421000,421100,421200,421300,422800,429004,429005,429006,429021,430100,430200,430300,430400,430500,430600,430700,430800,430900,431000,431100,431200,431300,433100,440100,440200,440300,440400,440500,440600,440700,440800,440900,441200,441300,441400,441500,441600,441700,441800,441900,442000,445100,445200,445300,450100,450200,450300,450400,450500,450600,450700,450800,450900,451000,451100,451200,451300,451400,460100,460200,460300,460400,469001,469002,469005,469006,469007,469021,469022,469023,469024,469025,469026,469027,469028,469029,469030,500000,500200,510100,510300,510400,510500,510600,510700,510800,510900,511000,511100,511300,511400,511500,511600,511700,511800,511900,512000,513200,513300,513400,520100,520200,520300,520400,520500,520600,522300,522600,522700,530100,530300,530400,530500,530600,530700,530800,530900,532300,532500,532600,532800,532900,533100,533300,533400,540100,540200,540300,540400,540500,540600,542500,610100,610200,610300,610400,610500,610600,610700,610800,610900,611000,620100,620200,620300,620400,620500,620600,620700,620800,620900,621000,621100,621200,622900,623000,630100,630200,632200,632300,632500,632600,632700,632800,640100,640200,640300,640400,640500,650100,650200,650400,650500,652300,652700,652800,652900,653000,653100,653200,654000,654200,654300,659001,659002,659003,659004,659005,659006,659007,659008,659009,659010,659011,659012";
        String code = "150700,150800,150900";
        List<String> regionCodes = Arrays.asList(code.split(","));
        long start = System.currentTimeMillis();
        long totalUpdated = 0L;
        int total = regionCodes.size();

        final String sql =
                "UPDATE resident_map_cell_resource " +
                        "SET grid_geom_0 = ST_SRID(ST_PolygonFromText(CONCAT(" +
                        " 'POLYGON((', lower_left_lng, ' ', lower_left_lat, ',', " +
                        " lower_right_lng, ' ', lower_right_lat, ',', " +
                        " up_right_lng, ' ', up_right_lat, ',', " +
                        " up_left_lng, ' ', up_left_lat, ',', " +
                        " lower_left_lng, ' ', lower_left_lat, '))')), 0) " +
                        "WHERE city_code = ?";

        try (Connection conn = DriverManager.getConnection(DB_URL, USER, PASSWORD);
             // 调大会话层超时，避免长事务被断开（服务端无法“无限”，设比较大即可）
             PreparedStatement ps = conn.prepareStatement(sql)) {
            conn.setAutoCommit(false);
            ps.setQueryTimeout(1200); // 10 分钟

            for (int i = 0; i < total; i++) {
                String regionCode = regionCodes.get(i);
                long cityStart = System.currentTimeMillis();
                log.info("[{}/{} {:.1f}%] 开始处理 city_code={}", (i + 1), total, ((i + 1) * 100.0 / total), regionCode);

                ps.setString(1, regionCode);
                int affected = ps.executeUpdate();
                conn.commit();

                totalUpdated += affected;
                long cityCost = System.currentTimeMillis() - cityStart;
                log.info("[{}/{}] 完成 city_code={}, 本城更新 {} 行, 总累计 {} 行, 耗时 {} ms",
                        (i + 1), total, regionCode, affected, totalUpdated, cityCost);
            }
        } catch (SQLException e) {
            log.error("数据库操作失败：{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }

        long cost = System.currentTimeMillis() - start;
        log.info("全部完成：共处理城市 {} 个，总更新 {} 行，总耗时 {} ms", total, totalUpdated, cost);
    }

}
