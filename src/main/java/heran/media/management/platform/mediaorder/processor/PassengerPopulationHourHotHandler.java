package heran.media.management.platform.mediaorder.processor;

import com.fasterxml.jackson.databind.JsonNode;
import heran.media.sharelib.client.CustomizationMapClient;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.bo.LocType;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.bo.TimeType;
import heran.media.sharelib.domain.db.mapper.main.GridHotDataMapper;
import heran.media.sharelib.domain.db.model.main.GridHotData;
import heran.media.sharelib.domain.dto.amap.request.MonthlyFlowHeatMapRequest;
import heran.media.sharelib.domain.dto.amap.request.SendMessagesContext;
import heran.media.sharelib.domain.dto.amap.response.HeatMapResponse;
import heran.media.sharelib.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PassengerPopulationHourHotHandler extends AbstractHotAdCodeProcessor {

    @Resource
    private GridHotDataMapper gridHotDataMapper;
    @Resource
    private CustomizationMapClient customizationMapClient;

    @Override
    public void handle(HotContext context) {
        Integer hotDataCount = gridHotDataMapper.getGridHotDataTimeTypeCount(context.getRequest().getStatMonth(),
                context.getTypeInfo(),
                context.getType(),
                getStatisticalType().name()
                , TimeType.HOUR.getCode());
        if (hotDataCount > 0) {
            return;
        }
        MonthlyFlowHeatMapRequest residentHeatMapRequest = createResidentHeatMapRequest(context, TimeType.HOUR.getCode());
        SendMessagesContext messagesContext = SendMessagesContext.builder().userId(context.getUserKey()).locType(getStatisticalType().name()).typeInfo(context.getTypeInfo()).build();
        HeatMapResponse heatMapResponse = customizationMapClient.monthlyFlowHeatMap(residentHeatMapRequest, messagesContext);
        processResidentHeatMapHourResponse(context, heatMapResponse);
    }


    protected MonthlyFlowHeatMapRequest createResidentHeatMapRequest(HotContext context, String timeType) {
        MonthlyFlowHeatMapRequest monthlyFlowHeatMapRequest = new MonthlyFlowHeatMapRequest();
        monthlyFlowHeatMapRequest.setType("3");
        monthlyFlowHeatMapRequest.setTypeInfo(context.getTypeInfo());
        monthlyFlowHeatMapRequest.setLocType(LocType.ALL_DAY.getCode());
        monthlyFlowHeatMapRequest.setMonth(context.getRequest().getStatMonth());
        monthlyFlowHeatMapRequest.setScale(1000);
        monthlyFlowHeatMapRequest.setTimeType(timeType);
        return monthlyFlowHeatMapRequest;
    }

    public void processResidentHeatMapHourResponse(HotContext context, HeatMapResponse heatMapResponse) {
        if (heatMapResponse == null || heatMapResponse.getCode() != 0 || heatMapResponse.getData() == null || heatMapResponse.getData().getQuery() == null || heatMapResponse.getData().getQuery().getIndexData() == null) {
            log.error("处理响应数据失败：响应数据为空");
            return;
        }
        HeatMapResponse.IndexData indexData = heatMapResponse.getData().getQuery().getIndexData();
        JsonNode dataHot = indexData.getDataHot();
        if (!dataHot.isObject()) {
            log.error("请求热力数据类型转换错误小时级别 indexData:{}", indexData);
            return;
        }
        Iterator<Map.Entry<String, JsonNode>> fields = dataHot.fields();
        List<GridHotData> gridHotData = new ArrayList<>();
        AuthUserInfo userInfo = AuthUtils.getUserInfo();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            // 获取小时数（如 "0", "1", "2", "3"）
            String hour = entry.getKey();
            // 获取对应小时的数据
            JsonNode hourData = entry.getValue();
            // 处理该小时的数据
            hourData.fields().forEachRemaining(locationEntry -> {
                try {
                    String[] index = locationEntry.getKey().split(",");
                    JsonNode value = locationEntry.getValue();
                    if (index.length >= 2) {
                        GridHotData gridHotDataHour = createGridHotData(context, index[0], index[1], value.asText(), Integer.valueOf(hour));
                        gridHotData.add(gridHotDataHour);
                    }
                } catch (Exception e) {
                    log.error("处理小时级热力数据失败: hour={}, hourData={}, error={}",
                            hour, hourData, e.getMessage());
                }
            });
        }
        if (CollectionUtils.isNotEmpty(gridHotData)) {
            int batchSize = 5000;
            int total = gridHotData.size();
            for (int i = 0; i < total; i += batchSize) {
                int end = Math.min(i + batchSize, total);
                List<GridHotData> batchList = gridHotData.subList(i, end);
                gridHotDataMapper.batchInsertIgnore(batchList);
            }
        }

    }


    @Override
    public StatisticalType getStatisticalType() {
        return StatisticalType.PASSENGER_HOUR_POPULATION;
    }


}
