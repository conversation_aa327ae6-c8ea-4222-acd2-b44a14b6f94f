package heran.media.management.platform.mediaorder.processor;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class LockDataKpiTypeProcessorFactory {
    private static final Map<String, LockDataKpiTypeProcessor> LOCK_PROCESSOR_MAP = new HashMap<>();

    public LockDataKpiTypeProcessorFactory(LockDataKpiTypeProcessor... processors) {
        for (LockDataKpiTypeProcessor lockDataKpiTypeProcessor : processors) {
            LOCK_PROCESSOR_MAP.put(lockDataKpiTypeProcessor.getKpiType(), lockDataKpiTypeProcessor);
        }
    }

    public LockDataKpiTypeProcessor getProcessor(String type) {
        return LOCK_PROCESSOR_MAP.get(type);
    }

    public void execute(String type, LockDataContext lockDataContext) {
        this.getProcessor(type).executeKpi(lockDataContext);
    }
}
