package heran.media.management.platform.mediaorder.processor;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import heran.media.management.platform.common.error.MapDataException;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.main.util.LngLatKeyUtils;
import heran.media.management.platform.mediaorder.error.LockOrderPrepareDataException;
import heran.media.management.platform.mediaorder.resolver.LockDataCalculateTypeResolver;
import heran.media.management.platform.mediaorder.resolver.LockDataCalculateTypeResolverFactory;
import heran.media.management.platform.mediaorder.service.MediaOrderService;
import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderEntityQueryMapper;
import heran.media.management.platform.mediaorder.sudomain.dto.CellMaxHotValueWktData;
import heran.media.management.platform.mediaorder.sudomain.dto.MergedResidentMapCellData;
import heran.media.management.platform.mediaorder.sudomain.dto.MergedResidentMapCellIdData;
import heran.media.management.platform.mediaorder.sudomain.dto.ResidentMapCoverageCellData;
import heran.media.management.platform.mediaorder.sudomain.request.LockDataRequest;
import heran.media.management.platform.mediaorder.sudomain.response.SelectBaseDataResponse;
import heran.media.management.platform.mediaorder.sudomain.response.SelectBaseResponse;
import heran.media.management.platform.system.subdomain.ds.AuthorityBizMapper;
import heran.media.sharelib.client.CustomizationMapClient;
import heran.media.sharelib.domain.bo.*;
import heran.media.sharelib.domain.db.mapper.main.*;
import heran.media.sharelib.domain.db.model.ManagementSysRoleAuth;
import heran.media.sharelib.domain.db.model.main.CellHotValueWktConfig;
import heran.media.sharelib.domain.db.model.main.GridHotData;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import heran.media.sharelib.domain.db.model.main.TaGridTaskHeatData;
import heran.media.sharelib.domain.dto.amap.ThroughClearlyQuery;
import heran.media.sharelib.domain.dto.amap.request.PassengerFlowThroughClearlyRequest;
import heran.media.sharelib.domain.dto.amap.request.SendMessagesContext;
import heran.media.sharelib.domain.dto.amap.request.ThroughClearlyRequest;
import heran.media.sharelib.domain.dto.amap.response.ThroughClearlyResponse;
import heran.media.sharelib.domain.dto.hot.GridHotDto;
import heran.media.sharelib.domain.dto.hot.MainCalendarPlacementData;
import heran.media.sharelib.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 锁定计算数据处理器基类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseLockCalculateDataProcessor extends BaseCallServiceImpl implements LockCalculateDataProcessor {

    protected static final Integer PAGE_SIZE = 500;
    protected static final int VARIATION_PRECISION = 4;
    protected static final String RESIDENT = "RESIDENT";
    protected static final String PASSENGER = "PASSENGER";

    @Resource
    protected MediaOrderEntityQueryMapper mediaOrderEntityQueryMapper;
    @Resource
    protected MainCalendarPlacementMapper mainCalendarPlacementMapper;
    @Resource
    protected GridHotDataMapper gridHotDataMapper;
    @Resource
    protected CustomizationMapClient customizationMapClient;
    @Resource
    protected MainCalendarOrderKpiMapper mainCalendarOrderKpiMapper;
    @Resource
    protected CellHotValueWktConfigMapper cellHotValueWktConfigMapper;
    @Resource
    protected TaGridTaskHeatDataMapper taGridTaskHeatDataMapper;
    @Resource
    protected LockDataCalculateTypeResolverFactory lockDataCalculateTypeResolverFactory;
    @Resource
    protected MediaOrderService mediaOrderService;
    @Resource
    private AuthorityBizMapper authorityBizMapper;

    @Override
    public void execute(LockDataContext context) {
        long startTime = System.currentTimeMillis();
        Integer orderId = context.getRequest().getOrderId();

        try {
            log.info("开始处理{}计算: orderId={}", getProcessorName(), orderId);

            List<Integer> selectionPlanId = mediaOrderEntityQueryMapper.getSelectionPlanId(orderId);
            if (CollectionUtils.isEmpty(selectionPlanId)) {
                log.warn("订单未找到绑定的方案: orderId={}", orderId);
                return;
            }

            validate(context, selectionPlanId);

            processByPage(context, selectionPlanId);

            long duration = System.currentTimeMillis() - startTime;
            log.info("{}计算完成: orderId={}, 耗时={}ms", getProcessorName(), orderId, duration);
        } catch (Exception e) {
            log.error("计算失败: orderId={}", orderId);
            throw e;
        } finally {
            //删除热力指数配置缓存数据
            deleteCache();
        }
    }

    @Override
    public void validate(LockDataContext context, List<Integer> planIds) {
        //校验是否符合锁定的要求 获取到这个订单绑定的点位下的能够计算的城市数据
        LockDataRequest request = context.getRequest();
        List<String> regionCodeList = mainCalendarPlacementMapper.getRegionCodeByPlanIds(request.getCategoryId(), planIds);
        if (CollectionUtils.isEmpty(regionCodeList)) {
            log.info("当前锁定分类没有点位城市相关信息");
            return;
        }
        //热力数据校验
        List<SelectBaseResponse> list = mediaOrderService.baseSelectBaseData(request.getStatMonth(), regionCodeList, request.getPackId());
        if (CollectionUtils.isEmpty(list)) {
            log.error("锁定的数据 没有相关的热力数据");
            throw new LockOrderPrepareDataException();
        }

        for (SelectBaseResponse response : list) {
            List<SelectBaseDataResponse> data = response.getData();
            for (SelectBaseDataResponse dataResponse : data) {
                //常驻的只有选择常驻的痩还会校验
                if (RESIDENT.equals(request.getType())) {
                    if (dataResponse.getResidentPopulationHot() == null || !dataResponse.getResidentPopulationHot()) {
                        log.error("没有常驻热力值相关配置");
                        throw new LockOrderPrepareDataException();
                    }
                    if (dataResponse.getResidentPopulationTa() == null || !dataResponse.getResidentPopulationTa()) {
                        log.error("没有城市常驻人口相关配置");
                        throw new LockOrderPrepareDataException();
                    }
                    if (dataResponse.getResidentPopulationTaHot() == null || !dataResponse.getResidentPopulationTaHot()) {
                        log.error("没有城市TA常驻人口热力相关配置");
                        throw new LockOrderPrepareDataException();
                    }
                } else {
                    if (dataResponse.getPassengerPopulationTa() == null || !dataResponse.getPassengerPopulationTa()) {
                        log.error("没有城市客流人口相关配置");
                        throw new LockOrderPrepareDataException();
                    }
                    if (dataResponse.getPassengerPopulationTaHot() == null || !dataResponse.getPassengerPopulationTaHot()) {
                        log.error("没有城市客流人口热力相关配置");
                        throw new LockOrderPrepareDataException();
                    }
                }
                boolean allTrue = Stream.of(
                        dataResponse.getDwellPopulationHot(),
                        dataResponse.getWorkPopulationHot(),
                        dataResponse.getPassengerPopulationHot(),
                        dataResponse.getPassengerHourPopulationHot()
                ).allMatch(Boolean.TRUE::equals);
                if (!allTrue) {
                    log.error("居住 工作 客流 客流小时级 有缺少配置");
                    throw new LockOrderPrepareDataException();
                }
            }
        }

        //处理网格人口数校验
        List<CellHotValueWktConfig> wktConfigList = cellHotValueWktConfigMapper.getCellHotValueWktConfigListByArrays(request.getStatMonth(), regionCodeList);
        if (CollectionUtils.isEmpty(wktConfigList)) {
            log.error("基础网格人口是空的");
            throw new LockOrderPrepareDataException();
        }
        //权限数据
        List<ManagementSysRoleAuth> managementSysMenusDefined = authorityBizMapper.getManagementSysRoleMenuAuthByAccount(context.getUserKey());

        Set<String> menuIdSet = managementSysMenusDefined.stream()
                .map(ManagementSysRoleAuth::getAuthedData)
                .filter(StringUtils::isNotEmpty)
                .flatMap(data -> Arrays.stream(data.split("\\|\\|")))
                .collect(Collectors.toSet());

        Map<String, List<CellHotValueWktConfig>> wktConfig = wktConfigList.stream()
                .collect(Collectors.groupingBy(
                        c -> c.getRegionCode() + "_" + c.getStatMonth()
                ));

        for (String regionCode : regionCodeList) {
            for (String statMonth : request.getStatMonth()) {
                String key = regionCode + "_" + statMonth;
                List<CellHotValueWktConfig> configList = wktConfig.get(key);

                Map<String, CellHotValueWktConfig> configMap = Optional.ofNullable(configList)
                        .orElseGet(Collections::emptyList)
                        .stream()
                        .collect(Collectors.toMap(
                                CellHotValueWktConfig::getStatisticalType,
                                Function.identity(),
                                (existing, replacement) -> existing
                        ));

                //要校验有没有权限能生成网格人口 有权限生成则继续走没有权限生成就跑出异常 走到这一步热力值一定会有的
                for (CellWktConfigRoleType type : CellWktConfigRoleType.values()) {
                    //常驻不需要
                    if (PASSENGER.equals(request.getType()) && type.name().equals(CellWktConfigRoleType.RESIDENT_POPULATION.name())) {
                        continue;
                    }
                    CellHotValueWktConfig config = configMap.get(type.name());
                    if (config == null) {
                        String keyToCheck = "ORDER".equals(request.getKpiType())
                                ? type.getOrderCode()
                                : type.getEffectCode();

                        if (!menuIdSet.contains(keyToCheck)) {
                            log.error("基础网格人口没想相关配置并且没有权限获取: key{},type:{}", key, type.name());
                            throw new LockOrderPrepareDataException();
                        }
                    }
                }
            }
        }

        List<CellHotValueWktConfig> taWktConfig = cellHotValueWktConfigMapper.getTaWktConfig(request.getPackId(), regionCodeList, request.getStatMonth());
        if (CollectionUtils.isEmpty(taWktConfig)) {
            log.error("TA 网格人口配置 是空的");
            throw new LockOrderPrepareDataException();
        }

        Map<String, List<CellHotValueWktConfig>> taWktConfigList = taWktConfig.stream()
                .collect(Collectors.groupingBy(
                        c -> c.getRegionCode() + "_" + c.getStatMonth()
                ));

        for (String regionCode : regionCodeList) {
            for (String statMonth : request.getStatMonth()) {
                String key = regionCode + "_" + statMonth;
                List<CellHotValueWktConfig> configList = taWktConfigList.get(key);
                if (CollectionUtils.isEmpty(configList)) {
                    log.error("TA 网格人口配置 是空的 key：{}", key);
                    throw new LockOrderPrepareDataException();
                }

                Set<String> types = configList.stream()
                        .map(CellHotValueWktConfig::getStatisticalType)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                if (RESIDENT.equals(request.getType()) && !types.contains(TaStatisticalType.TA_RESIDENT_POPULATION.name())) {
                    log.error("TA 常驻网格人口 缺少配置：{}", TaStatisticalType.TA_RESIDENT_POPULATION.name());
                    throw new LockOrderPrepareDataException();
                }
                if (PASSENGER.equals(request.getType()) && !types.contains(TaStatisticalType.TA_PASSENGER_POPULATION.name())) {
                    log.error("TA 客流网格人口 缺少配置：{}", TaStatisticalType.TA_PASSENGER_POPULATION.name());
                    throw new LockOrderPrepareDataException();
                }
            }
        }
    }

    /**
     * 分页处理
     */
    protected void processByPage(LockDataContext context, List<Integer> selectionPlanId) {
        int pageNo = 1;
        int totalProcessed = 0;

        while (true) {
            PageHelper.startPage(pageNo, PAGE_SIZE, false);
            List<MainCalendarPlacementData> placementList = mainCalendarPlacementMapper
                    .getMainCalendarPlacementByPlanIds(context.getRequest().getCategoryId(), selectionPlanId);

            if (CollectionUtils.isEmpty(placementList)) {
                break;
            }

            try {
                processPlacementBatch(placementList, context);
                totalProcessed += placementList.size();
                log.debug("已处理 {} 个点位", totalProcessed);

            } catch (Exception e) {
                log.error("处理点位批次失败: pageNo={}, error={}", pageNo, e.getMessage(), e);
            }
            pageNo++;
        }
    }

    /**
     * 处理点位批次 - 子类实现
     */
    protected abstract void processPlacementBatch(List<MainCalendarPlacementData> placementList, LockDataContext context);

    /**
     * 获取处理器名称
     */
    protected abstract String getProcessorName();


    private final Map<String, List<CellMaxHotValueWktData>> monthlyHotDataCache = new HashMap<>();
    private final Map<String, List<CellMaxHotValueWktData>> monthlyTaHotDataCache = new HashMap<>();

    protected void deleteCache() {
        monthlyHotDataCache.clear();
        monthlyTaHotDataCache.clear();
    }


    /**
     * 获取热力值数据列表 - 通用方法
     */
    protected List<CellMaxHotValueWktData> getHotValueWktDataList(LockDataContext context, String dataType, Integer dataId, String statMonth, String regionCode, boolean isTa) {
        String cacheKey = String.format("%s_%s_%s_%s", context.getRequest().getOrderId(), statMonth, regionCode, isTa ? "TA" : "NORMAL");

        // 选择对应的缓存
        Map<String, List<CellMaxHotValueWktData>> cache = isTa ? monthlyTaHotDataCache : monthlyHotDataCache;

        // 先从内存中取
        List<CellMaxHotValueWktData> cellMaxHotValueWktData = cache.get(cacheKey);
        if (CollectionUtils.isNotEmpty(cellMaxHotValueWktData)) {
            return cellMaxHotValueWktData;
        }

        Map<String, CellHotValueWktConfig> configMap = getCellHotValueWktConfigMap(statMonth, regionCode, dataType, dataId);
        List<CellMaxHotValueWktData> dataList;

        if (isTa) {
            // TA数据处理
            dataList = processTaHotValueData(configMap);
        } else {
            // 普通数据处理
            dataList = processNormalHotValueData(context, statMonth, regionCode, configMap, dataId, dataType);
        }

        // 存入缓存
        cache.put(cacheKey, dataList);
        return dataList;
    }

    /**
     * 处理普通热力值数据
     */
    private List<CellMaxHotValueWktData> processNormalHotValueData(LockDataContext context, String statMonth,
                                                                   String regionCode, Map<String, CellHotValueWktConfig> configMap,
                                                                   Integer dataId, String dataType) {
        List<CellMaxHotValueWktData> dataList = new ArrayList<>();

        //获取到这几个类型中热力值数为100的数据
        List<String> list = Arrays.stream(StatisticalType.values()).map(StatisticalType::name).collect(Collectors.toList());
        List<GridHotData> hotValue = gridHotDataMapper.getGridHotDataMaxHotValue(statMonth, regionCode, list);

        Map<String, GridHotData> groupedMap = hotValue.stream()
                .collect(Collectors.toMap(
                        GridHotData::getStatisticalType,
                        Function.identity()
                ));

        for (StatisticalType type : StatisticalType.values()) {
            GridHotData hotData = groupedMap.get(type.name());
            CellMaxHotValueWktData data = processHotValueData(context, statMonth, regionCode,
                    configMap, type.name(), type.getCode(), hotData, dataId, dataType);
            dataList.add(data);
        }

        return dataList;
    }

    /**
     * 处理TA热力值数据
     */
    private List<CellMaxHotValueWktData> processTaHotValueData(Map<String, CellHotValueWktConfig> configMap) {
        List<CellMaxHotValueWktData> dataList = new ArrayList<>();

        for (TaStatisticalType type : TaStatisticalType.values()) {

            CellHotValueWktConfig config = configMap.get(type.name());
            if (config != null) {
                CellMaxHotValueWktData build = CellMaxHotValueWktData.builder()
                        .uv(config.getMaxUv())
                        .variation(config.getVariation())
                        .statisticalType(type.name())
                        .build();
                dataList.add(build);
                continue;
            }

            CellMaxHotValueWktData build = CellMaxHotValueWktData.builder()
                    .uv(0)
                    .variation(0.0)
                    .statisticalType(type.name())
                    .build();
            dataList.add(build);
        }

        return dataList;
    }
//    private List<CellMaxHotValueWktData> processTaHotValueData(Integer taskId, LockDataContext context, String statMonth,
//                                                               String regionCode, Map<String, CellHotValueWktConfig> configMap,
//                                                               String dataType) {
//        List<CellMaxHotValueWktData> dataList = new ArrayList<>();
//
//        for (TaStatisticalType type : TaStatisticalType.values()) {
//            if (taskId == null) {
//                CellMaxHotValueWktData data = CellMaxHotValueWktData.builder().uv(0).variation(0.0).statisticalType(type.name()).build();
//                dataList.add(data);
//                continue;
//            }
//
//            TaGridTaskHeatData taGridTaskHeatData = taGridTaskHeatDataMapper.getTaGridTaskHeatData(statMonth, regionCode, type.name(), taskId);
//
//            CellMaxHotValueWktData data = processHotValueData(context, statMonth, regionCode, configMap,
//                    type.name(), type.getLabType(), taGridTaskHeatData, taskId, dataType);
//            dataList.add(data);
//        }
//
//        return dataList;
//    }

    /**
     * 处理单个热力值数据 - 通用方法
     */
    private CellMaxHotValueWktData processHotValueData(LockDataContext context, String statMonth, String regionCode,
                                                       Map<String, CellHotValueWktConfig> configMap, String statisticalType,
                                                       String labType, Object hotData, Integer dataId, String dataType) {
        // 检查配置缓存
        CellHotValueWktConfig config = configMap.get(statisticalType);

        if (config != null) {
            return CellMaxHotValueWktData.builder()
                    .uv(config.getMaxUv())
                    .variation(config.getVariation())
                    .statisticalType(statisticalType)
                    .build();
        }

        // 获取热力数据
        //Object hotData = getHotData(statMonth, regionCode, statisticalType, taskId);
        if (hotData == null) {
            return CellMaxHotValueWktData.builder()
                    .uv(0)
                    .variation(0.0)
                    .statisticalType(statisticalType)
                    .build();
        }

        // 提取经纬度
        String longitude = extractLongitude(hotData);
        String latitude = extractLatitude(hotData);

        // 调用API获取数据
        return getMapCellMaxHotValueWktData(longitude, latitude, context, statMonth, regionCode, statisticalType, labType, dataId, dataType);
    }

    /**
     * 获取热力数据
     */
    private Object getHotData(String statMonth, String regionCode, String statisticalType, Integer taskId) {
        if (taskId != null) {
            // TA数据
            return taGridTaskHeatDataMapper.getTaGridTaskHeatData(statMonth, regionCode, statisticalType, taskId);
        } else {
            // 普通数据
            return gridHotDataMapper.getGridHotDataMaxHotValue(statMonth, regionCode, Lists.newArrayList(statisticalType));
        }
    }

    /**
     * 提取经度
     */
    private String extractLongitude(Object hotData) {
        if (hotData instanceof GridHotData) {
            return ((GridHotData) hotData).getLongitude();
        } else if (hotData instanceof TaGridTaskHeatData) {
            return ((TaGridTaskHeatData) hotData).getCenterLng();
        }
        return null;
    }

    /**
     * 提取纬度
     */
    private String extractLatitude(Object hotData) {
        if (hotData instanceof GridHotData) {
            return ((GridHotData) hotData).getLatitude();
        } else if (hotData instanceof TaGridTaskHeatData) {
            return ((TaGridTaskHeatData) hotData).getCenterLat();
        }
        return null;
    }

    /**
     * 获取普通热力值数据列表 - 保持向后兼容
     */
    protected List<CellMaxHotValueWktData> getCellMaxHotValueWktDataList(LockDataContext context, String statMonth, String regionCode) {
        return getHotValueWktDataList(context, "BASE", -1, statMonth, regionCode, false);
    }

    /**
     * 获取TA热力值数据列表 - 保持向后兼容
     */
    protected List<CellMaxHotValueWktData> getTaCellMaxHotValueWktDataList(LockDataContext context, String statMonth, String regionCode) {
        String type = context.getRequest().getType();
        //获取到当前请求计算数据类型
        String taPopulation = LockDataType.RESIDENT.name().equals(type) ? TaStatisticalType.TA_RESIDENT_POPULATION.name() : TaStatisticalType.TA_PASSENGER_POPULATION.name();
        //获取到任务id
        Integer taskId = mediaOrderEntityQueryMapper.getTaskId(context.getRequest().getPackId(), regionCode, statMonth, taPopulation);
        context.setTaskId(taskId);
        return getHotValueWktDataList(context, "TASK", taskId, statMonth, regionCode, true);
    }


    protected Map<String, CellHotValueWktConfig> getCellHotValueWktConfigMap(String statMonth, String regionCode, String dataType, Integer dataId) {
        if (dataId == null) {
            return null;
        }
        //根据类型 任务id 月份  城市code 去获取到数据
        List<CellHotValueWktConfig> configList = cellHotValueWktConfigMapper.getCellHotValueWktConfigList(statMonth, regionCode, dataType, dataId);
        Map<String, CellHotValueWktConfig> configMap = new HashMap<>(8);
        if (CollectionUtils.isNotEmpty(configList)) {
            configMap = configList.stream()
                    .collect(Collectors.toMap(
                            CellHotValueWktConfig::getStatisticalType,
                            Function.identity()
                    ));
        }
        return configMap;
    }

    /**
     * 调用高德API 获取到UV人数
     *
     * @param longitude       经度
     * @param latitude        纬度
     * @param context         上下文
     * @param statMonth       月份
     * @param regionCode      城市
     * @param statisticalType 人口口径
     * @param labType         labType
     * @return CellMaxHotValueWktData
     */
    protected CellMaxHotValueWktData getMapCellMaxHotValueWktData(String longitude, String latitude, LockDataContext context,
                                                                  String statMonth, String regionCode,
                                                                  String statisticalType, String labType,
                                                                  Integer dataId, String dataType) {
        // 获取网格数据并调用API
        MergedResidentMapCellData cellData = mediaOrderEntityQueryMapper.getMergedResidentMapCellData(longitude, latitude);
        if (!isValidCellData(cellData)) {
            buildConfig(context, 0, statMonth, regionCode, new BigDecimal(0), dataId, dataType, statisticalType, -1);
            return CellMaxHotValueWktData.builder().uv(0).variation(0.0).statisticalType(statisticalType).build();
        }

        ThroughClearlyResponse response = null;

        //客流跟其他请求的方式不同
        try {
            if (statisticalType.equals(StatisticalType.PASSENGER_POPULATION.name())
                    || statisticalType.equals(StatisticalType.PASSENGER_HOUR_POPULATION.name()) ||
                    statisticalType.equals(TaStatisticalType.TA_PASSENGER_POPULATION.name())) {
                response = callPassengerMapApi(context, statMonth, labType, cellData, statisticalType);
            } else {
                response = callMapApi(context, statMonth, labType, cellData, statisticalType);
            }
            //小时特别处理
            if (statisticalType.equals(StatisticalType.PASSENGER_HOUR_POPULATION.name())) {
                return passengerHour(context, response, statMonth, regionCode, cellData.getPeopleCount(), dataId, dataType, statisticalType);
            }
        } catch (Exception e) {
            throw new MapDataException();
        }

        Integer uv = response != null && response.getData() != null && response.getData().getQuery() != null
                ? response.getData().getQuery().getUv() : null;

        BigDecimal bigDecimal = new BigDecimal(uv == null ? 0 : uv);
        BigDecimal variation = bigDecimal.divide(new BigDecimal(cellData.getPeopleCount()), VARIATION_PRECISION, RoundingMode.HALF_UP);

        buildConfig(context, uv, statMonth, regionCode, variation, dataId, dataType, statisticalType, -1);

        return CellMaxHotValueWktData.builder()
                .uv(uv)
                .timeIndex(-1)
                .variation(variation.doubleValue())
                .statisticalType(statisticalType)
                .build();
    }


    protected CellMaxHotValueWktData passengerHour(LockDataContext context, ThroughClearlyResponse response,
                                                   String statMonth, String regionCode, Integer peopleCount,
                                                   Integer dataId, String dataType, String statisticalType) {
        ThroughClearlyQuery query = response.getData().getQuery();
        Map<String, Map<String, String>> dimensions = query.getDimensions();

        List<CellHotValueWktConfig> cellMaxHotValueWktData = new ArrayList<>();

        for (Map.Entry<String, Map<String, String>> outerEntry : dimensions.entrySet()) {
            String timeIndex = outerEntry.getKey();
            Map<String, String> innerMap = outerEntry.getValue();
            for (Map.Entry<String, String> innerEntry : innerMap.entrySet()) {
                String value = innerEntry.getValue();
                int uv = StringUtils.isNotEmpty(value) ? Integer.parseInt(value) : 0;
                BigDecimal variation = new BigDecimal(uv).divide(new BigDecimal(peopleCount), VARIATION_PRECISION, RoundingMode.HALF_UP);

                CellHotValueWktConfig config = buildCellHotValueWktConfig(context, uv, statMonth, regionCode, variation,
                        dataId, dataType, statisticalType, Integer.valueOf(timeIndex));
                cellMaxHotValueWktData.add(config);
            }
        }

        if (CollectionUtils.isEmpty(cellMaxHotValueWktData)) {
            return new CellMaxHotValueWktData();
        }

        CellHotValueWktConfig maxData = cellMaxHotValueWktData.stream()
                .max(Comparator.comparing(CellHotValueWktConfig::getMaxUv))
                .orElse(null);

        if (maxData == null) {
            return new CellMaxHotValueWktData();
        }

        cellHotValueWktConfigMapper.insertIgnoreEntity(maxData);

        return CellMaxHotValueWktData.builder()
                .uv(maxData.getMaxUv())
                .timeIndex(maxData.getTimeIndex())
                .variation(maxData.getVariation())
                .statisticalType(statisticalType)
                .build();
    }

    /**
     * 调用高德API
     */
    protected ThroughClearlyResponse callMapApi(LockDataContext context, String statMonth, String labType, MergedResidentMapCellData data, String statisticalType) {
        ThroughClearlyRequest request = new ThroughClearlyRequest();
        request.setMonth(statMonth);
        request.setProfile("uv");
        request.setType("4");
        request.setTypeInfo(buildTypeInfo(data));
        request.setTgi(false);
        request.setLabType(labType);

        SendMessagesContext sendContext = SendMessagesContext.builder()
                .userId(context.getUserKey())
                .typeInfo(request.getTypeInfo())
                .locType(statisticalType)
                .bizData(getBizData())
                .build();

        return customizationMapClient.residentThroughClearly(request, sendContext);
    }

    /**
     * 调用高德API 客流
     *
     * @param context   上下文
     * @param statMonth 月份
     * @param labType   类型
     * @param data      聚合数据
     * @return ThroughClearlyResponse
     */
    protected ThroughClearlyResponse callPassengerMapApi(LockDataContext context, String statMonth, String labType, MergedResidentMapCellData data, String statisticalType) {
        PassengerFlowThroughClearlyRequest request = new PassengerFlowThroughClearlyRequest();
        request.setType("4");
        request.setTypeInfo(buildTypeInfo(data));
        request.setProfile("uv");
        request.setMonth(statMonth);
        if (statisticalType.equals(StatisticalType.PASSENGER_HOUR_POPULATION.name())) {
            request.setTimeType(TimeType.HOUR.getCode());
        } else {
            request.setTimeType(TimeType.DAY.getCode());
        }
        request.setLocType(labType);

        SendMessagesContext sendContext = SendMessagesContext.builder()
                .userId(context.getUserKey())
                .typeInfo(request.getTypeInfo())
                .locType(statisticalType)
                .bizData(getBizData())
                .build();

        return customizationMapClient.passengerFlowThroughClearly(request, sendContext);
    }

    /**
     * 构建类型信息
     */
    protected String buildTypeInfo(MergedResidentMapCellData data) {
        return String.format("%s %s,%s %s,%s %s,%s %s,%s %s",
                data.getLowerLeftLng(), data.getLowerLeftLat(),
                data.getLowerRightLng(), data.getLowerRightLat(),
                data.getUpRightLng(), data.getUpRightLat(),
                data.getUpLeftLng(), data.getUpLeftLat(),
                data.getLowerLeftLng(), data.getLowerLeftLat());
    }

    /**
     * 验证网格数据
     */
    protected boolean isValidCellData(MergedResidentMapCellData data) {
        return data != null && data.getLowerLeftLng() != 0 && data.getLowerLeftLat() != 0
                && data.getLowerRightLng() != 0 && data.getLowerRightLat() != 0
                && data.getUpLeftLng() != 0 && data.getUpLeftLat() != 0
                && data.getUpRightLng() != 0 && data.getUpRightLat() != 0;
    }

    /**
     * 构建配置
     */
    protected void buildConfig(LockDataContext context, Integer uv, String statMonth,
                               String regionCode, BigDecimal variation, Integer dataId, String dataType, String statisticalType, Integer timeIndex) {
        CellHotValueWktConfig config = buildCellHotValueWktConfig(context, uv, statMonth, regionCode, variation, dataId, dataType, statisticalType, timeIndex);
        cellHotValueWktConfigMapper.insertIgnoreEntity(config);
    }

    protected CellHotValueWktConfig buildCellHotValueWktConfig(LockDataContext context, Integer uv, String statMonth,
                                                               String regionCode, BigDecimal variation, Integer dataId, String dataType, String statisticalType, Integer timeIndex) {
        CellHotValueWktConfig config = new CellHotValueWktConfig();
        config.setMaxUv(uv);
        config.setStatisticalType(statisticalType);
        config.setTimeIndex(timeIndex);
        config.setRegionCode(regionCode);
        config.setStatMonth(statMonth);
        config.setVariation(variation.doubleValue());
        config.setDataType(dataType);
        config.setDataId(dataId);
        setCreatorInfo(context.getUserKey(), config);
        setUpdaterInfo(context.getUserKey(), config);
        return config;
    }

    /**
     * 预加载热力数据
     *
     * @param context    上下文
     * @param statMonth  查询时间
     * @param regionCode 区域code
     * @return Map<String, CellMaxHotValueWktData>
     */
    protected Map<String, CellMaxHotValueWktData> preloadHotData(LockDataContext context, String statMonth, String regionCode) {
        //查询基础人口数据
        List<CellMaxHotValueWktData> list = getCellMaxHotValueWktDataList(context, statMonth, regionCode);

        //查询TA人口数据
        List<CellMaxHotValueWktData> dataList = getTaCellMaxHotValueWktDataList(context, statMonth, regionCode);

        if (CollectionUtils.isNotEmpty(dataList)) {
            list.addAll(dataList);
        }

        return list.stream()
                .collect(Collectors.toMap(
                        CellMaxHotValueWktData::getStatisticalType,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 初始化KPI对象
     */
    protected MainCalendarOrderKpi initKpi(Long placementId, String type, String statMonth, String population, LockDataContext context) {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        MainCalendarOrderKpi kpi = new MainCalendarOrderKpi();
        kpi.setOrderId(context.getRequest().getOrderId());
        kpi.setCalendarPlacementId(placementId);
        kpi.setKpiType(type);
        kpi.setStatMonth(statMonth);
        kpi.setPopulation(population);
        kpi.setAoiTaPopulation(population);
        kpi.setAoiResidentPopulation(0);
        kpi.setAoiDwellPopulation(0);
        kpi.setAoiWorkPopulation(0);
        kpi.setAoiPassengerPopulation(0);
        kpi.setAoiPassengerHourPopulation(0);
        kpi.setAoiTaCount(0);

        setCreatorInfo(currentUser.getIdentifier(), kpi);
        setUpdaterInfo(currentUser.getIdentifier(), kpi);

        return kpi;
    }


    /**
     * 统一的人口计算方法
     */
    protected void calculatePopulation(MainCalendarPlacementData placementData,
                                       Map<String, List<GridHotDto>> gridHotMap,
                                       Map<String, Map<String, List<GridHotDto>>> gridHotListMap,
                                       List<MergedResidentMapCellIdData> cellIdData,
                                       List<ResidentMapCoverageCellData> residentMapCoverageCellData,
                                       Map<String, CellMaxHotValueWktData> cellWktMap,
                                       Map<String, Map<String, CellMaxHotValueWktData>> monthlyHotDataMap,
                                       MainCalendarOrderKpi kpi,
                                       LockDataContext context,
                                       List<String> statMonths) {

        LockDataRequest request = context.getRequest();

        LockDataCalculateTypeResolver resolver = lockDataCalculateTypeResolverFactory.getResolver(request.getType());
        if (resolver == null) {
            log.error("没有获取到计算人口的适配器：{}", request.getType());
            return;
        }

        // 根据是否有多月数据决定调用哪个方法
        if (request.getRefinedCalculation()) {
            // 多月加权平均计算
            resolver.resolverWeightedAverage(gridHotListMap, cellIdData, monthlyHotDataMap, residentMapCoverageCellData, placementData, kpi, statMonths);
        } else {
            // 单月计算
            resolver.resolver(gridHotMap, cellIdData, residentMapCoverageCellData, placementData, cellWktMap, kpi);
        }
    }


    // 批量查1000*1000 网格
    protected Map<Long, List<MergedResidentMapCellIdData>> getMergedMap(String regionCode, List<Long> placementIdList) {
        List<MergedResidentMapCellIdData> allMerged = mediaOrderEntityQueryMapper.batchGetMergedResidentMapCellId(regionCode, placementIdList);
        return allMerged.stream().collect(Collectors.groupingBy(MergedResidentMapCellIdData::getPlacementId));
    }

    // 批量查100*100 网格
    protected Map<Long, List<ResidentMapCoverageCellData>> getCoverageMap(Set<Long> allMergedIds, List<Long> placementIdList) {
        List<ResidentMapCoverageCellData> allCoverage = mediaOrderEntityQueryMapper.batchSelectIntersectCountAndSum(new ArrayList<>(allMergedIds), placementIdList);
        return allCoverage.stream().collect(Collectors.groupingBy(ResidentMapCoverageCellData::getPlacementId));
    }

    /**
     * 生成热力key
     */
    protected Set<String> getHotKeys(Collection<MergedResidentMapCellIdData> allMerged) {
        return allMerged.stream().map(cell -> LngLatKeyUtils.normalizeLngLat(cell.getCenterLng(), cell.getCenterLat())).collect(Collectors.toSet());
    }


    protected List<MainCalendarOrderKpi> baseProcessPlacements(List<MainCalendarPlacementData> placementListData,
                                                               LockDataContext context,
                                                               List<String> statMonths,
                                                               Map<String, CellMaxHotValueWktData> cellWktMap,
                                                               Map<String, Map<String, CellMaxHotValueWktData>> monthlyHotDataMap,
                                                               String regionCode,
                                                               boolean isRefinedCalculation) {

        List<MainCalendarOrderKpi> kpis = new ArrayList<>();
        LockDataRequest request = context.getRequest();

        // 1. 收集所有点位id
        List<Long> placementIdList = placementListData.stream().map(MainCalendarPlacementData::getId).distinct().collect(Collectors.toList());

        // 2. 批量查1000*1000 网格 最好用二进制 创建个字段 建立空间索引
        long l = System.currentTimeMillis();
        Map<Long, List<MergedResidentMapCellIdData>> mergedMap = getMergedMap(regionCode, placementIdList);
        log.info("消耗时间:{}", System.currentTimeMillis() - l);

        // 3. 批量查100*100 网格 最好用二进制 创建个字段 建立空间索引
        long a = System.currentTimeMillis();
        Set<Long> allMergedIds = mergedMap.values().stream().flatMap(List::stream).map(MergedResidentMapCellIdData::getMergedId).collect(Collectors.toSet());
        Map<Long, List<ResidentMapCoverageCellData>> coverageMap = getCoverageMap(allMergedIds, placementIdList);
        long l2 = System.currentTimeMillis();
        log.info("allMergedIds消耗时间:{}", l2 - a);
        long l1 = System.currentTimeMillis();
        // 4. 批量获取到高德请求回来的热力指数用户计算当前月的uv
        Set<String> hotKeys = getHotKeys(mergedMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));

        Map<String, List<GridHotDto>> gridHotMap = new HashMap<>(8);
        Map<String, Map<String, List<GridHotDto>>> gridHotListMap = new HashMap<>(8);

        if (!isRefinedCalculation) {
            String statMonth = statMonths.get(0);
            long l3 = System.currentTimeMillis();
            List<GridHotDto> gridHotDtoList = mediaOrderEntityQueryMapper.batchGetGridHotDataByKeys(hotKeys, statMonth, regionCode);
            log.info("batchGetGridHotDataByKeys消耗时间:{}", System.currentTimeMillis() - l3);
            // 5. 处理TA的
            long l4 = System.currentTimeMillis();
            Integer taskId = context.getTaskId();
            if (taskId != null) {
                List<GridHotDto> taGridHotDataList = mediaOrderEntityQueryMapper.batchGetTaGridHotDataByKeys(hotKeys, taskId,
                        TaStatisticalType.getByCode(request.getType()).name(), statMonth, regionCode);
                if (CollectionUtils.isNotEmpty(taGridHotDataList)) {
                    gridHotDtoList.addAll(taGridHotDataList);
                }
            }
            gridHotMap = gridHotDtoList.stream().collect(Collectors.groupingBy(GridHotDto::getStatisticalType));
            log.info("batchGetTaGridHotDataByKeys:{}", System.currentTimeMillis() - l4);
        } else {
            List<GridHotDto> gridHotDtoList = mediaOrderEntityQueryMapper.batchGetGridHotDataByKeysByStatMonthList(hotKeys, statMonths, regionCode);
            String taPopulation = LockDataType.RESIDENT.name().equals(request.getType()) ? TaStatisticalType.TA_RESIDENT_POPULATION.name() : TaStatisticalType.TA_PASSENGER_POPULATION.name();
            //获取TA数据
            for (String statMonth : statMonths) {
                Integer taskId = mediaOrderEntityQueryMapper.getTaskId(request.getPackId(), regionCode, statMonth, taPopulation);
                if (taskId != null) {
                    List<GridHotDto> taGridHotDataList = mediaOrderEntityQueryMapper.batchGetTaGridHotDataByKeys(hotKeys, taskId, taPopulation, statMonth, regionCode);
                    if (CollectionUtils.isNotEmpty(taGridHotDataList)) {
                        gridHotDtoList.addAll(taGridHotDataList);
                    }
                }
            }

            gridHotListMap = gridHotDtoList.stream()
                    .collect(Collectors.groupingBy(
                            GridHotDto::getStatMonth,
                            Collectors.groupingBy(
                                    GridHotDto::getStatisticalType
                            )
                    ));
        }
        log.info("请求热力值消耗时间：{}", System.currentTimeMillis() - l1);

        for (MainCalendarPlacementData placementData : placementListData) {

            Long placementId = placementData.getId();

            //聚合1000*1000网格数据
            List<MergedResidentMapCellIdData> cellIdData = mergedMap.get(placementId);

            //100*100网格数据
            List<ResidentMapCoverageCellData> residentMapCoverageCellData = coverageMap.get(placementId);

            MainCalendarOrderKpi kpi = null;
            if (isRefinedCalculation) {
                // 计算加权平均人口数据
                kpi = initKpi(placementData.getId(), request.getKpiType(), new Gson().toJson(statMonths), request.getType(), context);
                calculatePopulation(placementData, null, gridHotListMap, cellIdData, residentMapCoverageCellData, null, monthlyHotDataMap, kpi, context, statMonths);
            } else {
                // 计算人口数据 判断是不是 全域效果评估
                String statMonth = "APPRAISAL".equals(request.getKpiType()) ? new Gson().toJson(statMonths) : statMonths.get(0);
                kpi = initKpi(placementData.getId(), request.getKpiType(), statMonth, request.getType(), context);
                calculatePopulation(placementData, gridHotMap, null, cellIdData, residentMapCoverageCellData, cellWktMap, null, kpi, context, context.getRequest().getStatMonth());
            }
            kpis.add(kpi);
        }

        log.info("循环完消耗时间:{}", System.currentTimeMillis() - l1);

        return kpis;
    }

    /**
     * 获取业务数据标识
     */
    protected abstract String getBizData();
}