package heran.media.management.platform.mediaorder.template;

import heran.media.management.platform.mediaorder.sudomain.request.ComputationalDataRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ComputationalDataContext {
    private ComputationalDataRequest request;
    private String dataType;
    /**
     * 是否可计算天级时间占比
     */
    private String visibleAngleRatio;
}
