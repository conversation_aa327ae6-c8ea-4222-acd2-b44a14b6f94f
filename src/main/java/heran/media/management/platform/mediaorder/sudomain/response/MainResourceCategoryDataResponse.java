package heran.media.management.platform.mediaorder.sudomain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MainResourceCategoryDataResponse {
    @ApiModelProperty("主键id")
    private Integer id;
    @ApiModelProperty("分类名称")
    private String categoryName;
    @ApiModelProperty("父类id")
    private Integer parentId;
    @ApiModelProperty("级别")
    private Integer level;
    @ApiModelProperty("是否是叶子节点")
    private Boolean isLeaf;
    @ApiModelProperty("分组")
    private String groupLabel;
    @ApiModelProperty("额外属性")
    private String ext;
    @ApiModelProperty("订单计算模版")
    private String calculate;
    @ApiModelProperty("是否支持天级时间占比计算")
    private String visibleAngleRatio;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("拼接二三级名称")
    private String categoryJointName;

    public MainResourceCategoryDataResponse(MainResourceCategoryData data) {
        this.id = data.getId();
        this.categoryName = data.getCategoryName();
        this.parentId = data.getParentId();
        this.level = data.getLevel();
        this.isLeaf = data.getIsLeaf();
        this.groupLabel = data.getGroupLabel();
        this.ext = data.getExt();
        this.calculate = data.getCalculate();
        this.visibleAngleRatio = data.getVisibleAngleRatio();
        this.remark = data.getRemark();
        this.categoryJointName = data.getCategoryJointName() + "/" + data.getCategoryName();
    }
}
