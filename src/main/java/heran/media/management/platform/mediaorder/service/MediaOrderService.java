package heran.media.management.platform.mediaorder.service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import heran.media.management.platform.common.build.PageResponseBuilder;
import heran.media.management.platform.common.domain.dto.MetaFieldInfo;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.common.service.CommonService;
import heran.media.management.platform.common.utils.MetaFieldUtils;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.management.platform.effectestimate.service.OrderEffectEstimateBaseService;
import heran.media.management.platform.main.subdomain.bo.XlsMainMediaTag;
import heran.media.management.platform.main.subdomain.ds.MainResourceCategoryEntityQueryMapper;
import heran.media.management.platform.main.subdomain.ds.OrderEffectEstimateBaseManLandEntityQueryMapper;
import heran.media.management.platform.main.subdomain.dto.TCategoryIds;
import heran.media.management.platform.mediaorder.processor.*;
import heran.media.management.platform.mediaorder.resolver.EffectCollectResolver;
import heran.media.management.platform.mediaorder.resolver.EffectCollectResolverFactory;
import heran.media.management.platform.mediaorder.sudomain.bo.*;
import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderEntityQueryMapper;
import heran.media.management.platform.mediaorder.sudomain.dto.*;
import heran.media.management.platform.mediaorder.sudomain.request.*;
import heran.media.management.platform.mediaorder.sudomain.response.*;
import heran.media.management.platform.mediaorder.template.ComputationalDataContext;
import heran.media.management.platform.mediaorder.template.ComputationalDataTemplate;
import heran.media.management.platform.mediaorder.template.ComputationalDataTemplateFactory;
import heran.media.management.platform.mediaresource.nationwidemedia.dto.MainAttachmentContent;
import heran.media.management.platform.system.subdomain.db.CommonMapper;
import heran.media.sharelib.client.AMapClient;
import heran.media.sharelib.client.CustomizationMapClient;
import heran.media.sharelib.domain.bo.*;
import heran.media.sharelib.domain.db.mapper.main.*;
import heran.media.sharelib.domain.db.model.MainDictRegionData;
import heran.media.sharelib.domain.db.model.main.*;
import heran.media.sharelib.utils.redis.RedisLock;
import heran.media.sharelib.utils.upload.AliYunOssRedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.PassiveExpiringMap;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static heran.media.management.platform.common.utils.MetaFieldUtils.convertToMetaFieldInfoList;
import static heran.media.management.platform.common.utils.xls.XLSWriter.getExportFileName;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MediaOrderService extends BaseCallServiceImpl {

    private final static int EXPORT_PAGE_LIMIT = 5000;
    private static int EXPORT_START_PAGE = 1;
    private static int EXPORT_LIMIT_LOOP_COUNT = 20;

    private static final String REDIS_REGION_CODE = "heran:regionCode";

    private static final String REDIS_MEDIUM_CATEGORY_ID = "heran:mediumCategoryId";
    private static final String MAN_LAND = "MAN_LAND";

    @Resource
    private MediaOrderMatchDetailService mediaOrderMatchDetailService;
    @Resource
    private MediaOrderEntityQueryMapper mediaOrderEntityQueryMapper;
    @Resource
    private MediaPlacementBaseSalesPlanMapper mediaPlacementBaseSalesPlanMapper;
    @Resource
    private MediaOrderMapper mediaOrderMapper;
    @Resource(name = "RedisObjectTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private MainCaseTagRelMapper mainCaseTagRelMapper;
    @Resource
    private MainAttachementMapper mainAttachementMapper;
    @Resource
    private AliYunOssRedisUtils aliYunOssRedisUtils;
    @Resource
    private OrderIntelligenceEstimateDetailMapper orderIntelligenceEstimateDetailMapper;
    @Resource
    private MediaOrderCompetitorMonitorMapper mediaOrderCompetitorMonitorMapper;
    @Resource
    private OrderEffectEstimateBaseService orderEffectEstimateBaseService;
    @Resource
    private OrderEffectEstimateBaseMapper orderEffectEstimateBaseMapper;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private MainResourceCategoryMapper mainResourceCategoryMapper;
    @Resource
    private MainResourceCategoryEntityQueryMapper mainResourceCategoryEntityQueryMapper;
    @Resource
    private MainMediaOrderPlacementService mainMediaOrderPlacementService;
    @Resource
    private LockDataKpiTypeProcessorFactory lockDataKpiTypeProcessorFactory;
    @Resource
    private LockCalculateDataProcessorFactory lockCalculateDataProcessorFactory;
    @Resource
    private ComputationalDataTemplateFactory computationalDataTemplateFactory;
    @Resource
    private OrderEffectEstimateBaseManLandEntityQueryMapper orderEffectEstimateBaseManLandEntityQueryMapper;
    @Resource
    private HotAdCodeFactory hotAdCodeFactory;
    @Resource
    private MainCrowdBizPackMapper mainCrowdBizPackMapper;
    @Resource
    private PersonLandDataMapper personLandDataMapper;
    @Resource
    private MainDictRegionMapper mainDictRegionMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private EffectCollectResolverFactory effectCollectResolverFactory;
    @Resource
    private MediaSelectionPlanMenuFieldAttrMapper mediaSelectionPlanMenuFieldAttrMapper;
    @Resource
    private CustomizationMapClient customizationMapClient;
    @Resource
    private AMapClient aMapClient;

    @Value("${path.export}")
    private String tempDataPath;

    private static final String EXTENDED = "投后全域效果评估-拓展版";
    private static final String EXTENDED_LAND = "投后全域效果评估-人地版";


    private final PassiveExpiringMap<String, String> regionMap = new PassiveExpiringMap<>(24 * 60 * 60 * 1000);

    private final PassiveExpiringMap<String, String> categoryMap = new PassiveExpiringMap<>(24 * 60 * 60 * 1000);
    @Resource
    private StatisticsDimensionMapper statisticsDimensionMapper;

    public List<String> getStringToList(String name) {
        return Arrays.stream(name.split(",")).collect(Collectors.toList());
    }

    public MediaOrderRespone getById(Integer id) {
        MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(id);
        MediaPlacementBaseSalesPlan mediaPlacementBaseSalesPlan = mediaPlacementBaseSalesPlanMapper.getByIdEX(mediaOrder.getBasePlanId());
        List<Integer> tagIds = null;
        if (MAN_LAND.equals(mediaOrder.getOrderType())) {
            tagIds = mainCrowdBizPackMapper.getPackIdsByBizId(mediaOrder.getOrderType(), mediaOrder.getId());
        } else {
            tagIds = mediaOrderEntityQueryMapper.getBizIdAndTagId(mediaOrder.getId(), BusinessType.ORDER.name());
        }
        TSelectService selectService = TSelectService.builder()
                .materialProduction(jsonToList(mediaOrder.getMaterialProduction(), String.class))
                .mediaNegotiations(jsonToList(mediaOrder.getMediaNegotiation(), String.class))
                .strategicPlanning(jsonToList(mediaOrder.getStrategicPlanning(), String.class))
                .monitoringWay(jsonToList(mediaOrder.getMonitoringWay(), String.class))
                .orderEffectEvaluation(mediaOrder.getOrderEffectEvaluation())
                .thirdPartyEvaluation(mediaOrder.getThirdPartyEvaluation())
                .operationalExcellence(jsonToList(mediaOrder.getOperationalExcellence(), String.class))
                .dataTransmissionPlatform(mediaOrder.getDataTransmissionPlatform())
                .remark(mediaOrder.getRemark())
                .build();
        //获取附件临时链接
        MainAttachmentContent contentFile = new MainAttachmentContent();
        if (mediaOrder.getOfflineStoreAttachmentId() != null) {
            contentFile = getOrderDeliverContentFile(mediaOrder.getOfflineStoreAttachmentId());
        }
        TMediaOrder tMediaOrder = TMediaOrder.builder()
                .id(mediaOrder.getId())
                .orderSn(mediaOrder.getOrderSn())
                .orderName(mediaOrder.getOrderName())
                .referSelectionPlanId(mediaOrder.getReferSelectionPlanId())
                .actualAmountSpent(mediaOrder.getActualAmountSpent())
                .intentionScenes(jsonToList(mediaOrder.getIntentionScene(), Integer.class))
                .intentionSceneTags(mediaOrder.getIntentionSenceTags())
                .onlineChannelData(jsonToList(mediaOrder.getOnlineChannelData(), String.class))
                .onlineChannelRemark(mediaOrder.getOnlineChannelRemark())
                .offlineStoreSupport(mediaOrder.getOfflineStoreSupport())
                .offlineStoreAttachmentId(mediaOrder.getOfflineStoreAttachmentId())
                .offlineStoreAttachmentUrl(contentFile.getUrl())
                .offlineStoreAttachmentName(contentFile.getName())
                .offlineChannelRemark(mediaOrder.getOfflineChannelRemark())
                .channelMode(mediaOrder.getChannel())
                .authOpenPlatform(jsonToList(mediaOrder.getAuthOpenPlatform(), String.class))
                .selectService(selectService)
                .build();
        TMediaPlacementPlanReq tMediaPlacementPlanReq = TMediaPlacementPlanReq.builder()
                .planId(mediaPlacementBaseSalesPlan.getId())
                .belongIndustryCategoryId(mediaPlacementBaseSalesPlan.getBelongIndustryCategoryId())
                .brandName(mediaPlacementBaseSalesPlan.getBrandName())
                .groupName(mediaPlacementBaseSalesPlan.getGroupName())
                .productPromotion(mediaPlacementBaseSalesPlan.getProductPromotion())
                .competitor(mediaPlacementBaseSalesPlan.getCompetitor())
                .brandSituation(mediaPlacementBaseSalesPlan.getBrandSituation())
                .presentStageRequirement(mediaPlacementBaseSalesPlan.getPresentStageRequirement())
                .disseminatePose(jsonToList(mediaPlacementBaseSalesPlan.getDisseminatePurpose(), String.class))
                .coreSalesRegions(jsonToList(mediaPlacementBaseSalesPlan.getCoreSalesRegions(), String.class))
                .deliveryStartTime(mediaPlacementBaseSalesPlan.getDeliveryStartTime())
                .deliveryEndTime(mediaPlacementBaseSalesPlan.getDeliveryEndTime())
                .budget(mediaPlacementBaseSalesPlan.getBudget())
                .planName(mediaPlacementBaseSalesPlan.getBrandName())
                .productProspect(mediaPlacementBaseSalesPlan.getProductProspect())
                .distributionChannel(mediaPlacementBaseSalesPlan.getDistributionChannel())
                .productPricing(mediaPlacementBaseSalesPlan.getProductPricing())
                .build();
        MediaOrderRespone mediaOrderRespone = MediaOrderRespone.builder()
                .tMediaOrder(tMediaOrder)
                .tMediaPlacementPlanReq(tMediaPlacementPlanReq)
                .crowdLabelList(tagIds)
                .days((int) TimeUnit.MILLISECONDS.toDays(mediaPlacementBaseSalesPlan.getDeliveryEndTime().getTime() - mediaPlacementBaseSalesPlan.getDeliveryStartTime().getTime()) / (1000 * 60 * 60 * 24))
                .build();
        return mediaOrderRespone;
    }


    private <T> List<T> jsonToList(String json, Class<T> type) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(json, mapper.getTypeFactory().constructCollectionType(List.class, type));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<TReferenceSchemeRes> getReferenceSchemeList() {
        return mediaOrderEntityQueryMapper.getReferenceSchemeList();
    }


    public TMediaPlacementPlanRes getMediaPlacementPlanByPlanIdAndPlanName(String planId) {
        TMediaPlacementPlan tMediaPlacementPlan = mediaOrderEntityQueryMapper.getMediaPlacementPlanByPlanIdAndPlanName(planId);
        List<Integer> tagIds = mediaOrderEntityQueryMapper.getBizIdAndTagId(Integer.parseInt(planId), BusinessType.PLAN.name());
        if (tMediaPlacementPlan != null) {
            return TMediaPlacementPlanRes.builder()
                    .id(tMediaPlacementPlan.getPlanId())
                    .belongIndustryCategoryId(tMediaPlacementPlan.getBelongIndustryCategoryId())
                    .brandName(tMediaPlacementPlan.getBrandName())
                    .groupName(tMediaPlacementPlan.getGroupName())
                    .productPromotion(tMediaPlacementPlan.getProductPromotion())
                    .competitor(tMediaPlacementPlan.getCompetitor())
                    .brandSituation(tMediaPlacementPlan.getBrandSituation())
                    .presentStageRequirement(tMediaPlacementPlan.getPresentStageRequirement())
                    .disseminatePose(jsonToList(tMediaPlacementPlan.getDisseminatePose(), String.class))
                    .coreSalesRegions(jsonToList(tMediaPlacementPlan.getCoreSalesRegions(), String.class))
                    .intentionScenes(jsonToList(tMediaPlacementPlan.getIntentionScenes(), Integer.class))
                    .deliveryStartTime(tMediaPlacementPlan.getDeliveryStartTime())
                    .deliveryEndTime(tMediaPlacementPlan.getDeliveryEndTime())
                    .budget(tMediaPlacementPlan.getBudget())
                    .actualAmountSpent(tMediaPlacementPlan.getActualAmountSpent())
                    .planName(tMediaPlacementPlan.getPlanName())
                    .crowdLabelList(tagIds)
                    .build();
        }
        return TMediaPlacementPlanRes.builder().build();
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(UpdateMediaOrderReq updateMediaOrderReq, Integer identifier) {
        MediaOrder getByIdMediaOrder = mediaOrderMapper.getByIdEX(updateMediaOrderReq.getId().intValue());
        // 1、投放需求
        TMediaPlacementPlanReq tMediaPlacementPlanReq = updateMediaOrderReq.getTMediaPlacementPlanReq();
        //保存媒体点位基础营销方案
        MediaPlacementBaseSalesPlan mediaPlacementBaseSalesPlan = this.createMediaPlacementPlanReq(identifier, tMediaPlacementPlanReq);
        mediaPlacementBaseSalesPlan.setId(getByIdMediaOrder.getBasePlanId());
        mediaPlacementBaseSalesPlanMapper.updateByEntity(mediaPlacementBaseSalesPlan);
        //判断附件是否被修改
        if (updateMediaOrderReq.getOfflineStoreAttachmentId() != null) {
            if (getByIdMediaOrder.getOfflineStoreAttachmentId() != null && !getByIdMediaOrder.getOfflineStoreAttachmentId().equals(updateMediaOrderReq.getOfflineStoreAttachmentId())) {
                mainAttachementMapper.deleteByIdLogically(identifier, getByIdMediaOrder.getOfflineStoreAttachmentId());
            }
        }
        //保存订单主表信息
        MediaOrder mediaOrder = this.createMediaOrder(identifier, updateMediaOrderReq, mediaPlacementBaseSalesPlan, getByIdMediaOrder);
        //获取最新的状态
        String orderStatus = getOrderStatus(new Date(), mediaPlacementBaseSalesPlan.getDeliveryStartTime(), mediaPlacementBaseSalesPlan.getDeliveryEndTime());
        mediaOrder.setOrderStatus(orderStatus);
        mediaOrderMapper.updateByEntity(mediaOrder);

        //2、人群洞察
        //保存人群标签
        List<Integer> tagIds = updateMediaOrderReq.getTagIds();
        handleTagIds(identifier, getByIdMediaOrder, tagIds);
//        List<UpdateMainCaseTagRel> updateMainCaseTagRels = updateMediaOrderReq.getTagIds();
//        if (updateMainCaseTagRels != null && updateMainCaseTagRels.size() > 0) {
//            List<MainMediaTagRel> mainMediaTagRelList = new ArrayList<>();
//            for (UpdateMainCaseTagRel mainCaseTagRel : updateMainCaseTagRels) {
//                //要加的标签
//                ArrayList<Integer> addMainCaseTagRel = new ArrayList<>(mainCaseTagRel.getUpdateAfter());
//                addMainCaseTagRel.removeAll(mainCaseTagRel.getUpdateBefore());
//                //要减的标签
//                ArrayList<Integer> missingMainCaseTagRel = new ArrayList<>(mainCaseTagRel.getUpdateBefore());
//                missingMainCaseTagRel.removeAll(mainCaseTagRel.getUpdateAfter());
//                //维护数据
//                if (missingMainCaseTagRel.size() > 0) {
//                    mainCaseTagRelMapper.deleteBatchByBizIdAndTagIds(mediaOrder.getId(), missingMainCaseTagRel, BusinessType.ORDER.name());
//                }
//                if (addMainCaseTagRel.size() > 0) {
//                    for (Integer tagId : addMainCaseTagRel) {
//                        MainMediaTagRel tagRelation = this.createTagRelation(identifier, updateMediaOrderReq.getId().intValue(), tagId);
//                        mainMediaTagRelList.add(tagRelation);
//                    }
//                    mainCaseTagRelMapper.insertBatch(mainMediaTagRelList);
//                }
//            }
//        }

        //3、选择服务
        TSelectService selectService = updateMediaOrderReq.getSelectService();

        if (selectService.getMediaNegotiations() != null
                || selectService.getOperationalExcellence() != null
                || selectService.getMonitoringWay() != null
                || selectService.getMaterialProduction() != null
                || selectService.getStrategicPlanning() != null
                || StringUtils.isNotBlank(selectService.getDataTransmissionPlatform())
                || StringUtils.isNotBlank(selectService.getOrderEffectEvaluation())
                || StringUtils.isNotBlank(selectService.getRemark())
                || StringUtils.isNotBlank(selectService.getThirdPartyEvaluation())) {

            mediaOrder.setMediaNegotiation(new Gson().toJson(selectService.getMediaNegotiations()));
            mediaOrder.setStrategicPlanning(new Gson().toJson(selectService.getStrategicPlanning()));
            mediaOrder.setMaterialProduction(new Gson().toJson(selectService.getMaterialProduction()));
            mediaOrder.setMonitoringWay(new Gson().toJson(selectService.getMonitoringWay()));
            mediaOrder.setOrderEffectEvaluation(selectService.getOrderEffectEvaluation());
            mediaOrder.setOperationalExcellence(new Gson().toJson(selectService.getOperationalExcellence()));
            mediaOrder.setDataTransmissionPlatform(selectService.getDataTransmissionPlatform());
            mediaOrder.setThirdPartyEvaluation(selectService.getThirdPartyEvaluation());
            mediaOrder.setRemark(selectService.getRemark());
            mediaOrder.setOrderStatus(orderStatus);
            //维护全域评估默认数据
            maintainAssessDefaultData(identifier, getByIdMediaOrder, selectService);
            mediaOrderMapper.updateByEntity(mediaOrder);
        } else {
            //整个对象等于null 清除掉 全域评估数据
            orderEffectEstimateBaseService.delete(identifier, mediaOrder.getId());
        }
    }

    private void handleTagIds(Integer userKey, MediaOrder mediaOrder, List<Integer> tagIds) {
        //保存人群标签
        if (MAN_LAND.equals(mediaOrder.getOrderType())) {
            if (CollectionUtils.isNotEmpty(tagIds)) {
                mainCrowdBizPackMapper.deleteByBizId(mediaOrder.getOrderType(), mediaOrder.getId());
                List<MainCrowdBizPack> list = tagIds.stream().map(id -> {
                    MainCrowdBizPack crowdBizPack = new MainCrowdBizPack();
                    crowdBizPack.setBizType(mediaOrder.getOrderType());
                    crowdBizPack.setBizId(mediaOrder.getId());
                    crowdBizPack.setPackId(id);
                    crowdBizPack.setCreator(appName);
                    crowdBizPack.setUpdater(appName);
                    crowdBizPack.setCreateTime(new Date());
                    crowdBizPack.setUpdateTime(new Date());
                    return crowdBizPack;
                }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    mainCrowdBizPackMapper.insertBatch(list);
                }
            }
        } else {
            //先删除一下之前保存的数据
            mediaOrderEntityQueryMapper.deleteMainMediaTagRelByBizIdAndBizType(mediaOrder.getId(), BusinessType.ORDER.name());
            if (CollectionUtils.isNotEmpty(tagIds)) {
                List<MainMediaTagRel> mainMediaTagRelList = tagIds.stream().map(id -> {
                    return this.createTagRelation(userKey, mediaOrder.getId(), id);
                }).collect(Collectors.toList());
                mainCaseTagRelMapper.insertBatch(mainMediaTagRelList);
            }
        }
    }

    /**
     * 维护全域评估默认数据
     *
     * @param userKey       用户id
     * @param mediaOrder    订单信息
     * @param selectService 舒服数据
     */
    private void maintainAssessDefaultData(Integer userKey, MediaOrder mediaOrder, TSelectService selectService) {
        String orderEffectEvaluation = getSafeString(mediaOrder.getOrderEffectEvaluation());
        String thirdPartyEvaluation = getSafeString(mediaOrder.getThirdPartyEvaluation());
        String orderEffectEvaluationUpdate = getSafeString(selectService.getOrderEffectEvaluation());
        String thirdPartyEvaluationUpdate = getSafeString(selectService.getThirdPartyEvaluation());
        //处理全域评估
        if (orderEffectEvaluation.equals(orderEffectEvaluationUpdate) && thirdPartyEvaluation.equals(thirdPartyEvaluationUpdate)) {
            //表示没有修改
            return;
        }
        // 删除逻辑
        if (orderEffectEvaluationUpdate.isEmpty() && thirdPartyEvaluationUpdate.isEmpty()) {
            mediaOrderMapper.updateThirdPartyEstimateCreateTime(null, mediaOrder.getId());
            orderEffectEstimateBaseService.delete(userKey, mediaOrder.getId());
            return;
        }

        // 如果评估类型发生变化，处理不同类型
        if (!orderEffectEvaluation.equals(orderEffectEvaluationUpdate)) {
            if (EXTENDED.equals(orderEffectEvaluationUpdate) || EXTENDED_LAND.equals(orderEffectEvaluationUpdate)) {
                mediaOrderMapper.updateThirdPartyEstimateCreateTime(new Date(), mediaOrder.getId());
            } else {
                mediaOrderMapper.updateThirdPartyEstimateCreateTime(null, mediaOrder.getId());
            }
            orderEffectEstimateBaseService.delete(userKey, mediaOrder.getId());
            // 需要删除人地版相关数据
            mediaOrderMatchDetailService.deleteLand(mediaOrder.getId());
        }

        // 第三方评估附件变了
        if (!thirdPartyEvaluation.equals(thirdPartyEvaluationUpdate)) {
            if (mediaOrder.getThirdPartyEvaluationAttachmentId() != null) {
                mainAttachementMapper.deleteByIdLogically(userKey, mediaOrder.getThirdPartyEvaluationAttachmentId());
            }
        }

        // 判断是否需要维护默认基础版数据（非扩展 非人地版）
        if (!EXTENDED.equals(orderEffectEvaluationUpdate) && !EXTENDED_LAND.equals(orderEffectEvaluationUpdate)) {
            OrderEffectEstimateBase base = orderEffectEstimateBaseMapper.getOrderEffectEstimateBaseByOrderId(mediaOrder.getId());
            if (base == null) {
                createOrderEffectEstimateBase(userKey, mediaOrder.getId());
            }
        }

    }

    private String getSafeString(String value) {
        return StringUtils.isEmpty(value) ? "" : value;
    }
//    private void maintainAssessDefaultData(Integer userKey, MediaOrder mediaOrder, TSelectService selectService) {
//        String orderEffectEvaluation = (mediaOrder.getOrderEffectEvaluation() == null || mediaOrder.getOrderEffectEvaluation().isEmpty()) ? "" : mediaOrder.getOrderEffectEvaluation();
//        String thirdPartyEvaluation = (mediaOrder.getThirdPartyEvaluation() == null || mediaOrder.getThirdPartyEvaluation().isEmpty()) ? "" : mediaOrder.getThirdPartyEvaluation();
//        String orderEffectEvaluationUpdate = (selectService.getOrderEffectEvaluation() == null || selectService.getOrderEffectEvaluation().isEmpty()) ? "" : selectService.getOrderEffectEvaluation();
//        String thirdPartyEvaluationUpdate = (selectService.getThirdPartyEvaluation() == null || selectService.getThirdPartyEvaluation().isEmpty()) ? "" : selectService.getThirdPartyEvaluation();
//        //处理全域评估
//        if (orderEffectEvaluation.equals(orderEffectEvaluationUpdate) && thirdPartyEvaluation.equals(thirdPartyEvaluationUpdate)) {
//            //表示没有修改
//            return;
//        }
//        //都是null 删除掉就好
//        if (StringUtils.isEmpty(selectService.getOrderEffectEvaluation()) && StringUtils.isEmpty(selectService.getThirdPartyEvaluation())) {
//            mediaOrderMapper.updateThirdPartyEstimateCreateTime(null, mediaOrder.getId());
//            orderEffectEstimateBaseService.delete(userKey, mediaOrder.getId());
//            return;
//        }
//        // 最少有一个是不为null 要维护默认数据 首先判断全域评估是不是发生改变 发生改变的话删除之前的加入新增的
//        boolean evaluationChanged = !Objects.equals(orderEffectEvaluation, orderEffectEvaluationUpdate);
//        //发生改变要还原成最初的状态
//        if (evaluationChanged) {
//            //判断当前是不是修改为了 拓展版
//            if (selectService.getOrderEffectEvaluation() != null && selectService.getOrderEffectEvaluation().equals(EXTENDED)) {
//                mediaOrderMapper.updateThirdPartyEstimateCreateTime(new Date(), mediaOrder.getId());
//            } else {
//                mediaOrderMapper.updateThirdPartyEstimateCreateTime(null, mediaOrder.getId());
//            }
//            orderEffectEstimateBaseService.delete(userKey, mediaOrder.getId());
//        }
//        //判断第三方评估是不是发生变化
//        boolean evaluationThirdPartyEvaluation = !Objects.equals(thirdPartyEvaluation, thirdPartyEvaluationUpdate);
//        if (evaluationThirdPartyEvaluation) {
//            //删除上传附件数据
//            if (mediaOrder.getThirdPartyEvaluationAttachmentId() != null) {
//                mainAttachementMapper.deleteByIdLogically(userKey, mediaOrder.getThirdPartyEvaluationAttachmentId());
//            }
//        }
//        // 现在要判断具体维护那个默认数据 如果只选择第三方评估默认数据加载到基础版数据里面   判断现在是不是扩展版
//        if (StringUtils.isNotEmpty(selectService.getOrderEffectEvaluation()) && EXTENDED.equals(selectService.getOrderEffectEvaluation())) {
//            // 不加默认值列表是根据订单那个那个值去搜索的
//            return;
//        }
//        //只勾选了第三方评估
//        OrderEffectEstimateBase base = orderEffectEstimateBaseMapper.getOrderEffectEstimateBaseByOrderId(mediaOrder.getId());
//        //判断当前基础版数据是否存在 不存在就添加
//        if (base == null) {
//            createOrderEffectEstimateBase(userKey, mediaOrder.getId());
//        }
//    }

    /**
     * 校验当前订单状态
     *
     * @param date              当前时间
     * @param deliveryStartTime 投放开始时间
     * @param deliveryEndTime   投放结束时间
     * @return 状态
     */
    private String getOrderStatus(Date date, Date deliveryStartTime, Date deliveryEndTime) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate deliveryStartDate = deliveryStartTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate deliveryEndDate = deliveryEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        if (localDate.isEqual(deliveryStartDate) || localDate.isEqual(deliveryEndDate)) {
            return OrderStatusType.DELIVERING.name();
        }
        if (localDate.isAfter(deliveryStartDate) && localDate.isBefore(deliveryEndDate)) {
            return OrderStatusType.DELIVERING.name();
        } else if (localDate.isAfter(deliveryEndDate)) {
            return OrderStatusType.DELIVERY_COMPLETE.name();
        } else {
            return OrderStatusType.PENDING_DELIVERY.name();
        }
    }

    private MediaOrder createMediaOrder(Integer identifier, UpdateMediaOrderReq updateMediaOrderReq, MediaPlacementBaseSalesPlan mediaPlacementBaseSalesPlan, MediaOrder getByIdMediaOrder) {
        MediaOrder mediaOrder = new MediaOrder();
        mediaOrder.setId(updateMediaOrderReq.getId().intValue());
        mediaOrder.setOrderSn(getByIdMediaOrder.getOrderSn());
        mediaOrder.setOrderName(updateMediaOrderReq.getOrderName());
        mediaOrder.setReferSelectionPlanId(updateMediaOrderReq.getReferSelectionPlanId());
        mediaOrder.setBasePlanId(mediaPlacementBaseSalesPlan.getId());
        mediaOrder.setIntentionScene(new Gson().toJson(updateMediaOrderReq.getIntentionScenes()));
        mediaOrder.setIntentionSenceTags(updateMediaOrderReq.getIntentionSceneTags());
        mediaOrder.setChannel(updateMediaOrderReq.getChannelMode());
        mediaOrder.setOnlineChannelData(new Gson().toJson(updateMediaOrderReq.getOnlineChannelData()));
        mediaOrder.setOnlineChannelRemark(updateMediaOrderReq.getOnlineChannelRemark());
        mediaOrder.setOfflineStoreSupport(updateMediaOrderReq.getOfflineStoreSupport());
        mediaOrder.setOfflineStoreAttachmentId(updateMediaOrderReq.getOfflineStoreAttachmentId());
        mediaOrder.setOfflineChannelRemark(updateMediaOrderReq.getOfflineChannelRemark());
        mediaOrder.setAuthOpenPlatform(new Gson().toJson(updateMediaOrderReq.getAuthOpenPlatform()));
        mediaOrder.setOrderStatus(getByIdMediaOrder.getOrderStatus().equals(OrderStatusType.PENDING_SUBMIT.name()) ? OrderStatusType.PENDING_DELIVERY.name() : getByIdMediaOrder.getOrderStatus());
        mediaOrder.setOrderTime(getByIdMediaOrder.getOrderTime());
        mediaOrder.setIsDeleted(getByIdMediaOrder.getIsDeleted());
        mediaOrder.setThirdPartyBriefAttachmentId(getByIdMediaOrder.getThirdPartyBriefAttachmentId());
        mediaOrder.setThirdPartyEvaluationAttachmentId(getByIdMediaOrder.getThirdPartyEvaluationAttachmentId());
        mediaOrder.setScheduleAttachmentId(getByIdMediaOrder.getScheduleAttachmentId());
        mediaOrder.setBrandInspirationSuggestionAttachmentId(getByIdMediaOrder.getBrandInspirationSuggestionAttachmentId());
        setCreatorInfo(identifier, mediaOrder);
        setUpdaterInfo(identifier, mediaOrder);
        return mediaOrder;
    }

    private MediaPlacementBaseSalesPlan createMediaPlacementPlanReq(Integer identifier, TMediaPlacementPlanReq tMediaPlacementPlanReq) {
        MediaPlacementBaseSalesPlan mediaPlacementBaseSalesPlan = new MediaPlacementBaseSalesPlan();
        mediaPlacementBaseSalesPlan.setId(tMediaPlacementPlanReq.getPlanId());
        mediaPlacementBaseSalesPlan.setBelongIndustryCategoryId(tMediaPlacementPlanReq.getBelongIndustryCategoryId());
        mediaPlacementBaseSalesPlan.setBrandName(tMediaPlacementPlanReq.getBrandName());
        mediaPlacementBaseSalesPlan.setGroupName(tMediaPlacementPlanReq.getGroupName());
        mediaPlacementBaseSalesPlan.setProductPromotion(tMediaPlacementPlanReq.getProductPromotion());
        mediaPlacementBaseSalesPlan.setCompetitor(tMediaPlacementPlanReq.getCompetitor());
        mediaPlacementBaseSalesPlan.setBrandSituation(tMediaPlacementPlanReq.getBrandSituation());
        mediaPlacementBaseSalesPlan.setPresentStageRequirement(tMediaPlacementPlanReq.getPresentStageRequirement());
        mediaPlacementBaseSalesPlan.setDisseminatePurpose(new Gson().toJson(tMediaPlacementPlanReq.getDisseminatePose()));
        mediaPlacementBaseSalesPlan.setCoreSalesRegions(new Gson().toJson(tMediaPlacementPlanReq.getCoreSalesRegions()));
        mediaPlacementBaseSalesPlan.setDeliveryStartTime(tMediaPlacementPlanReq.getDeliveryStartTime());
        mediaPlacementBaseSalesPlan.setDeliveryEndTime(tMediaPlacementPlanReq.getDeliveryEndTime());
        mediaPlacementBaseSalesPlan.setBudget(tMediaPlacementPlanReq.getBudget());
        mediaPlacementBaseSalesPlan.setProductProspect(tMediaPlacementPlanReq.getProductProspect());
        mediaPlacementBaseSalesPlan.setDistributionChannel(tMediaPlacementPlanReq.getDistributionChannel());
        mediaPlacementBaseSalesPlan.setProductPricing(tMediaPlacementPlanReq.getProductPricing());
        setCreatorInfo(identifier, mediaPlacementBaseSalesPlan);
        setUpdaterInfo(identifier, mediaPlacementBaseSalesPlan);
        return mediaPlacementBaseSalesPlan;
    }

    private MainMediaTagRel createTagRelation(Integer identifier, Integer orderId, Integer tagId) {
        MainMediaTagRel mainCaseTagRel = new MainMediaTagRel();
        mainCaseTagRel.setId(null);
        mainCaseTagRel.setBizId(orderId);
        mainCaseTagRel.setTagId(tagId);
        mainCaseTagRel.setBizType(BusinessType.ORDER.name());
        setUpdaterInfo(identifier, mainCaseTagRel);
        setCreatorInfo(identifier, mainCaseTagRel);
        return mainCaseTagRel;
    }

    public PageResponse<TMediaOrderRes> list(SearchCriteria criteria) {
        SearchCriteria.Criteria notDeletedCriteria = new SearchCriteria.Criteria();
        notDeletedCriteria.setKey("isDeleted");
        notDeletedCriteria.setValue("0");
        criteria.getCriterias().add(notDeletedCriteria);
        //此分页为entity分页，如果有其他查询自行在前面增加不要导致以下分页失效
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<TMediaOrderRes> searchResult = mediaOrderEntityQueryMapper.list(criteria);
        searchResult.forEach(tMediaOrderRes -> {
            tMediaOrderRes.setIsScheduling(tMediaOrderRes.getIsScheduling() != null);
            tMediaOrderRes.setIsUploadMonitoring(tMediaOrderRes.getIsUploadMonitoring() != null);
            String orderStatus = "";
            //维护状态数据
            if (!tMediaOrderRes.getOrderStatus().equals(OrderStatusType.PENDING_SUBMIT.name())) {
                //先获取当前订单应该是什么状态
                String status = getOrderStatus(new Date(), tMediaOrderRes.getDeliveryStartTime(), tMediaOrderRes.getDeliveryEndTime());
                //判断当前状态是不是与应该的状态是一致的
                if (!tMediaOrderRes.getOrderStatus().equals(status)) {
                    MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(tMediaOrderRes.getId().intValue());
                    mediaOrder.setOrderStatus(status);
                    log.warn("Start update orderStatus ——>" + mediaOrder);
                    mediaOrderMapper.insertUpdateEntity(mediaOrder);
                    log.warn("Update orderStatus complete ——>" + mediaOrder);
                    orderStatus = OrderStatusType.valueOf(status).getDesc();
                }
//                if (date.after(tMediaOrderRes.getDeliveryStartTime()) && date.before(tMediaOrderRes.getDeliveryEndTime()) && !tMediaOrderRes.getOrderStatus().equals(OrderStatusType.DELIVERING.name())) {
//                    MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(tMediaOrderRes.getId().intValue());
//                    mediaOrder.setOrderStatus(OrderStatusType.DELIVERING.name());
//                    log.warn("Start update orderStatus ——>" + mediaOrder);
//                    mediaOrderMapper.insertUpdateEntity(mediaOrder);
//                    log.warn("Update orderStatus complete ——>" + mediaOrder);
//                    orderStatus = OrderStatusType.DELIVERING.getDesc();
//                } else if (date.after(tMediaOrderRes.getDeliveryEndTime()) && !tMediaOrderRes.getOrderStatus().equals(OrderStatusType.DELIVERY_COMPLETE.name())) {
//                    MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(tMediaOrderRes.getId().intValue());
//                    mediaOrder.setOrderStatus(OrderStatusType.DELIVERY_COMPLETE.name());
//                    log.warn("Start update orderStatus ——>" + mediaOrder);
//                    mediaOrderMapper.insertUpdateEntity(mediaOrder);
//                    log.warn("Update orderStatus complete ——>" + mediaOrder);
//                    orderStatus = OrderStatusType.DELIVERY_COMPLETE.getDesc();
//                }
            }
            tMediaOrderRes.setOrderStatus("".equals(orderStatus) ? OrderStatusType.valueOf(tMediaOrderRes.getOrderStatus()).getDesc() : orderStatus);
        });
        PageResponse<TMediaOrderRes> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult);
        return pageResponse;
    }

    private MainAttachmentContent getOrderDeliverContentFile(Long fileId) {
        MainAttachement mainAttachement = mainAttachementMapper.getByIdEX(fileId);
        MainAttachmentContent mainAttachmentContent = new MainAttachmentContent();
        if (mainAttachement != null) {
            aliYunOssRedisUtils.setTempUrl(mainAttachement);
            mainAttachmentContent.setId(mainAttachement.getId());
            mainAttachmentContent.setUrl(mainAttachement.getUrl());
            mainAttachmentContent.setName(mainAttachement.getOriginalFileName());
        }
        return mainAttachmentContent;
    }


    private void equalAnnex(Long annexId, Integer identifier) {
        if (annexId != null) {
            MainAttachement mainAttachement = mainAttachementMapper.getByIdEX(annexId);
            if (!mainAttachement.getId().equals(annexId)) {
                mainAttachementMapper.deleteByIdLogically(identifier, annexId);
            }
        }
    }

    public void uploadScheduling(TUploadSchedulingReq tUploadSchedulingReq, Integer identifier) {
        MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(tUploadSchedulingReq.getOrderId());
        this.equalAnnex(Long.valueOf(tUploadSchedulingReq.getUploadSchedulingId()), identifier);
        mediaOrder.setScheduleAttachmentId(Long.valueOf(tUploadSchedulingReq.getUploadSchedulingId()));
        mediaOrderMapper.insertUpdateEntity(mediaOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id, Integer identifier) {
        MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(id);
        log.warn("Start delete media_order");
        mediaOrderMapper.deleteByIdLogically(identifier, id);
        log.warn("Start delete media_placement_base_sales_plan");
        mediaPlacementBaseSalesPlanMapper.deleteByIdLogically(identifier, mediaOrder.getBasePlanId());
        log.warn("Start delete media_order_competitor_monitor");
        mediaOrderEntityQueryMapper.deleteCompetitorMonitorByOrderId(id);
        log.warn("Start delete order_intelligence_estimate_detail");
        mediaOrderEntityQueryMapper.deleteIntelligenceEstimateDetailOrderId(id);
        //删除全域评估数据
        log.warn("Start delete order_effect_estimate_base");
        orderEffectEstimateBaseService.delete(identifier, id);
        //删除智选详情相关数据
        mainMediaOrderPlacementService.deleteMediaPlacement(id);
        //删除人地版本相关数据
        if (MAN_LAND.equals(mediaOrder.getOrderType())) {
            mediaOrderMatchDetailService.deleteManLand(id);
        }
    }


    public TorderIntelligenceEstimatedetailReq getEstimatedEffectByOrderId(Integer id) {
        OrderIntelligenceEstimateDetail orderIntelligenceEstimateDetail = orderIntelligenceEstimateDetailMapper.getByOrderId(id);
        TorderIntelligenceEstimatedetailReq torderIntelligenceEstimatedetailReq = new TorderIntelligenceEstimatedetailReq();
        if (orderIntelligenceEstimateDetail != null) {
            BeanUtils.copyProperties(orderIntelligenceEstimateDetail, torderIntelligenceEstimatedetailReq);
        }
        return torderIntelligenceEstimatedetailReq;
    }

    @PostConstruct
    public void init() {
        regionMap.putAll(this.getRedisRegionCodeMap());
        categoryMap.putAll(this.getRedisMediumCategoryIdMap());
    }

    public String getCode(String name) {
        if (regionMap.isEmpty()) {
            this.init();
        }
        return regionMap.get(name);
    }

    public String getRedisMediumCategoryId(String key) {
        if (categoryMap.isEmpty()) {
            categoryMap.putAll(this.getRedisMediumCategoryIdMap());
        }
        return categoryMap.get(key);
    }

    public void deleteCategoryMap() {
        categoryMap.clear();
    }

    public HashMap<String, String> getRedisRegionCodeMap() {
        HashMap<String, String> valueMap = (HashMap<String, String>) redisTemplate.opsForValue().get(REDIS_REGION_CODE);
        if (valueMap != null && !valueMap.isEmpty()) {
            return valueMap;
        }
        List<GaodeArea> gaodeAreaList = mediaOrderEntityQueryMapper.getRegionCode();
        HashMap<String, String> map = new HashMap<>();
        if (gaodeAreaList != null) {
            for (GaodeArea gaodeArea : gaodeAreaList) {
                if (gaodeArea != null) {
                    String key = "";
                    String value = "";
                    if (gaodeArea.getCode1() != null && StringUtils.isNotBlank(gaodeArea.getRegionName1())) {
                        value = value + gaodeArea.getCode1() + ",";
                        key = key + gaodeArea.getRegionName1();
                    }
                    if (gaodeArea.getCode2() != null && StringUtils.isNotBlank(gaodeArea.getRegionName2())) {
                        value = value + gaodeArea.getCode2() + ",";
                        key = key + gaodeArea.getRegionName2();
                        map.put(key, value);
                    }

                    if (gaodeArea.getCode3() != null && StringUtils.isNotBlank(gaodeArea.getRegionName3())) {
                        value = value + gaodeArea.getCode3() + "";
                        key = key + gaodeArea.getRegionName3();
                        map.put(key, value);
                    }
                }
            }
            redisTemplate.opsForValue().set(REDIS_REGION_CODE, map);
        }
        return map;
    }


    public List<TMediaMatchingResults> adptTo(List<TCodeCount> resultList, Integer pageNumber, Integer pageSize) {
        Integer startIdx = (pageNumber - 1) * pageSize;
        List<TMediaMatchingResults> results = resultList.stream()
                .map(tCodeCount -> {
                    List<String> codes = getStringToList(tCodeCount.getCode());
                    List<String> categoryIds = getStringToList(tCodeCount.getCategoryId());
                    TMediaMatchingResults tMediaMatchingResults = new TMediaMatchingResults();
                    tMediaMatchingResults.setCodeLv1(codes.get(0));
                    tMediaMatchingResults.setCodeLv2(codes.get(1));
                    tMediaMatchingResults.setCodeLv3(codes.get(2));
                    tMediaMatchingResults.setProvinceName(tCodeCount.getProvinceName());
                    tMediaMatchingResults.setCityName(tCodeCount.getCityName());
                    tMediaMatchingResults.setDistrictName(tCodeCount.getDistrictName());
                    tMediaMatchingResults.setCategoryIdLv1(categoryIds.get(0));
                    tMediaMatchingResults.setCategoryIdLv2(categoryIds.get(1));
                    tMediaMatchingResults.setCategoryIdLv3(categoryIds.get(2));
                    tMediaMatchingResults.setCount(tCodeCount.getCount());
                    return tMediaMatchingResults;
                })
                .skip(startIdx)
                .limit(pageSize)
                .collect(Collectors.toList());
        return results;
    }

    public File generalTemplate() throws Exception {
        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        String fileName = URLEncoder.encode(getExportFileName(tempDataPath, "门店地址信息模板"), "UTF-8");
        File newFile = new File(fileName);
        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), XlsStoreAddress.class);
        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }

    public List<CrowdLabelResponse> getCrowdLabelList() {
        List<MediaTagData> crowdLabelList = commonMapper.getTagsByGroupLabel("CROWD_LABEL");
        if (CollectionUtils.isEmpty(crowdLabelList)) {
            return Collections.emptyList();
        }
        List<CrowdLabelResponse> tagHierarchy = assembleCrowdLabel(crowdLabelList.stream().map(CrowdLabelResponse::new).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(tagHierarchy)) {
            return Collections.emptyList();
        }
        //将标签分类作为一级目录
        List<CrowdLabelResponse> result = new ArrayList<>();
        Map<Integer, List<CrowdLabelResponse>> collect = tagHierarchy.stream().collect(Collectors.groupingBy(CrowdLabelResponse::getLabelCategoryId));
        for (Map.Entry<Integer, List<CrowdLabelResponse>> entry : collect.entrySet()) {
            CrowdLabelResponse category = entry.getValue().get(0);
            result.add(new CrowdLabelResponse(category, entry.getValue()));
        }
        return result;
    }

    private List<CrowdLabelResponse> assembleCrowdLabel(List<CrowdLabelResponse> tagsByPlanId) {
        Map<Integer, List<CrowdLabelResponse>> groupByParentId = new HashMap<>();
        for (CrowdLabelResponse labelResponse : tagsByPlanId) {
            groupByParentId.computeIfAbsent(labelResponse.getParentId(), k -> new ArrayList<>()).add(labelResponse);
        }
        Function<CrowdLabelResponse, CrowdLabelResponse> buildHierarchy = new Function<CrowdLabelResponse, CrowdLabelResponse>() {
            @Override
            public CrowdLabelResponse apply(CrowdLabelResponse crowdLabelResponse) {
                List<CrowdLabelResponse> secondaryCrowdLabelList = groupByParentId.get(crowdLabelResponse.getLabelId());
                if (CollectionUtils.isNotEmpty(secondaryCrowdLabelList)) {
                    crowdLabelResponse.setSecondaryCrowdLabelList(secondaryCrowdLabelList
                            .stream()
                            .map(this)
                            .collect(Collectors.toList()));
                }
                return crowdLabelResponse;
            }
        };
        List<CrowdLabelResponse> lv1CrowdLabelList = groupByParentId.get(null);
        if (CollectionUtils.isNotEmpty(lv1CrowdLabelList)) {
            return lv1CrowdLabelList.stream().map(buildHierarchy).collect(Collectors.toList());
        }
        return null;
    }

    public String getMediumCategoryId(String s) {
        if (categoryMap.isEmpty()) {
            this.init();
        }
        return categoryMap.get(s);
    }

    public HashMap<String, String> getRedisMediumCategoryIdMap() {
        HashMap<String, String> valueMap = (HashMap<String, String>) redisTemplate.opsForValue().get(REDIS_MEDIUM_CATEGORY_ID);
        if (valueMap != null && !valueMap.isEmpty()) {
            return valueMap;
        }
        List<TCategoryIds> categoryIds = mediaOrderEntityQueryMapper.getMediumCategoryId();
        HashMap<String, String> map = new HashMap<>();
        if (categoryIds != null) {
            for (TCategoryIds tCategoryIds : categoryIds) {
                if (tCategoryIds != null) {
                    String key = "";
                    String value = "";
                    if (tCategoryIds.getCategoryIdLv1() != null && StringUtils.isNotBlank(tCategoryIds.getCategoryIdLv1Name())) {
                        value = value + tCategoryIds.getCategoryIdLv1() + ",";
                        key = key + tCategoryIds.getCategoryIdLv1Name();
                    }
                    if (tCategoryIds.getCategoryIdLv2() != null && StringUtils.isNotBlank(tCategoryIds.getCategoryIdLv2Name())) {
                        value = value + tCategoryIds.getCategoryIdLv2() + ",";
                        key = key + tCategoryIds.getCategoryIdLv2Name();
                    }

                    if (tCategoryIds.getCategoryIdLv3() != null && StringUtils.isNotBlank(tCategoryIds.getCategoryIdLv3Name())) {
                        value = value + tCategoryIds.getCategoryIdLv3() + "";
                        key = key + tCategoryIds.getCategoryIdLv3Name();
                        map.put(key, value);
                    }
                }
            }
            redisTemplate.opsForValue().set(REDIS_MEDIUM_CATEGORY_ID, map);
        }
        return map;
    }

    public void uploadLaunchMonitoring(TUploadLaunchMonitoring tUploadLaunchMonitoring, Integer identifier) {
        MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(tUploadLaunchMonitoring.getId());
        this.equalAnnex(tUploadLaunchMonitoring.getThirdPartyBriefAttachmentId(), identifier);
        mediaOrder.setThirdPartyBriefAttachmentId(tUploadLaunchMonitoring.getThirdPartyBriefAttachmentId());
        mediaOrderMapper.insertUpdateEntity(mediaOrder);
    }

    public List<TCompetitiveMonitoring> getCompetitiveMonitoring() {
        List<TCompetitiveMonitoring> tCompetitiveMonitorings = mediaOrderEntityQueryMapper.getCompetitiveMonitoring();
        return tCompetitiveMonitorings;
    }

    public void CreateCompetitiveMonitoring(CreateCompetitiveMonitoring createCompetitiveMonitoring, Integer identifier) {
        MediaOrderCompetitorMonitor competitorMonitor = new MediaOrderCompetitorMonitor();
        List<Integer> competitorOrderIds = createCompetitiveMonitoring.getCompetitorBrandId();
        if (!competitorOrderIds.isEmpty()) {
            for (Integer competitorOrderId : competitorOrderIds) {
                competitorMonitor.setId(null);
                competitorMonitor.setOrderId(createCompetitiveMonitoring.getOrderId());
                competitorMonitor.setCompetitorBrandId(competitorOrderId);
                setCreatorInfo(identifier, competitorMonitor);
                setUpdaterInfo(identifier, competitorMonitor);
                mediaOrderCompetitorMonitorMapper.insertUpdateEntity(competitorMonitor);
            }
        }
        if (createCompetitiveMonitoring.getDeleteCompetitorBrandId() != null && createCompetitiveMonitoring.getDeleteCompetitorBrandId().size() > 0) {
            //删除要删除的数据
            mediaOrderEntityQueryMapper.deleteBatchByIds(createCompetitiveMonitoring.getOrderId(), createCompetitiveMonitoring.getDeleteCompetitorBrandId());
        }
    }

    public List<FromItemResponse> getFromItemList(String itemGroup) {
        List<ItemData> itemDataList = commonMapper.getItemDataByItemGroup(itemGroup);
        Map<Long, List<ItemData>> groupByItemId = itemDataList.stream().collect(Collectors.groupingBy(ItemData::getItemId));
        List<FromItemResponse> result = new ArrayList<>();
        for (Map.Entry<Long, List<ItemData>> entry : groupByItemId.entrySet()) {
            List<ItemLabelResponse> itemLabelList = entry.getValue().stream().map(ItemLabelResponse::new).collect(Collectors.toList());
            result.add(new FromItemResponse(entry.getValue().get(0), itemLabelList));
        }
        return result;
    }

    public TCompetitor getCompetitor() {
        TCompetitor tCompetitor = new TCompetitor();
        List<String> tCompetitors = mediaOrderEntityQueryMapper.getCompetitor();
        tCompetitor.setCompetitorNameList(tCompetitors);
        return tCompetitor;
    }

    public File downloadTemplateToMediaResult() throws IOException {
        File file = this.downloadTemplate("媒体匹配结果文件模板", XlsMediaMatchingResult.class);
        return file;
    }

    public File downloadTemplate(String templateName, Class clz) throws IOException {
        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        String fileName = URLEncoder.encode(getExportFileName(tempDataPath, templateName), "UTF-8");
        File newFile = new File(fileName);
        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), clz);
        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }

    public List<TCompetitiveMonitoring> getCompetitiveMonitoringByOrderId(Integer orderId) {
        return mediaOrderEntityQueryMapper.getCompetitiveMonitoringByOrderId(orderId);
    }

    public void brandInspirationAndSuggestions(TUploadLaunchMonitoring tUploadLaunchMonitoring, Integer identifier) {
        MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(tUploadLaunchMonitoring.getId());
        this.equalAnnex(tUploadLaunchMonitoring.getThirdPartyBriefAttachmentId(), identifier);
        mediaOrder.setBrandInspirationSuggestionAttachmentId(tUploadLaunchMonitoring.getThirdPartyBriefAttachmentId().intValue());
        mediaOrderMapper.insertUpdateEntity(mediaOrder);
    }

    /**
     * 维护全域效果评估基础数据
     *
     * @param userKey 用户id
     * @param orderId 订单编号
     */
    private void createOrderEffectEstimateBase(Integer userKey, Integer orderId) {
        OrderEffectEstimateBase base = new OrderEffectEstimateBase();
        base.setOrderId(orderId);
        base.setPv("0");
        base.setUv("0");
        base.setCpm("0");
        base.setCpuv("0");
        base.setTauv("0");
        base.setEpcc("0");
        base.setTaReach("0");
        setCreatorInfo(userKey, base);
        setUpdaterInfo(userKey, base);
        orderEffectEstimateBaseMapper.insert(base);
    }


    /**
     * 根据订单id获取到媒体形式
     *
     * @param orderId 订单id
     */
    public List<MainResourceCategoryDataResponse> getOrderResourceCategory(Integer orderId, Boolean isBus) {
        List<String> categoryJsonList = mediaOrderEntityQueryMapper.getOrderResourceCategory(orderId);
        if (CollectionUtils.isEmpty(categoryJsonList)) {
            return Collections.emptyList();
        }
        Gson gson = new Gson();
        Type listType = new TypeToken<List<Integer>>() {
        }.getType();
        List<Integer> categoryIds = categoryJsonList.stream()
                .filter(StringUtils::isNotEmpty)
                .flatMap(json -> {
                    try {
                        List<Integer> ids = gson.fromJson(json, listType);
                        return ids != null ? ids.stream() : Stream.empty();
                    } catch (JsonSyntaxException e) {
                        log.error("转换类型失败 json：{}", json);
                        throw new RuntimeException();
                    }
                })
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }

        List<MainResourceCategoryData> dataListByiIds = mainResourceCategoryEntityQueryMapper.getMainResourceCategoryDataListByiIds(categoryIds);

        if (isBus) {
            return dataListByiIds.stream()
                    .filter(item -> "公交车身".equals(item.getCategoryName()))
                    .map(MainResourceCategoryDataResponse::new)
                    .collect(Collectors.toList());
        }
        return dataListByiIds.stream().map(MainResourceCategoryDataResponse::new).collect(Collectors.toList());
    }

    /**
     * 智选订单智选详情方位点位总览 订单分组
     *
     * @param criteria criteria
     * @return List<OrderSmartDetailsListData>
     */
    public PageResponse<OrderSmartDetailsListDataResponse> orderSmartDetailsList(SearchCriteria criteria) {
        Page<Object> page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<OrderSmartDetailsListData> searchResult = mediaOrderEntityQueryMapper.getOrderSmartDetailsListData(criteria);
        PageResponse<OrderSmartDetailsListDataResponse> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult.stream().map(OrderSmartDetailsListDataResponse::new).collect(Collectors.toList()));
        return pageResponse;
    }

    /**
     * 智选订单智选详情方位点位总览 媒体形式分组
     *
     * @param orderId 订单id
     * @return List<OrderSmartDetailsCategoryListData>
     */
    public List<OrderSmartDetailsCategoryListDataResponse> orderSmartDetailsCategoryList(Integer orderId) {
        List<OrderSmartDetailsCategoryListData> orderSmartDetailsCategoryListData = mediaOrderEntityQueryMapper.getOrderSmartDetailsCategoryListData(orderId);
        if (CollectionUtils.isEmpty(orderSmartDetailsCategoryListData)) {
            return null;
        }
        return orderSmartDetailsCategoryListData.stream().map(OrderSmartDetailsCategoryListDataResponse::new).collect(Collectors.toList());
    }

    /**
     * 智选订单智选详情方位点位总览 城市分组
     *
     * @param orderId    订单id
     * @param categoryId 分类id
     * @return List<OrderSmartDetailsCityListData>
     */
    public List<OrderSmartDetailsCityListDataResponse> orderSmartDetailsCityList(Integer orderId, Integer categoryId) {
        List<OrderSmartDetailsCityListData> cityListData = mediaOrderEntityQueryMapper.getOrderSmartDetailsCityListData(orderId, categoryId);
        if (CollectionUtils.isEmpty(cityListData)) {
            return null;
        }
        return cityListData.stream().map(OrderSmartDetailsCityListDataResponse::new).collect(Collectors.toList());
    }

    /**
     * 获取订单绑定方案的城市信息
     *
     * @param orderId 订单id
     * @return 城市编号
     */
    public List<String> getSelectionPlanRegion(Integer orderId) {
        List<String> cityList = mediaOrderEntityQueryMapper.getOrderResourceCity(orderId);
        if (CollectionUtils.isEmpty(cityList)) {
            return new ArrayList<>();
        }
        Gson gson = new Gson();
        Type listType = new TypeToken<List<String>>() {
        }.getType();
        return cityList.stream()
                .filter(StringUtils::isNotEmpty)
                .flatMap(json -> {
                    try {
                        List<String> code = gson.fromJson(json, listType);
                        return code != null ? code.stream() : Stream.empty();
                    } catch (JsonSyntaxException e) {
                        log.error("转换类型失败 json：{}", json);
                        throw new RuntimeException();
                    }
                })
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 查询数据库回显城市热力值状态
     *
     * @param request 请求对象
     * @return List<SelectBaseDataResponse>
     */
    public List<SelectBaseResponse> selectBaseData(SelectBaseDataRequest request) {
        List<String> selectionPlanRegion = getSelectionPlanRegion(request.getOrderId());
        if (CollectionUtils.isEmpty(selectionPlanRegion)) {
            return Collections.emptyList();
        }
        return baseSelectBaseData(request.getStatMonth(), selectionPlanRegion, request.getPackId());
    }

    public List<SelectBaseResponse> baseSelectBaseData(List<String> statMonthList, List<String> regionCodeList, Integer packId) {
        List<SelectBaseResponse> list = new ArrayList<>();
        for (String statMonth : statMonthList) {
            SelectBaseResponse selectBaseResponse = new SelectBaseResponse();
            selectBaseResponse.setStatMonth(statMonth);
            //获取热力值数据 根据城市 时间 获取
            List<GridHotDataOrder> gridHotDataList = mediaOrderEntityQueryMapper.getGridHotDataOrder(statMonth, regionCodeList);
            //获取ta数据
            List<GridHotDataOrder> gridTaHotDataList = mediaOrderEntityQueryMapper.getGridTAHotDataOrder(statMonth, packId, regionCodeList);
            //城市常驻人口
            List<StatisticsDimension> statisticsDimensionList = statisticsDimensionMapper
                    .getDimensionListByStatMonth(Lists.newArrayList(StatisticalType.RESIDENT_POPULATION.name(), StatisticalType.PASSENGER_POPULATION.name()), regionCodeList, statMonth);

            if (CollectionUtils.isEmpty(gridHotDataList) && CollectionUtils.isEmpty(gridTaHotDataList) && CollectionUtils.isEmpty(statisticsDimensionList)) {
                List<SelectBaseDataResponse> responseList = regionCodeList.stream().map(code -> {
                    MainDictRegionData byCode = mainDictRegionMapper.getByCode(code);
                    return new SelectBaseDataResponse(code, byCode.getId());
                }).collect(Collectors.toList());
                selectBaseResponse.setData(responseList);
            } else {
                Map<String, GridHotDataOrder> taHotDataOrderMap = gridTaHotDataList
                        .stream()
                        .collect(Collectors.toMap(
                                GridHotDataOrder::getCode,
                                Function.identity(),
                                (v1, v2) -> v1
                        ));

                Map<String, StatisticsDimension> dimensionMap = statisticsDimensionList.stream()
                        .collect(Collectors.toMap(
                                d -> d.getStatisticalType() + "_" + d.getRegionCode(),
                                d -> d,
                                (a, b) -> a
                        ));


                Map<String, GridHotDataOrder> hotDataOrderMap = gridHotDataList
                        .stream()
                        .collect(Collectors.toMap(
                                GridHotDataOrder::getCode,
                                Function.identity(),
                                (v1, v2) -> v1
                        ));
                List<SelectBaseDataResponse> responses = regionCodeList.stream()
                        .map(regionCode -> {
                            MainDictRegionData byCode = mainDictRegionMapper.getByCode(regionCode);
                            GridHotDataOrder gridHotDataOrder = hotDataOrderMap.get(regionCode);
                            GridHotDataOrder gridTaHotDataOrder = taHotDataOrderMap.get(regionCode);
                            StatisticsDimension residentDimension = dimensionMap.get(StatisticalType.RESIDENT_POPULATION.name() + "_" + regionCode);
                            StatisticsDimension passengerDimension = dimensionMap.get(StatisticalType.PASSENGER_POPULATION.name() + "_" + regionCode);

                            if (gridHotDataOrder == null && gridTaHotDataOrder == null && residentDimension == null && passengerDimension == null) {
                                return new SelectBaseDataResponse(regionCode, byCode.getId());
                            }
                            SelectBaseDataResponse selectBaseDataResponse = new SelectBaseDataResponse(regionCode, byCode.getId(), gridHotDataOrder, gridTaHotDataOrder);

                            //城市常驻人口
                            selectBaseDataResponse.setResidentPopulationTa(residentDimension != null && StringUtils.isNotEmpty(residentDimension.getDimensionValue()));

                            //城市客流人口
                            selectBaseDataResponse.setPassengerPopulationTa(passengerDimension != null && StringUtils.isNotEmpty(passengerDimension.getDimensionValue()));

                            return selectBaseDataResponse;
                        })
                        .collect(Collectors.toList());
                selectBaseResponse.setData(responses);
            }
            list.add(selectBaseResponse);
        }
        return list;
    }

    /**
     * 请求数据
     *
     * @param request LockDataRequest
     */
    @RedisLock(category = "lockData:", key = "#request.orderId + '_' + #request.categoryId", tryLock = 2000, lockTime = 1200000)
    public void lockData(Integer userKey, LockDataRequest request) {
        LockDataContext lockDataContext = new LockDataContext(request, userKey, null);
        String kpiType = lockDataContext.getTransitionType();
        lockCalculateDataProcessorFactory.execute(kpiType, lockDataContext);
        //lockDataKpiTypeProcessorFactory.execute(request.getKpiType(), lockDataContext);
    }

    /**
     * 计算数据
     *
     * @param request ComputationalDataRequest
     */
    public void computationalData(ComputationalDataRequest request) {
        //获取到每个形式指向的模板code
        MainResourceCategory mediumFormat = mainResourceCategoryMapper.getByIdEX(request.getCategoryId());
        String templateName = StringUtils.isEmpty(mediumFormat.getCalculate()) ? "BASE" : mediumFormat.getCalculate();
        ComputationalDataTemplate template = computationalDataTemplateFactory.getTemplate(templateName);
        //构建上下文
        template.execute(new ComputationalDataContext(request, request.getDataType(), mediumFormat.getVisibleAngleRatio()));
    }

    /**
     * 按媒介
     *
     * @param request 请求参数
     * @return List<MediumFormatCollectListResponse>
     */
    public List<MediumFormatCollectGroupListResponse> mediumFormatCollectList(MediumCollectListRequest request) {
        List<MediumFormatCollectListData> listData = orderEffectEstimateBaseManLandEntityQueryMapper
                .getCollectListData(request.getOrderId(), request.getExposureType(), request.getBizType());
        if (CollectionUtils.isEmpty(listData)) {
            return new ArrayList<>();
        }
        Map<String, List<MediumFormatCollectListData>> map = listData.stream()
                .collect(Collectors.groupingBy(MediumFormatCollectListData::getCategoryId));

        List<MediumFormatCollectGroupListResponse> listResponses = new ArrayList<>();

        for (Map.Entry<String, List<MediumFormatCollectListData>> entry : map.entrySet()) {
            MediumFormatCollectGroupListResponse response = new MediumFormatCollectGroupListResponse();
            Integer categoryId = Integer.parseInt(entry.getKey());
            response.setCategoryId(categoryId);
            List<MediumFormatCollectListData> valueList = entry.getValue();
            //获取汇总
            MediumFormatCollectListData mediumCollectListData = orderEffectEstimateBaseManLandEntityQueryMapper.getMediumCollectListData(request.getOrderId(), request.getBizType(), categoryId);
            response.setCollectData(new MediumFormatCollectListResponse(mediumCollectListData));
            response.setCollectList(valueList.stream().map(MediumFormatCollectListResponse::new).collect(Collectors.toList()));
            listResponses.add(response);
        }
        return listResponses;
    }


    /**
     * 按城市
     *
     * @param request 请求参数
     * @return List<MediumFormatCollectListResponse>
     */
    public List<CityCollectListResponse> cityCollectList(MediumCollectListRequest request) {
        List<MediumFormatCollectListData> listData = orderEffectEstimateBaseManLandEntityQueryMapper
                .getCollectListData(request.getOrderId(), request.getExposureType(), request.getBizType());
        if (CollectionUtils.isEmpty(listData)) {
            return new ArrayList<>();
        }
        return listData.stream().map(CityCollectListResponse::new).collect(Collectors.toList());
    }

    /**
     * 按城市
     *
     * @param request 请求参数
     * @return List<MediumFormatCollectListResponse>
     */
    public AllCollectListResponse allCollectList(MediumCollectListRequest request) {
        AllCollectListData listData = orderEffectEstimateBaseManLandEntityQueryMapper
                .getAllCollectListData(request.getOrderId(), request.getExposureType(), request.getBizType());
        if (listData == null) {
            return null;
        }
        return new AllCollectListResponse(listData);
    }

    /**
     * 设置计算重叠比例状态与系数
     *
     * @param request 请求参数
     */
    public void updateCalculateRatio(UpdateCalculateRatioRequest request) {
        /*
         * 重叠比例=（所有点位AOI的工作人口统计+所有点位AOI的居住人口统计 — 所有点位AOI的常驻人口统计）/所有点位AOI的常驻人口统计*100%
         * 最后重叠比例再去乘上系数
         */
//        OrderKpiValueData valueData = mediaOrderEntityQueryMapper.getOrderKpiValueData(request.getOrderId(), request.getDataType());
//
//        BigDecimal workPop = valueData.getAoiWorkPopulationCount() == null
//                ? BigDecimal.ZERO
//                : new BigDecimal(valueData.getAoiWorkPopulationCount());
//
//        BigDecimal dwellPop = valueData.getAoiDwellPopulationCount() == null
//                ? BigDecimal.ZERO
//                : new BigDecimal(valueData.getAoiDwellPopulationCount());
//
//        BigDecimal residentPop = valueData.getResidentPopulation() == null
//                ? BigDecimal.ZERO
//                : new BigDecimal(valueData.getResidentPopulation());
//
//
//        Double ratio;
//
//        if (residentPop.compareTo(BigDecimal.ZERO) == 0) {
//            ratio = BigDecimal.ZERO.doubleValue();
//        } else {
//            BigDecimal subtract = workPop.add(dwellPop).subtract(residentPop);
//            BigDecimal percent = subtract
//                    .divide(residentPop, 2, RoundingMode.HALF_UP)
//                    .multiply(BigDecimal.valueOf(1)).multiply(BigDecimal.valueOf(request.getRatioCoefficient()));
//            ratio = percent.doubleValue();
//        }
//
//        if (request.verifyDataType()) {
//            mediaOrderEntityQueryMapper.updateCalculateRatio(request.getCalculateRatio(), ratio, request.getOrderId());
//        } else {
//            mediaOrderEntityQueryMapper.updateEstimateCalculateRatio(request.getCalculateRatio(), ratio, request.getOrderId());
//        }
    }

    /**
     * 回显重叠比例系数与状态
     *
     * @param orderId 订单id
     * @return GetCalculateRatioResponse
     */
    public GetCalculateRatioResponse getCalculateRatio(Integer orderId) {
        MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(orderId);
        if (mediaOrder == null) {
            return null;
        }
        return new GetCalculateRatioResponse(mediaOrder);
    }

    /**
     * 按媒介导出
     *
     * @param request   MediumCollectListRequest
     * @param exportAll exportAll
     * @return File
     * @throws IOException IOException
     */
    public File exportMediumFormatCollectList(MediumCollectListRequest request, boolean exportAll) throws IOException {
        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        //获取到媒体形式数据
        List<MainResourceCategory> resourceCategoryList = mainResourceCategoryMapper.getByGroupLabel("MEDIUM_FORMAT");
        Map<Integer, MainResourceCategory> categoryMap = resourceCategoryList.stream().collect(Collectors.toMap(MainResourceCategory::getId, Function.identity()));

        String fileName = getExportFileName(tempDataPath, "exportMediumFormatCollectList");
        File newFile = new File(fileName);

        List<MediumFormatCollectListData> queryResult = orderEffectEstimateBaseManLandEntityQueryMapper.getCollectListData(request.getOrderId(), request.getExposureType(), request.getBizType());
        //分组然后添加汇总数据 因为要添加汇总所以没有做分页
        Map<String, List<MediumFormatCollectListData>> map = queryResult.stream()
                .collect(Collectors.groupingBy(MediumFormatCollectListData::getCategoryId));
        //处理汇总数据
        for (Map.Entry<String, List<MediumFormatCollectListData>> entry : map.entrySet()) {
            Integer categoryId = Integer.parseInt(entry.getKey());
            List<MediumFormatCollectListData> valueList = entry.getValue();
            //获取汇总
            MediumFormatCollectListData mediumCollectListData = orderEffectEstimateBaseManLandEntityQueryMapper.getMediumCollectListData(request.getOrderId(), request.getBizType(), categoryId);
            if (mediumCollectListData != null) {
                valueList.add(mediumCollectListData);
            }
        }
        queryResult = map.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), XlsExportMediumFormatCollectList.class);
        List<XlsExportMediumFormatCollectList> appCallHistories = queryResult.stream().map(data -> new XlsExportMediumFormatCollectList(data, categoryMap)).collect(Collectors.toList());
        workbook = XLSWriter.appendLineFromObject("sheet1", appCallHistories, XlsExportMediumFormatCollectList.class, workbook);
        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }


    public File exportAllCollectList(MediumCollectListRequest request, boolean exportAll) throws IOException {
        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        String fileName = getExportFileName(tempDataPath, "exportAllCollectList");
        File newFile = new File(fileName);
        AllCollectListData queryResult = orderEffectEstimateBaseManLandEntityQueryMapper.getAllCollectListData(request.getOrderId(), request.getExposureType(), request.getBizType());
        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), XlsAllCollectData.class);
        XlsAllCollectData xlsAllCollectData = new XlsAllCollectData(queryResult);
        workbook = XLSWriter.appendLineFromObject("sheet1", Collections.singletonList(xlsAllCollectData), XlsExportMediumFormatCollectList.class, workbook);
        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }

    /**
     * 按城市导出
     *
     * @param request   请求参数
     * @param exportAll exportAll
     * @return File
     * @throws IOException IOException
     */
    public File exportCityCollectList(MediumCollectListRequest request, boolean exportAll) throws IOException {
        int startPage = EXPORT_START_PAGE;
        int pageLimit = EXPORT_PAGE_LIMIT;
        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        String fileName = getExportFileName(tempDataPath, "exportCityCollectListSearchCriteria");
        File newFile = new File(fileName);

        List<MainResourceCategory> resourceCategoryList = mainResourceCategoryMapper.getByGroupLabel("MEDIUM_FORMAT");
        Map<Integer, MainResourceCategory> categoryMap = resourceCategoryList.stream().collect(Collectors.toMap(MainResourceCategory::getId, Function.identity()));

        PageHelper.startPage(startPage, pageLimit, false);
        List<MediumFormatCollectListData> queryResult = orderEffectEstimateBaseManLandEntityQueryMapper
                .getCollectListData(request.getOrderId(), request.getExposureType(), request.getBizType());
        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), XlsCityCollectList.class);

        while (!queryResult.isEmpty()) {
            List<XlsCityCollectList> appCallHistories = queryResult.stream().map(data -> new XlsCityCollectList(data, categoryMap)).collect(Collectors.toList());
            workbook = XLSWriter.appendLineFromObject("sheet1", appCallHistories, XlsMainMediaTag.class, workbook);

            if (exportAll) {
                if (startPage * pageLimit >= EXPORT_LIMIT_LOOP_COUNT * EXPORT_PAGE_LIMIT) {
                    log.warn("导出数据超过可导出限制（不符合实时导出场景），如需变更自行更改");
                    break;
                }
                startPage++;
                PageHelper.startPage(startPage, pageLimit, false);
                queryResult = orderEffectEstimateBaseManLandEntityQueryMapper.getCollectListData(request.getOrderId(), request.getExposureType(), request.getBizType());
                continue;
            }
            break;
        }

        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }

    /**
     * 确认数据
     *
     * @param request ConfirmTheDataRequest
     */
    public void confirmTheData(ConfirmTheDataRequest request) {
        mediaOrderEntityQueryMapper.confirmTheData(request);
        //汇总的也确认上去
        if (request.getExposureType().equals(ExposureType.MEDIUM_FORMAT.name())) {
            request.setExposureType(ExposureType.MEDIUM_FORMAT_COLLECT.name());
            mediaOrderEntityQueryMapper.confirmTheData(request);
        }
    }

    /**
     * 计算点位计算数量
     *
     * @param request CalculatePlacementCountRequest
     * @return CalculatePlacementCountResponse
     */
    public CalculatePlacementCountResponse calculatePlacementCount(CalculatePlacementCountRequest request) {
        return mediaOrderEntityQueryMapper.calculatePlacementCount(request);
    }

    /**
     * 生成总览
     *
     * @param request CreateOverviewDataRequest
     */
    public void createOverviewData(CreateOverviewDataRequest request) {
        //判断下有没有没有计算的数据
//        Integer count = mediaOrderEntityQueryMapper.selectOrderAllCollectDataNotCount(request.getOrderId(), request.getBizType());
//        if (count > 0) {
//            throw new NotCalculateException();
//        }
        EffectCollectResolver resolver = effectCollectResolverFactory.getResolver(request.getDataType());
        if (resolver == null) {
            log.error("没有找到该适配的类型：{}", request.getDataType());
            return;
        }

        resolver.resolver(request);
    }

    /**
     * 请求热力值
     *
     * @param request HotAdCodeRequest
     */
    public void getHotDataByCode(Integer userKey, HotAdCodeRequest request) {
        for (String code : request.getTypeInfo()) {
            //判断当前请求的地区时间 在人地数据中有没有
            PersonLandData personLandData = personLandDataMapper.getByStatMonthCityCode(request.getStatMonth(), code);
            String timeType = request.getStatisticalType().name()
                    .equals(StatisticalType.PASSENGER_HOUR_POPULATION.name())
                    ? TimeType.HOUR.getCode() : TimeType.DAY.getCode();
            HotContext hotContext = new HotContext(request,
                    "3",
                    1000,
                    "person_land_data",
                    personLandData == null ? -1 : personLandData.getId(),
                    timeType,
                    code,
                    userKey);
            hotAdCodeFactory.execute(request.getStatisticalType(), hotContext);
        }
    }

    /**
     * 请求城市人口
     *
     * @param userKey 用户id
     * @param request 请求参数
     */
    public void getStatisticalUv(Integer userKey, HotAdCodeRequest request) {
        for (String code : request.getTypeInfo()) {
            String timeType = request.getStatisticalType().name().equals(StatisticalType.PASSENGER_HOUR_POPULATION.name()) ? TimeType.HOUR.getCode() : TimeType.DAY.getCode();
            HotContext context = new HotContext(request,
                    "3",
                    null,
                    "person_land_data",
                    null,
                    timeType,
                    code,
                    userKey);
            hotAdCodeFactory.executeUv(request.getStatisticalType(), context);
        }
    }


    /**
     * 订单智选模式动态列表
     *
     * @param categoryId 分类id
     * @return List<MetaFieldInfo>
     */
    public List<MetaFieldInfo> getOrderPlacementSnLabel(Integer categoryId) {
        //获取到 当前分类 二级三级分类名称 如社区/电梯框架
        String categoryName = mainResourceCategoryMapper.getCategoryNameConcat(categoryId);
        List<MediaSelectionPlanMenuFieldAttr> menuFieldAttrs = mediaSelectionPlanMenuFieldAttrMapper.getFieldAttrByCategoryName(categoryName);
        if (CollectionUtils.isEmpty(menuFieldAttrs)) {
            //走公共的模版
            String name = "商圈/LED大屏";
            menuFieldAttrs = mediaSelectionPlanMenuFieldAttrMapper.getFieldAttrByCategoryName(name);
        }
        List<MetaFieldInfo> placementSnLabel = convertToMetaFieldInfoList(menuFieldAttrs);
        //把智选详情点位的 公共字段加进去
        List<MetaFieldInfo> metaFieldInfoList = MetaFieldUtils.getMetaFieldInfoList(OrderPlacementSnLabelFieldInfo.class);
        placementSnLabel.addAll(metaFieldInfoList);
        MetaFieldUtils.updateColumnIndex(placementSnLabel);
        return placementSnLabel.stream()
                .sorted(Comparator.comparingInt(MetaFieldInfo::getColumnIndex))
                .collect(Collectors.toList());
    }

    /**
     * 订单智选模式动态列表数据
     *
     * @param request 请求参数
     * @return PageResponse<List < Map < String, Object>>>
     */
    public PageResponse<Map<String, Object>> getOrderPlacementList(OrderPlacementListRequest request) {
        // 分页处理
        SearchCriteria criteria = request.getCriteria();
        Page<Object> page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        // 获取订单数据
        List<OrderMetaFieldInfo> orderPlacementList = mediaOrderEntityQueryMapper.getOrderPlacementList(criteria, request.getDataType());
        // 获取当前分类下的字段定义（表头）
        List<MetaFieldInfo> orderPlacementSnLabel = getOrderPlacementSnLabel(request.getCategoryId());
        List<Map<String, Object>> searchResult = new ArrayList<>();
        Table<String, String, String> resourceCategoryTableAsMap = commonService.getResourceCategoryTableAsMap();
        // 动态组装基础信心与事件返回结果
        for (OrderMetaFieldInfo info : orderPlacementList) {
            Map<String, Object> commonObjectMap = PageResponseBuilder.buildObjectMap(info, orderPlacementSnLabel, null, resourceCategoryTableAsMap);
            searchResult.add(commonObjectMap);
        }
        PageResponse<Map<String, Object>> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult);
        return pageResponse;
    }

    /**
     * 详情展示字段
     *
     * @param categoryId 分类
     * @return List<MetaFieldInfo>
     */
    public List<MetaFieldInfo> getByIdOrderPlacementDetailsLabel(Integer categoryId) {
        return getOrderPlacementSnLabel(categoryId);
    }

    /**
     * 详情展示数据
     *
     * @param placementId 点位id
     * @param orderId     订单id
     * @param categoryId  分类id
     * @return Map<String, Object>
     */
    public Map<String, Object> getByIdOrderPlacementDetails(Long placementId, Integer orderId, Integer categoryId) {
        OrderMetaFieldInfo info = mediaOrderEntityQueryMapper.getByIdOrderPlacementDetails(placementId, orderId, "ORDER");
        List<MetaFieldInfo> orderPlacementSnLabel = getByIdOrderPlacementDetailsLabel(categoryId);
        Table<String, String, String> resourceCategoryTableAsMap = commonService.getResourceCategoryTableAsMap();
        return PageResponseBuilder.buildObjectMap(info, orderPlacementSnLabel, null, resourceCategoryTableAsMap);
    }

    /**
     * 导出文件
     *
     * @param response HttpServletResponse
     * @param request  OrderPlacementListRequest
     */
    public void exportOrderMetaPlacementSnList(HttpServletResponse response, OrderPlacementListRequest request) {
        PageResponseBuilder.exportExcelWithMetaHeaderScroll(
                response,
                request.getCriteria(),
                criteria -> getOrderPlacementSnLabel(request.getCategoryId()),
                (criteria, page) -> {
                    OrderPlacementListRequest copied = new OrderPlacementListRequest();
                    BeanUtils.copyProperties(request, copied);
                    copied.setCriteria(criteria);
                    return getOrderPlacementList(copied);
                },
                "订单人地数据投放点位列表导出",
                true
        );
    }

}
