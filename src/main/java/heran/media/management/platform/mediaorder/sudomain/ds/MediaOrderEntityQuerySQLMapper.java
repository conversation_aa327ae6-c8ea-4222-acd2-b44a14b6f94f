package heran.media.management.platform.mediaorder.sudomain.ds;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.List;

public class MediaOrderEntityQuerySQLMapper {
    public String select(SearchCriteria criteria) {
        return new SQL() {
            {
                SELECT("m1.id as id,m1.order_sn as orderSn,m1.order_type orderType,m1.order_name as orderName,m2.brand_name as brandName,m2.group_name as groupName,m1.create_time as createTime,m1.update_time as updateTime,m1.order_status as orderStatus,m1.schedule_attachment_id as isScheduling,m1.third_party_brief_attachment_id as isUploadMonitoring,m2.delivery_start_time as deliveryStartTime,m2.delivery_end_time as deliveryEndTime,COUNT( m3.id ) planCount ");
                FROM("media_order m1");
                LEFT_OUTER_JOIN("media_placement_base_sales_plan m2 ON m1.base_plan_id = m2.id");
                LEFT_OUTER_JOIN("media_order_refer_selection_plan m3 ON m1.id = m3.order_id");
                if (criteria.getCriterias() != null && !criteria.getCriterias().isEmpty()) {
                    for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                        if (StringUtils.isBlank(cri.getKey())) {
                            continue;
                        }
                        if ("orderSn".equals(cri.getKey()) && cri.getValue() != null) {
                            WHERE("m1.order_sn like '%" + cri.getValue() + "%'");
                        } else if ("orderType".equals(cri.getKey()) && cri.getValue() != null) {
                            WHERE("m1.order_type = '" + cri.getValue() + "'");
                        } else if ("orderStatus".equals(cri.getKey()) && cri.getValue() != null) {
                            List<String> conditions = getStrings(cri);
                            if (!conditions.isEmpty()) {
                                String whereClause = "(" + String.join(" OR ", conditions) + ")";
                                WHERE(whereClause);
                            }
                        } else if ("orderName".equals(cri.getKey()) && cri.getValue() != null) {
                            WHERE("m1.order_name like '%" + cri.getValue() + "%'");
                        } else if ("brandName".equals(cri.getKey()) && cri.getValue() != null) {
                            WHERE("m2.brand_name like '%" + cri.getValue() + "%'");
                        } else if ("groupName".equals(cri.getKey()) && cri.getValue() != null) {
                            WHERE("m2.group_name like '%" + cri.getValue() + "%'");
                        } else if ("deliveryStartTime".equals(cri.getKey())) {
                            if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                                WHERE("m2.delivery_start_time between '" + cri.getMinValue() + "' AND '" + cri.getMaxValue() + "'");
                            } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                                WHERE("m2.delivery_start_time >= '" + cri.getMinValue() + "'");
                            } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                                WHERE("m2.delivery_start_time <= '" + cri.getMaxValue() + "'");
                            } else if (cri.getValue() != null) {
                                WHERE("m2.delivery_start_time = '" + cri.getValue() + "'");
                            }
                        } else if ("deliveryEndTime".equals(cri.getKey())) {
                            if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                                WHERE("m2.delivery_end_time between '" + cri.getMinValue() + "' AND '" + cri.getMaxValue() + "'");
                            } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                                WHERE("m2.delivery_end_time >= '" + cri.getMinValue() + "'");
                            } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                                WHERE("m2.delivery_end_time <= '" + cri.getMaxValue() + "'");
                            } else if (cri.getValue() != null) {
                                WHERE("m2.delivery_end_time = '" + cri.getValue() + "'");
                            }
                        } else if ("isDeleted".equals(cri.getKey()) && cri.getValue() != null) {
                            WHERE("m1.is_deleted = '" + cri.getValue() + "'");
                        } else {
                            throw new RuntimeException("Not supported properties");
                        }
                    }
                }
                WHERE("m2.is_deleted = false");
                GROUP_BY("m1.id");
                ORDER_BY("m1.create_time DESC");
            }

            private List<String> getStrings(SearchCriteria.Criteria cri) {
                List<String> conditions = new ArrayList<>();
                for (String code : cri.getValue().split(",")) {
                    switch (code.trim()) {
                        case "DELIVERING":
                            conditions.add("CURRENT_DATE BETWEEN DATE(m2.delivery_start_time) AND DATE(m2.delivery_end_time) AND m1.order_status != 'PENDING_SUBMIT'");
                            break;
                        case "PENDING_DELIVERY":
                            conditions.add("CURRENT_DATE < DATE(m2.delivery_start_time) AND m1.order_status != 'PENDING_SUBMIT'");
                            break;
                        case "DELIVERY_COMPLETE":
                            conditions.add("CURRENT_DATE > DATE(m2.delivery_end_time) AND m1.order_status != 'PENDING_SUBMIT'");
                            break;
                        case "PENDING_SUBMIT":
                            conditions.add("m1.order_status = '" + code + "'");
                            break;
                    }
                }
                return conditions;
            }
        }.toString();
    }


    public String getOrderSmartDetailsListData(SearchCriteria criteria) {
        return new SQL() {{

            SELECT("t1.order_name, COUNT( DISTINCT t3.city_id ) AS cityCount,COUNT( DISTINCT t3.medium_lv3_category_id ) AS categoryCount,t4.delivery_start_time,t4.delivery_end_time," +
                    "TIMESTAMPDIFF(DAY, t4.delivery_start_time, t4.delivery_end_time) + 1 AS period_days ,COUNT( t3.id ) AS placementCount,t1.update_time,t4.budget ");
            FROM("media_order t1");
            JOIN("media_order_refer_selection_plan t2 ON t1.id = t2.order_id");
            JOIN("media_placement_base_sales_plan t4 ON t4.id = t1.base_plan_id");
            LEFT_OUTER_JOIN("main_calendar_placement t3 ON t2.selection_plan_id = t3.plan_id");
            LEFT_OUTER_JOIN("main_calendar_placement_ext t5 ON t3.id = t5.calendar_placement_id");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    } else if ("orderId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.id = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            WHERE("t3.is_deleted = false AND t2.is_deleted = false");
            WHERE("t3.execute_quotation = true AND (t3.placement_cost > 0 OR t5.launch > 0)");
            GROUP_BY("t1.id");
        }}.toString();
    }


    public String selectCalendarPlacement(Integer categoryId, SearchCriteria criteria, String type) {
        return new SQL() {{
            SELECT("t3.medium_lv3_category_id,t3.placement_sn,t3.aoi_coordinates,t3.delivery_start_time,t3.delivery_end_time,t3.vacancy_position,t4.*,t5.brighten,t5.mileage,t5.launch_time,t5.launch_rate,t5.launch");
            SELECT("(IFNULL( t3.placement_cost, 0 ) * IFNULL( t5.launch_count, 0 ) * ( DATEDIFF( t3.delivery_end_time, t3.delivery_start_time ) + 1 )) + (IFNULL( t5.launch, 0 ) * IFNULL( t5.launch_period, 0 ) * IFNULL( t5.issue_amount, 0 )) + (IFNULL( t5.launch, 0 ) * IFNULL( t5.production_amount, 0 )) placement_cost");
            FROM("media_order t1");
            JOIN("media_order_refer_selection_plan t2 ON t1.id = t2.order_id");
            JOIN("main_calendar_placement t3 ON t2.selection_plan_id = t3.plan_id");
            JOIN("main_calendar_order_kpi t4 ON t4.order_id = t1.id AND t3.id = t4.calendar_placement_id");
            LEFT_OUTER_JOIN("main_calendar_placement_ext t5 ON t5.calendar_placement_id = t3.id");
            LEFT_OUTER_JOIN("main_dict_region t6 ON t3.city_id = t6.id");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    } else if ("idList".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t3.id IN (" + cri.getValue() + ")");
                    } else if ("cityId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t6.code IN (" + cri.getValue() + ")");
                    } else if ("orderId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.id = " + cri.getValue());
                    } else if ("categoryId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t3.medium_lv3_category_id = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            WHERE("t4.kpi_type = '" + type + "'");
            WHERE("t2.is_deleted = false");
            WHERE("t3.execute_quotation = true AND (t3.placement_cost > 0 OR t5.launch > 0)");
        }}.toString();
    }

    public String selectPlacementList(SearchCriteria criteria, String type) {
        String baseSql = buildBaseSql(type);
        StringBuilder whereSql = new StringBuilder(" WHERE t3.is_deleted = false AND t3.execute_quotation = true AND t2.is_deleted = false AND (t3.placement_cost > 0 OR t5.launch > 0)");
        if (CollectionUtils.isNotEmpty(criteria.getCriterias())) {
            for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                if (StringUtils.isBlank(cri.getKey())) {
                    continue;
                }
                if ("categoryId".equals(cri.getKey())) {
                    whereSql.append(" AND t3.medium_lv3_category_id = ").append(cri.getValue());
                } else if ("orderId".equals(cri.getKey())) {
                    whereSql.append(" AND t1.id = ").append(cri.getValue());
                } else if ("cityId".equals(cri.getKey())) {
                    whereSql.append(" AND (t6.code IN(").append(cri.getValue()).append(") OR t7.code IN(").append(cri.getValue()).append(") OR t8.code IN(").append(cri.getValue()).append("))");
                } else {
                    throw new RuntimeException("Not supported properties");
                }
            }
        }

        return baseSql + whereSql;
    }

    public String buildBaseSql(String type) {
        return new SQL() {{
            SELECT("t3.id calendarPlacementId, t3.placement_sn,t3.media_name,t3.media_position,t3.resource_provider,t3.resource_provider_type,t3.province_id,t3.city_id,t3.district_id,t3.medium_lv1_category_id,t3.medium_lv2_category_id,t3.medium_lv3_category_id,t3.pack_id,t10.pack_name,t3.aoi_coordinates,t3.vacancy_position,t3.buffer_area,t3.buffer_type,t3.delivery_start_time,t3.delivery_end_time,t3.placement_cost,t3.ta_hot_value,t3.aoi_area,t3.extend_aoi_coordinates,t3.aoi_hot_value,t3.longitude,t3.latitude");
            SELECT("t5.extra_attrs,t5.brighten,t5.launch_time,t5.launch_rate,t5.mileage,t5.bus_name,t5.launch,t5.issue_amount,t5.production_amount,t5.launch_count,t5.launch_period");
            SELECT("t4.stat_month orderExposureStatMonth,t4.population orderExposurePopulation,t4.aoi_resident_population,t4.aoi_dwell_population,t4.aoi_work_population,t4.aoi_passenger_population,t4.aoi_passenger_hour_population,t4.aoi_ta_population,t4.aoi_ta_count,t4.heaven_proportion,t4.visible_angle,t4.pv,t4.uv,t4.af,t4.tauv,t4.cpm,t4.cpuv,t4.cpta,t4.kpi_type");
            SELECT("t6.region_name provinceName,t7.region_name cityName,t8.region_name districtName");
            SELECT("t9.stat_month statMonth,t9.statistical_type statisticalType");
            FROM("media_order t1");
            JOIN("media_order_refer_selection_plan t2 ON t1.id = t2.order_id");
            JOIN("main_calendar_placement t3 ON t2.selection_plan_id = t3.plan_id");
            LEFT_OUTER_JOIN("main_calendar_placement_ext t5 ON t5.calendar_placement_id = t3.id");
            LEFT_OUTER_JOIN("main_calendar_order_kpi t4 ON t4.calendar_placement_id = t3.id AND t4.order_id = t1.id AND t4.kpi_type = '" + type + "'");
            JOIN("main_dict_region t6 ON t6.id = t3.province_id");
            JOIN("main_dict_region t7 ON t7.id = t3.city_id");
            LEFT_OUTER_JOIN("main_dict_region t8 ON t8.id = t3.district_id");
            LEFT_OUTER_JOIN("selection_plan_hottype_detail t9 ON t9.plan_id = t3.plan_id");
            LEFT_OUTER_JOIN("media_ta_crowd_pack t10 ON t10.id = t3.pack_id");
        }}.toString();
    }

    public String getByIdOrderPlacementDetails(Long placementId, Integer orderId, String type) {
        String baseSql = buildBaseSql(type);
        String whereSql = " WHERE t3.is_deleted = false AND t2.is_deleted = false " + " AND t3.id = " + placementId +
                " AND t1.id = " + orderId +
                " GROUP BY t3.id";
        return baseSql + whereSql;
    }
}
