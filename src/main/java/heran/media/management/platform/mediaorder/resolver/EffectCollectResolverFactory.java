package heran.media.management.platform.mediaorder.resolver;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class EffectCollectResolverFactory {
    private static final Map<String, EffectCollectResolver> RESOLVER_MAP = new HashMap<>();

    public EffectCollectResolverFactory(EffectCollectResolver... processors) {
        for (EffectCollectResolver resolver : processors) {
            RESOLVER_MAP.put(resolver.getType(), resolver);
        }
    }

    public EffectCollectResolver getResolver(String type) {
        return RESOLVER_MAP.get(type);
    }

}
