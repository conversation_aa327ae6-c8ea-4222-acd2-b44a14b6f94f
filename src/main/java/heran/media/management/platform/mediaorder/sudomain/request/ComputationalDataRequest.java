package heran.media.management.platform.mediaorder.sudomain.request;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ComputationalDataRequest {
    @ApiModelProperty("订单(ORDER),效果评估(APPRAISAL)")
    private String dataType;
    @ApiModelProperty("订单id")
    private Integer orderId;
    @ApiModelProperty("三级分类id")
    private Integer categoryId;
    @ApiModelProperty("天级时间占比")
    private String dayTimeProportion;
    @ApiModelProperty("可视角度")
    private String visibleAngle;
    @ApiModelProperty("可视模型")
    private Boolean visualModel;
    @ApiModelProperty("点位列表筛选条件 idList(点位id数组)，cityIds(城市筛选)")
    private SearchCriteria criteria;
}
