package heran.media.management.platform.mediaorder.sudomain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class UpdateMediaOrderReq {
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("订单编号")
    private String orderSn;
    @ApiModelProperty("订单名称")
    private String orderName;
    @ApiModelProperty("引用方案id")
    private Integer referSelectionPlanId;
    @ApiModelProperty("营销方案")
    private TMediaPlacementPlanReq tMediaPlacementPlanReq;
    @ApiModelProperty("意向渗透场景")
    private List<String> intentionScenes;
    @ApiModelProperty("POI/AOI标签补充")
    private String intentionSceneTags;
    @ApiModelProperty("渠道{online(线上)，offline(线下)}")
    private String channelMode;
    @ApiModelProperty("线上渠道配合数据")
    private List<String> onlineChannelData;
    @ApiModelProperty("线上渠道补充")
    private String onlineChannelRemark;
    @ApiModelProperty("是否提供门店地址")
    private Boolean offlineStoreSupport;
    @ApiModelProperty("线下门店渠道附件id")
    private Long offlineStoreAttachmentId;
    @ApiModelProperty("线下渠道补充")
    private String offlineChannelRemark;
    @ApiModelProperty("可开放数据授权平台")
    private List<String> authOpenPlatform;
    @ApiModelProperty("选择服务对象")
    private TSelectService selectService;
    //    @ApiModelProperty("人群标签")
//    private List<UpdateMainCaseTagRel> tagIds;
    @ApiModelProperty("人群标签")
    private List<Integer> tagIds;
}
