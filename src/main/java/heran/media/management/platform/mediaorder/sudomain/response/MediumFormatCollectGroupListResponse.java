package heran.media.management.platform.mediaorder.sudomain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MediumFormatCollectGroupListResponse {
    @ApiModelProperty("媒介形式")
    private Integer categoryId;
    @ApiModelProperty("汇总数据")
    private MediumFormatCollectListResponse collectData;
    @ApiModelProperty("列表数据")
    private List<MediumFormatCollectListResponse> collectList;
}
