package heran.media.management.platform.mediaorder.sudomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class ConfirmTheDataRequest {
    @ApiModelProperty("订单id")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;
    @ApiModelProperty("分组类型 MEDIUM_FORMAT(按媒介)，CITY_FORMAT(按城市)，ALL_FORMAT(关键曝光指标)")
    private String exposureType;
    @ApiModelProperty("事件类型 指标类型ORDER(订单)，APPRAISAL(全域效果评估)")
    private String bizType;
}
