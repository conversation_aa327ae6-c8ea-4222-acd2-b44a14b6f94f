package heran.media.management.platform.mediaorder.sudomain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class TMediaPlacementPlanReq {
    @ApiModelProperty("方案id")
    private Integer planId;

    @ApiModelProperty("所属行业分类id")
    private Integer belongIndustryCategoryId;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("集团名称")
    private String groupName;

    @ApiModelProperty("推广产品")
    private String productPromotion;

    @ApiModelProperty("竞争对手")
    private String competitor;

    @ApiModelProperty("品牌现状")
    private String brandSituation;

    @ApiModelProperty("现阶段需求/痛点")
    private String presentStageRequirement;

    @ApiModelProperty("产品场景")
    private String productProspect;

    @ApiModelProperty("销售渠道")
    private String distributionChannel;

    @ApiModelProperty("产品定价")
    private String productPricing;

    @ApiModelProperty("传播目的")
    private List<String> disseminatePose;

    @ApiModelProperty("核心销售省市区编码")
    private List<String> coreSalesRegions;

    @ApiModelProperty("投放开始时间")
    private Date deliveryStartTime;

    @ApiModelProperty("投放结束时间")
    private Date deliveryEndTime;

    @ApiModelProperty("投放预算")
    private String budget;

    @ApiModelProperty("方案名称")
    private String planName;
}
