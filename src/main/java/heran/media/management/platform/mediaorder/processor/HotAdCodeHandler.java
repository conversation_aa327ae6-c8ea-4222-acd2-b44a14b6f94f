package heran.media.management.platform.mediaorder.processor;

import heran.media.sharelib.domain.bo.StatisticalType;

/**
 * <AUTHOR>
 */
public interface HotAdCodeHandler {
    /**
     * 热力值处理
     *
     * @param context HotContext
     */
    void handle(HotContext context);

    /**
     * 洞察维度
     *
     * @param context HotContext
     */
    void dimensionUvHandle(HotContext context);

    /**
     * getStatisticalType
     *
     * @return StatisticalType
     */
    StatisticalType getStatisticalType();
}
