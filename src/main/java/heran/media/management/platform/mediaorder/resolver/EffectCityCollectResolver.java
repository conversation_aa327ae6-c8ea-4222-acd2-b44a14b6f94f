package heran.media.management.platform.mediaorder.resolver;

import com.alibaba.nacos.common.utils.CollectionUtils;
import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderEntityQueryMapper;
import heran.media.management.platform.mediaorder.sudomain.dto.OrderEstimateBaseLandData;
import heran.media.management.platform.mediaorder.sudomain.dto.OrderKpiValueData;
import heran.media.management.platform.mediaorder.sudomain.request.CreateOverviewDataRequest;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.db.mapper.main.OrderEffectEstimateBaseManLandMapper;
import heran.media.sharelib.domain.db.model.main.StatisticsDimension;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class EffectCityCollectResolver extends EffectCollectResolver {

    @Resource
    private OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper;
    @Resource
    private MediaOrderEntityQueryMapper mediaOrderEntityQueryMapper;


    @Override
    public void resolver(CreateOverviewDataRequest request) {
        //加之前先删除一下之前的
        orderEffectEstimateBaseManLandMapper.deleteByType(request.getOrderId(), request.getBizType(), request.getDataType());
        List<OrderEstimateBaseLandData> landData = mediaOrderEntityQueryMapper.selectOrderCityEstimateBaseLandData(request.getOrderId(), request.getBizType());
        if (CollectionUtils.isEmpty(landData)) {
            return;
        }

        //获取当前的人口口径
        OrderEstimateBaseLandData data = landData.get(0);
        String statisticalType = "RESIDENT".equals(data.getPopulation()) ? StatisticalType.RESIDENT_POPULATION.name() : StatisticalType.PASSENGER_POPULATION.name();
        //城市人口数
        Map<String, BigDecimal> mapPopulation = getMapPopulation(statisticalType, request);

        //计算重叠比例
        Double ratioCoefficient = calculateRatio(request);

        collectInsertData(landData, mapPopulation, request, ratioCoefficient, null);
    }

    /**
     * 计算城市人口数据
     *
     * @param statisticalType 类型
     * @param request         查询条件
     * @return Map<String, Integer>
     */
    private Map<String, BigDecimal> getMapPopulation(String statisticalType, CreateOverviewDataRequest request) {
        Map<String, Set<String>> cityUvSet = getCityUvSet(request);

        //statMonth
        List<String> statMonthList = cityUvSet.values().stream()
                .flatMap(Set::stream)
                .collect(Collectors.toList());

        //regionCode
        List<String> regionCodeList = new ArrayList<>(cityUvSet.keySet());

        Map<String, StatisticsDimension> cityResidentMap = getCityPopulationDataMap(regionCodeList, statMonthList, statisticalType);

        Map<String, BigDecimal> mapPopulation = new HashMap<>(8);

        getPopulationDataMap(cityResidentMap, mapPopulation, cityUvSet);
        return mapPopulation;
    }


    /**
     * 计算重叠比例
     *
     * @param request 请求参数
     * @return 重叠比例
     */
    private Double calculateRatio(CreateOverviewDataRequest request) {
        if (!request.getCalculateRatio()) {
            return null;
        }
        OrderKpiValueData orderKpiValueData = mediaOrderEntityQueryMapper.getOrderKpiValueData(request.getOrderId(), request.getBizType());
        double ratio = 0;
        if (orderKpiValueData == null) {
            return ratio;
        }

        BigDecimal workPop = orderKpiValueData.getAoiWorkPopulationCount() == null
                ? BigDecimal.ZERO
                : new BigDecimal(orderKpiValueData.getAoiWorkPopulationCount());

        BigDecimal residentPop = orderKpiValueData.getResidentPopulation() == null
                ? BigDecimal.ZERO
                : new BigDecimal(orderKpiValueData.getResidentPopulation());

        BigDecimal dwellPop = orderKpiValueData.getAoiDwellPopulationCount() == null
                ? BigDecimal.ZERO
                : new BigDecimal(orderKpiValueData.getAoiDwellPopulationCount());

        if (residentPop.compareTo(BigDecimal.ZERO) == 0) {
            ratio = BigDecimal.ZERO.doubleValue();
        } else {
            BigDecimal subtract = workPop.add(dwellPop).subtract(residentPop);

            BigDecimal percent = subtract
                    .divide(residentPop, 2, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(1));

//            if (request.getRatioCoefficient() != null) {
//                percent = percent.multiply(BigDecimal.valueOf(request.getRatioCoefficient()));
//            }
            ratio = percent.doubleValue();
        }

        return ratio;
    }



    @Override
    public String getType() {
        return "CITY_FORMAT";
    }
}
