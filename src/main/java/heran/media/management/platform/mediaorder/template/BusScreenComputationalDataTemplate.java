package heran.media.management.platform.mediaorder.template;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.mediaorder.sudomain.dto.CalendarPlacementOrderData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 公交车身
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BusScreenComputationalDataTemplate extends LedScreenComputationalDataTemplate {

    @Override
    protected double getHeavenProportion(ComputationalDataContext kpiQueryContext, CalendarPlacementOrderData data) {
        //计算天级时间占比
        if (StringUtils.isNotEmpty(kpiQueryContext.getRequest().getDayTimeProportion())) {
            BigDecimal proportion = new BigDecimal(kpiQueryContext.getRequest().getDayTimeProportion()).divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_UP);
            return proportion.doubleValue();
        }

        //没有传 判断能不能计算
        if (StringUtils.isNotEmpty(kpiQueryContext.getVisibleAngleRatio())) {
            if (StringUtils.isNotEmpty(data.getMileage())) {
                BigDecimal avgTime = new BigDecimal(data.getMileage()).divide(new BigDecimal(15), 5, RoundingMode.HALF_UP);
                BigDecimal divide = avgTime.multiply(new BigDecimal(8))
                        .multiply(new BigDecimal(data.getLaunch()))
                        .divide(new BigDecimal(24), 5, RoundingMode.HALF_UP);

                BigDecimal bigDecimal = divide.setScale(4, RoundingMode.HALF_UP);
                return bigDecimal.doubleValue();
            } else {
                //如果没有公里数 计算出来的占比就是0
                return 0;
            }
        }

        //没有计算天级时间占比得情况
        return 1;
    }

    @Override
    protected BigDecimal getUv(ComputationalDataContext kpiQueryContext, BigDecimal population, BigDecimal heaven, BigDecimal visibleAngle, String aoiCoordinates, String mileage) {
        if (heaven.doubleValue() > 1) {
            return population;
        }
        return population.multiply(heaven);
    }

    @Override
    protected BigDecimal getAf(CalendarPlacementOrderData data, String heavenProportion) {
        BigDecimal af = super.getAf(data, heavenProportion);
        BigDecimal heaven = StringUtils.isNotEmpty(heavenProportion) ? new BigDecimal(heavenProportion) : new BigDecimal(0);
        if (heaven.doubleValue() > 1) {
            //要加上天级时间占比
            return af.add(heaven).setScale(2, RoundingMode.HALF_UP);
        }
        return af;
    }

    @Override
    public String templateName() {
        return "BUS_SCREEN";
    }
}
