package heran.media.management.platform.mediaorder.processor;

import com.alibaba.nacos.common.utils.CollectionUtils;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.bo.TaStatisticalType;
import heran.media.sharelib.domain.db.mapper.main.MainCalendarOrderKpiMapper;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import heran.media.sharelib.domain.dto.hot.MainCalendarPlacementData;
import heran.media.sharelib.utils.GridCircleCoverUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RefinedPassengerLockProcessor extends RefinedResidentLockProcessor {

    @Resource
    private MainCalendarOrderKpiMapper mainCalendarOrderKpiMapper;

    @Override
    public String getType() {
        return "REFINED_PASSENGER";
    }

    @Override
    protected void calendarOrderKpi(List<MainCalendarPlacementData> placementList, LockDataContext context) {
        List<MainCalendarOrderKpi> resultList = new ArrayList<>();
        Map<String, Integer> taPopulationCache = new HashMap<>(8);
        Map<String, Map<String, List<GridCircleCoverUtil.Grid>>> hotMapCache = new HashMap<>(8);
        for (MainCalendarPlacementData placement : placementList) {
            Map<String, Integer> indicators = calculatePopulationIndicators(
                    placement,
                    context,
                    Arrays.asList(
                            StatisticalType.DWELL_POPULATION.name(),
                            StatisticalType.WORK_POPULATION.name(),
                            StatisticalType.PASSENGER_POPULATION.name(),
                            StatisticalType.PASSENGER_HOUR_POPULATION.name(),
                            TaStatisticalType.TA_PASSENGER_POPULATION.name()
                    ),
                    hotMapCache,
                    taPopulationCache,
                    TaStatisticalType.TA_PASSENGER_POPULATION.name()
            );

            MainCalendarOrderKpi kpi = baseBuildMainCalendarOrderKpi(context, placement);
            kpi.setAoiWorkPopulation(indicators.get(StatisticalType.WORK_POPULATION.name()));
            kpi.setAoiDwellPopulation(indicators.get(StatisticalType.DWELL_POPULATION.name()));
            kpi.setAoiPassengerPopulation(indicators.get(StatisticalType.PASSENGER_POPULATION.name()));
            kpi.setAoiPassengerHourPopulation(indicators.get(StatisticalType.PASSENGER_HOUR_POPULATION.name()));
            kpi.setAoiTaPopulation(indicators.get(TaStatisticalType.TA_PASSENGER_POPULATION.name()).toString());
            kpi.setTaPopulaceCount(indicators.get(TaStatisticalType.TA_PASSENGER_POPULATION.name() + "_UV"));
            resultList.add(kpi);
        }

        if (CollectionUtils.isNotEmpty(resultList)) {
            mainCalendarOrderKpiMapper.batchInsertUpdate(resultList);
        }
    }
}
