package heran.media.management.platform.mediaorder.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.common.utils.DownloadFileUtils;
import heran.media.management.platform.common.utils.GeoValidatorUtils;
import heran.media.management.platform.common.utils.StringMapUtils;
import heran.media.management.platform.common.utils.xls.PlacementGeneralTemplateUtils;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.main.error.PlacementSnAddResourceProviderUniqueException;
import heran.media.management.platform.main.service.MainMediaPlacementService;
import heran.media.management.platform.main.subdomain.ds.MainMediaPlacementEntityQueryMapper;
import heran.media.management.platform.main.subdomain.dto.TCategoryIds;
import heran.media.management.platform.main.subdomain.dto.TExtentAttr;
import heran.media.management.platform.mediaorder.sudomain.bo.XlsCollectListData;
import heran.media.management.platform.mediaorder.sudomain.bo.XlsMainMediaOrderPlacement;
import heran.media.management.platform.mediaorder.sudomain.bo.XlsMediaOrderMatchDetail;
import heran.media.management.platform.mediaorder.sudomain.ds.MainMediaOrderPlacementEntityQueryMapper;
import heran.media.management.platform.mediaorder.sudomain.dto.*;
import heran.media.management.platform.mediaorder.sudomain.request.OrderPlacementCreateRequest;
import heran.media.management.platform.mediaorder.sudomain.response.OrderPlacementResponse;
import heran.media.management.platform.mediaresource.nationwidemedia.dto.AttachmentRequest;
import heran.media.management.platform.task.db.po.PlacementAddressInfo;
import heran.media.sharelib.client.AMapClient;
import heran.media.sharelib.client.handle.AMapResponseHandle;
import heran.media.sharelib.domain.bo.MainMediaAlbumAlbumType;
import heran.media.sharelib.domain.bo.MainMediaTagRelBizType;
import heran.media.sharelib.domain.db.mapper.main.*;
import heran.media.sharelib.domain.db.model.main.*;
import heran.media.sharelib.domain.dto.amap.AMapGeoInfo;
import heran.media.sharelib.domain.dto.amap.AMapGeoSearchResponse;
import heran.media.sharelib.utils.redis.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static heran.media.management.platform.common.utils.xls.XLSWriter.getExportFileName;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class MainMediaOrderPlacementService extends BaseCallServiceImpl {
    private static int EXPORT_START_PAGE = 1;
    private static int EXPORT_PAGE_LIMIT = 5000;
    private static int EXPORT_LIMIT_LOOP_COUNT = 20;

    @Value("${path.export}")
    private String tempDataPath;

    private final Map<String, String> map = new LinkedHashMap<>();

    @Resource
    private MainMediaOrderPlacementEntityQueryMapper mainMediaOrderPlacementEntityQueryMapper;
    @Resource
    private MainMediaOrderPlacementMapper mainMediaOrderPlacementMapper;
    @Resource
    private MainMediaPlacementService mainMediaPlacementService;
    @Resource
    private MainMediaPlacementEntityQueryMapper mainMediaPlacementEntityQueryMapper;
    @Resource
    private AMapClient aMapClient;
    @Resource
    private MainCaseTagRelMapper mainCaseTagRelMapper;
    @Resource
    private MainMediaAlbumMapper mainMediaAlbumMapper;
    @Resource
    private MainMediaAlbumDetailMapper mainMediaAlbumDetailMapper;
    @Resource
    private MainAttachementMapper mainAttachementMapper;
    @Resource
    private MediaOrderMatchDetailMapper mediaOrderMatchDetailMapper;
    @Resource
    private OrderIntelligenceEstimateDetailMapper orderIntelligenceEstimateDetailMapper;


    /**
     * 列表
     *
     * @param criteria 查询条件
     * @return PageResponse<MainMediaOrderPlacementData>
     */
    public PageResponse<MainMediaOrderPlacementData> list(SearchCriteria criteria) {
        SearchCriteria.Criteria notDeletedCriteria = new SearchCriteria.Criteria();
        notDeletedCriteria.setKey("isDeleted");
        notDeletedCriteria.setValue("0");
        criteria.getCriterias().add(notDeletedCriteria);

        //此分页为entity分页，如果有其他查询自行在前面增加不要导致以下分页失效
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<MainMediaOrderPlacementData> searchResult = mainMediaOrderPlacementEntityQueryMapper.list(criteria);
        PageResponse<MainMediaOrderPlacementData> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult);
        return pageResponse;
    }

    /**
     * 查询单个
     *
     * @param id 主键
     * @return OrderPlacementResponse
     */
    public OrderPlacementResponse getById(Long id) {
        OrderPlacementResponse response = new OrderPlacementResponse();
        MainMediaOrderPlacement mainMediaOrderPlacement = mainMediaOrderPlacementMapper.getByIdEX(id);
        if (mainMediaOrderPlacement == null) {
            return response;
        }
        BeanUtils.copyProperties(mainMediaOrderPlacement, response);
        String extraAttrs = mainMediaOrderPlacement.getExtraAttrs();
        List<Map<String, String>> attrsList = StringMapUtils.splitExtraAttrs(extraAttrs);
        if (attrsList != null) {
            List<TExtentAttr> extentAttrs = attrsList.stream().map(TExtentAttr::new).collect(Collectors.toList());
            response.setAttrs(extentAttrs);
        }
        //获取媒体点位图片临时链接
        List<AttachmentRequest> attachmentContentList = new ArrayList<>();
        List<Integer> albumIds = mainMediaOrderPlacementEntityQueryMapper.getAttachmentIdByPlacementId(mainMediaOrderPlacement.getId());
        if (!albumIds.isEmpty()) {
            for (Integer albumId : albumIds) {
                AttachmentRequest attachmentRequest = mainMediaPlacementService.getMediaContentFile(Long.valueOf(albumId));
                attachmentContentList.add(attachmentRequest);
            }
        }
        List<Integer> mediaPlacementTagsIds = mainMediaPlacementEntityQueryMapper.getBizIdAndTagId(id.intValue(), MainMediaTagRelBizType.MEDIA_ORDER_PLACEMENT_TAGS.name());
        response.setAttachmentRequestList(attachmentContentList);
        response.setMediumPlacementTags(mediaPlacementTagsIds);
        return response;
    }

    /**
     * 导出数据
     *
     * @param criteria  查询条件
     * @param resp      返回体
     * @param exportAll 是否导出全部
     * @return File
     * @throws Exception 异常处理
     */
    public File exportData(SearchCriteria criteria, HttpServletResponse resp, Boolean exportAll) throws Exception {
        Integer serialNumber = 1;
        Integer categoryId = extractCategoryIdFromCriteria(criteria);
        TCategoryIds tCategoryIds = mainMediaPlacementEntityQueryMapper.getByCategoryId(categoryId);
        List<MainPlacementExtendAttr> extendAttr = mainMediaPlacementEntityQueryMapper.getCategoryIdByExtendAttr(categoryId);
        if (exportAll) {
            criteria.setPage(EXPORT_START_PAGE);
            criteria.setLimit(EXPORT_PAGE_LIMIT);
        }
        int startPage = criteria.getPage();
        int pageLimit = criteria.getLimit();

        String fileName = URLEncoder.encode(getExportFileName(tCategoryIds.getCategoryIdLv1Name(), tCategoryIds.getCategoryIdLv2Name(), tCategoryIds.getCategoryIdLv3Name()), "UTF-8");
        File newFile = new File(fileName);
        SearchCriteria.Criteria notDeletedCriteria = new SearchCriteria.Criteria();
        notDeletedCriteria.setKey("isDeleted");
        notDeletedCriteria.setValue("false");
        criteria.getCriterias().add(notDeletedCriteria);
        // 准备要导出的字段列表
        List<Object> objects = prepareExportObjects(categoryId);
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        Sheet sheet = workbook.createSheet("sheet1");
        int rowIndex = 0;
        mainMediaPlacementService.createHeaderRow(sheet, objects, rowIndex++); // 创建表头行
        Page page = PageHelper.startPage(startPage, pageLimit, true);
        List<MainMediaOrderPlacementData> queryResult = mainMediaOrderPlacementEntityQueryMapper.list(criteria);
        while (!queryResult.isEmpty()) {
            for (MainMediaOrderPlacementData mainMediaPlacement : queryResult) {
                XlsMainMediaOrderPlacement xlsMainMediaPlacement = new XlsMainMediaOrderPlacement(mainMediaPlacement);
                xlsMainMediaPlacement.setSerialNumber(serialNumber);
                List<Object> rowData = new ArrayList<>();
                Field[] fields = XlsMainMediaOrderPlacement.class.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    Object value = field.get(xlsMainMediaPlacement);
                    rowData.add(value != null ? value : "");
                }
                // 获取扩展属性值
                try {
                    rowData.addAll(mainMediaPlacementService.getExtentAttributes(mainMediaPlacement.getExtraAttrs(), extendAttr));
                } catch (Exception e) {
                    log.warn("数据格式转换错误:" + rowData);
                }
                // 创建数据行
                try {
                    mainMediaPlacementService.createDataRow(sheet, objects, rowData, rowIndex++);
                } catch (Exception e) {
                    log.warn("数据格式转换错误:" + rowData);
                }
                serialNumber++;
            }
            if (exportAll) {
                startPage++;
                PageHelper.startPage(startPage, pageLimit, false);
                queryResult = mainMediaOrderPlacementEntityQueryMapper.list(criteria);
                continue;
            }
            break;

        }
        // 设置响应头
        resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        resp.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        workbook.dispose();
        map.clear();
        return newFile;
    }

    public List<Object> prepareExportObjects(Integer categoryId) {
        List<Object> objects = new ArrayList<>();
        Field[] declaredFields = XlsMainMediaOrderPlacement.class.getDeclaredFields();
        for (Field field : declaredFields) {
            if (field.isAnnotationPresent(XlsField.class) && field.getAnnotation(XlsField.class).columnIndex() != -1) {
                XlsField xlsField = field.getAnnotation(XlsField.class);
                objects.add(xlsField.title());
            }
        }
        List<MainPlacementExtendAttr> mainPlacementExtendAttrs = mainMediaPlacementEntityQueryMapper.getCategoryIdByExtendAttr(categoryId);
        for (MainPlacementExtendAttr mainPlacementExtendAttr : mainPlacementExtendAttrs) {
            objects.add(mainPlacementExtendAttr.getLabel());
        }
        return objects;
    }


    private Integer extractCategoryIdFromCriteria(SearchCriteria criteria) {
        Integer categoryId = null;
        for (SearchCriteria.Criteria criteriaItem : criteria.getCriterias()) {
            if ("mediumLv3CategoryId".equals(criteriaItem.getKey())) {
                categoryId = Integer.valueOf(criteriaItem.getValue());
                break;
            }
        }
        return categoryId;
    }

    /**
     * 添加点位数据
     *
     * @param userKey 用户id
     * @param request 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void create(Integer userKey, OrderPlacementCreateRequest request) {
        MainMediaOrderPlacement mediaOrderPlacement = request.adaToPo();
        //验证资源方资源方编号是否唯一
        MainMediaOrderPlacement resourceProvider = mainMediaOrderPlacementMapper.getValidByPlacementSnResourceProvider(request.getOrderId(), request.getPlacementSn(), request.getResourceProvider());
        if (resourceProvider != null) {
            throw new PlacementSnAddResourceProviderUniqueException();
        }
        List<TExtentAttr> extentAttrs = request.getExtentAttrsList();
        setCreatorInfo(userKey, mediaOrderPlacement);
        setUpdaterInfo(userKey, mediaOrderPlacement);
        mediaOrderPlacement.setIsDeleted(false);
        if (extentAttrs.size() <= 1) {
            mediaOrderPlacement.setExtraAttrs(null);
        } else {
            Map<String, String> attrMap = extentAttrs.stream()
                    .filter(attr -> attr.getAttrValue() != null) // Filter out null values
                    .collect(Collectors.toMap(TExtentAttr::getAttrKey, TExtentAttr::getAttrValue,
                            (existingValue, newValue) -> existingValue,
                            HashMap::new));
            mediaOrderPlacement.setExtraAttrs(new Gson().toJson(extentAttrs));
            String address = attrMap.get("媒体地址");
            if (StringUtils.isNotEmpty(address)) {
                mediaOrderPlacement.setAddress(address);
            }
            mediaOrderPlacement.setExtraAttrs(new Gson().toJson(extentAttrs));
        }
        getPlacementAddressInfo(request, mediaOrderPlacement,userKey);

        //维护标签
        mediaOrderPlacement.setId(null);
        mainMediaOrderPlacementMapper.insertUpdateEntity(mediaOrderPlacement);
        List<Integer> mediumPlacementTags = request.getMediumPlacementTags();
        if (!mediumPlacementTags.isEmpty()) {
            List<MainMediaTagRel> mainMediaTagRelList = new ArrayList<>();
            for (Integer mediumPlacementTag : mediumPlacementTags) {
                MainMediaTagRel tagRelation = this.createTagRelation(userKey, mediaOrderPlacement.getId(), mediumPlacementTag);
                mainMediaTagRelList.add(tagRelation);
            }
            mainCaseTagRelMapper.insertBatch(mainMediaTagRelList);
        }
        // 维护相册 MAIN_MEDIA_ORDER_PLACEMENT 相册类型
        insertUpdateAlbum(userKey, mediaOrderPlacement.getId().intValue(), request);
    }

    /**
     * 维护相册数据
     *
     * @param userKey          用户id
     * @param mediaPlacementId 点位id
     * @param request          请求参数
     */
    private void insertUpdateAlbum(Integer userKey, Integer mediaPlacementId, OrderPlacementCreateRequest request) {
        List<MainAttachement> attachmentList = new ArrayList<>();
        if (!request.getAttachmentRequestList().isEmpty()) {
            List<Integer> inputAttachmentIdList = new ArrayList<>();
            for (AttachmentRequest attachmentRequest : request.getAttachmentRequestList()) {
                MainAttachement mainAttachement = new MainAttachement();
                mainAttachement.setId(attachmentRequest.getId().longValue());
                mainAttachement.setDescription(attachmentRequest.getRemark());
                setUpdaterInfo(userKey, mainAttachement);
                attachmentList.add(mainAttachement);
                inputAttachmentIdList.add(attachmentRequest.getId());
            }
            mainMediaPlacementEntityQueryMapper.updateMainAttachement(attachmentList);
            MainMediaAlbum mediaAlbum = new MainMediaAlbum();
            mediaAlbum.setAlbumType(MainMediaAlbumAlbumType.MAIN_MEDIA_ORDER_PLACEMENT.name());
            mediaAlbum.setAlbumName(MainMediaAlbumAlbumType.MAIN_MEDIA_ORDER_PLACEMENT.getDesc());
            mediaAlbum.setBizId(mediaPlacementId);
            setCreatorInfo(userKey, mediaAlbum);
            setUpdaterInfo(userKey, mediaAlbum);
            mainMediaAlbumMapper.insertUpdateEntity(mediaAlbum);
            Long albumId = mediaAlbum.getId();
            if (albumId == null) {
                MainMediaAlbum album = mainMediaAlbumMapper.getByAlbumTypeBizIdCategoryId(mediaAlbum.getAlbumType(), mediaPlacementId, -1);
                albumId = album.getId();
            }
            insertOrDeleteAlbumDetail(userKey, albumId.intValue(), inputAttachmentIdList);
        }
    }

    /**
     * 维护附件数据
     *
     * @param userKey               用户id
     * @param albumId               相册id
     * @param inputAttachmentIdList 增加的相册数据
     */
    private void insertOrDeleteAlbumDetail(Integer userKey, Integer albumId, List<Integer> inputAttachmentIdList) {
        List<MainMediaAlbumDetail> detailList = mainMediaAlbumDetailMapper.getByAlbumId(albumId);
        List<Integer> attachmentIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(detailList)) {
            attachmentIdList = detailList.stream().map(MainMediaAlbumDetail::getAttachmentId).collect(Collectors.toList());
        }
        //删除关联附件
        List<Integer> attachmentIdToDelete = new ArrayList<>(attachmentIdList);
        attachmentIdToDelete.removeAll(inputAttachmentIdList);
        // 过滤掉null元素
        attachmentIdToDelete = attachmentIdToDelete.stream().filter(Objects::nonNull).collect(Collectors.toList());
        //先删除原来所有附件关联关系
        mainMediaAlbumDetailMapper.deleteBatchByAlbumIds(Collections.singletonList(Long.valueOf(albumId)));
        //然后重新插入
        List<MainMediaAlbumDetail> detailListToAdd = new ArrayList<>();
        for (Integer attachmentId : inputAttachmentIdList) {
            MainMediaAlbumDetail mediaAlbumDetail = new MainMediaAlbumDetail();
            mediaAlbumDetail.setAlbumId(albumId);
            mediaAlbumDetail.setAttachmentId(attachmentId);
            setUpdaterInfo(userKey, mediaAlbumDetail);
            setCreatorInfo(userKey, mediaAlbumDetail);
            detailListToAdd.add(mediaAlbumDetail);
        }
        mainMediaAlbumDetailMapper.insertBatch(detailListToAdd);
        //逻辑删除附件
        if (CollectionUtils.isNotEmpty(attachmentIdToDelete)) {
            mainAttachementMapper.deleteByIdsLogicallyBatch(userKey, attachmentIdToDelete);
        }
    }

    /**
     * 绑定标签
     *
     * @param userKey     用户id
     * @param placementId 点位id
     * @param tagId       标签id
     * @return MainMediaTagRel
     */
    private MainMediaTagRel createTagRelation(Integer userKey, Long placementId, Integer tagId) {
        MainMediaTagRel mainCaseTagRel = new MainMediaTagRel();
        mainCaseTagRel.setId(null);
        mainCaseTagRel.setBizId(placementId.intValue());
        mainCaseTagRel.setTagId(tagId);
        mainCaseTagRel.setBizType(MainMediaTagRelBizType.MEDIA_ORDER_PLACEMENT_TAGS.name());
        setUpdaterInfo(userKey, mainCaseTagRel);
        setCreatorInfo(userKey, mainCaseTagRel);
        return mainCaseTagRel;
    }

    private void getPlacementAddressInfo(OrderPlacementCreateRequest request, MainMediaOrderPlacement mediaOrderPlacement,Integer userKey) {
        PlacementAddressInfo placementAddressInfo = null;
        String address = mediaOrderPlacement.getAddress();
        if (StringUtils.isNotEmpty(address)) {
            if (address.contains("/")) {
                // 如果包含 '/'，说明可能有地址和经纬度信息
                String[] split = mediaOrderPlacement.getAddress().split("/");
                // 将地址部分赋值给 columnValue
                mediaOrderPlacement.setAddress(split[0]);
                if (GeoValidatorUtils.isValidLatLong(split[1])) {
                    // 验证第二部分是否为有效的经纬度
                    String[] latLong = split[1].split(",");
                    mediaOrderPlacement.setLongitude(latLong[0]);
                    mediaOrderPlacement.setLatitude(latLong[1]);
                }
            } else if (GeoValidatorUtils.isValidLatLong(address)) {
                // 如果没有 '/' 且整段是有效的经纬度
                String[] latLong = address.split(",");
                mediaOrderPlacement.setLongitude(latLong[0]);
                mediaOrderPlacement.setLatitude(latLong[1]);
            } else {
                String addressDetail = null;
                if (mediaOrderPlacement.getDistrictId() != null) {
                    placementAddressInfo = mainMediaPlacementEntityQueryMapper.getRegion(mediaOrderPlacement.getDistrictId());
                    addressDetail = placementAddressInfo.getProvince() + placementAddressInfo.getCity() + placementAddressInfo.getArea() + request.getAddress();
                }
                if (placementAddressInfo == null && mediaOrderPlacement.getCityId() != null) {
                    placementAddressInfo = mainMediaPlacementEntityQueryMapper.getRegionCityId(mediaOrderPlacement.getCityId());
                    addressDetail = placementAddressInfo.getProvince() + placementAddressInfo.getCity() + mediaOrderPlacement.getAddress();
                }
                if (placementAddressInfo == null && mediaOrderPlacement.getProvinceId() != null) {
                    placementAddressInfo = mainMediaPlacementEntityQueryMapper.getRegionProvinceId(mediaOrderPlacement.getProvinceId());
                    addressDetail = placementAddressInfo.getProvince();
                }
                AMapGeoSearchResponse geoSearchResponse = aMapClient.searchGeo(placementAddressInfo.getArea(), addressDetail,userKey);
                AMapResponseHandle.handle(geoSearchResponse);
                if (CollectionUtils.isNotEmpty(geoSearchResponse.getGeocodes())) {
                    AMapGeoInfo geoInfo = geoSearchResponse.getGeocodes().get(0);
                    String location = geoInfo.getLocation();
                    String[] split = location.split(",");
                    mediaOrderPlacement.setLongitude(split[0]);
                    mediaOrderPlacement.setLatitude(split[1]);
                } else {
                    mediaOrderPlacement.setLongitude(null);
                    mediaOrderPlacement.setLatitude(null);
                }
            }
        } else {
            mediaOrderPlacement.setLongitude(null);
            mediaOrderPlacement.setLatitude(null);
        }
    }

    /**
     * 修改点位数据
     *
     * @param userKey 用户id
     * @param request 请求参数
     */
    @RedisLock(category = "quoteOrderPlacementData:", key = "#orderId", tryLock = 2000, lockTime = 10000)
    public void modify(Integer userKey, OrderPlacementCreateRequest request) {
        MainMediaOrderPlacement mediaOrderPlacement = request.adaToPo();
        MainMediaOrderPlacement resourceProvider = mainMediaOrderPlacementMapper.getValidByPlacementSnResourceProvider(request.getOrderId(), request.getPlacementSn(), request.getResourceProvider());
        if (resourceProvider != null && !resourceProvider.getId().equals(request.getId())) {
            throw new PlacementSnAddResourceProviderUniqueException();
        }
        List<TExtentAttr> extentAttrs = request.getExtentAttrsList();
        mediaOrderPlacement.setUpdatedByUser(userKey);
        if (extentAttrs.size() <= 1) {
            mediaOrderPlacement.setExtraAttrs(null);
        } else {
            Map<String, String> attrMap = extentAttrs.stream()
                    .filter(attr -> attr.getAttrValue() != null) // Filter out null values
                    .collect(Collectors.toMap(TExtentAttr::getAttrKey, TExtentAttr::getAttrValue,
                            (existingValue, newValue) -> existingValue,
                            HashMap::new));
            mediaOrderPlacement.setExtraAttrs(new Gson().toJson(extentAttrs));
            String address = attrMap.get("媒体地址");
            if (StringUtils.isNotEmpty(address)) {
                mediaOrderPlacement.setAddress(address);
            }
            mediaOrderPlacement.setExtraAttrs(new Gson().toJson(extentAttrs));
        }
        mediaOrderPlacement.setIsDeleted(false);
        getPlacementAddressInfo(request, mediaOrderPlacement,userKey);
        setUpdaterInfo(userKey, mediaOrderPlacement);
        setCreatorInfo(userKey, mediaOrderPlacement);
        mainMediaOrderPlacementMapper.insertUpdateEntity(mediaOrderPlacement);
        List<Integer> mediumPlacementTags = request.getMediumPlacementTags();
        if (!mediumPlacementTags.isEmpty()) {
            List<MainMediaTagRel> mainMediaTagRelList = new ArrayList<>();
            mainMediaPlacementEntityQueryMapper.deleteMainMediaTagRelByBizIdAndBizTypae(request.getId().intValue(), MainMediaTagRelBizType.MEDIA_ORDER_PLACEMENT_TAGS.name());
            for (Integer mediumPlacementTag : mediumPlacementTags) {
                MainMediaTagRel tagRelation = this.createTagRelation(userKey, request.getId(), mediumPlacementTag);
                mainMediaTagRelList.add(tagRelation);
            }
            mainCaseTagRelMapper.insertBatch(mainMediaTagRelList);
        } else {
            mainMediaPlacementEntityQueryMapper.deleteMainMediaTagRelByBizIdAndBizTypae(request.getId().intValue(), MainMediaTagRelBizType.MEDIA_ORDER_PLACEMENT_TAGS.name());
        }
        if (request.getAttachmentRequestList().isEmpty()) {
            List<Integer> albumIds = mainMediaPlacementEntityQueryMapper.getByAlbumTypeBizId(request.getId(), MainMediaAlbumAlbumType.MAIN_MEDIA_ORDER_PLACEMENT.name());
            if (!albumIds.isEmpty()) {
                mainMediaAlbumDetailMapper.deleteBatchByAlbumIds(Collections.singletonList(albumIds.get(0).longValue()));
            }
        }
        insertUpdateAlbum(userKey, mediaOrderPlacement.getId().intValue(), request);
    }


    public void delete(Object userKey, Long id) {
        mainMediaOrderPlacementMapper.deleteByIdLogically(userKey, id);
    }

    /**
     * 批量删除
     *
     * @param userKey      用户id
     * @param orderId      订单id
     * @param ids          点位id
     * @param isDeletedAll 是否删除全部
     */
    @RedisLock(category = "quoteOrderPlacementData:", key = "#orderId", tryLock = 2000, lockTime = 10000)
    public void deleteBatch(Integer orderId, Integer userKey, Long[] ids, Boolean isDeletedAll) {
        if (isDeletedAll) {
            //删除投放点位绑定的标签
            mainMediaOrderPlacementEntityQueryMapper.deleteMainMediaTagRelByOrderId(orderId);
            //删除相册关联表数据
            mainMediaOrderPlacementEntityQueryMapper.deleteMainMediaAlbumDetailByOrderId(orderId);
            //删除相册数据
            mainMediaOrderPlacementEntityQueryMapper.deleteMainMediaAlbumByOrderId(orderId);
            //删除投放点位数据
            mainMediaOrderPlacementEntityQueryMapper.deleteAll(orderId);
        } else if (ids != null) {
            for (Long id : ids) {
                //删除标签
                mainMediaOrderPlacementEntityQueryMapper.deleteMainMediaTagRelById(id, MainMediaTagRelBizType.MEDIA_ORDER_PLACEMENT_TAGS.name());
                //删除与相册关联表
                mainMediaOrderPlacementEntityQueryMapper.deleteMainMediaAlbumDetailById(id, MainMediaAlbumAlbumType.MAIN_MEDIA_ORDER_PLACEMENT.name());
                //删除相册数据
                mainMediaOrderPlacementEntityQueryMapper.deleteMainMediaAlbumById(id, MainMediaAlbumAlbumType.MAIN_MEDIA_ORDER_PLACEMENT.name());
                //删除投放点位
                mainMediaOrderPlacementMapper.deleteByIdEX(id);
            }
        }
    }

    /**
     * 根据筛选条件删除点位数据
     *
     * @param criteria 筛选条件
     */
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(category = "quoteOrderPlacementData:", key = "#orderId", tryLock = 2000, lockTime = 10000)
    public void deleteSearchCriteria(Integer orderId, SearchCriteria criteria) {
        //移除掉orderId
        List<SearchCriteria.Criteria> updatedCriteria = criteria.getCriterias().stream()
                .filter(c -> !"orderId".equals(c.getKey()))
                .collect(Collectors.toList());
        criteria.setCriterias(updatedCriteria);
        //删除标签
        mainMediaOrderPlacementEntityQueryMapper.deleteCriteriaMainMediaTagRel(orderId, criteria);
        //删除与相册关联表
        mainMediaOrderPlacementEntityQueryMapper.deleteMainMediaAlbumCriteriaDetail(orderId, criteria);
        //删除相册数据
        mainMediaOrderPlacementEntityQueryMapper.deleteCriteriaMainMediaAlbum(orderId, criteria);
        //删除点位数据
        mainMediaOrderPlacementEntityQueryMapper.deleteCriteriaMainPlacement(orderId, criteria);
    }


    public void updateIsDeleted(Integer id, boolean status, Integer userKey) {
        mainMediaOrderPlacementMapper.updateIsDeleted(id, status, userKey, appName);
    }

    /**
     * 批量新增数据
     *
     * @param batchInsertList 新增数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(List<MainMediaOrderPlacement> batchInsertList) {
        mainMediaOrderPlacementEntityQueryMapper.batchInsertEntities(batchInsertList);
    }

    /**
     * 引用主数据
     *
     * @param criteria 请求参数
     */
    @RedisLock(category = "quoteOrderPlacementData:", key = "#orderId", tryLock = 2000, lockTime = 1200000)
    public void quoteOrderPlacementData(Integer userKey, SearchCriteria criteria, Integer orderId) {
        int page = 1;
        int count = 3000;
        //获取到所有的点位数据
        PageHelper.startPage(page, count, false);
        List<MainMediaPlacement> placementList = mainMediaPlacementEntityQueryMapper.selectCriteria(criteria);
        while (!placementList.isEmpty()) {
            //要添加的点位数据
            ArrayList<MainMediaOrderPlacement> orderPlacements = new ArrayList<>();
            //维护投放点位数据
            for (MainMediaPlacement mainMediaPlacement : placementList) {
                MainMediaOrderPlacement orderPlacement = new MainMediaOrderPlacement();
                BeanUtils.copyProperties(mainMediaPlacement, orderPlacement);
                orderPlacement.setId(null);
                orderPlacement.setOrderId(orderId);
                orderPlacements.add(orderPlacement);
            }
            //将投放点位保存在数据库中
            insertBatch(orderPlacements);
            page++;
            PageHelper.startPage(page, count, false);
            placementList = mainMediaPlacementEntityQueryMapper.selectCriteria(criteria);
        }
        //处理标签数据
        mediaPlacementTags(userKey, orderId, criteria);
        //处理图片
        mediaPlacementAlbum(userKey, orderId, criteria);
    }


    /**
     * 处理标签
     *
     * @param userKey  用户id
     * @param orderId  订单id
     * @param criteria 查询条件
     */
    private void mediaPlacementTags(Integer userKey, Integer orderId, SearchCriteria criteria) {
        int page = 1;
        int count = 900;
        //进行分批处理标签 分批查询到要增加的标签
        PageHelper.startPage(page, count, false);
        List<MediaPlacementTagData> tagData = mainMediaPlacementEntityQueryMapper.mediaPlacementTags(criteria);
        while (!tagData.isEmpty()) {
            //处理标签数据 key为主数据的唯一值 点位编号 + 资源方  集合是对应的标签数据
            Map<String, List<Integer>> tagMap = tagData.stream()
                    .collect(Collectors.groupingBy(
                            data -> data.getPlacementSn() + data.getResourceProvider(),
                            Collectors.mapping(MediaPlacementTagData::getTagId, Collectors.toList())
                    ));
            ArrayList<String> list = new ArrayList<>(tagMap.keySet());
            //构建标签数据
            List<GetMediaOrderPlacementData> orderPlacement = mainMediaOrderPlacementEntityQueryMapper.getMediaOrderPlacementByTagData(orderId, list);
            ArrayList<MainMediaTagRel> mainMediaTagRelList = new ArrayList<>();
            for (GetMediaOrderPlacementData data : orderPlacement) {
                List<Integer> tagIds = tagMap.get(data.getPlacementSn() + data.getResourceProvider());
                for (Integer id : tagIds) {
                    MainMediaTagRel mediaTagRel = createTagRelation(userKey, data.getId(), id);
                    mainMediaTagRelList.add(mediaTagRel);
                }
            }
            List<Long> idList = orderPlacement.stream()
                    .map(GetMediaOrderPlacementData::getId)
                    .collect(Collectors.toList());
            //先删除原来的标签数据
            mainCaseTagRelMapper.deleteBizIdAndBizTypeList(idList, MainMediaTagRelBizType.MEDIA_ORDER_PLACEMENT_TAGS.name());
            //绑定标签数据
            //mainCaseTagRelMapper.insertBatch(mainMediaTagRelList);
            mainCaseTagRelInsertBatch(mainMediaTagRelList);
            page++;
            PageHelper.startPage(page, count, false);
            tagData = mainMediaPlacementEntityQueryMapper.mediaPlacementTags(criteria);
        }
    }


    /**
     * 图片处理
     *
     * @param userKey  用户id
     * @param orderId  订单id
     * @param criteria 查询条件
     */
    private void mediaPlacementAlbum(Integer userKey, Integer orderId, SearchCriteria criteria) {
        int page = 1;
        int count = 900;
        PageHelper.startPage(page, count, false);
        //进行分批处理相册数据
        List<MediaPlacementAlbumData> albumData = mainMediaPlacementEntityQueryMapper.selectAlbumCriteria(criteria);
        while (!albumData.isEmpty()) {
            //将附件数据 转为 map key为 点位编号 资源方（点位编号+资源方 = id）  value为 相册名称
            Map<String, String> albumMap = albumData.stream()
                    .collect(Collectors.toMap(
                            album -> album.getPlacementSn() + album.getResourceProvider(),
                            MediaPlacementAlbumData::getAlbumName,
                            (existing, replacement) -> existing));

            List<String> list = new ArrayList<>(albumMap.keySet());
            //根据相册数据获取到对应的投放点位数据 这些数据的相册附件数据要发生改变 一个相册对应一个投放点位
            List<GetMediaOrderPlacementData> data = mainMediaOrderPlacementEntityQueryMapper.getMediaOrderPlacementByAlbumData(orderId, list);
            // 投放点位id集合
            List<Long> idList = data.stream()
                    .map(GetMediaOrderPlacementData::getId)
                    .collect(Collectors.toList());
            //要添加的相册数据
            ArrayList<MainMediaAlbum> mediaAlbumsAdd = new ArrayList<>();
            //要修改的相册数据
            for (GetMediaOrderPlacementData placementData : data) {
                String albumName = albumMap.get(placementData.getPlacementSn() + placementData.getResourceProvider());
                MainMediaAlbum mainMediaAlbum = new MainMediaAlbum();
                mainMediaAlbum.setAlbumType(MainMediaAlbumAlbumType.MAIN_MEDIA_ORDER_PLACEMENT.name());
                mainMediaAlbum.setAlbumName(albumName);
                mainMediaAlbum.setBizId(placementData.getId().intValue());
                mainMediaAlbum.setMediaCategoryId(-1);
                setUpdaterInfo(userKey, mainMediaAlbum);
                setCreatorInfo(userKey, mainMediaAlbum);
                mediaAlbumsAdd.add(mainMediaAlbum);
            }
            //批量保存在数据库
            //mainMediaAlbumMapper.insertUpdateBatch(mediaAlbumsAdd);
            mainMediaAlbumInsertBatch(mediaAlbumsAdd);
            //获取到当前投放点位的相册数据
            List<GetOrderPlacementAlbumData> orderPlacementAlbum = mainMediaOrderPlacementEntityQueryMapper.getOrderPlacementAlbum(orderId, idList);
            //获取到 媒体点位 相册下的附件数据
            List<Long> albumIds = albumData.stream()
                    .map(MediaPlacementAlbumData::getAlbumId)
                    .collect(Collectors.toList());
            //表示要加附件有这么多
            List<MediaAlbumDetail> mediaAlbumDetailList = mainMediaOrderPlacementEntityQueryMapper.getMediaAlbumDetailList(albumIds);
            //转为map
            Map<String, List<Long>> albumDataMap = mediaAlbumDetailList.stream()
                    .collect(Collectors.groupingBy(
                            d -> d.getPlacementSn() + d.getResourceProvider(),
                            Collectors.mapping(MediaAlbumDetail::getAttachmentId, Collectors.toList())
                    ));
            //删除关联表数据
            List<Long> albumTypeBizIds = mainMediaAlbumMapper.getByAlbumIdBizIds(MainMediaAlbumAlbumType.MAIN_MEDIA_ORDER_PLACEMENT.name(), idList);
            if (!albumTypeBizIds.isEmpty()) {
                mainMediaAlbumDetailMapper.deleteBatchByAlbumIds(albumTypeBizIds);
            }
            //构建关联表数据
            List<MainMediaAlbumDetail> details = new ArrayList<>();
            for (GetOrderPlacementAlbumData placementAlbumData : orderPlacementAlbum) {
                //要保存的中间表数据
                List<Long> listIds = albumDataMap.get(placementAlbumData.getPlacementSn() + placementAlbumData.getResourceProvider());
                if (listIds != null) {
                    details.addAll(insertMainMediaAlbumDetail(userKey, listIds, placementAlbumData.getAlbumId()));
                }
            }
            mainMediaAlbumDetailInsertBatch(details);
            page++;
            PageHelper.startPage(page, count, false);
            albumData = mainMediaPlacementEntityQueryMapper.selectAlbumCriteria(criteria);
        }
    }

    /**
     * 维护中间表
     *
     * @param userKey 用户id
     * @param list    添加数据
     * @param albumId 相册id
     */
    private List<MainMediaAlbumDetail> insertMainMediaAlbumDetail(Integer userKey, List<Long> list, Long albumId) {
        List<MainMediaAlbumDetail> details = new ArrayList<>();
        for (Long id : list) {
            MainMediaAlbumDetail detail = new MainMediaAlbumDetail();
            detail.setId(null);
            detail.setAttachmentId(id.intValue());
            detail.setAlbumId(albumId.intValue());
            setUpdaterInfo(userKey, detail);
            setCreatorInfo(userKey, detail);
            details.add(detail);
        }
        return details;
    }

    /**
     * 汇总列表
     *
     * @param criteria 查询条件
     * @return PageResponse<CollectListData>
     */
    public PageResponse<CollectListData> collectList(SearchCriteria criteria) {
        SearchCriteria.Criteria notDeletedCriteria = new SearchCriteria.Criteria();
        notDeletedCriteria.setKey("isDeleted");
        notDeletedCriteria.setValue("0");
        criteria.getCriterias().add(notDeletedCriteria);
        //此分页为entity分页，如果有其他查询自行在前面增加不要导致以下分页失效
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<CollectListData> searchResult = mainMediaOrderPlacementEntityQueryMapper.collectList(criteria);
        PageResponse<CollectListData> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult);
        return pageResponse;
    }

    public File collectListExportData(SearchCriteria criteria, boolean exportAll) throws IOException {
        if (exportAll) {
            criteria.setPage(EXPORT_START_PAGE);
            criteria.setLimit(EXPORT_PAGE_LIMIT);
        }
        int startPage = criteria.getPage();
        int pageLimit = criteria.getLimit();

        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        String fileName = URLEncoder.encode(getExportFileName("汇总数据"), "UTF-8");
        File newFile = new File(fileName);

        SearchCriteria.Criteria notDeletedCriteria = new SearchCriteria.Criteria();
        notDeletedCriteria.setKey("isDeleted");
        notDeletedCriteria.setValue("0");
        criteria.getCriterias().add(notDeletedCriteria);

        PageHelper.startPage(startPage, pageLimit, false);
        List<CollectListData> queryResult = mainMediaOrderPlacementEntityQueryMapper.collectList(criteria);

        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), XlsCollectListData.class);
        while (!queryResult.isEmpty()) {
            List<XlsCollectListData> appCallHistories = queryResult.stream().map(XlsCollectListData::new).collect(Collectors.toList());
            workbook = XLSWriter.appendLineFromObject("sheet1", appCallHistories, XlsMediaOrderMatchDetail.class, workbook);
            if (exportAll) {
                if (startPage * pageLimit >= EXPORT_LIMIT_LOOP_COUNT * EXPORT_PAGE_LIMIT) {
                    log.warn("导出数据超过可导出限制（不符合实时导出场景），如需变更自行更改");
                    break;
                }
                startPage++;
                PageHelper.startPage(startPage, pageLimit, false);
                queryResult = mainMediaOrderPlacementEntityQueryMapper.collectList(criteria);
                continue;
            }
            break;
        }
        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }


    /**
     * 根据订单id 删除掉智选详情所有数据
     *
     * @param orderId 订单id
     */
    public void deleteMediaPlacement(Integer orderId) {
        //删除匹配结果
        mediaOrderMatchDetailMapper.deleteByOrderId(orderId);
        //删除预估效果
        orderIntelligenceEstimateDetailMapper.deleteByOrderId(orderId);
        //删除投放点位绑定的标签
        mainMediaOrderPlacementEntityQueryMapper.deleteMainMediaTagRelByOrderId(orderId);
        //删除相册关联表数据
        mainMediaOrderPlacementEntityQueryMapper.deleteMainMediaAlbumDetailByOrderId(orderId);
        //删除相册数据
        mainMediaOrderPlacementEntityQueryMapper.deleteMainMediaAlbumByOrderId(orderId);
        //删除投放点位数据
        mainMediaOrderPlacementMapper.deleteMainMediaOrderPlacementByOrderId(orderId);
    }

    /**
     * 批量添加标签
     *
     * @param list 数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void mainCaseTagRelInsertBatch(List<MainMediaTagRel> list) {
        if (list.size() > 0) {
            mainCaseTagRelMapper.insertBatch(list);
        }
    }

    /**
     * 批量添加标签
     *
     * @param list 数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void mainMediaAlbumInsertBatch(List<MainMediaAlbum> list) {
        if (list.size() > 0) {
            mainMediaAlbumMapper.insertUpdateBatch(list);
        }
    }

    /**
     * 批量添加标签
     *
     * @param list 数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void mainMediaAlbumDetailInsertBatch(List<MainMediaAlbumDetail> list) {
        if (list.size() > 0) {
            mainMediaAlbumDetailMapper.insertBatch(list);
        }
    }

    /**
     * 下载模板
     *
     * @param resp         HttpServletResponse
     * @param tCategoryIds TCategoryIds
     * @throws IOException 异常处理
     */
    public void generalTemplate(HttpServletResponse resp, TCategoryIds tCategoryIds) throws IOException {
        String fileName = tCategoryIds.getCategoryIdLv1Name() + "_" + tCategoryIds.getCategoryIdLv2Name() + "_" + tCategoryIds.getCategoryIdLv3Name();
        String remark = tCategoryIds.getRemark();
        if (StringUtils.isEmpty(remark)) {
            remark = "基础模板";
        }
        remark = "订单-" + remark;
        DownloadFileUtils.downloadFile(resp, remark, fileName);
    }

    public File getTemplate(Integer categoryId, TCategoryIds tCategoryIds) throws Exception {
        List<MainPlacementExtendAttr> mainPlacementExtendAttrs = mainMediaPlacementEntityQueryMapper.getCategoryIdByExtendAttr(categoryId);
        XlsMainMediaOrderPlacement xlsMainMediaOrderPlacement = exampleData(tCategoryIds);
        return PlacementGeneralTemplateUtils.generalTemplate(categoryId, tCategoryIds, xlsMainMediaOrderPlacement, mainPlacementExtendAttrs);
    }

    private XlsMainMediaOrderPlacement exampleData(TCategoryIds tCategoryIds) {
        XlsMainMediaOrderPlacement placement = new XlsMainMediaOrderPlacement();
        placement.setSerialNumber(1);
        placement.setPlacementSn("添加用例 请勿导入当条数据");
        placement.setMediumLv1CategoryId(tCategoryIds.getCategoryIdLv1Name());
        placement.setMediumLv2CategoryId(tCategoryIds.getCategoryIdLv2Name());
        placement.setMediumLv3CategoryId(tCategoryIds.getCategoryIdLv3Name());
        placement.setMediaName("体育西路站");
        placement.setProvinceId("广东省");
        placement.setCityId("广州市");
        placement.setDistrictId("天河区");
        placement.setStatRegionId("广州市");
        placement.setResourceProviderType("BM");
        placement.setResourceProvider("白马");
        placement.setResourceCount(2);
        placement.setConcentrationValue("10");
        return placement;
    }

}
