package heran.media.management.platform.mediaorder.resolver;

import heran.media.management.platform.mediaorder.sudomain.dto.CellMaxHotValueWktData;
import heran.media.management.platform.mediaorder.sudomain.dto.MergedResidentMapCellIdData;
import heran.media.management.platform.mediaorder.sudomain.dto.ResidentMapCoverageCellData;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.bo.TaStatisticalType;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import heran.media.sharelib.domain.dto.hot.GridHotDto;
import heran.media.sharelib.domain.dto.hot.MainCalendarPlacementData;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class ResidentCalculateTypeResolver extends LockDataCalculateTypeResolver {
    @Override
    public void resolver(Map<String, List<GridHotDto>> gridHotMap, List<MergedResidentMapCellIdData> mergedIds, List<ResidentMapCoverageCellData> coverageCellData, MainCalendarPlacementData placementData, Map<String, CellMaxHotValueWktData> cellWktMap, MainCalendarOrderKpi mainCalendarOrderKpi) {

        //计算曝光人口口径1000网格人口数
        calculatePopulationByType(true, placementData, gridHotMap, mergedIds, StatisticalType.RESIDENT_POPULATION.name(),
                cellWktMap, coverageCellData, mainCalendarOrderKpi::setResidentPopulation);

        //计算曝光人口口径100网格人口数 当前只支持常驻人口
        calculatePopulationByType(false, placementData, gridHotMap, mergedIds, StatisticalType.RESIDENT_POPULATION.name(),
                cellWktMap, coverageCellData, mainCalendarOrderKpi::setAoiResidentPopulation);


        calculatePopulationByType(true, placementData, gridHotMap, mergedIds, StatisticalType.DWELL_POPULATION.name(),
                cellWktMap, coverageCellData, mainCalendarOrderKpi::setAoiDwellPopulation);

        calculatePopulationByType(true, placementData, gridHotMap, mergedIds, StatisticalType.WORK_POPULATION.name(),
                cellWktMap, coverageCellData, mainCalendarOrderKpi::setAoiWorkPopulation);

        calculatePopulationByType(true, placementData, gridHotMap, mergedIds, StatisticalType.PASSENGER_POPULATION.name(),
                cellWktMap, coverageCellData, mainCalendarOrderKpi::setAoiPassengerPopulation);

        //客流小时级别特殊处理 AOI曝光人口等于每个小时AOI人口累加
        calculatePopulationByTypePassengerHour(placementData, gridHotMap, mergedIds, StatisticalType.PASSENGER_HOUR_POPULATION.name(),
                cellWktMap, mainCalendarOrderKpi::setAoiPassengerHourPopulation);


        //TA人口处理
        calculatePopulationByType(true, placementData, gridHotMap, mergedIds, TaStatisticalType.TA_RESIDENT_POPULATION.name(),
                cellWktMap, coverageCellData, mainCalendarOrderKpi::setAoiTaCount);

    }

    @Override
    public void resolverWeightedAverage(Map<String, Map<String, List<GridHotDto>>> gridHotListMap, List<MergedResidentMapCellIdData> mergedIds, Map<String, Map<String, CellMaxHotValueWktData>> monthlyHotDataMap, List<ResidentMapCoverageCellData> coverageCellData, MainCalendarPlacementData placementData, MainCalendarOrderKpi kpi, List<String> statMonths) {

        calculateWeightedAverage(true, StatisticalType.DWELL_POPULATION.name(), gridHotListMap,
                monthlyHotDataMap, coverageCellData, mergedIds, placementData, kpi::setAoiDwellPopulation, statMonths);

        calculateWeightedAverage(true, StatisticalType.WORK_POPULATION.name(), gridHotListMap,
                monthlyHotDataMap, coverageCellData, mergedIds, placementData, kpi::setAoiWorkPopulation, statMonths);

        calculateWeightedAverage(true, StatisticalType.PASSENGER_POPULATION.name(), gridHotListMap,
                monthlyHotDataMap, coverageCellData, mergedIds, placementData, kpi::setAoiPassengerPopulation, statMonths);

        //客流小时的特殊处理 要处理每个小时的人口数
        calculateWeightedAveragePassengerHour(StatisticalType.PASSENGER_HOUR_POPULATION.name(), gridHotListMap,
                monthlyHotDataMap, coverageCellData, mergedIds, placementData, kpi::setAoiPassengerHourPopulation, statMonths);

        calculateWeightedAverage(true, TaStatisticalType.TA_RESIDENT_POPULATION.name(), gridHotListMap,
                monthlyHotDataMap, coverageCellData, mergedIds, placementData, kpi::setAoiTaCount, statMonths);


        //1000网格人口计算
        calculateWeightedAverage(true, StatisticalType.RESIDENT_POPULATION.name(), gridHotListMap,
                monthlyHotDataMap, coverageCellData, mergedIds, placementData, kpi::setResidentPopulation, statMonths);

        //100常驻网格人口计算
        calculateWeightedAverage(false, StatisticalType.RESIDENT_POPULATION.name(), gridHotListMap,
                monthlyHotDataMap, coverageCellData, mergedIds, placementData, kpi::setAoiResidentPopulation, statMonths);
    }

    @Override
    public String type() {
        return "RESIDENT";
    }
}
