package heran.media.management.platform.mediaorder.template;

import heran.media.management.platform.mediaorder.sudomain.dto.CalendarPlacementOrderData;
import heran.media.sharelib.domain.db.model.main.MainCalendarPlacement;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ComputationalDataTemplate {

    /**
     * 查询点位数据
     *
     * @param kpiQueryContext ComputationalDataContext
     * @return List<MainCalendarPlacement>
     */
    List<CalendarPlacementOrderData> buildQuery(ComputationalDataContext kpiQueryContext);

    /**
     * 计算数据
     *
     * @param kpiQueryContext ComputationalDataContext
     */
    void execute(ComputationalDataContext kpiQueryContext);

    /**
     * 模板类型
     *
     * @return String
     */
    String templateName();
}
