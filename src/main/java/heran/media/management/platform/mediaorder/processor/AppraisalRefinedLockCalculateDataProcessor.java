package heran.media.management.platform.mediaorder.processor;

import com.alibaba.nacos.common.utils.CollectionUtils;
import heran.media.management.platform.common.utils.RegionConversionUtils;
import heran.media.management.platform.mediaorder.sudomain.dto.CellMaxHotValueWktData;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import heran.media.sharelib.domain.dto.hot.MainCalendarPlacementData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 评估精细化锁定计算数据处理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AppraisalRefinedLockCalculateDataProcessor extends BaseLockCalculateDataProcessor {

    @Override
    protected void processPlacementBatch(List<MainCalendarPlacementData> placementList, LockDataContext context) {
        // 按城市分组处理
        Map<String, List<MainCalendarPlacementData>> groupedByCity = placementList.stream()
                .collect(Collectors.groupingBy(MainCalendarPlacementData::getRegionCode));

        List<String> statMonths = context.getRequest().getStatMonth();

        for (Map.Entry<String, List<MainCalendarPlacementData>> entry : groupedByCity.entrySet()) {
            String regionCode = entry.getKey();

            regionCode = RegionConversionUtils.regionCodeConversion(regionCode);

            List<MainCalendarPlacementData> placementListData = entry.getValue();

            // 预加载多月份热力数据
            Map<String, Map<String, CellMaxHotValueWktData>> monthlyHotDataMap = preloadMonthlyHotData(context, statMonths, regionCode);

            List<MainCalendarOrderKpi> kpis = processPlacements(placementListData, context, statMonths, monthlyHotDataMap, regionCode);

            // 批量保存KPI数据
            if (CollectionUtils.isNotEmpty(kpis)) {
                mainCalendarOrderKpiMapper.batchInsertUpdate(kpis);
            }
        }
    }

    /**
     * 预加载多月份热力数据
     */
    private Map<String, Map<String, CellMaxHotValueWktData>> preloadMonthlyHotData(LockDataContext context,
                                                                                   List<String> statMonths,
                                                                                   String regionCode) {
        Map<String, Map<String, CellMaxHotValueWktData>> monthlyHotDataMap = new HashMap<>(8);

        for (String statMonth : statMonths) {
            Map<String, CellMaxHotValueWktData> hotValueWktDataMap = preloadHotData(context, statMonth, regionCode);
            monthlyHotDataMap.put(statMonth, hotValueWktDataMap);
        }

        return monthlyHotDataMap;
    }

    /**
     * 处理点位
     */
    private List<MainCalendarOrderKpi> processPlacements(List<MainCalendarPlacementData> placementListData,
                                                         LockDataContext context,
                                                         List<String> statMonths,
                                                         Map<String, Map<String, CellMaxHotValueWktData>> monthlyHotDataMap,
                                                         String regionCode) {
        return baseProcessPlacements(placementListData, context, statMonths, null, monthlyHotDataMap, regionCode, true);
    }


    @Override
    protected String getProcessorName() {
        return "评估精确计算锁定";
    }

    @Override
    public String getType() {
        return "APPRAISAL_REFINED";
    }

    @Override
    protected String getBizData() {
        return "APPRAISAL_REFINED_LOCK_CALCULATE_DATA";
    }
}