package heran.media.management.platform.mediaorder.processor;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderEntityQueryMapper;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.bo.TaStatisticalType;
import heran.media.sharelib.domain.db.mapper.main.GridHotDataMapper;
import heran.media.sharelib.domain.db.mapper.main.MainCalendarOrderKpiMapper;
import heran.media.sharelib.domain.db.mapper.main.MainCalendarPlacementMapper;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import heran.media.sharelib.domain.dto.hot.HotData;
import heran.media.sharelib.domain.dto.hot.MainCalendarPlacementData;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.GridCircleCoverUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class LockBaseProcessor extends BaseCallServiceImpl implements LockProcessor {

    protected final static int EXPORT_PAGE_LIMIT = 5000;


    @Resource
    private MediaOrderEntityQueryMapper mediaOrderEntityQueryMapper;
    @Resource
    private MainCalendarPlacementMapper mainCalendarPlacementMapper;
    @Resource
    private GridHotDataMapper gridHotDataMapper;
    @Resource
    private MainCalendarOrderKpiMapper mainCalendarOrderKpiMapper;

    @Override
    public void execute(LockDataContext context) {
        selectOrderCalendarPlacement(context);
    }

    /**
     * 获取订单下的方案数据
     *
     * @param context 上下文
     */
    private void selectOrderCalendarPlacement(LockDataContext context) {
        Integer orderId = context.getRequest().getOrderId();
        //获取到这个订单下绑定的方案id
        List<Integer> selectionPlanId = mediaOrderEntityQueryMapper.getSelectionPlanId(orderId);
        if (CollectionUtils.isEmpty(selectionPlanId)) {
            return;
        }
        //获取到多个方案下绑定的点位数据 数据量有可能会很多分批执行
        placementData(context, selectionPlanId);
    }

    /**
     * 批量获取点位数据
     *
     * @param context         上下文
     * @param selectionPlanId 方案id
     */
    protected void placementData(LockDataContext context, List<Integer> selectionPlanId) {
        int pageNo = 1;
        Map<String, Map<String, List<GridCircleCoverUtil.Grid>>> hotMapCache = new HashMap<>(8);
        AtomicReference<String> code = new AtomicReference<>("");
        while (true) {
            PageHelper.startPage(pageNo, EXPORT_PAGE_LIMIT, false);
            List<MainCalendarPlacementData> placementList = mainCalendarPlacementMapper.getMainCalendarPlacementByPlanIds(context.getRequest().getCategoryId(), selectionPlanId);
            if (CollectionUtils.isEmpty(placementList)) {
                break;
            }
            //将点位的AOI数据与网格热力数据进行合并
            calendarOrderKpi(placementList, context, hotMapCache, code);
            pageNo++;
        }
    }

    /**
     * 转换为MainCalendarOrderKpi数据
     *
     * @param placementList 点位id
     * @param context       上下文文件
     */
    private void calendarOrderKpi(List<MainCalendarPlacementData> placementList,
                                  LockDataContext context,
                                  Map<String, Map<String, List<GridCircleCoverUtil.Grid>>> hotMapCache, AtomicReference<String> code) {
        // 用于缓存当前处理城市的人群数
        AtomicReference<Integer> cachedTaPopulation = new AtomicReference<>(null);

        List<MainCalendarOrderKpi> mainCalendarOrderKpiList = placementList.stream()
                .map(data -> {
                    if (!data.getRegionCode().equals(code.get())) {
                        // 城市变化时清理旧数据
                        hotMapCache.clear();
                        code.set(data.getRegionCode());
                        cachedTaPopulation.set(getTaPopulation(context, code.get(), context.getRequest().getStatMonth().get(0), TaStatisticalType.getByCode(context.getRequest().getType()).name()));
                    }
                    Map<String, List<GridCircleCoverUtil.Grid>> hotMap = getHotMap(data.getRegionCode(), context, hotMapCache);
                    MainCalendarOrderKpi kpi = buildMainCalendarOrderKpi(context, data, hotMap);
                    kpi.setTaPopulaceCount(cachedTaPopulation.get());
                    return kpi;
                }).collect(Collectors.toList());
        //保存在数据库
        if (CollectionUtils.isNotEmpty(mainCalendarOrderKpiList)) {
            mainCalendarOrderKpiMapper.batchInsertUpdate(mainCalendarOrderKpiList);
            log.info("Processed {} KPI records for calendar placement", mainCalendarOrderKpiList.size());
        }
    }

    /**
     * 获取热力值数据
     *
     * @param cityCode 城市code
     * @param context  上下文
     * @return Map<String, List < GridCircleCoverUtil.Grid>>
     */
    private Map<String, List<GridCircleCoverUtil.Grid>> getHotMap(String cityCode, LockDataContext context,
                                                                  Map<String, Map<String, List<GridCircleCoverUtil.Grid>>> hotMapCache) {
        return hotMapCache.computeIfAbsent(cityCode, c -> {
            String statMonth = context.getRequest().getStatMonth().get(0);
            Map<String, List<GridCircleCoverUtil.Grid>> listMap = buildGrid(statMonth, c);
            Map<String, List<GridCircleCoverUtil.Grid>> taListMap = buildTaHotGrid(context, statMonth, c);
            listMap.putAll(taListMap);
            return listMap;
        });
    }


    /**
     * 人地商热力值数据
     *
     * @param statMonth  月份
     * @param regionCode 区域编号
     * @return Map<String, List < GridCircleCoverUtil.Grid>>
     */
    protected Map<String, List<GridCircleCoverUtil.Grid>> buildGrid(String statMonth, String regionCode) {
        List<HotData> gridHotDataByDataId = gridHotDataMapper.getGridHotDataByStatMonthAndRegionCode(statMonth, regionCode);
        return resultGrid(gridHotDataByDataId);
    }

    /**
     * AT 热力值数据
     *
     * @param context    LockDataContext
     * @param statMonth  月份
     * @param regionCode 区域code
     * @return Map<String, List < GridCircleCoverUtil.Grid>>
     */
    protected Map<String, List<GridCircleCoverUtil.Grid>> buildTaHotGrid(LockDataContext context, String statMonth, String regionCode) {
//        Integer packId = context.getRequest().getPackId();
        Integer packId = 45;
        TaStatisticalType type = TaStatisticalType.getByCode(context.getRequest().getType());
        if (type == null) {
            return new HashMap<>(8);
        }
        Integer taskId = mediaOrderEntityQueryMapper.getTaskId(packId, regionCode, statMonth, type.name());
        if (taskId == null) {
            return new HashMap<>(8);
        }
        //获取热力值数据
        List<HotData> taHotData = mediaOrderEntityQueryMapper.getTaHotData(statMonth, regionCode, taskId);
        if (CollectionUtils.isEmpty(taHotData)) {
            return new HashMap<>(8);
        }
        return resultGrid(taHotData);
    }

    /**
     * 通过热力值计算网格人口数
     *
     * @param taHotData List<HotData>
     * @return Map<String, List < GridCircleCoverUtil.Grid>>
     */
    protected Map<String, List<GridCircleCoverUtil.Grid>> resultGrid(List<HotData> taHotData) {
        return taHotData.stream()
                .collect(Collectors.groupingBy(
                        HotData::getStatisticalType,
                        Collectors.mapping(
                                data -> {
                                    BigDecimal hotValue = new BigDecimal(data.getHotValue());
                                    //0.003 系数 到时候要更换 暂收不用
                                    BigDecimal adjustedValue = hotValue.divide(BigDecimal.valueOf(0.003), 2, RoundingMode.HALF_UP);
                                    return new GridCircleCoverUtil.Grid(Double.parseDouble(data.getLongitude()),
                                            Double.parseDouble(data.getLatitude()),
                                            adjustedValue, new BigDecimal(data.getHotValue()));
                                },
                                Collectors.toList()
                        )
                ));
    }

    /**
     * 获取TA洞察人口数
     *
     * @param context    上下文
     * @param regionCode 城市code
     * @return 人口数
     */
    protected Integer getTaPopulation(LockDataContext context, String regionCode, String statMonth, String typeName) {
//        Integer packId = context.getRequest().getPackId();
        Integer packId = 45;
        return mediaOrderEntityQueryMapper.getTaskIdInsightUv(packId, regionCode, statMonth, typeName);
    }


    /**
     * AOI覆盖网格数据 构建MainCalendarOrderKpi 对象
     *
     * @param placement 点位
     * @param context   上下文
     * @param hotMap    热力值数据
     * @return MainCalendarOrderKpi
     */
    public abstract MainCalendarOrderKpi buildMainCalendarOrderKpi(LockDataContext context, MainCalendarPlacementData placement, Map<String, List<GridCircleCoverUtil.Grid>> hotMap);

    /**
     * 初始化对象
     *
     * @param placementId 点位id
     * @param type        类型
     * @param statMonth   月份
     * @param population  人口口径
     * @return MainCalendarOrderKpi
     */
    public MainCalendarOrderKpi initializeMainCalendarOrderKpi(Long placementId, String type, String statMonth, String population, LockDataContext context) {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        MainCalendarOrderKpi mainCalendarOrderKpi = new MainCalendarOrderKpi();
        mainCalendarOrderKpi.setOrderId(context.getRequest().getOrderId());
        mainCalendarOrderKpi.setCalendarPlacementId(placementId);
        mainCalendarOrderKpi.setKpiType(type);
        boolean monthJson = context.setStatMonthJson();
        //全域效果评估转为数组
        if (monthJson){
            List<String> list = Collections.singletonList(statMonth);
            mainCalendarOrderKpi.setStatMonth(new Gson().toJson(list));
        }
        mainCalendarOrderKpi.setPopulation(population);
        mainCalendarOrderKpi.setAoiTaPopulation(population);
        mainCalendarOrderKpi.setAoiResidentPopulation(0);
        mainCalendarOrderKpi.setAoiDwellPopulation(0);
        mainCalendarOrderKpi.setAoiWorkPopulation(0);
        mainCalendarOrderKpi.setAoiPassengerPopulation(0);
        mainCalendarOrderKpi.setAoiPassengerHourPopulation(0);
        mainCalendarOrderKpi.setAoiTaCount(0);
        setCreatorInfo(currentUser.getIdentifier(), mainCalendarOrderKpi);
        setUpdaterInfo(currentUser.getIdentifier(), mainCalendarOrderKpi);
        return mainCalendarOrderKpi;
    }

    /**
     * @param hotMap          热力值
     * @param statisticalType 人口口径
     * @param aoiCoordinates  点位AOI
     */
    protected Integer setKpiValue(Map<String, List<GridCircleCoverUtil.Grid>> hotMap, String statisticalType, String aoiCoordinates, Double aoiArea) {
        if (!hotMap.isEmpty() && hotMap.containsKey(statisticalType)) {
            List<GridCircleCoverUtil.Grid> gridList = hotMap.get(statisticalType);
            try {
                BigDecimal bigDecimal = GridCircleCoverUtil.areaCalculateCoveredValue(aoiArea, aoiCoordinates, gridList);
                return bigDecimal.intValue();
            } catch (Exception e) {
                log.error("AOI覆盖网格数据异常：{}", e.getMessage());
            }
        }
        return 0;
    }

}
