package heran.media.management.platform.mediaorder.template;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class ComputationalDataTemplateFactory {

    private static final Map<String, ComputationalDataTemplate> COMPUTATIONAL_TEMPLATE_MAP = new HashMap<>();

    public ComputationalDataTemplateFactory(ComputationalDataTemplate... kpiQueryTemplates) {
        for (ComputationalDataTemplate kpiQueryTemplate : kpiQueryTemplates) {
            COMPUTATIONAL_TEMPLATE_MAP.put(kpiQueryTemplate.templateName(), kpiQueryTemplate);
        }
    }

    public ComputationalDataTemplate getTemplate(String templateName) {
        return COMPUTATIONAL_TEMPLATE_MAP.get(templateName);
    }

    public void execute(String templateName, ComputationalDataContext kpiQueryContext) {
        this.getTemplate(templateName).execute(kpiQueryContext);
    }
}
