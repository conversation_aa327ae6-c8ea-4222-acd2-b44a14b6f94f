package heran.media.management.platform.mediaorder.processor;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class AppraisalLockDataKpiTypeProcessor implements LockDataKpiTypeProcessor {

    @Resource
    private LockProcessorFactory lockProcessorFactory;

    @Override
    public void executeKpi(LockDataContext context) {
        if (!context.getRequest().getRefinedCalculation()) {
            lockProcessorFactory.execute(context.getRequest().getType(), context);
        } else {
            //进行精确计算
            String type = "RESIDENT".equals(context.getRequest().getType()) ? "REFINED_RESIDENT" : "REFINED_PASSENGER";
            lockProcessorFactory.execute(type, context);
        }
    }

    @Override
    public String getKpiType() {
        return "APPRAISAL";
    }
}
