package heran.media.management.platform.mediaorder.sudomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.TCategoryIds;
import heran.media.management.platform.mediaorder.sudomain.dto.*;
import heran.media.management.platform.mediaorder.sudomain.request.CalculatePlacementCountRequest;
import heran.media.management.platform.mediaorder.sudomain.request.ConfirmTheDataRequest;
import heran.media.management.platform.mediaorder.sudomain.response.CalculatePlacementCountResponse;
import heran.media.management.platform.mediaorder.sudomain.response.OrderMetaFieldInfo;
import heran.media.sharelib.domain.db.model.main.MediaOrderCompetitorMonitor;
import heran.media.sharelib.domain.dto.hot.GridHotDto;
import heran.media.sharelib.domain.dto.hot.HotData;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Set;

@Mapper
public interface MediaOrderEntityQueryMapper {

    @SelectProvider(type = MediaOrderEntityQuerySQLMapper.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "orderSn", column = "orderSn"),
            @Result(property = "orderType", column = "orderType"),
            @Result(property = "orderName", column = "orderName"),
            @Result(property = "brandName", column = "brandName"),
            @Result(property = "groupName", column = "groupName"),
            @Result(property = "createTime", column = "createTime"),
            @Result(property = "updateTime", column = "updateTime"),
            @Result(property = "orderStatus", column = "orderStatus"),
            @Result(property = "isScheduling", column = "isScheduling"),
            @Result(property = "isUploadMonitoring", column = "isUploadMonitoring"),
            @Result(property = "deliveryStartTime", column = "deliveryStartTime"),
            @Result(property = "deliveryEndTime", column = "deliveryEndTime"),
            @Result(property = "planCount", column = "planCount"),
    }
    )
    List<TMediaOrderRes> list(SearchCriteria criteria);

    @Select("select m1.tag_id from main_media_tag_rel m1 where m1.biz_id = #{id} AND m1.biz_type = #{bizType}")
    List<Integer> getBizIdAndTagId(Integer id, String bizType);

    @Select("SELECT m1.id as id,m1.plan_name as plan_name FROM media_placement_selection_plan m1 " +
            "LEFT JOIN  media_placement_base_sales_plan m2 ON m1.base_plan_id = m2.id WHERE m1.is_deleted = 0")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "planName", column = "planName")
    }
    )
    List<TReferenceSchemeRes> getReferenceSchemeList();

    @Select("SELECT m1.id as id,m2.belong_industry_category_id as belongIndustryCategoryId,m2.brand_name as brandName,m2.group_name as groupName,m2.product_promotion as productPromotion,m2.competitor as competitor,m2.brand_situation as  brandSituation,m2.present_stage_requirement as presentStageRequirement,m2.disseminate_purpose as disseminatePose,m2.core_sales_regions as coreSalesRegions,m1.medium_category_ids as intentionScenes,m2.delivery_start_time as deliveryStartTime,m2.delivery_end_time as deliveryEndTime,m2.budget as budget,m2.actual_amount_spent as actualAmountSpent,m1.plan_name as planName\n" +
            "FROM media_placement_selection_plan m1 " +
            "LEFT JOIN media_placement_base_sales_plan m2 ON m1.base_plan_id = m2.id " +
            "WHERE m1.id = #{planId}")
    @Results(value = {
            @Result(property = "planId", column = "id"),
            @Result(property = "belongIndustryCategoryId", column = "belongIndustryCategoryId"),
            @Result(property = "brandName", column = "brandName"),
            @Result(property = "groupName", column = "groupName"),
            @Result(property = "productPromotion", column = "productPromotion"),
            @Result(property = "competitor", column = "competitor"),
            @Result(property = "brandSituation", column = "brandSituation"),
            @Result(property = "presentStageRequirement", column = "presentStageRequirement"),
            @Result(property = "disseminatePose", column = "disseminatePose"),
            @Result(property = "coreSalesRegions", column = "coreSalesRegions"),
            @Result(property = "intentionScenes", column = "intentionScenes"),
            @Result(property = "deliveryStartTime", column = "deliveryStartTime"),
            @Result(property = "deliveryEndTime", column = "deliveryEndTime"),
            @Result(property = "budget", column = "budget"),
            @Result(property = "actualAmountSpent", column = "actualAmountSpent"),
            @Result(property = "planName", column = "planName")
    }
    )
    TMediaPlacementPlan getMediaPlacementPlanByPlanIdAndPlanName(String planId);


    @Insert({"<script>",
            "INSERT IGNORE INTO media_order_competitor_monitor(id,order_id,competitor_order_id,creator,updater,created_by_user,updated_by_user)",
            "VALUES ",
            "<foreach item='item' collection='competitorMonitorList' separator=','>",
            "(#{item.id}, #{item.orderId}, #{item.competitorOrderId}, #{item.createdByUser}, #{item.updatedByUser}, #{item.creator}, #{item.updater})",
            "</foreach>",
            "ON DUPLICATE KEY UPDATE ",
            "order_id = VALUES(order_id),",
            "competitor_order_id = VALUES(competitor_order_id),",
            "creator = VALUES(creator),",
            "updater = VALUES(updater),",
            "created_by_user = VALUES(created_by_user),",
            "updated_by_user = VALUES(updated_by_user)",
            "</script>"
    })
    void insertUpdateBatch(List<MediaOrderCompetitorMonitor> competitorMonitorList);

    @Delete("delete from media_order_competitor_monitor where order_id = #{id}")
    void deleteCompetitorMonitorByOrderId(Integer id);

    @Delete("delete from order_intelligence_estimate_detail where order_id = #{id}")
    void deleteIntelligenceEstimateDetailOrderId(Integer id);

    @Select("select m1.region_name regionName1,m2.region_name regionName2,m3.region_name regionName3,m1.code as code1,m2.code as code2,m3.code as code3 FROM main_dict_region m1\n" +
            "left JOIN main_dict_region m2 ON m1.code = m2.parent_code " +
            "left JOIN main_dict_region m3 ON m2.code = m3.parent_code " +
            "where m1.level = 1 ")
    @Results(value = {
            @Result(property = "regionName1", column = "regionName1"),
            @Result(property = "regionName2", column = "regionName2"),
            @Result(property = "regionName3", column = "regionName3"),
            @Result(property = "code1", column = "code1"),
            @Result(property = "code2", column = "code2"),
            @Result(property = "code3", column = "code3"),
    }
    )
    List<GaodeArea> getRegionCode();

    @Select({
            "SELECT",
            "t3.id tagId ,t3.parent_id, t3.label,t4.category_name, t4.group_label",
            "FROM main_media_tag t3 ",
            "INNER JOIN main_resource_category t4 ON t3.cagetory_id = t4.id",
            "WHERE t4.group_label = #{groupLabel}"
    })
    @Results(value = {
            @Result(property = "tagId", column = "tagId"),
            @Result(property = "parentId", column = "parent_id"),
            @Result(property = "tagName", column = "label"),
            @Result(property = "categoryName", column = "category_name"),
            @Result(property = "tagCategory", column = "group_label")
    })
    List<MediaTagData> getTagsByGroupLabel(@Param("groupLabel") String groupLabel);

    @Select("select m1.id as categoryIdLv1,m2.id as categoryIdLv2,m3.id as categoryIdLv3,m1.category_name as categoryIdLv1Name,m2.category_name as categoryIdLv2Name,m3.category_name as categoryIdLv3Name from main_resource_category m1 " +
            "LEFT JOIN main_resource_category m2 ON m1.id = m2.parent_id " +
            "LEFT JOIN main_resource_category m3 ON m2.id = m3.parent_id " +
            "WHERE m1.parent_id IS NULL AND m1.group_label = 'MEDIUM_FORMAT'")
    @Results(value = {
            @Result(property = "categoryIdLv1", column = "categoryIdLv1"),
            @Result(property = "categoryIdLv2", column = "categoryIdLv2"),
            @Result(property = "categoryIdLv3", column = "categoryIdLv3"),
            @Result(property = "categoryIdLv1Name", column = "categoryIdLv1Name"),
            @Result(property = "categoryIdLv2Name", column = "categoryIdLv2Name"),
            @Result(property = "categoryIdLv3Name", column = "categoryIdLv3Name")
    })
    List<TCategoryIds> getMediumCategoryId();


    @Select("SELECT m1.id as id,m1.brand_name as brandName FROM media_competitor_brand m1")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "brandName", column = "brandName"),
    }
    )
    List<TCompetitiveMonitoring> getCompetitiveMonitoring();

    @Select({
            "SELECT",
            "t2.biz_id bizId,t3.id tagId ,t3.parent_id, t3.label,t4.category_name, t4.group_label",
            "FROM main_media_tag_rel t2 ",
            "INNER JOIN main_media_tag t3 ON t2.tag_id = t3.id",
            "INNER JOIN main_resource_category t4 ON t3.cagetory_id = t4.id",
            "WHERE t2.biz_type = #{bizType}",
            "AND t2.biz_id = #{orderId}"
    })
    @Results(value = {
            @Result(property = "tagId", column = "tagId"),
            @Result(property = "bizId", column = "bizId"),
            @Result(property = "parentId", column = "parent_id"),
            @Result(property = "tagName", column = "label"),
            @Result(property = "categoryName", column = "category_name"),
            @Result(property = "tagCategory", column = "group_label")
    })
    List<MediaTagData> getTagsByPlanId(Integer orderId, String bizType);

    @Select("SELECT m1.id as id,m1.brand_name as brandName FROM media_competitor_brand m1 " +
            "INNER JOIN media_order_competitor_monitor m2 ON m1.id = m2.competitor_brand_id WHERE m2.order_id = #{orderId}")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "brandName", column = "brandName"),
    }
    )
    List<TCompetitiveMonitoring> getCompetitiveMonitoringByOrderId(Integer orderId);

    @Select("SELECT m3.core_sales_regions as coreSalesRegion,m1.intention_scene as intentionScene " +
            "FROM media_order m1 " +
            "LEFT JOIN media_placement_base_sales_plan m3 ON m1.base_plan_id = m3.id " +
            "WHERE m1.id = #{id}")
    @Results(value = {
            @Result(property = "coreSalesRegion", column = "coreSalesRegion"),
            @Result(property = "intentionScene", column = "intentionScene"),
    }
    )
    TOrderAndBasePlan getIntentionSceneAndCoreSalesRegion(Integer id);

    @Select("SELECT m1.brand_name FROM media_competitor_brand m1")
    List<String> getCompetitor();

    @Delete({
            "<script>",
            "DELETE FROM media_order_competitor_monitor WHERE order_id = #{orderId} and competitor_brand_id IN",
            "<foreach item='id' collection='ids' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    int deleteBatchByIds(@Param("orderId") Integer orderId, @Param("ids") List<Integer> ids);

    /**
     * 获取到订单引用方案绑定的分级分类数据
     *
     * @param orderId 订单id
     * @return List<String>
     */
    @Select("SELECT t3.medium_category_ids FROM media_order t1 JOIN media_order_refer_selection_plan t2 ON t1.id = t2.order_id JOIN media_placement_selection_plan t3 ON t2.selection_plan_id = t3.id WHERE t1.id = #{orderId} AND t2.is_deleted = false")
    List<String> getOrderResourceCategory(@Param("orderId") Integer orderId);

    /**
     * 智选订单智选详情方位点位总览 订单分组
     *
     * @param criteria criteria
     * @return List<OrderSmartDetailsListData>
     */
    @SelectProvider(type = MediaOrderEntityQuerySQLMapper.class, method = "getOrderSmartDetailsListData")
    @Results(value = {
            @Result(property = "orderName", column = "order_name"),
            @Result(property = "cityCount", column = "cityCount"),
            @Result(property = "categoryCount", column = "categoryCount"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
            @Result(property = "periodDays", column = "period_days"),
            @Result(property = "placementCount", column = "placementCount"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "periodAmount", column = "budget"),
    }
    )
    List<OrderSmartDetailsListData> getOrderSmartDetailsListData(SearchCriteria criteria);

    /**
     * 智选订单智选详情方位点位总览 媒体形式分组
     *
     * @param orderId 订单id
     * @return List<OrderSmartDetailsCategoryListData>
     */
    @Select("SELECT t3.medium_lv3_category_id, COUNT( DISTINCT t3.city_id ) AS cityCount, MIN(t3.delivery_start_time) AS delivery_start_time, MAX(t3.delivery_end_time) AS delivery_end_time, " +
            "TIMESTAMPDIFF(DAY, MIN(t3.delivery_start_time), MAX(t3.delivery_end_time)) + 1 AS period_days, COUNT( t3.id ) AS placementCount, t1.update_time, " +
            "SUM((IFNULL( t3.placement_cost, 0 ) * IFNULL( t5.launch_count, 0 ) * ( DATEDIFF( t3.delivery_end_time, t3.delivery_start_time ) + 1 )) + " +
            "(IFNULL( t5.launch, 0 ) * IFNULL( t5.launch_period, 0 ) * IFNULL( t5.issue_amount, 0 )) + (IFNULL( t5.launch, 0 ) * IFNULL( t5.production_amount, 0 )) ) placement_cost " +
            "FROM media_order t1 JOIN media_order_refer_selection_plan t2 ON t1.id = t2.order_id " +
            "LEFT JOIN main_calendar_placement t3 ON t2.selection_plan_id = t3.plan_id " +
            "JOIN media_placement_base_sales_plan t4 ON t4.id = t1.base_plan_id " +
            "LEFT JOIN main_calendar_placement_ext t5 ON t3.id = t5.calendar_placement_id " +
            "WHERE t1.id = #{orderId} AND t3.is_deleted = false AND t2.is_deleted = false AND t3.execute_quotation = true AND (t3.placement_cost > 0 OR t5.launch > 0) GROUP BY t3.medium_lv3_category_id")
    @Results(value = {
            @Result(property = "categoryId", column = "medium_lv3_category_id"),
            @Result(property = "cityCount", column = "cityCount"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
            @Result(property = "periodDays", column = "period_days"),
            @Result(property = "placementCount", column = "placementCount"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "periodAmount", column = "placement_cost"),
    }
    )
    List<OrderSmartDetailsCategoryListData> getOrderSmartDetailsCategoryListData(@Param("orderId") Integer orderId);

    /**
     * 智选订单智选详情方位点位总览 城市分组
     *
     * @param orderId    订单id
     * @param categoryId 分类id
     * @return List<OrderSmartDetailsCityListData>
     */
    @Select("SELECT t3.city_id,MIN( t3.delivery_start_time ) AS delivery_start_time, MAX( t3.delivery_end_time ) AS delivery_end_time," +
            "TIMESTAMPDIFF(DAY,MIN( t3.delivery_start_time ),MAX( t3.delivery_end_time )) + 1 AS period_days,COUNT( t3.id ) AS placementCount, t1.update_time , " +
            "SUM((IFNULL( t3.placement_cost, 0 ) * IFNULL( t5.launch_count, 0 ) * ( DATEDIFF( t3.delivery_end_time, t3.delivery_start_time ) + 1 )) + " +
            "(IFNULL( t5.launch, 0 ) * IFNULL( t5.launch_period, 0 ) * IFNULL( t5.issue_amount, 0 )) + (IFNULL( t5.launch, 0 ) * IFNULL( t5.production_amount, 0 )) ) placement_cost " +
            "FROM media_order t1 JOIN media_order_refer_selection_plan t2 ON t1.id = t2.order_id " +
            "LEFT JOIN main_calendar_placement t3 ON t2.selection_plan_id = t3.plan_id " +
            "JOIN media_placement_base_sales_plan t4 ON t4.id = t1.base_plan_id " +
            "LEFT JOIN main_calendar_placement_ext t5 ON t3.id = t5.calendar_placement_id " +
            "WHERE " +
            "t1.id = #{orderId} AND t3.medium_lv3_category_id = #{categoryId} AND t3.is_deleted = false AND t2.is_deleted = false AND t3.execute_quotation = true AND (t3.placement_cost > 0 OR t5.launch > 0)" +
            "GROUP BY t3.city_id")
    @Results(value = {
            @Result(property = "cityId", column = "city_id"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
            @Result(property = "periodDays", column = "period_days"),
            @Result(property = "placementCount", column = "placementCount"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "periodAmount", column = "placement_cost"),
    }
    )
    List<OrderSmartDetailsCityListData> getOrderSmartDetailsCityListData(@Param("orderId") Integer orderId, @Param("categoryId") Integer categoryId);

    /**
     * 获取到订单引用方案绑定的分级分类数据
     *
     * @param orderId 订单id
     * @return List<String>
     */
    @Select("SELECT t3.sales_city_regions FROM media_order t1 JOIN media_order_refer_selection_plan t2 ON t1.id = t2.order_id JOIN media_placement_selection_plan t3 ON t2.selection_plan_id = t3.id WHERE t1.id = #{orderId} AND t2.is_deleted = false")
    List<String> getOrderResourceCity(@Param("orderId") Integer orderId);

    /**
     * 获取热力值类型
     *
     * @param statMonth 月份
     * @param codes     城市code
     * @return List<GridHotDataOrder>
     */
    @Select("<script>" +
            "SELECT\n" +
            "type_info code ,GROUP_CONCAT( DISTINCT statistical_type ) AS statisticalTypes \n" +
            "FROM\n" +
            "grid_hot_data \n" +
            "WHERE\n" +
            "stat_month = #{statMonth} AND data_type = 'person_land_data' AND type = 3 AND hot_value = '100.0' \n" +
            "AND type_info IN " +
            "<foreach item='code' collection='codes' open='(' separator=',' close=')'>" +
            "#{code}" +
            "</foreach>" +
            "GROUP BY stat_month,type_info" +
            "</script>")
    List<GridHotDataOrder> getGridHotDataOrder(@Param("statMonth") String statMonth, @Param("codes") List<String> codes);

    /**
     * 获取TA热力值类型
     *
     * @param statMonth   月份
     * @param codes       城市code
     * @param crowdPackId 人群包id
     * @return List<GridHotDataOrder>
     */
    @Select("<script>" +
            "SELECT\n" +
            "t1.region_code code ,GROUP_CONCAT( DISTINCT t2.ta_energetics ) AS statisticalTypes \n" +
            "FROM\n" +
            "ta_grid_task_heat_data t1 JOIN media_ta_data_management_task t2 ON t1.task_id = t2.id \n" +
            "WHERE\n" +
            "t1.stat_month = #{statMonth} AND t2.crowd_pack_id = #{crowdPackId} \n" +
            "AND t1.region_code IN " +
            "<foreach item='code' collection='codes' open='(' separator=',' close=')'>" +
            "#{code}" +
            "</foreach>" +
            "GROUP BY t1.stat_month,t1.region_code" +
            "</script>")
    List<GridHotDataOrder> getGridTAHotDataOrder(@Param("statMonth") String statMonth, @Param("crowdPackId") Integer crowdPackId, @Param("codes") List<String> codes);

    /**
     * 获取TA城市人口数
     *
     * @param statMonth   月份
     * @param codes       城市code
     * @param crowdPackId 人群包id
     * @return List<GridHotDataOrder>
     */
    @Select("<script>" +
            "SELECT\n" +
            "t2.region_code code ,t1.ta_population AS statisticalTypes, CASE WHEN COUNT(t2.populace_count) > 0 THEN 1 ELSE 0 END AS has_value \n" +
            "FROM\n" +
            "media_ta_data_management_task t1 JOIN media_ta_data_management_task_detail t2 ON t1.id = t2.management_task_id \n" +
            "WHERE\n" +
            "t1.stat_month = #{statMonth} AND t1.crowd_pack_id = #{crowdPackId} \n" +
            "AND t2.region_code IN " +
            "<foreach item='code' collection='codes' open='(' separator=',' close=')'>" +
            "#{code}" +
            "</foreach>" +
            "GROUP BY t2.region_code, t1.ta_population " +
            "</script>")
    List<GridHotDataOrder> getGridTAPopulationHotDataOrder(@Param("statMonth") String statMonth, @Param("crowdPackId") Integer crowdPackId, @Param("codes") List<String> codes);

    /**
     * 获取订单下绑定的方案id
     *
     * @param orderId 订单id
     * @return List<Integer>
     */
    @Select("SELECT selection_plan_id FROM `media_order_refer_selection_plan` WHERE order_id = #{orderId} AND is_deleted = false")
    List<Integer> getSelectionPlanId(@Param("orderId") Integer orderId);

    /**
     * 获取订单下点位kpi指标数据
     *
     * @param categoryId 分类
     * @param criteria   请求参数
     * @param type       类型
     * @return List<CalendarPlacementOrderData>
     */
    @SelectProvider(type = MediaOrderEntityQuerySQLMapper.class, method = "selectCalendarPlacement")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "orderId", column = "order_id"),
            @Result(property = "calendarPlacementId", column = "calendar_placement_id"),
            @Result(property = "kpiType", column = "kpi_type"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "population", column = "population"),
            @Result(property = "aoiResidentPopulation", column = "aoi_resident_population"),
            @Result(property = "aoiDwellPopulation", column = "aoi_dwell_population"),
            @Result(property = "aoiWorkPopulation", column = "aoi_work_population"),
            @Result(property = "aoiPassengerPopulation", column = "aoi_passenger_population"),
            @Result(property = "aoiPassengerHourPopulation", column = "aoi_passenger_hour_population"),
            @Result(property = "aoiTaPopulation", column = "aoi_ta_population"),
            @Result(property = "aoiTaCount", column = "aoi_ta_count"),
            @Result(property = "heavenProportion", column = "heaven_proportion"),
            @Result(property = "visibleAngle", column = "visible_angle"),
            @Result(property = "categoryId", column = "medium_lv3_category_id"),
            @Result(property = "placementSn", column = "placement_sn"),
            @Result(property = "aoiCoordinates", column = "aoi_coordinates"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
            @Result(property = "placementCost", column = "placement_cost"),
            @Result(property = "launchTime", column = "launch_time"),
            @Result(property = "launchRate", column = "launch_rate"),
            @Result(property = "launch", column = "launch"),
    }
    )
    List<CalendarPlacementOrderData> selectCalendarPlacement(Integer categoryId, SearchCriteria criteria, String type);

    /**
     * 按媒介查询
     *
     * @param orderId orderId
     * @param bizType bizType
     * @return List<OrderEstimateBaseLandData>
     */
    @Select("SELECT t4.population,t3.medium_lv3_category_id categoryId,t5.`code` regionCode,IFNULL(SUM(t6.launch_count), 0) + IFNULL(SUM(t6.launch), 0) AS vacancyPosition," +
            "SUM(t4.pv) pv,SUM(t4.uv) uv,SUM(t4.tauv) taUv,SUM(t4.aoi_passenger_hour_population)aoi_passenger_hour_population,SUM(t4.aoi_passenger_population)aoi_passenger_population, DATEDIFF(MAX(t3.delivery_end_time), MIN(t3.delivery_start_time)) + 1 AS deliveryDays, t4.aoi_passenger_population aoiResidentPopulation, " +
            "SUM((IFNULL( t3.placement_cost, 0 ) * IFNULL( t6.launch_count, 0 ) * ( DATEDIFF( t3.delivery_end_time, t3.delivery_start_time ) + 1 )) + " +
            "(IFNULL( t6.launch, 0 ) * IFNULL( t6.launch_period, 0 ) * IFNULL( t6.issue_amount, 0 )) + (IFNULL( t6.launch, 0 ) * IFNULL( t6.production_amount, 0 )) ) placementCost " +
            "FROM media_order t1 " +
            "JOIN media_order_refer_selection_plan t2 ON t1.id = t2.order_id " +
            "JOIN main_calendar_placement t3 ON t2.selection_plan_id = t3.plan_id " +
            "JOIN main_calendar_order_kpi t4 ON t4.calendar_placement_id = t3.id AND t4.order_id = t1.id " +
            "JOIN main_dict_region t5 ON t5.id = t3.city_id " +
            "LEFT JOIN main_calendar_placement_ext t6 ON t3.id = t6.calendar_placement_id " +
            "WHERE t1.id = #{orderId} AND t4.kpi_type = #{bizType} AND t4.uv IS NOT NULL AND t2.is_deleted = false AND t3.is_deleted = false " +
            "GROUP BY t3.medium_lv3_category_id, t3.city_id")
    List<OrderEstimateBaseLandData> selectOrderMediumEstimateBaseLandData(@Param("orderId") Integer orderId, @Param("bizType") String bizType);

    /**
     * 媒体形式汇总
     *
     * @param orderId    订单id
     * @param bizType    事件类型
     * @param categoryId 分类id
     * @return OrderEstimateBaseLandData
     */
    @Select("SELECT t4.population,t3.medium_lv3_category_id categoryId,t5.`code` regionCode,IFNULL(SUM(t6.launch_count), 0) + IFNULL(SUM(t6.launch), 0) AS vacancyPosition," +
            "SUM(t4.pv) pv,SUM(t4.uv) uv,SUM(t4.tauv) taUv,SUM(t4.aoi_passenger_hour_population)aoi_passenger_hour_population,SUM(t4.aoi_passenger_population)aoi_passenger_population,DATEDIFF(MAX(t3.delivery_end_time), MIN(t3.delivery_start_time)) + 1 AS deliveryDays, t4.aoi_passenger_population aoiResidentPopulation, " +
            "SUM((IFNULL( t3.placement_cost, 0 ) * IFNULL( t6.launch_count, 0 ) * ( DATEDIFF( t3.delivery_end_time, t3.delivery_start_time ) + 1 )) + " +
            "(IFNULL( t6.launch, 0 ) * IFNULL( t6.launch_period, 0 ) * IFNULL( t6.issue_amount, 0 )) + (IFNULL( t6.launch, 0 ) * IFNULL( t6.production_amount, 0 )) ) placementCost " +
            "FROM media_order t1 " +
            "JOIN media_order_refer_selection_plan t2 ON t1.id = t2.order_id " +
            "JOIN main_calendar_placement t3 ON t2.selection_plan_id = t3.plan_id " +
            "JOIN main_calendar_order_kpi t4 ON t4.calendar_placement_id = t3.id AND t4.order_id = t1.id " +
            "JOIN main_dict_region t5 ON t5.id = t3.city_id " +
            "LEFT JOIN main_calendar_placement_ext t6 ON t3.id = t6.calendar_placement_id " +
            "WHERE t1.id = #{orderId} AND t4.kpi_type = #{bizType} AND t3.medium_lv3_category_id = #{categoryId} AND t4.uv IS NOT NULL AND t2.is_deleted = false AND t3.is_deleted = false " +
            "GROUP BY t3.medium_lv3_category_id")
    OrderEstimateBaseLandData selectOrderMediumData(@Param("orderId") Integer orderId, @Param("bizType") String bizType, @Param("categoryId") Integer categoryId);

    /**
     * 关键曝光指标
     *
     * @param orderId 订单id
     * @param bizType 事件类型
     * @return OrderEstimateBaseLandData
     */
    @Select("SELECT '-1' categoryId,'-1' regionCode,COUNT( DISTINCT t3.city_id ) cityCount,COUNT( DISTINCT t3.medium_lv3_category_id )categoryCount,IFNULL(SUM(t6.launch_count), 0) + IFNULL(SUM(t6.launch), 0) AS vacancyPosition," +
            "SUM(t4.pv) pv,SUM(t4.uv) uv,SUM(t4.tauv) taUv,SUM(t4.aoi_passenger_hour_population)aoi_passenger_hour_population,SUM(t4.aoi_passenger_population)aoi_passenger_population, DATEDIFF(MAX(t3.delivery_end_time), MIN(t3.delivery_start_time)) + 1 AS deliveryDays, t4.aoi_passenger_population aoiResidentPopulation,t4.population, " +
            "SUM((IFNULL( t3.placement_cost, 0 ) * IFNULL( t6.launch_count, 0 ) * ( DATEDIFF( t3.delivery_end_time, t3.delivery_start_time ) + 1 )) + " +
            "(IFNULL( t6.launch, 0 ) * IFNULL( t6.launch_period, 0 ) * IFNULL( t6.issue_amount, 0 )) + (IFNULL( t6.launch, 0 ) * IFNULL( t6.production_amount, 0 )) ) placementCost " +
            "FROM media_order t1 " +
            "JOIN media_order_refer_selection_plan t2 ON t1.id = t2.order_id " +
            "JOIN main_calendar_placement t3 ON t2.selection_plan_id = t3.plan_id " +
            "JOIN main_calendar_order_kpi t4 ON t4.calendar_placement_id = t3.id AND t4.order_id = t1.id " +
            "JOIN main_dict_region t5 ON t5.id = t3.city_id " +
            "LEFT JOIN main_calendar_placement_ext t6 ON t3.id = t6.calendar_placement_id " +
            "WHERE t1.id = #{orderId} AND t4.kpi_type = #{bizType} AND t4.uv IS NOT NULL AND t2.is_deleted = false AND t3.is_deleted = false " +
            "GROUP BY t1.id")
    OrderEstimateBaseLandData selectOrderAllCollectData(@Param("orderId") Integer orderId, @Param("bizType") String bizType);

    /**
     * 关键曝光指标计算未计算条数
     *
     * @param orderId 订单id
     * @param bizType 事件类型
     * @return 条数
     */
    @Select("SELECT COUNT(*) FROM media_order t1 JOIN media_order_refer_selection_plan t2 ON t1.id = t2.order_id " +
            "JOIN main_calendar_placement t3 ON t2.selection_plan_id = t3.plan_id LEFT JOIN main_calendar_order_kpi t4 ON t4.calendar_placement_id = t3.id AND t4.order_id = t1.id  AND t4.kpi_type = #{bizType} " +
            "WHERE t1.id = #{orderId} AND t4.uv IS NULL AND t2.is_deleted = false AND t3.is_deleted = false ")
    Integer selectOrderAllCollectDataNotCount(@Param("orderId") Integer orderId, @Param("bizType") String bizType);

    /**
     * 按城市查询
     *
     * @param orderId orderId
     * @param bizType bizType
     * @return List<OrderEstimateBaseLandData>
     */
    @Select("SELECT t4.population,GROUP_CONCAT( DISTINCT t3.medium_lv3_category_id SEPARATOR ',' ) categoryId,t5.`code` regionCode,IFNULL(SUM(t6.launch_count), 0) + IFNULL(SUM(t6.launch), 0) AS vacancyPosition," +
            "SUM(t4.pv) pv,SUM(t4.uv) uv,SUM(t4.tauv) taUv,AVG(t4.af) af,SUM(t4.aoi_passenger_hour_population)aoi_passenger_hour_population,SUM(t4.aoi_passenger_population)aoi_passenger_population,DATEDIFF(MAX(t3.delivery_end_time), MIN(t3.delivery_start_time)) + 1 AS deliveryDays,t4.aoi_passenger_population aoiResidentPopulation, " +
            "SUM((IFNULL( t3.placement_cost, 0 ) * IFNULL( t6.launch_count, 0 ) * ( DATEDIFF( t3.delivery_end_time, t3.delivery_start_time ) + 1 )) + " +
            "(IFNULL( t6.launch, 0 ) * IFNULL( t6.launch_period, 0 ) * IFNULL( t6.issue_amount, 0 )) + (IFNULL( t6.launch, 0 ) * IFNULL( t6.production_amount, 0 )) ) placementCost " +
            "FROM media_order t1 " +
            "JOIN media_order_refer_selection_plan t2 ON t1.id = t2.order_id " +
            "JOIN main_calendar_placement t3 ON t2.selection_plan_id = t3.plan_id " +
            "JOIN main_calendar_order_kpi t4 ON t4.calendar_placement_id = t3.id AND t4.order_id = t1.id " +
            "JOIN main_dict_region t5 ON t5.id = t3.city_id " +
            "LEFT JOIN main_calendar_placement_ext t6 ON t3.id = t6.calendar_placement_id " +
            "WHERE t1.id = #{orderId} AND t4.kpi_type = #{bizType} AND t4.uv IS NOT NULL AND t2.is_deleted = false AND t3.is_deleted = false " +
            "GROUP BY t3.city_id")
    List<OrderEstimateBaseLandData> selectOrderCityEstimateBaseLandData(@Param("orderId") Integer orderId, @Param("bizType") String bizType);

    /**
     * 修改按城市 计算重叠比例
     *
     * @param calculateRatio   是否计算重叠比例
     * @param ratioCoefficient 重叠比例
     * @param orderId          订单id
     */
    @Update("UPDATE media_order SET calculate_ratio = #{calculateRatio}, ratio_coefficient = #{ratioCoefficient} WHERE id = #{orderId}")
    void updateCalculateRatio(@Param("calculateRatio") Boolean calculateRatio, @Param("ratioCoefficient") Double ratioCoefficient, @Param("orderId") Integer orderId);

    /**
     * 全域效果评估修改按城市 计算重叠比例
     *
     * @param estimateCalculateRatio   是否计算重叠比例
     * @param estimateRatioCoefficient 重叠比例
     * @param orderId                  订单id
     */
    @Update("UPDATE media_order SET estimate_calculate_ratio = #{estimateCalculateRatio}, estimate_ratio_coefficient = #{estimateRatioCoefficient} WHERE id = #{orderId}")
    void updateEstimateCalculateRatio(@Param("estimateCalculateRatio") Boolean estimateCalculateRatio, @Param("estimateRatioCoefficient") Double estimateRatioCoefficient, @Param("orderId") Integer orderId);

    /**
     * 确认数据
     *
     * @param request ConfirmTheDataRequest
     */
    @Update("UPDATE order_effect_estimate_base_man_land SET confirm_the_data = true WHERE order_id = #{request.orderId} AND biz_type = #{request.bizType} AND exposure_type = #{request.exposureType} ")
    void confirmTheData(@Param("request") ConfirmTheDataRequest request);

    /**
     * 计算点位计算数量
     *
     * @param request CalculatePlacementCountRequest
     * @return CalculatePlacementCountResponse
     */
    @Select("<script>" +
            "SELECT " +
            "    SUM(CASE WHEN t3.uv IS NULL THEN 1 ELSE 0 END) AS notCalculateCount, " +
            "    SUM(CASE WHEN t3.uv IS NOT NULL THEN 1 ELSE 0 END) AS calculateCount " +
            "FROM media_order_refer_selection_plan t1 " +
            "JOIN main_calendar_placement t2 ON t1.selection_plan_id = t2.plan_id " +
            "LEFT JOIN main_calendar_placement_ext t4 ON t2.id = t4.calendar_placement_id " +
            "LEFT JOIN main_calendar_order_kpi t3 ON t3.calendar_placement_id = t2.id " +
            "    AND t3.kpi_type = #{request.bizType} " +
            "    AND t3.order_id = #{request.orderId} " +
            "WHERE t1.order_id = #{request.orderId} AND t2.is_deleted = false AND t1.is_deleted = false AND t2.execute_quotation = true AND (t2.placement_cost > 0 OR t4.launch > 0) " +
            "<if test='request.categoryId != null'> " +
            "    AND t2.medium_lv3_category_id = #{request.categoryId} " +
            "</if>" +
            "</script>")
    CalculatePlacementCountResponse calculatePlacementCount(@Param("request") CalculatePlacementCountRequest request);

    /**
     * 删除标签
     *
     * @param bizId   bizId
     * @param bizType bizType
     */
    @Delete("DELETE FROM main_media_tag_rel WHERE biz_id= #{bizId} AND biz_type = #{bizType}")
    void deleteMainMediaTagRelByBizIdAndBizType(@Param("bizId") Integer bizId, @Param("bizType") String bizType);


    /**
     * getOrderPlacementList
     *
     * @param criteria SearchCriteria
     * @param type     type
     * @return List<OrderMetaFieldInfo>
     */
    @SelectProvider(type = MediaOrderEntityQuerySQLMapper.class, method = "selectPlacementList")
    @Results(value = {
            @Result(property = "id", column = "calendarPlacementId"),
    }
    )
    List<OrderMetaFieldInfo> getOrderPlacementList(SearchCriteria criteria, String type);

    /**
     * 点位详情数据
     *
     * @param placementId 点位id
     * @param orderId     订单id
     * @param type        类型
     * @return OrderMetaFieldInfo
     */
    @SelectProvider(type = MediaOrderEntityQuerySQLMapper.class, method = "getByIdOrderPlacementDetails")
    @Results(value = {
            @Result(property = "id", column = "calendarPlacementId"),
    }
    )
    OrderMetaFieldInfo getByIdOrderPlacementDetails(Long placementId, Integer orderId, String type);

    /**
     * 获取任务id
     *
     * @param packId     人群包id
     * @param regionCode 区域code
     * @param statMonth  月份
     * @return 任务id
     */
    @Select("SELECT t1.id FROM media_ta_data_management_task t1 JOIN media_ta_data_management_task_detail t2 ON t1.id = t2.management_task_id WHERE t1.stat_month = #{statMonth} AND t1.crowd_pack_id = #{packId} AND t2.region_code = #{regionCode} AND t1.ta_energetics = #{taPopulation} ORDER BY t1.create_time DESC LIMIT 1")
    Integer getTaskId(@Param("packId") Integer packId, @Param("regionCode") String regionCode, @Param("statMonth") String statMonth, @Param("taPopulation") String taPopulation);

    /**
     * 获取到TA人口数
     *
     * @param packId
     * @param regionCode
     * @param statMonth
     * @return
     */
    @Select("SELECT t2.populace_count FROM media_ta_data_management_task t1 JOIN media_ta_data_management_task_detail t2 ON t1.id = t2.management_task_id WHERE t1.stat_month = #{statMonth} AND t1.crowd_pack_id = #{packId} AND t2.region_code = #{regionCode} LIMIT 1")
    Integer getTaPopulation(@Param("packId") Integer packId, @Param("regionCode") String regionCode, @Param("statMonth") String statMonth);

    /**
     * 获取TA热力值
     *
     * @param statMonth  月份
     * @param regionCode 区域code
     * @param taskId     任务id
     * @return List<HotData>
     */
    @Select("SELECT center_lng longitude,center_lat latitude,heat hotValue,ta_population statisticalType FROM ta_grid_task_heat_data WHERE region_code = #{regionCode} AND stat_month = #{statMonth} AND task_id = #{taskId}")
    List<HotData> getTaHotData(@Param("statMonth") String statMonth, @Param("regionCode") String regionCode, @Param("taskId") Integer taskId);


    /**
     * 获取任务id
     *
     * @param packId     人群包id
     * @param regionCode 区域code
     * @param statMonth  月份
     * @return 任务id
     */
    @Select("SELECT t2.populace_count FROM media_ta_data_management_task t1 JOIN media_ta_data_management_task_detail t2 ON t1.id = t2.management_task_id WHERE t1.stat_month = #{statMonth} AND t1.crowd_pack_id = #{packId} AND t2.region_code = #{regionCode} AND t1.ta_insight = #{taPopulation} ORDER BY t1.create_time DESC LIMIT 1")
    Integer getTaskIdInsightUv(@Param("packId") Integer packId, @Param("regionCode") String regionCode, @Param("statMonth") String statMonth, @Param("taPopulation") String taPopulation);

    /**
     * 根据中心点获取到聚合网格数据
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return MergedResidentMapCellData
     */
    @Select("SELECT lower_left_lng,lower_left_lat,lower_right_lng,lower_right_lat,up_left_lng,up_left_lat,up_right_lng,up_right_lat,people_count FROM `merged_resident_map_cell_resource` WHERE center_lng = #{longitude} AND center_lat = #{latitude}")
    MergedResidentMapCellData getMergedResidentMapCellData(@Param("longitude") String longitude, @Param("latitude") String latitude);

    /**
     * 空间算法计算点位AOI覆盖了多少个1000*1000的网格数据
     *
     * @param regionCode 区域code
     * @param aoiWkt     wkt
     * @return List<Long>
     */
//    @Select({
//            "<script>",
//            "SELECT t1.id mergedId, t2.hot_value hotValue, t2.statistical_type statisticalType,t1.people_count,t2.stat_month",
//            "FROM merged_resident_map_cell_resource t1",
//            "AND t2.stat_month IN",
//            "<foreach collection='statMonths' item='month' open='(' separator=',' close=')'>",
//            "#{month}",
//            "</foreach>",
//            "AND t2.type_info = #{regionCode}",
//            "WHERE t1.city_code = #{regionCode}",
//            "AND ST_Intersects(ST_GeomFromText(#{aoiWkt}),",
//            "ST_PolygonFromText(CONCAT('POLYGON((' , t1.lower_left_lng, ' ', t1.lower_left_lat, ',',",
//            "t1.lower_right_lng, ' ', t1.lower_right_lat, ',', t1.up_right_lng, ' ', t1.up_right_lat, ',',",
//            "t1.up_left_lng, ' ', t1.up_left_lat, ',', t1.lower_left_lng, ' ', t1.lower_left_lat, '))')))",
//            "</script>"
//    })
//    List<MergedResidentMapCellIdData> getMergedResidentMapCellId(
//            @Param("regionCode") String regionCode,
//            @Param("statMonths") List<String> statMonths,
//            @Param("aoiWkt") String aoiWkt
//    );

    /**
     * 空间算法计算点位AOI覆盖了多少个1000*1000的网格数据
     *
     * @param regionCode 区域code
     * @param aoiWkt     wkt
     * @return List<Long>
     */
    @Select("SELECT id,people_count,center_lng,center_lat FROM merged_resident_map_cell_resource WHERE city_code = #{regionCode}  AND ST_Intersects( ST_GeomFromText(#{aoiWkt}),ST_PolygonFromText(CONCAT( 'POLYGON((',lower_left_lng, ' ', lower_left_lat, ',',lower_right_lng, ' ', lower_right_lat, ',', up_right_lng, ' ', up_right_lat, ',',up_left_lng, ' ', up_left_lat, ',',lower_left_lng, ' ', lower_left_lat,'))')))")
    List<MergedResidentMapCellIdData> getMergedResidentMapCellId(@Param("regionCode") String regionCode, @Param("aoiWkt") String aoiWkt);

    /**
     * 空间算法计算点位AOI覆盖了多少个TA 1000*1000的网格数据
     *
     * @param regionCode 区域code
     * @param aoiWkt     wkt
     * @return List<Long>
     */
    @Select({
            "<script>",
            "SELECT t1.id mergedId, t2.heat hotValue, t2.ta_population statisticalType",
            "FROM merged_resident_map_cell_resource t1",
            "JOIN ta_grid_task_heat_data t2 ON t1.center_lng = t2.center_lng AND t1.center_lat = t2.center_lat AND t2.task_id = #{taskId}",
            "AND t2.stat_month IN",
            "<foreach collection='statMonths' item='month' open='(' separator=',' close=')'>",
            "#{month}",
            "</foreach>",
            "AND t2.region_code = #{regionCode}",
            "WHERE t1.city_code = #{regionCode}",
            "AND ST_Intersects(ST_GeomFromText(#{aoiWkt}),",
            "ST_PolygonFromText(CONCAT('POLYGON((' , t1.lower_left_lng, ' ', t1.lower_left_lat, ',',",
            "t1.lower_right_lng, ' ', t1.lower_right_lat, ',', t1.up_right_lng, ' ', t1.up_right_lat, ',',",
            "t1.up_left_lng, ' ', t1.up_left_lat, ',', t1.lower_left_lng, ' ', t1.lower_left_lat, '))')))",
            "</script>"
    })
    List<MergedResidentMapCellIdData> getMergedResidentMapCellIdTa(
            @Param("taskId") Integer taskId,
            @Param("regionCode") String regionCode,
            @Param("statMonths") List<String> statMonths,
            @Param("aoiWkt") String aoiWkt
    );

    /**
     * 空间算法计算点位覆盖100*100的网格数据汇总数据
     *
     * @param mergeIds 举1000*1000网格数据id
     * @param aoiWkt   点位AOI
     * @return ResidentMapCoverageCellData
     */
    @Select({
            "<script>",
            "SELECT",
            "  merge_id mergeId,",
            "  COUNT(*) AS cellCount,",
            "  SUM(people_count) AS peopleCount",
            "FROM resident_map_cell_resource",
            "WHERE merge_id IN",
            "  <foreach item='id' index='index' collection='mergeIds' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach>",
            "AND ST_Intersects(",
            "  ST_GeomFromText(#{aoiWkt}),",
            "  ST_PolygonFromText(CONCAT(",
            "    'POLYGON((' ,",
            "    lower_left_lng, ' ', lower_left_lat, ',',",
            "    lower_right_lng, ' ', lower_right_lat, ',',",
            "    up_right_lng, ' ', up_right_lat, ',',",
            "    up_left_lng, ' ', up_left_lat, ',',",
            "    lower_left_lng, ' ', lower_left_lat,",
            "    '))'",
            "  ))",
            ") GROUP BY merge_id",
            "</script>"
    })
    List<ResidentMapCoverageCellData> selectIntersectCountAndSum(@Param("mergeIds") List<Long> mergeIds, @Param("aoiWkt") String aoiWkt);

/*    @Select({
            "<script>",
            "SELECT r.id AS mergedId, r.center_lng, r.center_lat, r.people_count, p.id placementId",
            "FROM merged_resident_map_cell_resource r",
            "JOIN main_dict_region m ON r.city_code = m.`code` AND `level` = 2",
            "JOIN main_calendar_placement p ON p.city_id = m.id",
            "WHERE r.city_code = #{regionCode}",
            "  AND p.id IN",
            "  <foreach item='id' collection='placementIdList' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach>",
            "  AND p.aoi_coordinates IS NOT NULL",
            "  AND ST_Intersects(",
            "        p.aoi_wkt,",
            "        ST_PolygonFromText(CONCAT(",
            "            'POLYGON((' ,",
            "            r.lower_left_lng, ' ', r.lower_left_lat, ',',",
            "            r.lower_right_lng, ' ', r.lower_right_lat, ',',",
            "            r.up_right_lng, ' ', r.up_right_lat, ',',",
            "            r.up_left_lng, ' ', r.up_left_lat, ',',",
            "            r.lower_left_lng, ' ', r.lower_left_lat,",
            "            '))'",
            "        ))",
            "    ) = 1",
            "</script>"
    })
    List<MergedResidentMapCellIdData> batchGetMergedResidentMapCellId(
            @Param("regionCode") String regionCode,
            @Param("placementIdList") List<Long> placementIdList
    );*/



    @Select({
            "<script>",
            "SELECT r.id AS mergedId, r.center_lng, r.center_lat, r.people_count, p.id placementId",
            "FROM merged_resident_map_cell_resource r",
            "JOIN main_calendar_placement p ON ST_Intersects ( p.aoi_wkt, r.grid_geom_0 )",
            "WHERE p.aoi_coordinates IS NOT NULL AND p.id IN",
            "  <foreach item='id' collection='placementIdList' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach>",
            "</script>"
    })
    List<MergedResidentMapCellIdData> batchGetMergedResidentMapCellId(
            @Param("regionCode") String regionCode,
            @Param("placementIdList") List<Long> placementIdList
    );





/*    @Select({
            "<script>",
            "SELECT",
            "  r.merge_id AS mergeId, p.id placementId,",
            "  COUNT(*) AS cellCount,",
            "  SUM(r.people_count) AS peopleCount",
            "FROM resident_map_cell_resource r",
            "JOIN main_dict_region m ON r.city_code = m.`code`",
            "JOIN main_calendar_placement p ON p.city_id = m.id",
            "WHERE r.merge_id IN",
            "  <foreach item='id' collection='mergedIds' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach>",
            "  AND p.id IN",
            "  <foreach item='pid' collection='placementIdList' open='(' separator=',' close=')'>",
            "    #{pid}",
            "  </foreach>",
            "  AND p.aoi_coordinates IS NOT NULL",
            "  AND ST_Intersects(",
            "        p.aoi_wkt,",
            "        ST_PolygonFromText(CONCAT(",
            "            'POLYGON((' ,",
            "            r.lower_left_lng, ' ', r.lower_left_lat, ',',",
            "            r.lower_right_lng, ' ', r.lower_right_lat, ',',",
            "            r.up_right_lng, ' ', r.up_right_lat, ',',",
            "            r.up_right_lng, ' ', r.up_right_lat, ',',",
            "            r.up_right_lng, ' ', r.up_right_lat, ',',",
            "            '))'",
            "        ))",
            "    ) = 1",
            "GROUP BY r.merge_id,p.id",
            "</script>"
    })
    List<ResidentMapCoverageCellData> batchSelectIntersectCountAndSum(
            @Param("mergedIds") List<Long> mergedIds,
            @Param("placementIdList") List<Long> placementIdList
    );*/


    @Select({
            "<script>",
            "SELECT",
            "  r.merge_id AS mergeId, p.id placementId,",
            "  COUNT(*) AS cellCount,",
            "  SUM(r.people_count) AS peopleCount",
            "FROM main_calendar_placement p",
            "JOIN resident_map_cell_resource r ON ST_Intersects ( p.aoi_wkt, r.grid_geom_0 ) ",
            "WHERE  p.aoi_coordinates IS NOT NULL AND p.id IN",
            "  <foreach item='pid' collection='placementIdList' open='(' separator=',' close=')'>",
            "    #{pid}",
            "  </foreach>",
            "GROUP BY r.merge_id,p.id",
            "</script>"
    })
    List<ResidentMapCoverageCellData> batchSelectIntersectCountAndSum(
            @Param("mergedIds") List<Long> mergedIds,
            @Param("placementIdList") List<Long> placementIdList
    );


    @Select({
            "<script>",
            "SELECT longitude,latitude,hot_value,statistical_type,stat_month,time_index ",
            "FROM grid_hot_data",
            "WHERE stat_month = #{statMonth}",
            "  AND type_info = #{regionCode}",
            "  AND data_type = 'person_land_data'",
            "  AND extra_attrs IN",
            "  <foreach collection='keys' item='key' open='(' separator=',' close=')'>",
            "    #{key}",
            "  </foreach>",
            "</script>"
    })
    List<GridHotDto> batchGetGridHotDataByKeys(
            @Param("keys") Set<String> keys,
            @Param("statMonth") String statMonth,
            @Param("regionCode") String regionCode
    );

    @Select({
            "<script>",
            "SELECT stat_month,longitude,latitude,hot_value,statistical_type,time_index ",
            "FROM grid_hot_data",
            "WHERE stat_month IN",
            "<foreach collection='statMonths' item='statMonth' open='(' separator=',' close=')'>",
            "  #{statMonth}",
            "</foreach>",
            "  AND type_info = #{regionCode}",
            "  AND data_type = 'person_land_data'",
            "  AND extra_attrs IN",
            "<foreach collection='keys' item='key' open='(' separator=',' close=')'>",
            "  #{key}",
            "</foreach>",
            "</script>"
    })
    List<GridHotDto> batchGetGridHotDataByKeysByStatMonthList(
            @Param("keys") Set<String> keys,
            @Param("statMonths") List<String> statMonths,
            @Param("regionCode") String regionCode
    );

    @Select({
            "<script>",
            "SELECT stat_month,center_lng longitude,center_lat latitude,heat hot_value,ta_population statistical_type ",
            "FROM ta_grid_task_heat_data",
            "WHERE stat_month = #{statMonth}",
            "  AND region_code = #{regionCode}",
            "  AND task_id = #{taskId}",
            "  AND ta_population = #{taPopulation}",
            "  AND extra_attrs IN",
            "  <foreach collection='keys' item='key' open='(' separator=',' close=')'>",
            "    #{key}",
            "  </foreach>",
            "</script>"
    })
    List<GridHotDto> batchGetTaGridHotDataByKeys(
            @Param("keys") Set<String> keys,
            @Param("taskId") Integer taskId,
            @Param("taPopulation") String taPopulation,
            @Param("statMonth") String statMonth,
            @Param("regionCode") String regionCode
    );


    @Select("SELECT SUM(aoi_work_population)aoiWorkPopulationCount,SUM(aoi_dwell_population)aoiDwellPopulationCount,SUM(resident_population)residentPopulation FROM `main_calendar_order_kpi` WHERE order_id = #{orderId} AND kpi_type = #{dataType}")
    OrderKpiValueData getOrderKpiValueData(@Param("orderId") Integer orderId, @Param("dataType") String dataType);


    @Select("SELECT\n" +
            "\tt3.`code` regionCode,\n" +
            "\tt1.stat_month \n" +
            "FROM\n" +
            "\t`main_calendar_order_kpi` t1\n" +
            "\tJOIN main_calendar_placement t2 ON t1.calendar_placement_id = t2.id\n" +
            "\tJOIN main_dict_region t3 ON t3.id = t2.city_id \n" +
            "WHERE\n" +
            "\torder_id = #{orderId} \n" +
            "\tAND kpi_type = #{dataType} \n" +
            "GROUP BY\n" +
            "\tt3.`code`,\n" +
            "\tt1.stat_month")
    List<CityResidentPopulationData> getCityResidentPopulationData(@Param("orderId") Integer orderId, @Param("dataType") String dataType);

    @Select("SELECT\n" +
            "\tt3.`code` regionCode,\n" +
            "\tt1.stat_month, \n" +
            "\tt2.medium_lv3_category_id categoryId \n" +
            "FROM\n" +
            "\t`main_calendar_order_kpi` t1\n" +
            "\tJOIN main_calendar_placement t2 ON t1.calendar_placement_id = t2.id\n" +
            "\tJOIN main_dict_region t3 ON t3.id = t2.city_id \n" +
            "WHERE\n" +
            "\torder_id = #{orderId} \n" +
            "\tAND kpi_type = #{dataType} \n" +
            "GROUP BY\n" +
            "\tt2.medium_lv3_category_id,\n" +
            "\tt3.`code`,\n" +
            "\tt1.stat_month")
    List<CityResidentPopulationData> getCityResidentPopulationCategoryData(@Param("orderId") Integer orderId, @Param("dataType") String dataType);

}
