package heran.media.management.platform.mediaorder.resolver;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.main.util.LngLatKeyUtils;
import heran.media.management.platform.mediaorder.sudomain.dto.CellMaxHotValueWktData;
import heran.media.management.platform.mediaorder.sudomain.dto.MergedResidentMapCellIdData;
import heran.media.management.platform.mediaorder.sudomain.dto.ResidentMapCoverageCellData;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import heran.media.sharelib.domain.dto.hot.GridHotDto;
import heran.media.sharelib.domain.dto.hot.MainCalendarPlacementData;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static heran.media.sharelib.utils.date.DataTmeUtils.getCustomDaysInMonth;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class LockDataCalculateTypeResolver {

    private static final int DECIMAL_PRECISION = 10;
    private static final Integer CELL_AREA = 10000;
    private static final Integer CELL_AREA_1000 = 1000000;

    public abstract void resolver(Map<String, List<GridHotDto>> gridHotMap, List<MergedResidentMapCellIdData> mergedIds, List<ResidentMapCoverageCellData> coverageCellData, MainCalendarPlacementData placementData,
                                  Map<String, CellMaxHotValueWktData> cellWktMap, MainCalendarOrderKpi mainCalendarOrderKpi);

    public abstract void resolverWeightedAverage(Map<String, Map<String, List<GridHotDto>>> gridHotListMap, List<MergedResidentMapCellIdData> mergedIds, Map<String, Map<String, CellMaxHotValueWktData>> monthlyHotDataMap, List<ResidentMapCoverageCellData> coverageCellData, MainCalendarPlacementData placementData, MainCalendarOrderKpi kpi, List<String> statMonths);

    abstract String type();


    /**
     * 按类型计算100网格人口数据
     */
    protected void calculatePopulationByType(boolean isThousand, MainCalendarPlacementData placementData,
                                             Map<String, List<GridHotDto>> gridHotMap,
                                             List<MergedResidentMapCellIdData> mergedIds,
                                             String statisticalType,
                                             Map<String, CellMaxHotValueWktData> cellWktMap,
                                             List<ResidentMapCoverageCellData> coverageCellData,
                                             Consumer<Integer> setter) {
        //获取这个城市下 这个人口口径下 这个月份下得 热力指数数据 用于uv的计算
        List<GridHotDto> gridHotDtoList = gridHotMap.get(statisticalType);
        Map<String, String> gridHotData = gridHotDataMap(gridHotDtoList);
        CellMaxHotValueWktData cellMaxHotValueWktData = cellWktMap.get(statisticalType);

        Integer peopleCount = null;

        if (isThousand) {
            peopleCount = calculate1000PopulationCommon(placementData, mergedIds, cellMaxHotValueWktData, gridHotData);
        } else {
            peopleCount = calculatePopulationCommon(placementData, mergedIds, cellMaxHotValueWktData, gridHotData, coverageCellData);
        }

        setter.accept(peopleCount);
    }

    protected void calculateWeightedAverageUnified(boolean isThousand,
                                                   boolean isPassengerHour,
                                                   String statisticalType,
                                                   Map<String, Map<String, List<GridHotDto>>> gridHotListMap,
                                                   Map<String, Map<String, CellMaxHotValueWktData>> monthlyHotDataMap,
                                                   List<ResidentMapCoverageCellData> coverageCellData,
                                                   List<MergedResidentMapCellIdData> mergedIds,
                                                   MainCalendarPlacementData placementData,
                                                   Consumer<Integer> setter,
                                                   List<String> statMonths) {

        if (monthlyHotDataMap.isEmpty()
                || CollectionUtils.isEmpty(coverageCellData)
                || CollectionUtils.isEmpty(mergedIds)) {
            setter.accept(0);
            return;
        }

        int totalDays = statMonths.stream().mapToInt(this::getMonthDays).sum();
        if (totalDays == 0) {
            setter.accept(0);
            return;
        }

        BigDecimal weightedSum = BigDecimal.ZERO;

        for (String statMonth : statMonths) {
            Map<String, CellMaxHotValueWktData> monthHotDataMap = monthlyHotDataMap.get(statMonth);
            if (monthHotDataMap == null) {
                continue;
            }

            CellMaxHotValueWktData cellData = monthHotDataMap.get(statisticalType);
            if (cellData == null) {
                continue;
            }

            Map<String, List<GridHotDto>> gridMap = gridHotListMap.get(statMonth);
            if (gridMap == null || gridMap.isEmpty()) {
                continue;
            }

            List<GridHotDto> gridHotDtoList = gridMap.get(statisticalType);
            if (CollectionUtils.isEmpty(gridHotDtoList)) {
                continue;
            }

            int monthPeopleCount;

            if (isPassengerHour) {
                // 分组：按小时 timeIndex
                Map<Integer, List<GridHotDto>> groupedByTimeIndex = gridHotDtoList.stream()
                        .collect(Collectors.groupingBy(GridHotDto::getTimeIndex));
                if (groupedByTimeIndex.isEmpty()) {
                    continue;
                }

                monthPeopleCount = getPassengerHourPeopleCount(groupedByTimeIndex, placementData, mergedIds, cellData);

            } else {
                Map<String, String> gridHotData = gridHotDataMap(gridHotDtoList);

                monthPeopleCount = isThousand
                        ? calculate1000PopulationCommon(placementData, mergedIds, cellData, gridHotData)
                        : calculatePopulationCommon(placementData, mergedIds, cellData, gridHotData, coverageCellData);
            }

            int monthDays = getMonthDays(statMonth);
            weightedSum = weightedSum.add(BigDecimal.valueOf(monthPeopleCount).multiply(BigDecimal.valueOf(monthDays)));
        }

        int weightedAverage = weightedSum.divide(BigDecimal.valueOf(totalDays), 0, RoundingMode.HALF_UP).intValue();
        setter.accept(weightedAverage);
    }


    /**
     * 计算加权平均
     */
    protected void calculateWeightedAverage(boolean isThousand, String statisticalType,
                                            Map<String, Map<String, List<GridHotDto>>> gridHotListMap,
                                            Map<String, Map<String, CellMaxHotValueWktData>> monthlyHotDataMap,
                                            List<ResidentMapCoverageCellData> coverageCellData,
                                            List<MergedResidentMapCellIdData> mergedIds,
                                            MainCalendarPlacementData placementData,
                                            Consumer<Integer> setter,
                                            List<String> statMonths) {
        calculateWeightedAverageUnified(
                isThousand,
                false,
                statisticalType,
                gridHotListMap,
                monthlyHotDataMap,
                coverageCellData,
                mergedIds,
                placementData,
                setter,
                statMonths
        );

    }

    protected void calculateWeightedAveragePassengerHour(String statisticalType,
                                                         Map<String, Map<String, List<GridHotDto>>> gridHotListMap,
                                                         Map<String, Map<String, CellMaxHotValueWktData>> monthlyHotDataMap,
                                                         List<ResidentMapCoverageCellData> coverageCellData,
                                                         List<MergedResidentMapCellIdData> mergedIds,
                                                         MainCalendarPlacementData placementData,
                                                         Consumer<Integer> setter,
                                                         List<String> statMonths) {

        calculateWeightedAverageUnified(
                false,
                true,
                statisticalType,
                gridHotListMap,
                monthlyHotDataMap,
                coverageCellData,
                mergedIds,
                placementData,
                setter,
                statMonths
        );


    }

    /**
     * 客流小时数特殊处理 因为要单独计算每个小时的AOI人口相加
     */
    protected void calculatePopulationByTypePassengerHour(MainCalendarPlacementData placementData,
                                                          Map<String, List<GridHotDto>> gridHotMap,
                                                          List<MergedResidentMapCellIdData> mergedIds,
                                                          String statisticalType,
                                                          Map<String, CellMaxHotValueWktData> cellWktMap,
                                                          Consumer<Integer> setter) {
        //热力值
        List<GridHotDto> gridHotDtoList = gridHotMap.get(statisticalType);

        CellMaxHotValueWktData cellMaxHotValueWktData = cellWktMap.get(statisticalType);

        if (CollectionUtils.isEmpty(gridHotDtoList) || cellMaxHotValueWktData == null) {
            setter.accept(0);
            return;
        }

        //根据里面的 小时数分组
        Map<Integer, List<GridHotDto>> groupedByTimeIndex = gridHotDtoList.stream()
                .collect(Collectors.groupingBy(GridHotDto::getTimeIndex));

        int peopleCount = getPassengerHourPeopleCount(groupedByTimeIndex, placementData, mergedIds, cellMaxHotValueWktData);

        setter.accept(peopleCount);
    }

    /**
     * 计算 客流小时累计人口数
     *
     * @param groupedByTimeIndex 热力值
     * @param placementData      点位数据
     * @param mergedIds          聚合网格数据
     * @return 客流小时累计人口数
     */
    private int getPassengerHourPeopleCount(Map<Integer, List<GridHotDto>> groupedByTimeIndex, MainCalendarPlacementData placementData, List<MergedResidentMapCellIdData> mergedIds, CellMaxHotValueWktData cellMaxHotValueWktData) {
        int peopleCount = 0;
        if (!groupedByTimeIndex.isEmpty()) {
            //每个小时都计算一下AOi人口数
            for (Map.Entry<Integer, List<GridHotDto>> entry : groupedByTimeIndex.entrySet()) {
                List<GridHotDto> value = entry.getValue();
                Map<String, String> gridHotData = gridHotDataMap(value);
                peopleCount = peopleCount + calculate1000PopulationCommon(placementData, mergedIds, cellMaxHotValueWktData, gridHotData);
            }
        }

        return peopleCount;
    }

    /**
     * 热力值转为map
     *
     * @param gridHotDtoList 热力值数组
     * @return Map<String, String>
     */
    private Map<String, String> gridHotDataMap(List<GridHotDto> gridHotDtoList) {
        Map<String, String> gridHotData = new HashMap<>(8);
        if (CollectionUtils.isNotEmpty(gridHotDtoList)) {
            gridHotData = gridHotDtoList.stream()
                    .collect(Collectors.toMap(
                            dto -> dto.getLongitude() + "," + dto.getLatitude(),
                            GridHotDto::getHotValue,
                            (v1, v2) -> v1
                    ));
        }
        return gridHotData;
    }

    /**
     * 公共人口计算方法
     */
    private Integer calculatePopulationCommon(MainCalendarPlacementData placementData,
                                              List<MergedResidentMapCellIdData> mergedResidentMapCellIdData,
                                              CellMaxHotValueWktData cellData,
                                              Map<String, String> gridHotData,
                                              List<ResidentMapCoverageCellData> coverageCellData) {

        //如果1000*1000是null 或者 1000*1000热力值数是null 或者是 100*100 是null 都返回0
        if (CollectionUtils.isEmpty(mergedResidentMapCellIdData) || gridHotData.isEmpty() || CollectionUtils.isEmpty(coverageCellData)) {
            return 0;
        }

        Map<Long, MergedResidentMapCellIdData> map = mergedResidentMapCellIdData.stream()
                .collect(Collectors.toMap(MergedResidentMapCellIdData::getMergedId, Function.identity()));

        int cellCount = 0;
        int peopleVariation = 0;

        for (ResidentMapCoverageCellData data : coverageCellData) {
            MergedResidentMapCellIdData cellIdData = map.get(data.getMergeId());
            if (cellIdData != null) {
                String key = LngLatKeyUtils.normalizeLngLat(cellIdData.getCenterLng(), cellIdData.getCenterLat());
                String hotValue = gridHotData.get(key);
                //根据热力值计算出1000网格人口数
                BigDecimal newUv = new BigDecimal(cellData.getUv())
                        .multiply(new BigDecimal(StringUtils.isEmpty(hotValue) ? "0" : hotValue))
                        .divide(new BigDecimal(100), 5, RoundingMode.HALF_UP);

                Integer peopleCount = cellIdData.getPeopleCount();
                if (peopleCount == null || peopleCount == 0) {
                    // 如果人口数为null或0，跳过此网格的计算
                    continue;
                }

                //变化量
                BigDecimal variation = newUv.divide(new BigDecimal(peopleCount), 5, RoundingMode.HALF_UP);
                //100网格人口数
                BigDecimal uv = new BigDecimal(data.getPeopleCount()).multiply(variation).setScale(0, RoundingMode.HALF_UP);
                peopleVariation = peopleVariation + uv.intValue();
            }

            cellCount = cellCount + data.getCellCount();
        }

        int cellArea = CELL_AREA * cellCount;

        // 一个网格都没有覆盖的情况
        if (cellArea == 0) {
            return 0;
        }

        // AOI占所有网格的占比
        BigDecimal ratio = BigDecimal.valueOf(placementData.getAoiArea())
                .divide(new BigDecimal(cellArea), DECIMAL_PRECISION, RoundingMode.HALF_UP);

        return new BigDecimal(peopleVariation).multiply(ratio).setScale(0, RoundingMode.HALF_UP).intValue();
    }

    /**
     * 计算1000人口数量 - 单月版本
     */
    private Integer calculate1000PopulationCommon(MainCalendarPlacementData placementData,
                                                  List<MergedResidentMapCellIdData> mergedResidentMapCellIdData,
                                                  CellMaxHotValueWktData cellData,
                                                  Map<String, String> gridHotData) {
        if (CollectionUtils.isEmpty(mergedResidentMapCellIdData) || gridHotData.isEmpty()) {
            return 0;
        }

        int peopleVariation = 0;

        //计算每个1000网格uv人口数
        for (MergedResidentMapCellIdData cellIdData : mergedResidentMapCellIdData) {
            String key = LngLatKeyUtils.normalizeLngLat(cellIdData.getCenterLng(), cellIdData.getCenterLat());
            //获取到这个1000 网格的热力值
            String hotValue = gridHotData.get(key);
            //根据热力值计算出1000网格人口数
            BigDecimal newUv = new BigDecimal(cellData.getUv())
                    .multiply(new BigDecimal(StringUtils.isEmpty(hotValue) ? "0" : hotValue))
                    .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
            peopleVariation = peopleVariation + newUv.setScale(0, RoundingMode.HALF_UP).intValue();
        }

        //覆盖1000网格数量
        int cellArea = CELL_AREA_1000 * mergedResidentMapCellIdData.size();

        // 一个网格都没有覆盖的情况
        if (cellArea == 0) {
            return 0;
        }

        // AOI占所有网格的占比
        BigDecimal ratio = BigDecimal.valueOf(placementData.getAoiArea()).divide(new BigDecimal(cellArea), DECIMAL_PRECISION, RoundingMode.HALF_UP);
        //AOI覆盖1000AOI人口数
        return new BigDecimal(peopleVariation).multiply(ratio).setScale(0, RoundingMode.HALF_UP).intValue();
    }


    /**
     * 获取月份天数
     */
    private int getMonthDays(String statMonth) {
        return getCustomDaysInMonth(statMonth);
    }


}
