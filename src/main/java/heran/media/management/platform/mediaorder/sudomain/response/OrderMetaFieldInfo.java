package heran.media.management.platform.mediaorder.sudomain.response;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.aioptionmode.po.CalendarPlacementData;
import heran.media.management.platform.common.utils.xls.annotation.MetaField;
import heran.media.management.platform.effectestimate.subdomian.dto.ExposureMetaFieldInfo;
import heran.media.sharelib.domain.bo.LockDataType;
import heran.media.sharelib.utils.date.DataTmeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderMetaFieldInfo extends CalendarPlacementData {
    @MetaField(columnIndex = 40, labelCn = "曝光数据查询时间", width = 120)
    private String orderExposureStatMonth;
    @MetaField(columnIndex = 41, labelCn = "曝光人口口径", width = 120)
    private String orderExposurePopulation;
    @MetaField(columnIndex = 42, labelCn = "AOI曝光常驻人口")
    private Integer aoiResidentPopulation;
    @MetaField(columnIndex = 43, labelCn = "AOI曝光居住人口")
    private Integer aoiDwellPopulation;
    @MetaField(columnIndex = 44, labelCn = "AOI曝光工作人口")
    private Integer aoiWorkPopulation;
    @MetaField(columnIndex = 45, labelCn = "AOI曝光客流人口（天级）")
    private Integer aoiPassengerPopulation;
    @MetaField(columnIndex = 46, labelCn = "AOI曝光客流人口（小时级）")
    private Integer aoiPassengerHourPopulation;
    @MetaField(columnIndex = 47, labelCn = "AOI曝光TA口径")
    private String aoiTaPopulation;
    @MetaField(columnIndex = 48, labelCn = "AOI曝光TA人口")
    private Integer aoiTaCount;
    @MetaField(columnIndex = 49, labelCn = "天级时间占比")
    private String heavenProportion;
    @MetaField(columnIndex = 50, labelCn = "可视角度")
    private String visibleAngle;
    @MetaField(columnIndex = 51, labelCn = "曝光人次PV（人次）")
    private Integer pv;
    @MetaField(columnIndex = 52, labelCn = "曝光人数UV（次）")
    private Integer uv;
    @MetaField(columnIndex = 53, labelCn = "平均触达频次AF（次）")
    private Double af;
    @MetaField(columnIndex = 54, labelCn = "曝光潜客人数TAUV（人）")
    private Integer taUv;
    @MetaField(columnIndex = 55, labelCn = "千人成本CPM（元）")
    private Double cpm;
    @MetaField(columnIndex = 56, labelCn = "单人成本CPUV（元）")
    private Double cpUv;
    @MetaField(columnIndex = 57, labelCn = "单TA成本CPTA（元）")
    private Double cpTa;

    public void setPopulationName() {
        if (StringUtils.isNotEmpty(orderExposurePopulation)) {
            LockDataType lockDataType = LockDataType.valueOf(orderExposurePopulation);
            this.orderExposurePopulation = lockDataType.getName();
        }
        if (StringUtils.isNotEmpty(aoiTaPopulation)) {
            LockDataType lockDataType = LockDataType.valueOf(aoiTaPopulation);
            this.aoiTaPopulation = lockDataType.getName();
        }
        if (StringUtils.isNotEmpty(heavenProportion)) {
            BigDecimal bigDecimal = new BigDecimal(heavenProportion);
            BigDecimal multiply = bigDecimal.multiply(new BigDecimal(100)).stripTrailingZeros();;
            this.heavenProportion = multiply.toPlainString() + "%";
        }
        if (StringUtils.isNotEmpty(orderExposureStatMonth)) {
            List<String> list = ExposureMetaFieldInfo.parseStatMonth(orderExposureStatMonth);

            List<String> stringList = list.stream()
                    .map(String::valueOf)
                    .map(DataTmeUtils::formatYearMonth)
                    .collect(Collectors.toList());
            this.orderExposureStatMonth = String.join(",", stringList);
        }
    }

}
