package heran.media.management.platform.mediaorder.template;

import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.mediaorder.sudomain.dto.CalendarPlacementOrderData;
import heran.media.sharelib.domain.db.mapper.main.MainCalendarOrderKpiMapper;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * 处理基础的媒体形式点位数据
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractComputationalDataTemplate extends BaseCallServiceImpl implements ComputationalDataTemplate {

    @Resource
    private MainCalendarOrderKpiMapper mainCalendarOrderKpiMapper;

    @Override
    public List<CalendarPlacementOrderData> buildQuery(ComputationalDataContext kpiQueryContext) {

        return null;
    }

    @Override
    public void execute(ComputationalDataContext kpiQueryContext) {

    }

    protected void updateMainCalendarOrderKpi(List<MainCalendarOrderKpi> list) {
        mainCalendarOrderKpiMapper.batchInsertUpdate(list);
    }

}
