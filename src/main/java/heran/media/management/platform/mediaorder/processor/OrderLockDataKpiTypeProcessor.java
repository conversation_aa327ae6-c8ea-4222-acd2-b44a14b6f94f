package heran.media.management.platform.mediaorder.processor;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class OrderLockDataKpiTypeProcessor implements LockDataKpiTypeProcessor {
    @Resource
    private LockProcessorFactory lockProcessorFactory;

    @Override
    public void executeKpi(LockDataContext context) {
        lockProcessorFactory.execute(context.getRequest().getType(), context);
    }

    @Override
    public String getKpiType() {
        return "ORDER";
    }
}
