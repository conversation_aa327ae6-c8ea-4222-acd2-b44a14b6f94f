package heran.media.management.platform.mediaorder.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.management.platform.effectestimate.error.HeadMapDataException;
import heran.media.management.platform.maincategoryextentattr.service.MainCategoryExtendAttrService;
import heran.media.management.platform.mediaorder.service.MediaOrderService;
import heran.media.management.platform.mediaorder.sudomain.bo.XlsCityCollectData;
import heran.media.management.platform.mediaorder.sudomain.dto.ResultMatchDetailErrorData;
import heran.media.sharelib.domain.db.mapper.main.OrderEffectEstimateBaseManLandMapper;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseManLand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
public class EasyExcelCityCollectDataListener extends AnalysisEventListener<XlsCityCollectData> {

    private static final Pattern NUMBER_PATTERN = Pattern.compile("^-?\\d+(\\.\\d+)?$");

    private final Integer orderId;
    private final String bizType;
    private final String appName;
    private final Integer userKey;

    /**
     * 错误信息
     */
    private final List<String> errorMsgList = new ArrayList<>();
    private final List<ResultMatchDetailErrorData> errorResultDataList = new ArrayList<>();

    /**
     * 用于存储读取的数据
     */
    private final List<OrderEffectEstimateBaseManLand> insertDataList = new ArrayList<>();

    /**
     * 条数
     */
    private final AtomicInteger count = new AtomicInteger(1);
    /**
     * 每两千数据保存在数据库一次
     */
    public final static Integer BATCH_SIZE = 2000;

    private final MainCategoryExtendAttrService mainCategoryExtendAttrService;
    private final MediaOrderService mediaOrderService;
    private final OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper;

    public EasyExcelCityCollectDataListener(Integer orderId, String bizType, String appName, Integer userKey, MainCategoryExtendAttrService mainCategoryExtendAttrService, MediaOrderService mediaOrderService, OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper) {
        this.orderId = orderId;
        this.bizType = bizType;
        this.appName = appName;
        this.userKey = userKey;
        this.mainCategoryExtendAttrService = mainCategoryExtendAttrService;
        this.mediaOrderService = mediaOrderService;
        this.orderEffectEstimateBaseManLandMapper = orderEffectEstimateBaseManLandMapper;
    }

    @Override
    public void invoke(XlsCityCollectData data, AnalysisContext context) {
        OrderEffectEstimateBaseManLand validate = validate(data, context.readSheetHolder().getRowIndex());
        if (validate != null) {
            insertDataList.add(validate);
        }
        //按批次保存，判断当前当前条数是否到达最大限制条数
        int total = context.readSheetHolder().getApproximateTotalRowNumber() - 1;
        Integer batchSize = Math.min(total, count.get() * BATCH_SIZE);
        if (context.readSheetHolder().getRowIndex() >= batchSize) {
            count.getAndIncrement();
            log.info("start to insert city db with size=-------------------{}", insertDataList.size());
            //保存在数据库
            if (!insertDataList.isEmpty()) {
                orderEffectEstimateBaseManLandMapper.batchInsertIgnore(insertDataList);
            }
            insertDataList.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        //最后一次不满足 限制的数量
        if (!insertDataList.isEmpty()) {
            orderEffectEstimateBaseManLandMapper.batchInsertIgnore(insertDataList);
            //清除掉所有的内存数据
            insertDataList.clear();
        }
        //判断错误数据是否为null 不为null 下载错误信息表格
        if (!errorResultDataList.isEmpty()) {
            XLSWriter.export(errorResultDataList, ResultMatchDetailErrorData.class, "导入失败数据", Objects.requireNonNull(((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getResponse()));
        }
    }

    private OrderEffectEstimateBaseManLand validate(XlsCityCollectData data, Integer rowIndex) {
        List<String> ids = new ArrayList<>();
        if (StringUtils.isEmpty(data.getCategoryName())) {
            errorMsgList.add("媒介形式不能等于空");
        } else {
            for (String category : data.getCategoryName().split(",")) {
                if (category.split("-").length != 3) {
                    errorMsgList.add("媒介形式不合法");
                } else {
                    category = category.replace("-", "");
                    String[] categoryIds = mainCategoryExtendAttrService.getMediaCategoryByKey(category);
                    if (categoryIds == null) {
                        errorMsgList.add("该媒体三级分类属性不存在：" + category);
                    } else {
                        ids.add(categoryIds[categoryIds.length - 1]);
                    }
                }
            }
        }
        //校验城市
        String code = null;
        if (StringUtils.isEmpty(data.getProvince()) || StringUtils.isEmpty(data.getCityName())) {
            errorMsgList.add("省市不能为空");
        } else {
            if ("汇总".equals(data.getCityName())) {
                code = "-1";
            } else {
                String regions = data.getProvince() + data.getCityName();
                code = mediaOrderService.getCode(regions);
                if (StringUtils.isEmpty(code)) {
                    errorMsgList.add("省市有误");
                }
            }
        }
        OrderEffectEstimateBaseManLand baseManLand = data.advPo();
        if (StringUtils.isNotEmpty(code)) {
            String[] split = code.split(",");
            baseManLand.setRegionCode(split[split.length - 1]);
        }

        if (!isValidNumber(data.getPlacementCost())) {
            errorMsgList.add("投放费用 输入的值不合法");
        }
        if (!isValidNumber(data.getPv())) {
            errorMsgList.add("曝光人次PV 输入的值不合法");
        }
        if (!isValidNumber(data.getUv())) {
            errorMsgList.add("曝光人数UV 输入的值不合法");
        }
        if (!isValidNumber(data.getAf())) {
            errorMsgList.add("平均触达频次AF 输入的值不合法");
        }
        if (!isValidNumber(data.getTaUv())) {
            errorMsgList.add("曝光潜客人数TAUA 输入的值不合法");
        }
        if (!isValidNumber(data.getCpm())) {
            errorMsgList.add("千人成本CPM 输入的值不合法");
        }
        if (!isValidNumber(data.getCpUv())) {
            errorMsgList.add("单人成本CPUV 输入的值不合法");
        }
        if (!isValidNumber(data.getCpTa())) {
            errorMsgList.add("单TA成本CPTA 输入的值不合法");
        }
        if (!isValidNumber(data.getOverlapRatio())) {
            errorMsgList.add("重叠比例 输入的值不合法");
        } else {
            baseManLand.setOverlapRatio(Double.valueOf(data.getOverlapRatio()));
        }
        String ta = convertPercentToDecimal(data.getTa(), "曝光潜客浓度TA%");
        if (ta != null) {
            baseManLand.setTa(ta);
        }
        String reach = convertPercentToDecimal(data.getReach(), "城市渗透率Reach");
        if (reach != null) {
            baseManLand.setReach(reach);
        }
        baseManLand.setMediumId(String.join(",", ids));
        baseManLand.setOrderId(orderId);
        baseManLand.setBizType(bizType);
        baseManLand.setCreator(appName);
        baseManLand.setUpdater(appName);
        baseManLand.setCreatedByUser(userKey);
        baseManLand.setUpdatedByUser(userKey);
        if (!errorMsgList.isEmpty()) {
            ResultMatchDetailErrorData errorData = new ResultMatchDetailErrorData(rowIndex, errorMsgList);
            errorMsgList.clear();
            errorResultDataList.add(errorData);
            return null;
        }
        return baseManLand;
    }

    private String convertPercentToDecimal(String percentStr, String fieldName) {
        if (StringUtils.isEmpty(percentStr)) {
            errorMsgList.add(fieldName + " 输入的值不合法");
            return null;
        }
        String number = percentStr.replace("%", "");
        if (!NUMBER_PATTERN.matcher(number).matches()) {
            errorMsgList.add(fieldName + " 输入的值不合法");
            return null;
        }
        return new BigDecimal(number).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();
    }


    private boolean isValidNumber(String value) {
        return !StringUtils.isEmpty(value) && NUMBER_PATTERN.matcher(value).matches();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        //获取 接受的表头数据
        Field[] fields = XlsCityCollectData.class.getDeclaredFields();
        //如果不等于 说明表头与要求的表头不一致
        if (headMap.size() != fields.length) {
            //返回错误信息
            throw new HeadMapDataException();
        }
        for (Field field : fields) {
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (!headMap.get(annotation.index()).equals(annotation.value()[0])) {
                //列对应不上
                throw new HeadMapDataException();
            }
        }
    }
}
