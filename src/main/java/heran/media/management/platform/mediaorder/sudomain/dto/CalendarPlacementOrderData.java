package heran.media.management.platform.mediaorder.sudomain.dto;

import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CalendarPlacementOrderData extends MainCalendarOrderKpi {
    private String aoiCoordinates;
    private Integer categoryId;
    private String placementSn;
    private Date deliveryStartTime;
    private Date deliveryEndTime;
    private Double placementCost;
    private String brighten;
    private Integer launchTime;
    private Integer launchRate;
    private String mileage;
    private Integer vacancyPosition;
    private Integer launch;


    public MainCalendarOrderKpi adaToPo() {
        MainCalendarOrderKpi kpi = new MainCalendarOrderKpi();
        kpi.setId(this.getId());
        kpi.setOrderId(this.getOrderId());
        kpi.setCalendarPlacementId(this.getCalendarPlacementId());
        kpi.setKpiType(this.getKpiType());
        kpi.setStatMonth(this.getStatMonth());
        kpi.setPopulation(this.getPopulation());
        kpi.setAoiResidentPopulation(this.getAoiResidentPopulation());
        kpi.setResidentPopulation(this.getResidentPopulation());
        kpi.setAoiDwellPopulation(this.getAoiDwellPopulation());
        kpi.setAoiWorkPopulation(this.getAoiWorkPopulation());
        kpi.setAoiPassengerPopulation(this.getAoiPassengerPopulation());
        kpi.setAoiPassengerHourPopulation(this.getAoiPassengerHourPopulation());
        kpi.setAoiTaPopulation(this.getAoiTaPopulation());
        kpi.setAoiTaCount(this.getAoiTaCount());
        kpi.setHeavenProportion(this.getHeavenProportion());
        kpi.setVisibleAngle(this.getVisibleAngle());
        kpi.setPv(this.getPv());
        kpi.setUv(this.getUv());
        kpi.setAf(this.getAf());
        kpi.setTauv(this.getTauv());
        kpi.setCpm(this.getCpm());
        kpi.setCpuv(this.getCpuv());
        kpi.setCpta(this.getCpta());
        kpi.setCreatedByUser(this.getCreatedByUser());
        kpi.setUpdatedByUser(this.getUpdatedByUser());
        kpi.setCreator(this.getCreator());
        kpi.setUpdater(this.getUpdater());
        return kpi;
    }
}
