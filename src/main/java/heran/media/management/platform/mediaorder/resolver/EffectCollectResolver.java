package heran.media.management.platform.mediaorder.resolver;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderEntityQueryMapper;
import heran.media.management.platform.mediaorder.sudomain.dto.CityResidentPopulationData;
import heran.media.management.platform.mediaorder.sudomain.dto.OrderEstimateBaseLandData;
import heran.media.management.platform.mediaorder.sudomain.request.CreateOverviewDataRequest;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.db.mapper.main.OrderEffectEstimateBaseManLandMapper;
import heran.media.sharelib.domain.db.mapper.main.StatisticsDimensionMapper;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseManLand;
import heran.media.sharelib.domain.db.model.main.StatisticsDimension;
import heran.media.sharelib.utils.AuthUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class EffectCollectResolver extends BaseCallServiceImpl {

    @Resource
    private OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper;
    @Resource
    private MediaOrderEntityQueryMapper mediaOrderEntityQueryMapper;
    @Resource
    private StatisticsDimensionMapper statisticsDimensionMapper;


    /**
     * 计算汇总数据
     *
     * @param request CreateOverviewDataRequest
     */
    public abstract void resolver(CreateOverviewDataRequest request);

    /**
     * 类型
     *
     * @return String
     */
    abstract String getType();


    protected void collectInsertData(List<OrderEstimateBaseLandData> landData,
                                     Map<String, BigDecimal> mapPopulation,
                                     CreateOverviewDataRequest request,
                                     Double ratioCoefficient,
                                     BigDecimal populationCount) {
        if (CollectionUtils.isEmpty(landData)) {
            return;
        }
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        //转换数据
        List<OrderEffectEstimateBaseManLand> list = landData.stream().map(data -> {
            BigDecimal realPopulationCount = populationCount;
            if (mapPopulation != null && !mapPopulation.isEmpty()) {
                realPopulationCount = mapPopulation.get(data.getRegionCode());
            }
            OrderEffectEstimateBaseManLand baseManLand = data.adoTp(request, ratioCoefficient, realPopulationCount);
            setCreatorInfo(currentUser.getIdentifier(), baseManLand);
            setUpdaterInfo(currentUser.getIdentifier(), baseManLand);
            return baseManLand;
        }).collect(Collectors.toList());
        //添加数据
        orderEffectEstimateBaseManLandMapper.batchInsertIgnore(list);
    }


    protected Map<String, StatisticsDimension> getCityPopulationDataMap(List<String> regionCodeList, List<String> statMonthList, String statisticalType) {
        List<StatisticsDimension> statisticsDimensionList = statisticsDimensionMapper.getStatisticsDimensionList(statisticalType, regionCodeList, statMonthList);

        if (CollectionUtils.isEmpty(statisticsDimensionList)) {
            return new HashMap<>(8);
        }

        return statisticsDimensionList.stream()
                .collect(Collectors.toMap(
                        d -> d.getStatMonth() + "_" + d.getRegionCode(),
                        d -> d,
                        (a, b) -> a
                ));
    }


    protected Map<String, Set<String>> getCityUvSet(CreateOverviewDataRequest request) {
        List<CityResidentPopulationData> list = mediaOrderEntityQueryMapper.getCityResidentPopulationData(request.getOrderId(), request.getBizType());
        return getCityResidentMap(list);
    }

    protected Map<Integer, Map<String, Set<String>>> getMediumFormatUv(CreateOverviewDataRequest request) {
        List<CityResidentPopulationData> list = mediaOrderEntityQueryMapper.getCityResidentPopulationCategoryData(request.getOrderId(), request.getBizType());

        Map<Integer, Map<String, Set<String>>> map = new HashMap<>(8);

        Map<Integer, List<CityResidentPopulationData>> groupedByCategoryId = list.stream()
                .collect(Collectors.groupingBy(CityResidentPopulationData::getCategoryId));

        for (Map.Entry<Integer, List<CityResidentPopulationData>> entry : groupedByCategoryId.entrySet()) {

            Integer categoryId = entry.getKey();

            List<CityResidentPopulationData> value = entry.getValue();

            Map<String, Set<String>> cityResidentMap = getCityResidentMap(value);

            map.put(categoryId, cityResidentMap);

        }

        return map;
    }


    protected Map<String, Set<String>> getCityResidentMap(List<CityResidentPopulationData> list) {
        Map<String, Set<String>> result = new HashMap<>(8);
        ObjectMapper mapper = new ObjectMapper();

        for (CityResidentPopulationData data : list) {
            String code = data.getRegionCode();
            String statMonth = data.getStatMonth();
            Set<String> set = result.computeIfAbsent(code, k -> new HashSet<>());

            if (statMonth != null && statMonth.startsWith("[")) {
                // 是数组，解析JSON
                String[] arr = new String[0];
                try {
                    arr = mapper.readValue(statMonth, String[].class);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
                set.addAll(Arrays.asList(arr));
            } else if (statMonth != null) {
                set.add(statMonth);
            }
        }
        return result;
    }

    protected BigDecimal getPopulation(String statisticalType, Map<String, Set<String>> cityUvSet) {
        //regionCode
        List<String> regionCodeList = new ArrayList<>(cityUvSet.keySet());
        //statMonth
        List<String> statMonthList = cityUvSet.values().stream()
                .flatMap(Set::stream)
                .collect(Collectors.toList());

        Map<String, StatisticsDimension> cityResidentMap = getCityPopulationDataMap(regionCodeList, statMonthList, statisticalType);

        BigDecimal population = BigDecimal.ZERO;

        for (Map.Entry<String, Set<String>> entry : cityUvSet.entrySet()) {
            BigDecimal value = BigDecimal.ZERO;
            String regionCode = entry.getKey();
            Set<String> entryValue = entry.getValue();

            for (String statMonth : entryValue) {
                String key = statMonth + "_" + regionCode;
                StatisticsDimension dimension = cityResidentMap.get(key);
                if (dimension != null) {
                    value = value.add(new BigDecimal(dimension.getDimensionValue()));
                }
            }
            // 平均值
            if (!entryValue.isEmpty()) {
                BigDecimal divide = value.divide(new BigDecimal(entryValue.size()), 0, RoundingMode.HALF_UP);
                population = population.add(divide);
            }
        }
        return population;
    }


    protected void getPopulationDataMap(Map<String, StatisticsDimension> cityResidentMap, Map<String, BigDecimal> mapPopulation, Map<String, Set<String>> value) {
        for (Map.Entry<String, Set<String>> e : value.entrySet()) {
            String code = e.getKey();
            Set<String> statMonthSet = e.getValue();
            //计算这个媒体形式下 这个城市下 月份的平均值
            int uv = 0;
            for (String statMonth : statMonthSet) {
                String key = statMonth + "_" + code;
                StatisticsDimension dimension = cityResidentMap.get(key);
                if (dimension != null) {
                    uv = uv + Integer.parseInt(dimension.getDimensionValue());
                }
            }
            //计算平均值
            BigDecimal divide = new BigDecimal(uv).divide(new BigDecimal(statMonthSet.size()), 0, RoundingMode.HALF_UP);
            mapPopulation.put(code, divide);
        }
    }

}
