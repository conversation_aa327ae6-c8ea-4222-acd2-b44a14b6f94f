package heran.media.management.platform.mediaorder.processor;

import heran.media.sharelib.domain.bo.StatisticalType;
import org.springframework.stereotype.Component;

/**
 * 居住人口处理器
 *
 * <AUTHOR>
 */
@Component
public class DwellPopulationHotHandler extends AbstractHotAdCodeProcessor {
    @Override
    public StatisticalType getStatisticalType() {
        return StatisticalType.DWELL_POPULATION;
    }
}
