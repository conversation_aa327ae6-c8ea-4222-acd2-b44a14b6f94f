package heran.media.management.platform.mediaorder.resolver;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class LockDataCalculateTypeResolverFactory {
    private static final Map<String, LockDataCalculateTypeResolver> RESOLVER_MAP = new HashMap<>();

    public LockDataCalculateTypeResolverFactory(LockDataCalculateTypeResolver... processors) {
        for (LockDataCalculateTypeResolver resolver : processors) {
            RESOLVER_MAP.put(resolver.type(), resolver);
        }
    }

    public LockDataCalculateTypeResolver getResolver(String type) {
        return RESOLVER_MAP.get(type);
    }
}
