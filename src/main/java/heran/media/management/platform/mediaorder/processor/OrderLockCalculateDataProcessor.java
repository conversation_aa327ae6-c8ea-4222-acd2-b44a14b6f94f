package heran.media.management.platform.mediaorder.processor;

import com.alibaba.nacos.common.utils.CollectionUtils;
import heran.media.management.platform.common.utils.RegionConversionUtils;
import heran.media.management.platform.mediaorder.sudomain.dto.CellMaxHotValueWktData;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import heran.media.sharelib.domain.dto.hot.MainCalendarPlacementData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单锁定计算数据处理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderLockCalculateDataProcessor extends BaseLockCalculateDataProcessor {

    @Override
    protected void processPlacementBatch(List<MainCalendarPlacementData> placementList, LockDataContext context) {
        // 按城市分组处理
        Map<String, List<MainCalendarPlacementData>> groupedByCity = placementList.stream()
                .collect(Collectors.groupingBy(MainCalendarPlacementData::getRegionCode));

        String statMonth = context.getRequest().getStatMonth().get(0);

        for (Map.Entry<String, List<MainCalendarPlacementData>> entry : groupedByCity.entrySet()) {
            String regionCode = entry.getKey();

            //如果是直辖市区code 要转换一下
            regionCode = RegionConversionUtils.regionCodeConversion(regionCode);

            List<MainCalendarPlacementData> placementListData = entry.getValue();
            // 预加载热力数据
            Map<String, CellMaxHotValueWktData> cellWktMap = preloadHotData(context, statMonth, regionCode);

            List<MainCalendarOrderKpi> kpis = processPlacements(placementListData, context, statMonth, cellWktMap, regionCode);

            List<Long> ids = kpis.stream()
                    .map(MainCalendarOrderKpi::getCalendarPlacementId)
                    .collect(Collectors.toList());

            System.out.println(ids);


            // 批量保存KPI数据
            if (CollectionUtils.isNotEmpty(kpis)) {
                mainCalendarOrderKpiMapper.batchInsertUpdate(kpis);
            }
        }
    }

    /**
     * 处理点位
     */
    private List<MainCalendarOrderKpi> processPlacements(List<MainCalendarPlacementData> placementListData,
                                                         LockDataContext context,
                                                         String statMonth,
                                                         Map<String, CellMaxHotValueWktData> cellWktMap,
                                                         String regionCode) {
        return baseProcessPlacements(placementListData, context, Collections.singletonList(statMonth), cellWktMap, null, regionCode, false);
    }


    @Override
    protected String getProcessorName() {
        return "订单锁定";
    }

    @Override
    public String getType() {
        return "ORDER";
    }

    @Override
    protected String getBizData() {
        return "LOCK_CALCULATE_DATA";
    }
}