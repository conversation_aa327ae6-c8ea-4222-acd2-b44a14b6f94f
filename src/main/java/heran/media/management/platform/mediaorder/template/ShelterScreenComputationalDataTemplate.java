package heran.media.management.platform.mediaorder.template;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 候车亭/站名牌/路名牌/干道灯箱
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShelterScreenComputationalDataTemplate extends LedScreenComputationalDataTemplate {

    @Override
    protected BigDecimal getUv(ComputationalDataContext kpiQueryContext, BigDecimal population, BigDecimal heaven, BigDecimal visibleAngle, String aoiCoordinates, String mileage) {
        if (kpiQueryContext.getRequest().getVisualModel()) {
            //如果选了可视角度的话现在是暂定在uv后面*0.9
            return super.getUv(kpiQueryContext, population, heaven, visibleAngle, aoiCoordinates, mileage).multiply(new BigDecimal("0.9"));
        }
        return super.getUv(kpiQueryContext, population, heaven, visibleAngle, aoiCoordinates, mileage);
    }

    @Override
    public String templateName() {
        return "SHELTER_SCREEN";
    }
}
