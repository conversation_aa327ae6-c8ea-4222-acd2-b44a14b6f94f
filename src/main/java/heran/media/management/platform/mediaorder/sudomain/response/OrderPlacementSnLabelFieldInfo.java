package heran.media.management.platform.mediaorder.sudomain.response;

import heran.media.management.platform.common.utils.xls.annotation.MetaField;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderPlacementSnLabelFieldInfo {

    @MetaField(columnIndex = -1, labelCn = "点位id", hide = true)
    private Long id;
    @MetaField(columnIndex = -1, labelCn = "媒体形式三级分类id", hide = true)
    private Integer mediumLv3CategoryId;
    @MetaField(columnIndex = 13, labelCn = "扩展AOI坐标串", width = 300)
    private String extendAoiCoordinates;


    @MetaField(columnIndex = 40, labelCn = "曝光数据查询时间", width = 140)
    private String orderExposureStatMonth;
    @MetaField(columnIndex = 41, labelCn = "曝光人口口径", width = 140)
    private String orderExposurePopulation;
    @MetaField(columnIndex = 42, labelCn = "AOI曝光常驻人口", width = 135)
    private Integer aoiResidentPopulation;
    @MetaField(columnIndex = 43, labelCn = "AOI曝光居住人口", width = 135)
    private Integer aoiDwellPopulation;
    @MetaField(columnIndex = 44, labelCn = "AOI曝光工作人口", width = 135)
    private Integer aoiWorkPopulation;
    @MetaField(columnIndex = 45, labelCn = "AOI曝光客流人口（天级）", width = 180)
    private Integer aoiPassengerPopulation;
    @MetaField(columnIndex = 46, labelCn = "AOI曝光客流人口（小时级）", width = 200)
    private Integer aoiPassengerHourPopulation;
    @MetaField(columnIndex = 47, labelCn = "AOI曝光TA口径", width = 120)
    private String aoiTaPopulation;
    @MetaField(columnIndex = 48, labelCn = "AOI曝光TA人口", width = 120)
    private Integer aoiTaCount;
    @MetaField(columnIndex = 49, labelCn = "天级时间占比", width = 120)
    private String heavenProportion;
    @MetaField(columnIndex = 50, labelCn = "可视角度", width = 100)
    private String visibleAngle;
    @MetaField(columnIndex = 51, labelCn = "曝光人次PV（人次）", width = 150)
    private Integer pv;
    @MetaField(columnIndex = 52, labelCn = "曝光人数UV（次）", width = 150)
    private Integer uv;
    @MetaField(columnIndex = 53, labelCn = "平均触达频次AF（次）", width = 160)
    private Double af;
    @MetaField(columnIndex = 54, labelCn = "曝光潜客人数TAUV（人）", width = 180)
    private Integer taUv;
    @MetaField(columnIndex = 55, labelCn = "千人成本CPM（元）", width = 150)
    private Double cpm;
    @MetaField(columnIndex = 56, labelCn = "单人成本CPUV（元）", width = 160)
    private Double cpUv;
    @MetaField(columnIndex = 57, labelCn = "单TA成本CPTA（元）", width = 160)
    private Double cpTa;
}
