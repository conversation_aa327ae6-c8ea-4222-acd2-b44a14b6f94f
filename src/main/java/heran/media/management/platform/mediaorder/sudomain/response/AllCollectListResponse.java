package heran.media.management.platform.mediaorder.sudomain.response;

import heran.media.management.platform.mediaorder.sudomain.dto.AllCollectListData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AllCollectListResponse {
    @ApiModelProperty("投放费用 单位：元")
    private String placementCost;
    @ApiModelProperty("投放城市 单位：个")
    private Integer cityCount;
    @ApiModelProperty("投放媒体形式 单位：个")
    private Integer mediumCount;
    @ApiModelProperty("投放媒体总量 单位：个")
    private Integer vacancyPosition;
    @ApiModelProperty("投放周期 单位：天")
    private Integer deliveryDays;
    @ApiModelProperty("曝光人次PV 单位：人次")
    private String pv;
    @ApiModelProperty("曝光人数UV 单位：人")
    private String uv;
    @ApiModelProperty("平均触达频次AF 单位：次")
    private String af;
    @ApiModelProperty("曝光潜客人数TAUA 单位：人")
    private String taUv;
    @ApiModelProperty("曝光潜客浓度TA%")
    private String ta;
    @ApiModelProperty("城市渗透率Reach")
    private String reach;
    @ApiModelProperty("千人成本CPM 单位：元")
    private String cpm;
    @ApiModelProperty("单人成本CPUV 单位：元")
    private String cpUv;
    @ApiModelProperty("单TA成本CPTA 单位：元")
    private String cpTa;

    public AllCollectListResponse(AllCollectListData data) {
        this.placementCost = data.getPlacementCost();
        this.cityCount = data.getCityCount();
        this.mediumCount = data.getMediumCount();
        this.deliveryDays = data.getDeliveryDays();
        this.vacancyPosition = data.getVacancyPosition();
        this.pv = data.getPv();
        this.uv = data.getUv();
        this.af = data.getAf();
        this.taUv = data.getTaUv();
        this.ta = data.getTa();
        this.reach = data.getReach();
        this.cpm = data.getCpm();
        this.cpUv = data.getCpUv();
        this.cpTa = data.getCpTa();
    }
}
