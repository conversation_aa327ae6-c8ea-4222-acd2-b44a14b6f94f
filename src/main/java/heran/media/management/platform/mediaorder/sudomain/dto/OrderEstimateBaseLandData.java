package heran.media.management.platform.mediaorder.sudomain.dto;

import heran.media.management.platform.mediaorder.sudomain.request.CreateOverviewDataRequest;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseManLand;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
@Data
public class OrderEstimateBaseLandData {
    /**
     * 投放城市数量
     */
    private Integer cityCount;
    /**
     * 人口口径
     */
    private String population;
    /**
     * 投放媒体形式数据
     */
    private Integer categoryCount;
    /**
     * 分类
     */
    private String categoryId;
    /**
     * 城市id
     */
    private String regionCode;
    /**
     * 投放周期
     */
    private Integer deliveryDays;
    /**
     * 投放费用
     */
    private BigDecimal placementCost;
    /**
     * 投放点位
     */
    private Integer vacancyPosition;
    /**
     * PV
     */
    private BigDecimal pv;
    /**
     * UV
     */
    private BigDecimal uv;
    /**
     * TAUV
     */
    private BigDecimal taUv;
    /**
     * 常驻人口
     */
    private BigDecimal aoiResidentPopulation;
    /**
     * 天级客流累加
     */
    private BigDecimal aoiPassengerPopulation;
    /**
     * 小时级客流累加
     */
    private BigDecimal aoiPassengerHourPopulation;

    /**
     * 是否是汇总
     */
    private boolean collectData = false;

    public OrderEffectEstimateBaseManLand adoTp(CreateOverviewDataRequest request, Double ratioCoefficient,BigDecimal populationCount) {
        setDefaultValue();
        OrderEffectEstimateBaseManLand baseManLand = new OrderEffectEstimateBaseManLand();
        baseManLand.setOrderId(request.getOrderId());
        baseManLand.setBizType(request.getBizType());
        if (collectData) {
            baseManLand.setExposureType("MEDIUM_FORMAT_COLLECT");
        } else {
            baseManLand.setExposureType(request.getDataType());
        }
        baseManLand.setMediumId(categoryId);
        baseManLand.setRegionCode(regionCode);
        baseManLand.setMarketTime(deliveryDays.toString());
        baseManLand.setMarketPlacementCount(vacancyPosition.toString());
        baseManLand.setPlacementCost(placementCost.toString());
        baseManLand.setPv(pv.toString());
        baseManLand.setImpTaUv(taUv.toString());
        baseManLand.setCityCount(cityCount);
        baseManLand.setMediumCount(categoryCount);
        baseManLand.setOverlapRatio(ratioCoefficient);

        //如果是按城市 要添加上重叠比例
        if (request.getCalculateRatio()) {
            BigDecimal subtract = new BigDecimal(1).subtract(new BigDecimal(ratioCoefficient));
            uv = uv.multiply(subtract);
        }
        baseManLand.setUv(uv.setScale(0, RoundingMode.HALF_UP).toString());

        BigDecimal af = new BigDecimal(0);
        if (aoiPassengerPopulation.compareTo(BigDecimal.ZERO) != 0) {
            af = aoiPassengerHourPopulation.divide(aoiPassengerPopulation, 2, RoundingMode.HALF_UP);
        }
        baseManLand.setAf(af.toString());
        //计算TA%
        BigDecimal ta = new BigDecimal(0);
        if (uv.compareTo(BigDecimal.ZERO) != 0) {
            ta = taUv.divide(uv, 2, RoundingMode.HALF_UP);
        }
        baseManLand.setTa(ta.toString());

        BigDecimal reach = new BigDecimal(0);
        if (populationCount.compareTo(BigDecimal.ZERO) != 0) {
            reach = uv.divide(populationCount, 2, RoundingMode.HALF_UP);
        }
        baseManLand.setReach(reach.toString());

        BigDecimal cpm = new BigDecimal(0);
        if (pv.compareTo(BigDecimal.ZERO) != 0) {
            cpm = placementCost.divide(pv, 10, RoundingMode.HALF_UP).multiply(new BigDecimal(1000));
        }
        baseManLand.setCpm(cpm.setScale(2, RoundingMode.HALF_UP).toString());

        BigDecimal cpUv = new BigDecimal(0);
        if (uv.compareTo(BigDecimal.ZERO) != 0){
            cpUv = placementCost.divide(uv, 2, RoundingMode.HALF_UP);
        }
        baseManLand.setCpuv(cpUv.toString());

        BigDecimal cpTa = new BigDecimal(0);
        if (taUv.compareTo(BigDecimal.ZERO) != 0){
            cpTa = placementCost.divide(taUv, 2, RoundingMode.HALF_UP);
        }
        baseManLand.setCpta(cpTa.toString());

        return baseManLand;
    }

    private void setDefaultValue() {
        BigDecimal bigDecimal = new BigDecimal(0);
        if (pv == null) {
            this.pv = bigDecimal;
        }
        if (uv == null) {
            this.uv = bigDecimal;
        }
        if (taUv == null) {
            this.taUv = bigDecimal;
        }
        if (placementCost == null) {
            this.placementCost = bigDecimal;
        }
    }
}
