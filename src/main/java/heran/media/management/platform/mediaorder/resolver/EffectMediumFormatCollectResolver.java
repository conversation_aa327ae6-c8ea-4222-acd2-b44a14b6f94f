package heran.media.management.platform.mediaorder.resolver;

import com.google.common.collect.Lists;
import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderEntityQueryMapper;
import heran.media.management.platform.mediaorder.sudomain.dto.OrderEstimateBaseLandData;
import heran.media.management.platform.mediaorder.sudomain.request.CreateOverviewDataRequest;
import heran.media.sharelib.domain.bo.ExposureType;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.db.mapper.main.OrderEffectEstimateBaseManLandMapper;
import heran.media.sharelib.domain.db.model.main.StatisticsDimension;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class EffectMediumFormatCollectResolver extends EffectCollectResolver {
    @Resource
    private OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper;
    @Resource
    private MediaOrderEntityQueryMapper mediaOrderEntityQueryMapper;

    @Override
    public void resolver(CreateOverviewDataRequest request) {
        request.setCalculateRatio(false);
        //加之前先删除一下之前的
        orderEffectEstimateBaseManLandMapper.deleteByType(request.getOrderId(), request.getBizType(), request.getDataType());
        //删除一下汇总的
        orderEffectEstimateBaseManLandMapper.deleteByType(request.getOrderId(), request.getBizType(), ExposureType.MEDIUM_FORMAT_COLLECT.name());
        List<OrderEstimateBaseLandData> landData = mediaOrderEntityQueryMapper.selectOrderMediumEstimateBaseLandData(request.getOrderId(), request.getBizType());
        if (CollectionUtils.isEmpty(landData)) {
            return;
        }

        OrderEstimateBaseLandData data = landData.get(0);
        String statisticalType = "RESIDENT".equals(data.getPopulation()) ? StatisticalType.RESIDENT_POPULATION.name() : StatisticalType.PASSENGER_POPULATION.name();

        Map<Integer, Map<String, Set<String>>> mediumFormatUv = getMediumFormatUv(request);
        //城市人口数据
        HashMap<Integer, Map<String, BigDecimal>> mapPopulationMediumFormat = getMapPopulation(statisticalType, mediumFormatUv);

        Map<String, List<OrderEstimateBaseLandData>> map = landData.stream()
                .collect(Collectors.groupingBy(OrderEstimateBaseLandData::getCategoryId));

        for (Map.Entry<String, List<OrderEstimateBaseLandData>> entry : map.entrySet()) {
            String key = entry.getKey();
            List<OrderEstimateBaseLandData> value = entry.getValue();

            Map<String, BigDecimal> mapPopulation = mapPopulationMediumFormat.get(Integer.parseInt(key));
            //添加数据
            collectInsertData(value, mapPopulation, request, 1.0, null);


            //处理这个媒体形式下汇总数据
            OrderEstimateBaseLandData orderEstimateBaseLandData = mediaOrderEntityQueryMapper.selectOrderMediumData(request.getOrderId(), request.getBizType(), Integer.parseInt(key));
            orderEstimateBaseLandData.setRegionCode("-1");
            orderEstimateBaseLandData.setCollectData(true);

            Map<String, Set<String>> uvMap = mediumFormatUv.get(Integer.parseInt(key));
            BigDecimal population = getPopulation(statisticalType, uvMap);

            collectInsertData(Lists.newArrayList(orderEstimateBaseLandData), null, request, 1.0, population);

        }
    }


    private HashMap<Integer, Map<String, BigDecimal>> getMapPopulation(String statisticalType, Map<Integer, Map<String, Set<String>>> mediumFormatUv) {
        //regionCode
        List<String> regionCodeList = mediumFormatUv.values().stream()
                .flatMap(innerMap -> innerMap.keySet().stream())
                .distinct()
                .collect(Collectors.toList());

        //statMonth
        List<String> statMonthList = mediumFormatUv.values().stream()
                .flatMap(innerMap -> innerMap.values().stream())
                .flatMap(Set::stream)
                .distinct()
                .collect(Collectors.toList());

        Map<String, StatisticsDimension> cityResidentMap = getCityPopulationDataMap(regionCodeList, statMonthList, statisticalType);

        HashMap<Integer, Map<String, BigDecimal>> map = new HashMap<>(8);

        for (Map.Entry<Integer, Map<String, Set<String>>> entry : mediumFormatUv.entrySet()) {
            Integer mediumId = entry.getKey();

            Map<String, BigDecimal> mapPopulation = new HashMap<>(8);
            Map<String, Set<String>> value = entry.getValue();

            getPopulationDataMap(cityResidentMap, mapPopulation, value);
            map.put(mediumId, mapPopulation);
        }
        return map;
    }


    @Override
    String getType() {
        return "MEDIUM_FORMAT";
    }
}
