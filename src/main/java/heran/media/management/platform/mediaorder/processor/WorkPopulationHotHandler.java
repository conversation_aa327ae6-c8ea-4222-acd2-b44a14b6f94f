package heran.media.management.platform.mediaorder.processor;

import heran.media.sharelib.domain.bo.StatisticalType;
import org.springframework.stereotype.Component;

/**
 * 工作人口处理器
 *
 * <AUTHOR>
 */
@Component
public class WorkPopulationHotHandler extends AbstractHotAdCodeProcessor {
    @Override
    public StatisticalType getStatisticalType() {
        return StatisticalType.WORK_POPULATION;
    }
}
