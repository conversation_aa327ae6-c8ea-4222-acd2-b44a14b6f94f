package heran.media.management.platform.mediaorder.sudomain.response;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.mediaorder.sudomain.dto.GridHotDataOrder;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.bo.TaStatisticalType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SelectBaseDataResponse {
    @ApiModelProperty("城市编码")
    private String code;
    @ApiModelProperty("城市id")
    private Long cityId;
    @ApiModelProperty("城市常驻网格人口热力")
    private Boolean residentPopulationHot;
    @ApiModelProperty("城市居住网格人口热力")
    private Boolean dwellPopulationHot;
    @ApiModelProperty("城市工作网格人口热力")
    private Boolean workPopulationHot;
    @ApiModelProperty("城市客流网格人口热力")
    private Boolean passengerPopulationHot;
    @ApiModelProperty("城市客流网格人口热力")
    private Boolean passengerHourPopulationHot;
    @ApiModelProperty("城市常驻人口")
    private Boolean residentPopulationTa;
    @ApiModelProperty("城市TA常驻人口热力")
    private Boolean residentPopulationTaHot;
    @ApiModelProperty("城市客流人口")
    private Boolean passengerPopulationTa;
    @ApiModelProperty("城市客流人口热力")
    private Boolean passengerPopulationTaHot;

    public SelectBaseDataResponse(String code, Long cityId) {
        this.code = code;
        this.cityId = cityId;
        this.residentPopulationHot = false;
        this.dwellPopulationHot = false;
        this.workPopulationHot = false;
        this.passengerPopulationHot = false;
        this.passengerHourPopulationHot = false;
        this.residentPopulationTa = false;
        this.residentPopulationTaHot = false;
        this.passengerPopulationTa = false;
        this.passengerPopulationTaHot = false;
    }

    public SelectBaseDataResponse(String code, Long cityId, GridHotDataOrder gridHotDataOrder,GridHotDataOrder gridTaHotDataOrder) {
        this.code = code;
        this.cityId = cityId;
        if (gridHotDataOrder !=null){
            this.residentPopulationHot = !StringUtils.isEmpty(gridHotDataOrder.getStatisticalTypes()) &&
                    Arrays.asList(gridHotDataOrder.getStatisticalTypes().split(",")).contains(StatisticalType.RESIDENT_POPULATION.name());
            this.dwellPopulationHot = !StringUtils.isEmpty(gridHotDataOrder.getStatisticalTypes())
                    && Arrays.asList(gridHotDataOrder.getStatisticalTypes().split(",")).contains(StatisticalType.DWELL_POPULATION.name());
            this.workPopulationHot = !StringUtils.isEmpty(gridHotDataOrder.getStatisticalTypes())
                    && Arrays.asList(gridHotDataOrder.getStatisticalTypes().split(",")).contains(StatisticalType.WORK_POPULATION.name());
            this.passengerPopulationHot = !StringUtils.isEmpty(gridHotDataOrder.getStatisticalTypes())
                    && Arrays.asList(gridHotDataOrder.getStatisticalTypes().split(",")).contains(StatisticalType.PASSENGER_POPULATION.name());
            this.passengerHourPopulationHot = !StringUtils.isEmpty(gridHotDataOrder.getStatisticalTypes())
                    && Arrays.asList(gridHotDataOrder.getStatisticalTypes().split(",")).contains(StatisticalType.PASSENGER_HOUR_POPULATION.name());
        }

        if (gridTaHotDataOrder != null){
            this.residentPopulationTaHot = !StringUtils.isEmpty(gridTaHotDataOrder.getStatisticalTypes()) &&
                    Arrays.asList(gridTaHotDataOrder.getStatisticalTypes().split(",")).contains(TaStatisticalType.TA_RESIDENT_POPULATION.name());
            this.passengerPopulationTaHot = !StringUtils.isEmpty(gridTaHotDataOrder.getStatisticalTypes()) &&
                    Arrays.asList(gridTaHotDataOrder.getStatisticalTypes().split(",")).contains(TaStatisticalType.TA_PASSENGER_POPULATION.name());
        }

        this.residentPopulationTa = false;

        this.passengerPopulationTa = false;

    }

}
