package heran.media.management.platform.mediaorder.processor;

import heran.media.management.platform.mediaorder.sudomain.request.HotAdCodeRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HotContext {
    private HotAdCodeRequest request;
    private String type;
    private Integer scale;
    private String dataType;
    private Integer dataId;
    private String timeType;
    private String typeInfo;


    private Integer userKey;
}
