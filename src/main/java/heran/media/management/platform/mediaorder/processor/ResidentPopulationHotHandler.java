package heran.media.management.platform.mediaorder.processor;

import heran.media.sharelib.domain.bo.StatisticalType;
import org.springframework.stereotype.Component;

/**
 * 常驻人口处理器
 *
 * <AUTHOR>
 */
@Component
public class ResidentPopulationHotHandler extends AbstractHotAdCodeProcessor {
    @Override
    public StatisticalType getStatisticalType() {
        return StatisticalType.RESIDENT_POPULATION;
    }
}
