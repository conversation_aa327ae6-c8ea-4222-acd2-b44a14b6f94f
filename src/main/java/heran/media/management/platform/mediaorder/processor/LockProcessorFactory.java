package heran.media.management.platform.mediaorder.processor;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class LockProcessorFactory {
    private static final Map<String, LockProcessor> LOCK_PROCESSOR_MAP=new HashMap<>();

    public LockProcessorFactory(LockProcessor... kpiQueryTemplates){
        for(LockProcessor kpiQueryTemplate:kpiQueryTemplates){
            LOCK_PROCESSOR_MAP.put(kpiQueryTemplate.getType(),kpiQueryTemplate);
        }
    }

    public LockProcessor getProcessor(String type){
        return LOCK_PROCESSOR_MAP.get(type);
    }

    public void execute(String type, LockDataContext lockDataContext){
        this.getProcessor(type).execute(lockDataContext);
    }

}
