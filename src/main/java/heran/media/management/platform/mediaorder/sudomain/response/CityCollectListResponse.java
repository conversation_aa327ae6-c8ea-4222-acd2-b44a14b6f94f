package heran.media.management.platform.mediaorder.sudomain.response;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.mediaorder.sudomain.dto.MediumFormatCollectListData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class CityCollectListResponse {
    @ApiModelProperty("媒介形式")
    private List<Integer> categoryId;
    @ApiModelProperty("城市code")
    private String cityCode;
    @ApiModelProperty("城市名称")
    private String cityName;
    @ApiModelProperty("投放周期")
    private Integer deliveryDays;
    @ApiModelProperty("投放费用")
    private BigDecimal placementCost;
    @ApiModelProperty("投放点位")
    private Integer vacancyPosition;
    @ApiModelProperty("重叠比例")
    private Double overlapRatio;
    @ApiModelProperty("曝光人次PV 单位：人次")
    private BigDecimal pv;
    @ApiModelProperty("曝光人数UV 单位：人")
    private BigDecimal uv;
    @ApiModelProperty("平均触达频次AF 单位：次")
    private BigDecimal af;
    @ApiModelProperty("曝光潜客人数TA UA 单位：人")
    private BigDecimal taUv;
    @ApiModelProperty("曝光潜客浓度TA%")
    private BigDecimal ta;
    @ApiModelProperty("城市渗透率Reach")
    private BigDecimal reach;
    @ApiModelProperty("千人成本CPM 单位：元")
    private BigDecimal cpm;
    @ApiModelProperty("单人成本CP UV 单位：元")
    private BigDecimal cpUv;
    @ApiModelProperty("单TA成本CP TA 单位：元")
    private BigDecimal cpTa;

    public CityCollectListResponse(MediumFormatCollectListData data) {
        if (StringUtils.isNotEmpty(data.getCategoryId())) {
            this.categoryId = Arrays.stream(data.getCategoryId().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }
        this.cityCode = data.getCityCode();
        this.cityName = data.getRegionName();
        this.deliveryDays = data.getDeliveryDays();
        this.placementCost = data.getPlacementCost();
        this.vacancyPosition = data.getVacancyPosition();
        this.overlapRatio = data.getOverlapRatio();
        this.pv = data.getPv();
        this.uv = data.getUv();
        this.af = data.getAf();
        this.taUv = data.getTaUv();
        this.ta = data.getTa();
        this.reach = data.getReach();
        this.cpm = data.getCpm();
        this.cpUv = data.getCpUv();
        this.cpTa = data.getCpTa();
    }
}
