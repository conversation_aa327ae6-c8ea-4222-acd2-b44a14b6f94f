package heran.media.management.platform.mediaorder.sudomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CalculateOverlapRatioRequest {
    @ApiModelProperty("订单id")
    private Integer orderId;
    @ApiModelProperty("计算重叠比例状态")
    private Boolean calculateRatio;
    @ApiModelProperty("计算重叠比例系数")
    private Double ratioCoefficient;
    @ApiModelProperty("事件类型 指标类型ORDER(订单)，APPRAISAL(全域效果评估)")
    private String bizType;
}
