package heran.media.management.platform.mediaorder.controller;

import com.alibaba.excel.EasyExcel;
import heran.media.management.platform.common.domain.dto.MetaFieldInfo;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.service.CommonService;
import heran.media.management.platform.common.utils.DownloadFileUtils;
import heran.media.management.platform.maincategoryextentattr.service.MainCategoryExtendAttrService;
import heran.media.management.platform.mediaorder.listener.EasyExceGeneralDatalListener;
import heran.media.management.platform.mediaorder.listener.EasyExcelAllCollectDataListener;
import heran.media.management.platform.mediaorder.listener.EasyExcelCityCollectDataListener;
import heran.media.management.platform.mediaorder.listener.EasyExcelMediumFormatCollectDataListener;
import heran.media.management.platform.mediaorder.service.MediaOrderService;
import heran.media.management.platform.mediaorder.sudomain.bo.XlsAllCollectData;
import heran.media.management.platform.mediaorder.sudomain.bo.XlsCityCollectData;
import heran.media.management.platform.mediaorder.sudomain.bo.XlsMediumFormatCollectData;
import heran.media.management.platform.mediaorder.sudomain.dto.*;
import heran.media.management.platform.mediaorder.sudomain.request.*;
import heran.media.management.platform.mediaorder.sudomain.response.*;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.db.mapper.main.OrderEffectEstimateBaseManLandMapper;
import heran.media.sharelib.domain.db.mapper.main.OrderIntelligenceEstimateDetailMapper;
import heran.media.sharelib.domain.db.model.main.MainResourceCategory;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@ApiOperation(value = "智投订单管理")
@Slf4j
@RestController
@RequestMapping("/mediaOrder")
public class MediaOrderController {

    @Value("${spring.application.name}")
    public String appName;

    @Resource
    private MediaOrderService orderService;
    @Resource
    private OrderIntelligenceEstimateDetailMapper orderIntelligenceEstimateDetailMapper;
    @Resource
    private OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private MainCategoryExtendAttrService mainCategoryExtendAttrService;

    @ApiLog(storageDescription = "智投订单列表查询")
    @ApiOperation(value = "智投订单列表查询", notes = "查询条件【订单ID(orderSn)/订单名称(orderName)/品牌名称(brandName)/集团名称(groupName)/订单状态(orderStatus ——》【PENDING_SUBMIT(待提交), PENDING_DELIVERY(待投放),DELIVERING(投放中),DELIVERY_COMPLETE(投放完成)】)/投放起始时间(deliveryStartTime)/投放结束时间(deliveryEndTime)】")
    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<TMediaOrderRes>> list(@Valid @RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        PageResponse<TMediaOrderRes> pageResponse = orderService.list(criteria);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog(storageDescription = "获取引用方案数据列表")
    @ApiOperation(value = "获取引用方案数据列表")
    @RequestMapping(value = "/getReferenceSchemeList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<TReferenceSchemeRes> getReferenceSchemeList() {
        InternalResponse internalResponse = InternalResponse.success();
        List<TReferenceSchemeRes> results = orderService.getReferenceSchemeList();
        return internalResponse.withBody(results);
    }


    @ApiLog(storageDescription = "根据方案ID获取引用方案数据列表")
    @ApiOperation(value = "根据方案ID获取引用方案数据列表")
    @RequestMapping(value = "/getMediaPlacementPlanByPlanId", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<TMediaPlacementPlanRes> getMediaPlacementPlanByPlanId(@RequestParam("planId") String planId) {
        InternalResponse internalResponse = InternalResponse.success();
        TMediaPlacementPlanRes tMediaPlacementPlanRes = orderService.getMediaPlacementPlanByPlanIdAndPlanName(planId);
        return internalResponse.withBody(tMediaPlacementPlanRes);
    }

    @ApiLog(storageDescription = "单个智投订单查询")
    @ApiOperation(value = "单个智投订单查询")
    @RequestMapping(value = "/getById", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<MediaOrderRespone> getById(@RequestParam("id") Integer id) {
        InternalResponse internalResponse = null;
        MediaOrderRespone mediaOrderRespone = orderService.getById(id);
        internalResponse = InternalResponse.success().withBody(mediaOrderRespone);
        return internalResponse;
    }

    @ApiLog(storageDescription = "修改智投订单")
    @ApiOperation(value = "修改智投订单")
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'SMARTSELECTIONORDER_UPDATE')")
    @ResponseBody
    public InternalResponse<PageResponse<UpdateMediaOrderReq>> update(@Valid @RequestBody UpdateMediaOrderReq updateMediaOrderReq) {
        AuthUserInfo userInfo = AuthUtils.getUserInfo();
        InternalResponse<PageResponse<UpdateMediaOrderReq>> internalResponse = InternalResponse.success();
        orderService.update(updateMediaOrderReq, userInfo.getIdentifier());
        return internalResponse;
    }


    @ApiLog(storageDescription = "智选详情-预估效果详情")
    @ApiOperation(value = "智选详情-预估效果详情", notes = "入参为订单id")
    @RequestMapping(value = "/getEstimatedEffectByOrderId", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<TorderIntelligenceEstimatedetailReq> getEstimatedEffectByOrderId(@RequestParam("id") Integer id) {
        InternalResponse internalResponse = null;
        AuthUserInfo authUserInfo = AuthUtils.getUserInfo();
        TorderIntelligenceEstimatedetailReq torderIntelligenceEstimatedetailReq = orderService.getEstimatedEffectByOrderId(id);
        internalResponse = InternalResponse.success().withBody(torderIntelligenceEstimatedetailReq);
        return internalResponse;
    }


    @ApiLog(storageDescription = "上传排期")
    @ApiOperation(value = "上传排期")
    @RequestMapping(value = "/uploadScheduling", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'SMARTSELECTIONORDER_IMPORT')")
    @ResponseBody
    public InternalResponse uploadScheduling(@Valid @RequestBody TUploadSchedulingReq tUploadSchedulingReq) {
        InternalResponse internalResponse = null;
        AuthUserInfo authUserInfo = AuthUtils.getUserInfo();
        orderService.uploadScheduling(tUploadSchedulingReq, authUserInfo.getIdentifier());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiLog(storageDescription = "上传投放监测")
    @ApiOperation(value = "上传投放监测")
    @RequestMapping(value = "/uploadLaunchMonitoring", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'SMARTSELECTIONORDER_MONITOR')")
    @ResponseBody
    public InternalResponse uploadLaunchMonitoring(@Valid @RequestBody TUploadLaunchMonitoring tUploadLaunchMonitoring) {
        InternalResponse internalResponse = null;
        AuthUserInfo authUserInfo = AuthUtils.getUserInfo();
        orderService.uploadLaunchMonitoring(tUploadLaunchMonitoring, authUserInfo.getIdentifier());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiLog(storageDescription = "品牌启发与建议")
    @ApiOperation(value = "品牌启发与建议")
    @RequestMapping(value = "/brandInspirationAndSuggestions", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse brandInspirationAndSuggestions(@Valid @RequestBody TUploadLaunchMonitoring tUploadLaunchMonitoring) {
        InternalResponse internalResponse = null;
        AuthUserInfo authUserInfo = AuthUtils.getUserInfo();
        orderService.brandInspirationAndSuggestions(tUploadLaunchMonitoring, authUserInfo.getIdentifier());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiLog(storageDescription = "删除")
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'SMARTSELECTIONORDER_DELETE')")
    @ResponseBody
    public InternalResponse delete(@RequestParam("id") Integer id) {
        InternalResponse internalResponse = null;
        AuthUserInfo authUserInfo = AuthUtils.getUserInfo();
        orderService.delete(id, authUserInfo.getIdentifier());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "媒体匹配结果文件上传", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @PreAuthorize("hasPermission(null,'SMARTSELECTIONORDER_DETAILS')")
    @ResponseBody
    public void importData(HttpServletResponse response, @RequestPart("file") MultipartFile file, Integer orderId) throws Exception {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        EasyExcel.read(file.getInputStream(), new EasyExceGeneralDatalListener(currentUser.getIdentifier(), orderService, orderId, orderIntelligenceEstimateDetailMapper)).headRowNumber(0).sheet().doRead();

    }

    @ApiOperation(value = "媒体匹配结果文件模板下载", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/downloadTemplateToMediaResult", method = RequestMethod.POST, consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadTemplateToMediaResult(HttpServletResponse resp) throws Exception {
        File file = orderService.downloadTemplateToMediaResult();
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "媒体匹配结果文件模板");
    }

    @ApiOperation(value = "门店地址下载模板", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/downloadTemplate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadTemplate(HttpServletResponse resp) throws Exception {
        File file = orderService.generalTemplate();
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "门店地址信息模板");
    }

    @ApiOperation(value = "人群标签字典列表查询")
    @RequestMapping(value = "/getCrowdLabelList", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<CrowdLabelResponse>> getCrowdLabelList() {
        List<CrowdLabelResponse> crowdLabelList = orderService.getCrowdLabelList();
        return InternalResponse.<List<CrowdLabelResponse>>success().withBody(crowdLabelList);
    }

    @ApiOperation(value = "获取线上渠道字典")
    @RequestMapping(value = "/getFromItemList", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<FromItemResponse>> getFromItemList(@RequestParam("itemGroup") String itemGroup) {
        List<FromItemResponse> fromItemList = orderService.getFromItemList(itemGroup);
        return InternalResponse.<List<FromItemResponse>>success().withBody(fromItemList);
    }


    @ApiLog(storageDescription = "竞品监测下拉列表")
    @ApiOperation(value = "竞品监测下拉列表")
    @RequestMapping(value = "/getCompetitiveMonitoring", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<TCompetitiveMonitoring> getCompetitiveMonitoring() {
        InternalResponse internalResponse = null;
        List<TCompetitiveMonitoring> tCompetitiveMonitorings = orderService.getCompetitiveMonitoring();
        internalResponse = InternalResponse.success().withBody(tCompetitiveMonitorings);
        return internalResponse;
    }

    @ApiLog(storageDescription = "添加竞品监测")
    @ApiOperation(value = "添加竞品监测")
    @RequestMapping(value = "/CreateCompetitiveMonitoring", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'SMARTSELECTIONORDER_COMPETITORS')")
    @ResponseBody
    public InternalResponse<CreateCompetitiveMonitoring> CreateCompetitiveMonitoring(@Valid @RequestBody CreateCompetitiveMonitoring createCompetitiveMonitoring) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        orderService.CreateCompetitiveMonitoring(createCompetitiveMonitoring, currentUser.getIdentifier());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiLog(storageDescription = "竞品监测详情")
    @ApiOperation(value = "竞品监测详情")
    @RequestMapping(value = "/getCompetitiveMonitoringByOrderId", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<TCompetitiveMonitoring> getCompetitiveMonitoringByOrderId(@RequestParam("orderId") Integer orderId) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        List<TCompetitiveMonitoring> tCompetitiveMonitoringList = orderService.getCompetitiveMonitoringByOrderId(orderId);
        internalResponse = InternalResponse.success().withBody(tCompetitiveMonitoringList);
        return internalResponse;
    }


    @ApiLog(storageDescription = "竞争对手列表")
    @ApiOperation(value = "竞争对手列表")
    @RequestMapping(value = "/getCompetitor", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<TCompetitor> getCompetitor() {
        InternalResponse internalResponse = null;
        TCompetitor tCompetitor = orderService.getCompetitor();
        internalResponse = InternalResponse.success().withBody(tCompetitor);
        return internalResponse;
    }


    @ApiLog(storageDescription = "根据订单id获取到媒体形式")
    @ApiOperation(value = "根据订单id获取到媒体形式")
    @RequestMapping(value = "/getOrderResourceCategory", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<MainResourceCategoryDataResponse>> getOrderResourceCategory(@RequestParam("orderId") Integer orderId, @RequestParam("isBus") Boolean isBus) {
        InternalResponse internalResponse = null;
        List<MainResourceCategoryDataResponse> tCompetitiveMonitoringList = orderService.getOrderResourceCategory(orderId, isBus);
        internalResponse = InternalResponse.success().withBody(tCompetitiveMonitoringList);
        return internalResponse;
    }

    @ApiLog(storageDescription = "智选订单智选详情方位点位总览 订单分组")
    @ApiOperation(value = "智选订单智选详情方位点位总览 订单分组", notes = "查询字段可包含【orderId(订单id)】")
    @RequestMapping(value = "/orderSmartDetailsList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<OrderSmartDetailsListDataResponse>> orderSmartDetailsList(@Valid @RequestBody SearchCriteria criteria) {
        InternalResponse<PageResponse<OrderSmartDetailsListDataResponse>> internalResponse = InternalResponse.success();
        PageResponse<OrderSmartDetailsListDataResponse> pageResponse = orderService.orderSmartDetailsList(criteria);
        internalResponse.withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog(storageDescription = "智选订单智选详情方位点位总览 媒体形式分组")
    @ApiOperation(value = "智选订单智选详情方位点位总览 媒体形式分组")
    @RequestMapping(value = "/orderSmartDetailsCategoryList", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<OrderSmartDetailsCategoryListDataResponse> orderSmartDetailsCategoryList(@RequestParam("orderId") Integer orderId) {
        InternalResponse internalResponse = null;
        List<OrderSmartDetailsCategoryListDataResponse> dataResponseList = orderService.orderSmartDetailsCategoryList(orderId);
        internalResponse = InternalResponse.success().withBody(dataResponseList);
        return internalResponse;
    }

    @ApiLog(storageDescription = "智选订单智选详情方位点位总览 城市分组")
    @ApiOperation(value = "智选订单智选详情方位点位总览 城市分组")
    @RequestMapping(value = "/orderSmartDetailsCityList", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<OrderSmartDetailsCityListDataResponse> orderSmartDetailsCityList(@RequestParam("orderId") Integer orderId,
                                                                                             @RequestParam("categoryId") Integer categoryId) {
        InternalResponse internalResponse = null;
        List<OrderSmartDetailsCityListDataResponse> dataResponseList = orderService.orderSmartDetailsCityList(orderId, categoryId);
        internalResponse = InternalResponse.success().withBody(dataResponseList);
        return internalResponse;
    }

    @ApiLog(storageDescription = "根据订单id获取到城市数据")
    @ApiOperation(value = "根据订单id获取到城市数据")
    @RequestMapping(value = "/getSelectionPlanRegion", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<String>> getSelectionPlanRegion(@RequestParam("orderId") Integer orderId) {
        InternalResponse internalResponse = null;
        List<String> list = orderService.getSelectionPlanRegion(orderId);
        internalResponse = InternalResponse.success().withBody(list);
        return internalResponse;
    }

    @ApiLog(storageDescription = "查询数据库有无热力值回显")
    @ApiOperation(value = "查询数据库有无热力值回显")
    @RequestMapping(value = "/selectBaseData", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<SelectBaseResponse>> selectBaseData(@Valid @RequestBody SelectBaseDataRequest request) {
        InternalResponse<List<SelectBaseResponse>> internalResponse = InternalResponse.success();
        List<SelectBaseResponse> data = orderService.selectBaseData(request);
        internalResponse.withBody(data);
        return internalResponse;
    }

    @ApiLog(storageDescription = "请求和锁定数据")
    @ApiOperation(value = "请求和锁定数据")
    @RequestMapping(value = "/lockData", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<Void> lockData(@Valid @RequestBody LockDataRequest request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        orderService.lockData(currentUser.getIdentifier(), request);
        return internalResponse;
    }

    @ApiLog(storageDescription = "计算数据")
    @ApiOperation(value = "计算数据")
    @RequestMapping(value = "/computationalData", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<Void> computationalData(@Valid @RequestBody ComputationalDataRequest request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        orderService.computationalData(request);
        return internalResponse;
    }

    @ApiLog(storageDescription = "曝光数据按媒介")
    @ApiOperation(value = "曝光数据按媒介")
    @RequestMapping(value = "/mediumFormatCollectList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<MediumFormatCollectGroupListResponse>> mediumFormatCollectList(@Valid @RequestBody MediumCollectListRequest request) {
        InternalResponse<List<MediumFormatCollectGroupListResponse>> internalResponse = InternalResponse.success();
        List<MediumFormatCollectGroupListResponse> responses = orderService.mediumFormatCollectList(request);
        internalResponse.withBody(responses);
        return internalResponse;
    }

    @ApiLog(storageDescription = "曝光数据按城市")
    @ApiOperation(value = "曝光数据按城市")
    @RequestMapping(value = "/cityCollectList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<CityCollectListResponse>> cityCollectList(@Valid @RequestBody MediumCollectListRequest request) {
        InternalResponse<List<CityCollectListResponse>> internalResponse = InternalResponse.success();
        List<CityCollectListResponse> responses = orderService.cityCollectList(request);
        internalResponse.withBody(responses);
        return internalResponse;
    }

    @ApiLog(storageDescription = "关键曝光指标")
    @ApiOperation(value = "关键曝光指标")
    @RequestMapping(value = "/allCollectList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<AllCollectListResponse> allCollectList(@Valid @RequestBody MediumCollectListRequest request) {
        InternalResponse<AllCollectListResponse> internalResponse = InternalResponse.success();
        AllCollectListResponse responses = orderService.allCollectList(request);
        internalResponse.withBody(responses);
        return internalResponse;
    }


    @ApiLog(storageDescription = "设置计算重叠比例状态与系数")
    @ApiOperation(value = "设置计算重叠比例状态与系数")
    @RequestMapping(value = "/updateCalculateRatio", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<Void> updateCalculateRatio(@Valid @RequestBody UpdateCalculateRatioRequest request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        orderService.updateCalculateRatio(request);
        return internalResponse;
    }


    @ApiLog(storageDescription = "回显重叠比例系数与状态")
    @ApiOperation(value = "回显重叠比例系数与状态")
    @RequestMapping(value = "/getCalculateRatio", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<GetCalculateRatioResponse> getCalculateRatio(@RequestParam("orderId") Integer orderId) {
        InternalResponse internalResponse = null;
        GetCalculateRatioResponse response = orderService.getCalculateRatio(orderId);
        internalResponse = InternalResponse.success().withBody(response);
        return internalResponse;
    }

    @ApiOperation(value = "按媒介导出")
    @ApiLog(printOutputs = false, storage = true, storageDescription = "按媒介导出")
    @RequestMapping(value = "/exportMediumFormatCollectList", method = RequestMethod.POST, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportMediumFormatCollectList(HttpServletResponse resp,
                                              @RequestParam(value = "exportAll", required = false) Boolean exportAll,
                                              @RequestBody MediumCollectListRequest request) throws Exception {
        exportAll = exportAll != null && exportAll;
        File file = orderService.exportMediumFormatCollectList(request, exportAll);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "按媒介导出");
    }

    @ApiOperation(value = "按城市导出")
    @ApiLog(printOutputs = false, storage = true, storageDescription = "按城市导出")
    @RequestMapping(value = "/exportCityCollectList", method = RequestMethod.POST, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportCityCollectList(HttpServletResponse resp,
                                      @RequestParam(value = "exportAll", required = false) Boolean exportAll,
                                      @RequestBody MediumCollectListRequest request) throws Exception {
        exportAll = exportAll != null && exportAll;
        File file = orderService.exportCityCollectList(request, exportAll);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "按城市导出");
    }

    @ApiOperation(value = "关键曝光指标导出")
    @ApiLog(printOutputs = false, storage = true, storageDescription = "关键曝光指标导出")
    @RequestMapping(value = "/exportAllCollectList", method = RequestMethod.POST, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportAllCollectList(HttpServletResponse resp,
                                     @RequestParam(value = "exportAll", required = false) Boolean exportAll,
                                     @RequestBody MediumCollectListRequest request) throws Exception {
        exportAll = exportAll != null && exportAll;
        File file = orderService.exportAllCollectList(request, exportAll);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "关键曝光指标导出");
    }

    @ApiLog(storageDescription = "确认数据")
    @ApiOperation(value = "确认数据")
    @RequestMapping(value = "/confirmTheData", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<Void> confirmTheData(@Valid @RequestBody ConfirmTheDataRequest request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        orderService.confirmTheData(request);
        return internalResponse;
    }

    @ApiLog(storageDescription = "计算点位计算数量")
    @ApiOperation(value = "计算点位计算数量")
    @RequestMapping(value = "/calculatePlacementCount", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<CalculatePlacementCountResponse> calculatePlacementCount(@Valid @RequestBody CalculatePlacementCountRequest request) {
        InternalResponse<CalculatePlacementCountResponse> internalResponse = InternalResponse.success();
        CalculatePlacementCountResponse response = orderService.calculatePlacementCount(request);
        internalResponse.withBody(response);
        return internalResponse;
    }

    @ApiLog(storageDescription = "生成总览")
    @ApiOperation(value = "生成总览")
    @RequestMapping(value = "/createOverviewData", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<Void> createOverviewData(@Valid @RequestBody CreateOverviewDataRequest request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        orderService.createOverviewData(request);
        return internalResponse;
    }

    @ApiLog(storageDescription = "请求热力值")
    @ApiOperation(value = "请求热力值")
    @RequestMapping(value = "/getHotDataByCode", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<Void> getHotDataByCode(@Valid @RequestBody HotAdCodeRequest request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        orderService.getHotDataByCode(currentUser.getIdentifier(), request);
        return internalResponse;
    }

    @ApiLog(storageDescription = "请求城市人口")
    @ApiOperation(value = "请求城市人口")
    @RequestMapping(value = "/getStatisticalUv", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<Void> getStatisticalUv(@Valid @RequestBody HotAdCodeRequest request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        orderService.getStatisticalUv(currentUser.getIdentifier(), request);
        return internalResponse;
    }

    @ApiOperation(value = "下载按媒介导入模板", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/downloadTemplateMediumFormat", method = RequestMethod.POST, consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadTemplateMediumFormat(HttpServletResponse resp) throws Exception {
        File file = commonService.generalTemplate("按媒介导入数据模板", XlsMediumFormatCollectData.class);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "按媒介导入数据模板");
    }

    @ApiOperation(value = "下载按城市导入模板", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/downloadCityCollectData", method = RequestMethod.POST, consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadCityCollectData(HttpServletResponse resp) throws Exception {
        File file = commonService.generalTemplate("按城市导入数据模板", XlsCityCollectData.class);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "按城市导入数据模板");
    }

    @ApiOperation(value = "导入按媒体形式汇总", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/importOrderEffectEstimateBaseManLand", method = RequestMethod.POST)
    public void importOrderEffectEstimateBaseManLand(@RequestPart("file") MultipartFile file, Integer orderId, String bizType, String dataType) throws Exception {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        //加之前先删除一下之前的
        orderEffectEstimateBaseManLandMapper.deleteByType(orderId, bizType, dataType);
        //删除一下汇总的
        orderEffectEstimateBaseManLandMapper.deleteByType(orderId, bizType, "MEDIUM_FORMAT_COLLECT");
        EasyExcel.read(file.getInputStream(),
                XlsMediumFormatCollectData.class,
                new EasyExcelMediumFormatCollectDataListener(
                        orderId,
                        bizType,
                        appName,
                        currentUser.getIdentifier(),
                        mainCategoryExtendAttrService,
                        orderService,
                        orderEffectEstimateBaseManLandMapper)).sheet().doRead();
    }

    @ApiOperation(value = "导入按城市汇总", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/importOrderCityEffectEstimateBaseManLand", method = RequestMethod.POST)
    public void importOrderCityEffectEstimateBaseManLand(@RequestPart("file") MultipartFile file, Integer orderId, String bizType, String dataType) throws Exception {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        //加之前先删除一下之前的
        orderEffectEstimateBaseManLandMapper.deleteByType(orderId, bizType, dataType);
        EasyExcel.read(file.getInputStream(),
                XlsCityCollectData.class,
                new EasyExcelCityCollectDataListener(
                        orderId,
                        bizType,
                        appName,
                        currentUser.getIdentifier(),
                        mainCategoryExtendAttrService,
                        orderService,
                        orderEffectEstimateBaseManLandMapper)).sheet().doRead();
    }

    @ApiLog(storageDescription = "获取订单智选详情动态列表回显")
    @ApiOperation(value = "获取订单智选详情动态列表回显")
    @RequestMapping(value = "/getOrderPlacementSnLabel", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<MetaFieldInfo>> getOrderPlacementSnLabel(
            @RequestParam("categoryId") Integer categoryId) {
        InternalResponse internalResponse = null;
        List<MetaFieldInfo> response = orderService.getOrderPlacementSnLabel(categoryId);
        internalResponse = InternalResponse.success().withBody(response);
        return internalResponse;
    }

    @ApiLog(storageDescription = "获取订单智选详情动态列表数据回显")
    @ApiOperation(value = "获取订单智选详情动态列表数据回显")
    @RequestMapping(value = "/getOrderPlacementList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<Map<String, Object>>> getOrderPlacementList(@Valid @RequestBody OrderPlacementListRequest request) {
        InternalResponse<PageResponse<Map<String, Object>>> internalResponse = InternalResponse.success();
        PageResponse<Map<String, Object>> orderPlacementList = orderService.getOrderPlacementList(request);
        internalResponse.withBody(orderPlacementList);
        return internalResponse;
    }

    @ApiOperation(value = "下载关键曝光指标导入模板", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/downloadAllCollectData", method = RequestMethod.POST, consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadAllCollectData(HttpServletResponse resp) throws Exception {
        File file = commonService.generalTemplate("关键曝光指标导入数据模板", XlsAllCollectData.class);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "关键曝光指标导入数据模板");
    }


    @ApiOperation(value = "导入关键曝光指标汇总", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/importOrderAllEffectEstimateBaseManLand", method = RequestMethod.POST)
    public void importOrderAllEffectEstimateBaseManLand(@RequestPart("file") MultipartFile file, Integer orderId, String bizType, String dataType) throws Exception {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        //加之前先删除一下之前的
        orderEffectEstimateBaseManLandMapper.deleteByType(orderId, bizType, dataType);
        EasyExcel.read(file.getInputStream(),
                XlsAllCollectData.class,
                new EasyExcelAllCollectDataListener(
                        orderId,
                        bizType,
                        appName,
                        currentUser.getIdentifier(),
                        orderEffectEstimateBaseManLandMapper)).sheet().doRead();
    }

    @ApiLog(storageDescription = "订单点位详情动态表头")
    @ApiOperation(value = "订单点位详情动态表头")
    @RequestMapping(value = "/getByIdOrderPlacementDetailsLabel", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<MetaFieldInfo>> getByIdOrderPlacementDetailsLabel(@RequestParam("categoryId") Integer categoryId) {
        InternalResponse internalResponse = null;
        List<MetaFieldInfo> response = orderService.getByIdOrderPlacementDetailsLabel(categoryId);
        internalResponse = InternalResponse.success().withBody(response);
        return internalResponse;
    }

    @ApiLog(storageDescription = "订单点位详情动态列表数据")
    @ApiOperation(value = "订单点位详情动态列表数据")
    @RequestMapping(value = "/getByIdOrderPlacementDetails", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<Map<String, Object>> getByIdOrderPlacementDetails(@RequestParam("placementId") Long placementId,
                                                                              @RequestParam("orderId") Integer orderId,
                                                                              @RequestParam("categoryId") Integer categoryId) {
        InternalResponse internalResponse = null;
        Map<String, Object> response = orderService.getByIdOrderPlacementDetails(placementId, orderId, categoryId);
        internalResponse = InternalResponse.success().withBody(response);
        return internalResponse;
    }


    @ApiLog
    @ApiOperation(value = "导出订单人地数据投放点位数据", notes = "导出订单人地数据投放点位数据Excel")
    @RequestMapping(value = "/exportOrderMetaPlacementSnList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public void exportOrderMetaPlacementSnList(@RequestBody @Valid OrderPlacementListRequest request, HttpServletResponse response) {
        orderService.exportOrderMetaPlacementSnList(response, request);
    }


}
