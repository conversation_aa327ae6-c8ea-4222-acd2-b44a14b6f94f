package heran.media.management.platform.mediaorder.processor;

import heran.media.management.platform.mediaorder.sudomain.request.LockDataRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LockDataContext {
    private LockDataRequest request;
    private Integer userKey;

    private Integer taskId;

    public boolean setStatMonthJson() {
        return "APPRAISAL".equals(request.getKpiType()) && !request.getRefinedCalculation();
    }

    public String getTransitionType() {
        //精确计算
        if (request.getRefinedCalculation()) {
            return "APPRAISAL_REFINED";
        }
        //订单计算 与全域效果评估基础计算
        return request.getKpiType();
    }
}
