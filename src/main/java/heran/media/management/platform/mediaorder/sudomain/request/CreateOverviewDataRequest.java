package heran.media.management.platform.mediaorder.sudomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CreateOverviewDataRequest {
    @ApiModelProperty("订单id")
    private Integer orderId;
    @ApiModelProperty("事件类型 指标类型ORDER(订单)，APPRAISAL(全域效果评估)")
    private String bizType;
    @ApiModelProperty("按媒体(MEDIUM_FORMAT) 按城市(CITY_FORMAT) 关键曝光指标(ALL_FORMAT)")
    private String dataType;
    @ApiModelProperty("计算重叠比例状态")
    private Boolean calculateRatio;
    @ApiModelProperty("计算重叠比例系数")
    private Double ratioCoefficient;


    public boolean verifyCityFormat() {
        return "CITY_FORMAT".equals(dataType);
    }

}
