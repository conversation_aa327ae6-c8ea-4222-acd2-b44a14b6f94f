package heran.media.management.platform.mediaorder.sudomain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderSmartDetailsListData {
    @ApiModelProperty("订单名称")
    private String orderName;
    @ApiModelProperty("媒体形式分类")
    private Integer categoryCount;
    @ApiModelProperty("城市")
    private Integer cityCount;
    @ApiModelProperty("投放开始时间")
    private Date deliveryStartTime;
    @ApiModelProperty("投放结束时间")
    private Date deliveryEndTime;
    @ApiModelProperty("投放周期（天）")
    private Integer periodDays;
    @ApiModelProperty("点位总量（个）")
    private Integer placementCount;
    @ApiModelProperty("投放费用（元）")
    private BigDecimal periodAmount;
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
