package heran.media.management.platform.mediaorder.template;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.github.pagehelper.PageHelper;
import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderEntityQueryMapper;
import heran.media.management.platform.mediaorder.sudomain.dto.CalendarPlacementOrderData;
import heran.media.management.platform.mediaorder.sudomain.request.ComputationalDataRequest;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import heran.media.sharelib.utils.date.DataTmeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * LED屏/大牌
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LedScreenComputationalDataTemplate extends AbstractComputationalDataTemplate {

    private final static String RESIDENT = "RESIDENT";

    private final static int EXPORT_PAGE_LIMIT = 5000;

    @Resource
    private MediaOrderEntityQueryMapper mediaOrderEntityQueryMapper;

    @Override
    public void execute(ComputationalDataContext kpiQueryContext) {
        int pageNo = 1;
        ComputationalDataRequest request = kpiQueryContext.getRequest();
        List<CalendarPlacementOrderData> placementOrderData;
        do {
            PageHelper.startPage(pageNo, EXPORT_PAGE_LIMIT, false);
            placementOrderData = mediaOrderEntityQueryMapper.selectCalendarPlacement(request.getCategoryId(), request.getCriteria(), request.getDataType());
            if (CollectionUtils.isEmpty(placementOrderData)) {
                break;
            }
            List<MainCalendarOrderKpi> mainCalendarOrderKpis = calculateKpi(kpiQueryContext, placementOrderData);
            //修改指标数据
            updateMainCalendarOrderKpi(mainCalendarOrderKpis);
            pageNo++;
        } while (!placementOrderData.isEmpty());
    }

    protected List<MainCalendarOrderKpi> calculateKpi(ComputationalDataContext kpiQueryContext, List<CalendarPlacementOrderData> placementOrderData) {
        return placementOrderData.stream().map(data -> {
            MainCalendarOrderKpi kpi;
            if (data.getPopulation().equals(RESIDENT)) {
                kpi = calculateResident(kpiQueryContext, data);
            } else {
                kpi = calculateFloating(kpiQueryContext, data);
            }
            return kpi;
        }).collect(Collectors.toList());
    }

    /**
     * 设置天级时间占比
     *
     * @param kpiQueryContext 上下文
     * @param data            CalendarPlacementOrderData
     * @return double
     */
    protected double getHeavenProportion(ComputationalDataContext kpiQueryContext, CalendarPlacementOrderData data) {
        //先判断有没有传值天级时间占比
        if (StringUtils.isNotEmpty(kpiQueryContext.getRequest().getDayTimeProportion())) {
            //用传参天级时间占比去计算
            BigDecimal proportion = new BigDecimal(kpiQueryContext.getRequest().getDayTimeProportion()).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
            return proportion.doubleValue();
        }

        //没有传值 判断要不要进行 天级时间占比的计算
        if (StringUtils.isNotEmpty(kpiQueryContext.getVisibleAngleRatio())) {
            //计算天级时间占比
            return calculateHeavenProportion(data);
        }

        return 1;
    }

    /**
     * 计算天级时间占比
     *
     * @param data CalendarPlacementOrderData
     * @return 占比
     */
    protected double calculateHeavenProportion(CalendarPlacementOrderData data) {
        /*
         * 时长*次数=总投放秒数，
         * 总投放小时数=总投放秒数/3600，
         * 开屏小时数=开屏时间，
         * 天级时间占比=总投放小时数/开屏小时数（%）
         */
        if (StringUtils.isEmpty(data.getBrighten()) || data.getLaunchTime() == null || data.getLaunchRate() == null) {
            return 0;
        }
        //总投放秒数
        BigDecimal sumLaunchSecond = new BigDecimal(data.getLaunchTime()).multiply(new BigDecimal(data.getLaunchRate()));
        //总投放小时数
        BigDecimal sumLaunchHour = sumLaunchSecond.divide(new BigDecimal(3600), 5, RoundingMode.HALF_UP);
        //开屏小时数
        int spreadScreenHour = DataTmeUtils.calculateTimeRange(data.getBrighten());
        //0不能做除数
        if (spreadScreenHour == 0) {
            return 0;
        }
        //天级时间占比
        BigDecimal heavenProportion = sumLaunchHour.divide(new BigDecimal(spreadScreenHour), 4, RoundingMode.HALF_UP);
        return heavenProportion.doubleValue();
    }

    /**
     * 常驻计算
     *
     * @param data CalendarPlacementOrderData
     */
    protected MainCalendarOrderKpi calculateResident(ComputationalDataContext kpiQueryContext, CalendarPlacementOrderData data) {
        MainCalendarOrderKpi kpi = data.adaToPo();
        //计算天级时间占比
        double heavenProportion = getHeavenProportion(kpiQueryContext, data);
        kpi.setHeavenProportion(String.valueOf(heavenProportion));
        //计算 UV=WKT常驻人口统计*可视范围面积占比*天级时间占比
        String visibleAngle = StringUtils.isEmpty(kpiQueryContext.getRequest().getVisibleAngle()) ? "360" : kpiQueryContext.getRequest().getVisibleAngle();
        kpi.setVisibleAngle(kpiQueryContext.getRequest().getVisibleAngle());
        BigDecimal uv = getUv(kpiQueryContext,
                new BigDecimal(data.getAoiResidentPopulation()),
                new BigDecimal(heavenProportion),
                new BigDecimal(visibleAngle),
                data.getAoiCoordinates(),
                data.getMileage());
        kpi.setUv(uv.intValue());
        commonalityCalculate(kpi, data, uv);
        return kpi;
    }

    /**
     * 客流计算
     *
     * @param data CalendarPlacementOrderData
     */
    protected MainCalendarOrderKpi calculateFloating(ComputationalDataContext kpiQueryContext, CalendarPlacementOrderData data) {
        MainCalendarOrderKpi kpi = data.adaToPo();
        //UV=WKT客流人口（天级）统计*可视范围面积占比*天级时间占比
        String visibleAngle = StringUtils.isEmpty(kpiQueryContext.getRequest().getVisibleAngle()) ? "360" : kpiQueryContext.getRequest().getVisibleAngle();
        kpi.setVisibleAngle(kpiQueryContext.getRequest().getVisibleAngle());
        double heavenProportion = getHeavenProportion(kpiQueryContext, data);
        kpi.setHeavenProportion(String.valueOf(heavenProportion));
        BigDecimal uv = getUv(kpiQueryContext,
                new BigDecimal(data.getAoiPassengerPopulation()),
                new BigDecimal(heavenProportion),
                new BigDecimal(visibleAngle),
                data.getAoiCoordinates(),
                data.getMileage());
        kpi.setUv(uv.intValue());
        commonalityCalculate(kpi, data, uv);
        return kpi;
    }

    /**
     * 抽取出来公共的计算方式
     *
     * @param kpi  MainCalendarOrderKpi
     * @param data CalendarPlacementOrderData
     * @param uv   BigDecimal
     */
    protected void commonalityCalculate(MainCalendarOrderKpi kpi, CalendarPlacementOrderData data, BigDecimal uv) {
        BigDecimal placementCost = data.getPlacementCost() != null
                ? BigDecimal.valueOf(data.getPlacementCost())
                : BigDecimal.ZERO;
        //PV=【客流（小时级）/客流天级】*UV*投放天数
        BigDecimal pv = getPv(data, uv);
        kpi.setPv(pv.intValue());
        //AF=客流（小时级）/客流天级
        BigDecimal af = getAf(data, kpi.getHeavenProportion());
        kpi.setAf(af.doubleValue());
        //计算 TA_UV=WKT TA客流人统计
        kpi.setTauv(data.getAoiTaCount());
        //计算 CPM=（广告总费用/曝光总PV）*1000
        BigDecimal cpm = getCpm(pv, placementCost);
        kpi.setCpm(cpm.doubleValue());
        //计算 CP_UV=曝光总UV/广告总费用
        BigDecimal cpUv = getCpUv(uv, placementCost);
        kpi.setCpuv(cpUv.doubleValue());
        //计算 CP_TA=曝光总TA_UV/广告总费用
        BigDecimal cpTa = getCpTa(placementCost, new BigDecimal(data.getAoiTaCount()));
        kpi.setCpta(cpTa.doubleValue());
    }

    /**
     * 计算uv
     *
     * @param kpiQueryContext  上下文
     * @param population       AOI人口数
     * @param heavenProportion 天级时间占比
     * @param visibleAngle     可视角度
     * @param aoiCoordinates   AOI缓冲区
     * @param mileage          里程数 公交车身用到
     * @return BigDecimal
     */
    protected BigDecimal getUv(ComputationalDataContext kpiQueryContext, BigDecimal population,
                               BigDecimal heavenProportion, BigDecimal visibleAngle, String aoiCoordinates, String mileage) {
        BigDecimal uv = population.multiply(heavenProportion);
        if (StringUtils.isEmpty(aoiCoordinates)) {
            //计算可视角度 °转百分比
            BigDecimal visibleAngleRatio = visibleAngle.divide(BigDecimal.valueOf(360), 2, RoundingMode.HALF_UP);
            uv = uv.multiply(visibleAngleRatio);
        }
        return uv.setScale(0, RoundingMode.HALF_UP);
    }

    protected BigDecimal getPv(CalendarPlacementOrderData data, BigDecimal uv) {
        BigDecimal pv = new BigDecimal(0);
        if (data.getAoiPassengerPopulation() != 0) {
            long days = DataTmeUtils.getDaysBetween(data.getDeliveryStartTime(), data.getDeliveryEndTime());
            BigDecimal passengerHour = new BigDecimal(data.getAoiPassengerHourPopulation());
            BigDecimal dividePassenger = passengerHour.divide(new BigDecimal(data.getAoiPassengerPopulation()), 2, RoundingMode.HALF_UP);
            pv = dividePassenger.multiply(uv).multiply(new BigDecimal(days));
        }
        return pv.setScale(0, RoundingMode.HALF_UP);
    }

    protected BigDecimal getAf(CalendarPlacementOrderData data, String heavenProportion) {
        BigDecimal af = new BigDecimal(0);
        if (data.getAoiPassengerPopulation() != 0) {
            BigDecimal passengerHour = new BigDecimal(data.getAoiPassengerHourPopulation());
            af = passengerHour.divide(new BigDecimal(data.getAoiPassengerPopulation()), 2, RoundingMode.HALF_UP);
        }
        return af.setScale(2, RoundingMode.HALF_UP);
    }

    protected BigDecimal getCpm(BigDecimal pv, BigDecimal placementCost) {
        BigDecimal cpm = new BigDecimal(0);
        if (pv.compareTo(BigDecimal.ZERO) != 0) {
            cpm = placementCost.divide(pv, 10, RoundingMode.HALF_UP).multiply(new BigDecimal(1000));
        }
        return cpm.setScale(2, RoundingMode.HALF_UP);
    }

    protected BigDecimal getCpUv(BigDecimal uv, BigDecimal placementCost) {
        BigDecimal cpUv = new BigDecimal(0);
        if (uv.compareTo(BigDecimal.ZERO) != 0) {
            cpUv = placementCost.divide(uv, 2, RoundingMode.HALF_UP);
        }
        return cpUv.setScale(2, RoundingMode.HALF_UP);
    }

    protected BigDecimal getCpTa(BigDecimal placementCost, BigDecimal aoiTaCount) {
        BigDecimal cpTa = new BigDecimal(0);
        if (aoiTaCount.compareTo(BigDecimal.ZERO) != 0) {
            cpTa = placementCost.divide(aoiTaCount, 2, RoundingMode.HALF_UP);
        }
        return cpTa.setScale(2, RoundingMode.HALF_UP);
    }


    @Override
    public String templateName() {
        return "LED_SCREEN";
    }
}
