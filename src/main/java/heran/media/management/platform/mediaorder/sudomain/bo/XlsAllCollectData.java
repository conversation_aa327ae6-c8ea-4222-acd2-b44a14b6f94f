package heran.media.management.platform.mediaorder.sudomain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.mediaorder.sudomain.dto.AllCollectListData;
import heran.media.sharelib.domain.bo.ExposureType;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseManLand;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XlsAllCollectData {
    @XlsField(title = "投放费用 单位：元", columnIndex = 0)
    @ExcelProperty(value = "投放费用 单位：元", index = 0)
    private String placementCost;
    @XlsField(title = "投放城市 单位：个", columnIndex = 1)
    @ExcelProperty(value = "投放城市 单位：个", index = 1)
    private Integer cityCount;
    @XlsField(title = "投放媒体形式 单位：个", columnIndex = 2)
    @ExcelProperty(value = "投放媒体形式 单位：个", index = 2)
    private Integer mediumCount;
    @XlsField(title = "投放媒体总量 单位：个", columnIndex = 3)
    @ExcelProperty(value = "投放媒体总量 单位：个", index = 3)
    private Integer vacancyPosition;
    @XlsField(title = "投放周期 单位：天", columnIndex = 4)
    @ExcelProperty(value = "投放周期 单位：天", index = 4)
    private Integer deliveryDays;
    @XlsField(title = "曝光人次PV 单位：人次", columnIndex = 5)
    @ExcelProperty(value = "曝光人次PV 单位：人次", index = 5)
    private String pv;
    @XlsField(title = "曝光人数UV 单位：人", columnIndex = 6)
    @ExcelProperty(value = "曝光人数UV 单位：人", index = 6)
    private String uv;
    @XlsField(title = "平均触达频次AF 单位：次", columnIndex = 7)
    @ExcelProperty(value = "平均触达频次AF 单位：次", index = 7)
    private String af;
    @XlsField(title = "曝光潜客人数TAUA 单位：人", columnIndex = 8)
    @ExcelProperty(value = "曝光潜客人数TAUA 单位：人", index = 8)
    private String taUv;
    @XlsField(title = "曝光潜客浓度TA%", columnIndex = 9)
    @ExcelProperty(value = "曝光潜客浓度TA%", index = 9)
    private String ta;
    @XlsField(title = "城市渗透率Reach", columnIndex = 10)
    @ExcelProperty(value = "城市渗透率Reach", index = 10)
    private String reach;
    @XlsField(title = "千人成本CPM 单位：元", columnIndex = 11)
    @ExcelProperty(value = "千人成本CPM 单位：元", index = 11)
    private String cpm;
    @XlsField(title = "单人成本CPUV 单位：元", columnIndex = 12)
    @ExcelProperty(value = "单人成本CPUV 单位：元", index = 12)
    private String cpUv;
    @XlsField(title = "单TA成本CPTA 单位：元", columnIndex = 13)
    @ExcelProperty(value = "单TA成本CPTA 单位：元", index = 13)
    private String cpTa;

    public OrderEffectEstimateBaseManLand advPo() {
        OrderEffectEstimateBaseManLand baseManLand = new OrderEffectEstimateBaseManLand();
        baseManLand.setExposureType(ExposureType.ALL_FORMAT.name());
        baseManLand.setPlacementCost(placementCost);
        baseManLand.setCityCount(cityCount);
        baseManLand.setMarketPlacementCount(vacancyPosition.toString());
        baseManLand.setMediumCount(mediumCount);
        baseManLand.setMarketTime(deliveryDays.toString());
        baseManLand.setPv(pv);
        baseManLand.setUv(uv);
        baseManLand.setAf(af);
        baseManLand.setImpTaUv(taUv);
        baseManLand.setTa(ta);
        baseManLand.setReach(reach);
        baseManLand.setCpm(cpm);
        baseManLand.setCpuv(cpUv);
        baseManLand.setCpta(cpTa);
        baseManLand.setConfirmTheData(false);
        return baseManLand;
    }

    public XlsAllCollectData(AllCollectListData data) {
        this.placementCost = data.getPlacementCost();
        this.cityCount = data.getCityCount();
        this.mediumCount = data.getMediumCount();
        this.vacancyPosition = data.getVacancyPosition();
        this.deliveryDays = data.getDeliveryDays();
        this.pv = data.getPv();
        this.uv = data.getUv();
        this.af = data.getAf();
        this.taUv = data.getTaUv();
        this.ta = data.getTa();
        this.reach = data.getReach();
        this.cpm = data.getCpm();
        this.cpUv = data.getCpUv();
        this.cpTa = data.getCpTa();
    }
}
