package heran.media.management.platform.mediaorder.sudomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CalculatePlacementCountRequest {
    @ApiModelProperty("订单id")
    private Integer orderId;
    @ApiModelProperty("事件类型 指标类型ORDER(订单)，APPRAISAL(全域效果评估)")
    private String bizType;
    @ApiModelProperty("分类id")
    private Integer categoryId;
}
