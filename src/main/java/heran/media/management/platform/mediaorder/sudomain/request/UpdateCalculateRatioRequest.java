package heran.media.management.platform.mediaorder.sudomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class UpdateCalculateRatioRequest {
    @ApiModelProperty("订单id")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;
    @ApiModelProperty("数据类型 ORDER 订单，APPRAISAL全域效果")
    private String dataType;
    @ApiModelProperty("计算重叠比例状态")
    @NotNull(message = "计算重叠比例状态不能为空")
    private Boolean calculateRatio;
    @ApiModelProperty("计算重叠比例系数 （不设置的话就穿个1）")
    @NotNull(message = "计算重叠比例系数不能为空")
    private Double ratioCoefficient;


    public boolean verifyDataType() {
        return "ORDER".equals(dataType);
    }
}
