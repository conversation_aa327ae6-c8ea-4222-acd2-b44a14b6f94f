package heran.media.management.platform.mediaorder.sudomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class LockDataRequest {
    @ApiModelProperty("指标类型ORDER(订单)，APPRAISAL(全域效果评估)")
    private String kpiType;
    @ApiModelProperty("订单id")
    private Integer orderId;
    @ApiModelProperty("常驻人口：RESIDENT，客流人口：PASSENGER")
    private String type;
    @ApiModelProperty("查询时间")
    private List<String> statMonth;
    @ApiModelProperty("三级分类id")
    private Integer categoryId;
    @ApiModelProperty("是否精确计算")
    private Boolean refinedCalculation;
    @ApiModelProperty("人群包id")
    private Integer packId;
}
