package heran.media.management.platform.mediaorder.processor;

import com.fasterxml.jackson.databind.JsonNode;
import heran.media.management.platform.common.error.MapDataException;
import heran.media.management.platform.mediaorder.sudomain.request.HotAdCodeRequest;
import heran.media.sharelib.client.CustomizationMapClient;
import heran.media.sharelib.domain.db.mapper.main.GridHotDataMapper;
import heran.media.sharelib.domain.db.mapper.main.StatisticsDimensionMapper;
import heran.media.sharelib.domain.db.model.main.GridHotData;
import heran.media.sharelib.domain.db.model.main.StatisticsDimension;
import heran.media.sharelib.domain.dto.amap.request.ResidentHeatMapRequest;
import heran.media.sharelib.domain.dto.amap.request.SendMessagesContext;
import heran.media.sharelib.domain.dto.amap.request.ThroughClearlyRequest;
import heran.media.sharelib.domain.dto.amap.response.HeatMapResponse;
import heran.media.sharelib.domain.dto.amap.response.ThroughClearlyResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 城市code获取热力值数据公共类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public abstract class AbstractHotAdCodeProcessor implements HotAdCodeHandler {

    int pageSize = 1000;

    @Value("${spring.application.name}")
    private String appName;

    @Resource
    private GridHotDataMapper gridHotDataMapper;
    @Resource
    private CustomizationMapClient customizationMapClient;
    @Resource
    private StatisticsDimensionMapper statisticsDimensionMapper;

    @Override
    public void handle(HotContext context) {
        HotAdCodeRequest request = context.getRequest();
        //检查当前月份 城市 类型
        Integer hotDataCount = gridHotDataMapper.getGridHotDataCount(request.getStatMonth(), context.getTypeInfo(), context.getType(), getStatisticalType().name());
        if (hotDataCount > 0) {
            //已存在 跳过
            return;
        }
//        int currentPage = 1;
//        boolean hasMoreData = true;
//        while (hasMoreData) {
//            try {
//                ResidentHeatMapRequest heatMapRequest = createResidentHeatMapRequest(context, currentPage, pageSize);
//                HeatMapResponse heatMapResponse = customizationMapClient.residentHeatMap(heatMapRequest);
//                hasMoreData = processResidentHeatMapResponse(context, heatMapResponse);
//                //页码递增
//                currentPage++;
//            } catch (Exception e) {
//                log.error("处理第{}页热力图数据失败: error={}", currentPage, e.getMessage(), e);
//                break;
//            }
//        }
        try {
            ResidentHeatMapRequest heatMapRequest = createResidentHeatMapRequest(context, null, pageSize);
            SendMessagesContext messagesContext = SendMessagesContext.builder().userId(context.getUserKey()).locType(getStatisticalType().name()).build();
            HeatMapResponse heatMapResponse = customizationMapClient.residentHeatMap(heatMapRequest, messagesContext);
            processResidentHeatMapResponse(context, heatMapResponse);
        } catch (Exception e) {
            log.error("处理热力图数据失败: error={}", e.getMessage(), e);
        }
    }

    /**
     * 构建请求参数
     *
     * @param context     HotContext
     * @param currentPage currentPage
     * @param pageSize    pageSize
     * @return ResidentHeatMapRequest
     */
    protected ResidentHeatMapRequest createResidentHeatMapRequest(HotContext context, Integer currentPage, Integer pageSize) {
        ResidentHeatMapRequest residentHeatMapRequest = new ResidentHeatMapRequest();
        residentHeatMapRequest.setType(context.getType());
        residentHeatMapRequest.setTypeInfo(context.getTypeInfo());
        residentHeatMapRequest.setLabType(getStatisticalType().getCode());
        residentHeatMapRequest.setMonth(context.getRequest().getStatMonth());
        residentHeatMapRequest.setScale(context.getScale());
//        residentHeatMapRequest.setCurrentPage(currentPage);
//        residentHeatMapRequest.setPageSize(pageSize);
        return residentHeatMapRequest;
    }

    public Boolean processResidentHeatMapResponse(HotContext context, HeatMapResponse heatMapResponse) {
        if (!isValidHeatMapResponse(heatMapResponse)) {
            log.error("处理响应数据失败：响应数据为空或格式错误");
            return false;
        }
        //解析数据 保存在数据库
        HeatMapResponse.IndexData indexData = heatMapResponse.getData().getQuery().getIndexData();
        List<GridHotData> gridHotData = new ArrayList<>();
        JsonNode dataHot = indexData.getDataHot();
        if (!dataHot.isObject()) {
            log.error("请求热力数据类型转换发生错误 indexData:{}", indexData);
            return false;
        }
        Iterator<Map.Entry<String, JsonNode>> map = dataHot.fields();
        map.forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            try {
                String[] index = key.split(",");
                if (index.length >= 2) {
                    GridHotData data = createGridHotData(context, index[0], index[1], value.asText(), -1);
                    gridHotData.add(data);
                }
            } catch (Exception e) {
                log.error("处理热力数据失败: key={}, value={}, error={}",
                        key, value, e.getMessage());
            }
        });

        if (CollectionUtils.isNotEmpty(gridHotData)) {
            int batchSize = 5000;
            int total = gridHotData.size();
            for (int i = 0; i < total; i += batchSize) {
                int end = Math.min(i + batchSize, total);
                List<GridHotData> batchList = gridHotData.subList(i, end);
                gridHotDataMapper.batchInsertIgnore(batchList);
            }
        }

        //判断是否还有下一页
        //return indexData.getDataSize() == pageSize;
        return false;
    }

    protected boolean isValidHeatMapResponse(HeatMapResponse heatMapResponse) {
        return heatMapResponse != null
                && heatMapResponse.getCode() == 0
                && heatMapResponse.getData() != null
                && heatMapResponse.getData().getQuery() != null
                && heatMapResponse.getData().getQuery().getIndexData() != null;
    }

    protected GridHotData createGridHotData(HotContext context, String longitude, String latitude, String hotValue, Integer timeIndex) {
        GridHotData gridHotData = new GridHotData();
        gridHotData.setStatMonth(context.getRequest().getStatMonth());
        gridHotData.setTypeInfo(context.getTypeInfo());
        gridHotData.setType(context.getType());
        gridHotData.setDataType(context.getDataType());
        gridHotData.setDataId(context.getDataId());
        gridHotData.setStatisticalType(getStatisticalType().name());
        gridHotData.setLongitude(longitude);
        gridHotData.setLatitude(latitude);
        gridHotData.setHotValue(hotValue);
        gridHotData.setDataSize(context.getScale());
        gridHotData.setTimeType(context.getTimeType());
        gridHotData.setTimeIndex(timeIndex);
        gridHotData.setExtraAttrs(longitude + "," + latitude);
        gridHotData.setCreatedByUser(context.getUserKey());
        gridHotData.setUpdatedByUser(context.getUserKey());
        gridHotData.setCreator(appName);
        gridHotData.setUpdater(appName);
        return gridHotData;
    }


    @Override
    public void dimensionUvHandle(HotContext context) {
        ThroughClearlyRequest throughClearlyRequest = buildThroughClearlyRequest(context);
        SendMessagesContext messagesContext = SendMessagesContext.builder().userId(context.getUserKey()).locType(getStatisticalType().name()).build();
        ThroughClearlyResponse response = customizationMapClient.residentThroughClearly(throughClearlyRequest, messagesContext);
        isValidThroughClearlyResponse(response);
        Integer uv = response.getData().getQuery().getUv();

        createGridStatisticsDimension(context, uv);
    }


    protected ThroughClearlyRequest buildThroughClearlyRequest(HotContext context) {
        HotAdCodeRequest request = context.getRequest();
        ThroughClearlyRequest clearlyRequest = new ThroughClearlyRequest();
        clearlyRequest.setTypeInfo(context.getTypeInfo());
        clearlyRequest.setTgi(false);
        clearlyRequest.setMonth(request.getStatMonth());
        clearlyRequest.setType(context.getType());
        clearlyRequest.setProfile("uv");
        clearlyRequest.setLabType(getStatisticalType().getCode());
        return clearlyRequest;
    }


    protected void isValidThroughClearlyResponse(ThroughClearlyResponse throughClearlyResponse) {
        boolean isValid = throughClearlyResponse != null
                && throughClearlyResponse.getCode() == 0
                && throughClearlyResponse.getData() != null
                && throughClearlyResponse.getData().getQuery() != null;
        if (!isValid) {
            throw new MapDataException();
        }
    }

    protected void createGridStatisticsDimension(HotContext context, Integer uv) {
        HotAdCodeRequest request = context.getRequest();

        StatisticsDimension dimension = new StatisticsDimension();
        dimension.setStatMonth(request.getStatMonth());
        dimension.setRegionCode(context.getTypeInfo());
        dimension.setStatisticalType(getStatisticalType().name());
        dimension.setDimensionName("uv");
        dimension.setDimensionValue(uv.toString());
        dimension.setDataType(context.getDataType());
        dimension.setDataId(context.getDataId());
        dimension.setCreatedByUser(context.getUserKey());
        dimension.setUpdatedByUser(context.getUserKey());
        dimension.setCreator(appName);
        dimension.setUpdater(appName);

        statisticsDimensionMapper.insertUpdateEntity(dimension);

    }


}
