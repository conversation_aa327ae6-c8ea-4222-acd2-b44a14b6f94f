package heran.media.management.platform.mediaorder.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.management.platform.effectestimate.error.HeadMapDataException;
import heran.media.management.platform.mediaorder.sudomain.bo.XlsAllCollectData;
import heran.media.management.platform.mediaorder.sudomain.dto.ResultMatchDetailErrorData;
import heran.media.sharelib.domain.db.mapper.main.OrderEffectEstimateBaseManLandMapper;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseManLand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
public class EasyExcelAllCollectDataListener extends AnalysisEventListener<XlsAllCollectData> {

    private static final Pattern NUMBER_PATTERN = Pattern.compile("^-?\\d+(\\.\\d+)?$");


    private final Integer orderId;
    private final String bizType;
    private final String appName;
    private final Integer userKey;

    private final OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper;

    private final List<OrderEffectEstimateBaseManLand> insertDataList = new ArrayList<>();

    /**
     * 错误信息
     */
    private final List<String> errorMsgList = new ArrayList<>();
    private final List<ResultMatchDetailErrorData> errorResultDataList = new ArrayList<>();

    public EasyExcelAllCollectDataListener(Integer orderId, String bizType, String appName, Integer userKey, OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper) {
        this.orderId = orderId;
        this.bizType = bizType;
        this.appName = appName;
        this.userKey = userKey;
        this.orderEffectEstimateBaseManLandMapper = orderEffectEstimateBaseManLandMapper;
    }

    @Override
    public void invoke(XlsAllCollectData xlsAllCollectData, AnalysisContext context) {
        if (CollectionUtils.isEmpty(errorResultDataList) && CollectionUtils.isEmpty(insertDataList)) {
            OrderEffectEstimateBaseManLand validate = validate(xlsAllCollectData, context.readSheetHolder().getRowIndex());
            if (validate != null) {
                insertDataList.add(validate);
            }
        }
    }

    public OrderEffectEstimateBaseManLand validate(XlsAllCollectData xlsAllCollectData, Integer rowIndex) {
        OrderEffectEstimateBaseManLand baseManLand = xlsAllCollectData.advPo();
        if (!isValidNumber(xlsAllCollectData.getPlacementCost())) {
            errorMsgList.add("投放费用 输入的值不合法");
        }
        if (!isValidNumber(xlsAllCollectData.getPv())) {
            errorMsgList.add("曝光人次PV 输入的值不合法");
        }
        if (!isValidNumber(xlsAllCollectData.getUv())) {
            errorMsgList.add("曝光人数UV 输入的值不合法");
        }
        if (!isValidNumber(xlsAllCollectData.getAf())) {
            errorMsgList.add("平均触达频次AF 输入的值不合法");
        }
        if (!isValidNumber(xlsAllCollectData.getTaUv())) {
            errorMsgList.add("曝光潜客人数TAUA 输入的值不合法");
        }
        if (!isValidNumber(xlsAllCollectData.getCpm())) {
            errorMsgList.add("千人成本CPM 输入的值不合法");
        }
        if (!isValidNumber(xlsAllCollectData.getCpUv())) {
            errorMsgList.add("单人成本CPUV 输入的值不合法");
        }
        if (!isValidNumber(xlsAllCollectData.getCpTa())) {
            errorMsgList.add("单TA成本CPTA 输入的值不合法");
        }
        String ta = convertPercentToDecimal(xlsAllCollectData.getTa(), "曝光潜客浓度TA%");
        if (ta != null) {
            baseManLand.setTa(ta);
        }
        String reach = convertPercentToDecimal(xlsAllCollectData.getReach(), "城市渗透率Reach");
        if (reach != null) {
            baseManLand.setReach(reach);
        }

        if (!errorMsgList.isEmpty()) {
            ResultMatchDetailErrorData errorData = new ResultMatchDetailErrorData(rowIndex, errorMsgList);
            errorMsgList.clear();
            errorResultDataList.add(errorData);
            return null;
        }
        baseManLand.setMediumId("-1");
        baseManLand.setRegionCode("-1");
        baseManLand.setOrderId(orderId);
        baseManLand.setBizType(bizType);
        baseManLand.setCreator(appName);
        baseManLand.setUpdater(appName);
        baseManLand.setCreatedByUser(userKey);
        baseManLand.setUpdatedByUser(userKey);
        return baseManLand;
    }

    private String convertPercentToDecimal(String percentStr, String fieldName) {
        if (StringUtils.isEmpty(percentStr)) {
            errorMsgList.add(fieldName + " 输入的值不合法");
            return null;
        }
        String number = percentStr.replace("%", "");
        if (!NUMBER_PATTERN.matcher(number).matches()) {
            errorMsgList.add(fieldName + " 输入的值不合法");
            return null;
        }
        return new BigDecimal(number).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();
    }


    private boolean isValidNumber(String value) {
        return !StringUtils.isEmpty(value) && NUMBER_PATTERN.matcher(value).matches();
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        //最后一次不满足 限制的数量
        if (!insertDataList.isEmpty()) {
            orderEffectEstimateBaseManLandMapper.batchInsertIgnore(insertDataList);
            //清除掉所有的内存数据
            insertDataList.clear();
        }
        //判断错误数据是否为null 不为null 下载错误信息表格
        if (!errorResultDataList.isEmpty()) {
            XLSWriter.export(errorResultDataList, ResultMatchDetailErrorData.class, "导入失败数据", Objects.requireNonNull(((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getResponse()));
        }
    }


    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        //获取 接受的表头数据
        Field[] fields = XlsAllCollectData.class.getDeclaredFields();
        //如果不等于 说明表头与要求的表头不一致
        if (headMap.size() != fields.length) {
            //返回错误信息
            throw new HeadMapDataException();
        }
        for (Field field : fields) {
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (!headMap.get(annotation.index()).equals(annotation.value()[0])) {
                //列对应不上
                throw new HeadMapDataException();
            }
        }
    }
}
