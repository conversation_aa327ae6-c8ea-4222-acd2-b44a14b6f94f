package heran.media.management.platform.mediaorder.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.common.utils.ExceptionMessageUtils;
import heran.media.management.platform.common.utils.xls.XLSReader;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.management.platform.mediaorder.sudomain.bo.XlsMediaOrderMatchDetail;
import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderEntityQueryMapper;
import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderMatchDetailEntityQueryMapper;
import heran.media.management.platform.mediaorder.sudomain.dto.*;
import heran.media.management.platform.mediaorder.sudomain.request.SelectListLevelRequest;
import heran.media.management.platform.mediaorder.sudomain.response.IntentionSceneResponse;
import heran.media.sharelib.common.ObjectErrorInfo;
import heran.media.sharelib.domain.db.mapper.main.*;
import heran.media.sharelib.domain.db.model.main.MediaOrder;
import heran.media.sharelib.domain.db.model.main.MediaOrderMatchDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static heran.media.management.platform.common.utils.xls.XLSWriter.getExportFileName;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class MediaOrderMatchDetailService extends BaseCallServiceImpl {
    private static int EXPORT_START_PAGE = 1;
    private static int EXPORT_PAGE_LIMIT = 5000;
    private static int EXPORT_LIMIT_LOOP_COUNT = 20;

    private static final String APPRAISAL = "APPRAISAL";

    @Resource
    private MediaOrderMatchDetailEntityQueryMapper mediaOrderMatchDetailEntityQueryMapper;
    @Resource
    private MediaOrderMatchDetailMapper mediaOrderMatchDetailMapper;
    @Resource
    private MediaOrderMapper mediaOrderMapper;
    @Resource
    private MediaOrderEntityQueryMapper mediaOrderEntityQueryMapper;
    @Resource
    private MainCalendarOrderKpiMapper mainCalendarOrderKpiMapper;
    @Resource
    private OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper;
    @Resource
    private OrderEffectEstimateBaseSampleMapper orderEffectEstimateBaseSampleMapper;
    @Resource
    private OrderEffectEstimateBaseSampleResultMapper orderEffectEstimateBaseSampleResultMapper;
    @Resource
    private OrderEffectBusReportCollectResultMapper orderEffectBusReportCollectResultMapper;
    @Resource
    private OrderEffectBusProgrammeLineMapper orderEffectBusProgrammeLineMapper;
    @Resource
    private OrderEffectBaseSampleBusCbdMapper orderEffectBaseSampleBusCbdMapper;
    @Resource
    private MediaOrderReferSelectionPlanMapper mediaOrderReferSelectionPlanMapper;

    @Value("${path.upload}")
    private String userFilePath;

    @Value("${path.export}")
    private String tempDataPath;


    public PageResponse<TMediaOrderMatchDetail> list(SearchCriteria criteria) {
        //此分页为entity分页，如果有其他查询自行在前面增加不要导致以下分页失效
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<MediaOrderMatchDetailList> searchResult = mediaOrderMatchDetailEntityQueryMapper.list(criteria);
        PageResponse<TMediaOrderMatchDetail> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        //pageResponse.setResults(searchResult.stream().map(TMediaOrderMatchDetail::new).collect(Collectors.toList()));
        return pageResponse;
    }

    /**
     * 媒体匹配结果列表查询
     *
     * @param criteria 查询条件
     * @return PageResponse<MediaOrderMatchDetailListData>
     */
    public PageResponse<MediaOrderMatchDetailListData> selectList(SearchCriteria criteria) {
        //此分页为entity分页，如果有其他查询自行在前面增加不要导致以下分页失效
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<MediaOrderMatchDetailListData> searchResult = mediaOrderMatchDetailEntityQueryMapper.selectList(criteria);
        PageResponse<MediaOrderMatchDetailListData> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult);
        return pageResponse;
    }

    public TMediaOrderMatchDetail getById(Integer id) {
        TMediaOrderMatchDetail tmediaOrderMatchDetail = null;
        MediaOrderMatchDetail mediaOrderMatchDetail = mediaOrderMatchDetailMapper.getByIdEX(id);
        if (mediaOrderMatchDetail != null) {
            tmediaOrderMatchDetail = new TMediaOrderMatchDetail(mediaOrderMatchDetail);
        }
        return tmediaOrderMatchDetail;
    }

    public File exportData(SearchCriteria criteria, boolean exportAll) throws IOException, IllegalAccessException {
        if (exportAll) {
            criteria.setPage(EXPORT_START_PAGE);
            criteria.setLimit(EXPORT_PAGE_LIMIT);
        }
        int startPage = criteria.getPage();
        int pageLimit = criteria.getLimit();

        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        verifySearchCri(criteria, MediaOrderMatchDetail.class);
        String fileName = getExportFileName(tempDataPath, "MediaOrderMatchDetailData");
        File newFile = new File(fileName);

        PageHelper.startPage(startPage, pageLimit, false);
        List<MediaOrderMatchDetailList> queryResult = mediaOrderMatchDetailEntityQueryMapper.list(criteria);
        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), XlsMediaOrderMatchDetail.class);
        while (!queryResult.isEmpty()) {
            List<XlsMediaOrderMatchDetail> appCallHistories = queryResult.stream().map(XlsMediaOrderMatchDetail::new).collect(Collectors.toList());
            workbook = XLSWriter.appendLineFromObject("sheet1", appCallHistories, XlsMediaOrderMatchDetail.class, workbook);
            if (exportAll) {
                if (startPage * pageLimit >= EXPORT_LIMIT_LOOP_COUNT * EXPORT_PAGE_LIMIT) {
                    log.warn("导出数据超过可导出限制（不符合实时导出场景），如需变更自行更改");
                    break;
                }
                startPage++;
                PageHelper.startPage(startPage, pageLimit, false);
                queryResult = mediaOrderMatchDetailEntityQueryMapper.list(criteria);
                continue;
            }
            break;
        }
        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }

    public List<File> importData(Integer userKey, MultipartFile[] files) throws Exception {
        List<File> fileList = new ArrayList<>(files.length);
        for (MultipartFile importedFile : files) {
            String postfix = "";
            if (importedFile.getOriginalFilename().toUpperCase().endsWith("XLSX")) {
                postfix = ".xlsx";
            } else {
                postfix = ".xls";
            }

            File folder = new File(userFilePath);
            if (!folder.exists()) {
                folder.mkdirs();
            }
            File uploadedFile = new File(folder.getAbsolutePath() + "/" + UUID.randomUUID() + postfix);
            importedFile.transferTo(uploadedFile);
            List<XlsMediaOrderMatchDetail> xlsMediaOrderMatchDetails = XLSReader.readLineAsObject(uploadedFile, "sheet1", 1,
                    XlsMediaOrderMatchDetail.class);
            if (xlsMediaOrderMatchDetails.size() == 0) {
                continue;
            }

            List<MediaOrderMatchDetail> xlsDatas =
                    xlsMediaOrderMatchDetails.stream().map(p -> p.adapToPO(userKey)).collect(Collectors.toList());
            Integer failCount = 0, sucCount = 0;
            List<ObjectErrorInfo> failList = new ArrayList<>();
            for (int i = 0; i < xlsDatas.size(); i++) {
                MediaOrderMatchDetail mediaOrderMatchDetail = xlsDatas.get(i);
                try {
                    setCreatorInfo(userKey, mediaOrderMatchDetail);
                    setUpdaterInfo(userKey, mediaOrderMatchDetail);
                    mediaOrderMatchDetailMapper.insertUpdateEntity(mediaOrderMatchDetail);
                    sucCount++;
                } catch (Exception e) {
                    log.error("Error for user import with data=【{}】 and exception", new Gson().toJson(mediaOrderMatchDetail), e);
                    failCount++;
                    failList.add(
                            new ObjectErrorInfo(xlsMediaOrderMatchDetails.get(i), ExceptionMessageUtils.getExceptionErrorMessage(e)));
                }
            }
            if (!failList.isEmpty()) {
                fileList.add(XLSWriter.writeFailExcel(uploadedFile,
                        new File(folder.getAbsolutePath() + "/" + UUID.randomUUID() + postfix), failList));
            }
            log.info("Success count={}, Fail count={}", sucCount, failCount);
        }
        return fileList;
    }

    public void create(Integer userKey, MediaOrderMatchDetail mediaOrderMatchDetail) {
        mediaOrderMatchDetail.setId(null);
        setCreatorInfo(userKey, mediaOrderMatchDetail);
        setUpdaterInfo(userKey, mediaOrderMatchDetail);
        mediaOrderMatchDetailMapper.insert(mediaOrderMatchDetail);
    }


    public void modify(Integer userKey, TChangebleMediaOrderMatchDetail tmediaOrderMatchDetail) {
        MediaOrderMatchDetail mediaOrderMatchDetail = tmediaOrderMatchDetail.adapToPO();
        mediaOrderMatchDetail.setUpdatedByUser((Integer) userKey);
        setUpdaterInfo(userKey, mediaOrderMatchDetail);
        mediaOrderMatchDetailMapper.updateByEntity(mediaOrderMatchDetail);
    }


    public void delete(Integer userKey, int id) {
        mediaOrderMatchDetailMapper.deleteByIdEX(id);
    }

    /**
     * 批量增加
     *
     * @param insertDataList 增加的数据
     */
    public void insertBatch(List<MediaOrderMatchDetail> insertDataList) {
        mediaOrderMatchDetailEntityQueryMapper.insertBatch(insertDataList);
    }

    /**
     * 展开数据
     *
     * @param criteria 查询数据
     * @return PageResponse<SelectListLevelData>
     */
    public List<SelectListLevelData> selectListLevel(SelectListLevelRequest criteria) {
        return mediaOrderMatchDetailEntityQueryMapper.selectListLevelData(criteria);
    }

    /**
     * 返回订单的三级分类id 跟 城市id
     *
     * @param orderId 订单id
     * @return TOrderAndBasePlan
     */
    public IntentionSceneResponse getIntentionSceneAndCoreSalesRegion(Integer orderId) {
        MediaOrder mediaOrder = mediaOrderMapper.getByIdFilterIsDeleted(orderId);
        if (mediaOrder == null) {
            return null;
        }
        TOrderAndBasePlan region = mediaOrderEntityQueryMapper.getIntentionSceneAndCoreSalesRegion(orderId);
        IntentionSceneResponse response = new IntentionSceneResponse();
        Gson gson = new Gson();
        Type listType = new TypeToken<List<String>>() {
        }.getType();
        List<String> regionList = gson.fromJson(region.getCoreSalesRegion(), listType);
        List<String> sceneList = gson.fromJson(region.getIntentionScene(), listType);
        response.setCoreSalesRegion(regionList);
        response.setIntentionScene(sceneList);
        return response;
    }

    public void deleteLand(Integer orderId) {
        //删除指标的汇总数据
        orderEffectEstimateBaseManLandMapper.deleteByOrderId(orderId, APPRAISAL);
        //删除曝光数据的汇总数据
        orderEffectEstimateBaseSampleResultMapper.deleteByOrderId(orderId);
        //删除公交的汇总数据
        orderEffectBusReportCollectResultMapper.deleteByOrderId(orderId);
        //删除途径行政区线路数据
        orderEffectBusProgrammeLineMapper.deleteByOrderId(orderId);
    }

    public void deleteManLand(Integer orderId) {
        //删除kpi汇总数据
        orderEffectEstimateBaseManLandMapper.deleteAllByOrderId(orderId);
        //删除曝光人群洞察汇总数据
        orderEffectEstimateBaseSampleResultMapper.deleteByOrderId(orderId);
        //删除公交的汇总数据
        orderEffectBusReportCollectResultMapper.deleteByOrderId(orderId);
        //删除途径行政区线路数据
        orderEffectBusProgrammeLineMapper.deleteByOrderId(orderId);
        //删除热门商区路线数量
        orderEffectBaseSampleBusCbdMapper.deleteByOrderId(orderId);
        //删除行政区线路
        orderEffectBusProgrammeLineMapper.deleteByOrderId(orderId);
    }

}
