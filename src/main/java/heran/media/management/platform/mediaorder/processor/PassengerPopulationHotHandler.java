package heran.media.management.platform.mediaorder.processor;

import heran.media.management.platform.mediaorder.sudomain.request.HotAdCodeRequest;
import heran.media.sharelib.client.CustomizationMapClient;
import heran.media.sharelib.domain.bo.LocType;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.bo.TimeType;
import heran.media.sharelib.domain.db.mapper.main.GridHotDataMapper;
import heran.media.sharelib.domain.dto.amap.request.MonthlyFlowHeatMapRequest;
import heran.media.sharelib.domain.dto.amap.request.PassengerFlowThroughClearlyRequest;
import heran.media.sharelib.domain.dto.amap.request.SendMessagesContext;
import heran.media.sharelib.domain.dto.amap.request.ThroughClearlyRequest;
import heran.media.sharelib.domain.dto.amap.response.HeatMapResponse;
import heran.media.sharelib.domain.dto.amap.response.ThroughClearlyResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 客流人口处理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PassengerPopulationHotHandler extends AbstractHotAdCodeProcessor {

    @Resource
    private GridHotDataMapper gridHotDataMapper;
    @Resource
    private CustomizationMapClient customizationMapClient;

    @Override
    public void handle(HotContext context) {
        HotAdCodeRequest request = context.getRequest();
        Integer hotDataCount = gridHotDataMapper.getGridHotDataTimeTypeCount(request.getStatMonth(),
                context.getTypeInfo(),
                context.getType(),
                getStatisticalType().name()
                , TimeType.DAY.getCode());
        if (hotDataCount > 0) {
            return;
        }
//        int currentPage = 1;
//        boolean hasMoreData = true;
//        while (hasMoreData) {
//            try {
//                MonthlyFlowHeatMapRequest mapRequest = createResidentHeatMapRequest(context, currentPage, pageSize, TimeType.DAY.getCode());
//                HeatMapResponse heatMapResponse = customizationMapClient.monthlyFlowHeatMap(mapRequest);
//                hasMoreData = processResidentHeatMapResponse(context, heatMapResponse);
//                //页码递增
//                currentPage++;
//            } catch (Exception e) {
//                log.error("处理第{}页热力图数据PassengerPopulationHandler失败: error={}", currentPage, e.getMessage(), e);
//                break;
//            }
//        }
        try {
            MonthlyFlowHeatMapRequest mapRequest = createResidentHeatMapRequest(context, null, null, TimeType.DAY.getCode());
            SendMessagesContext messagesContext = SendMessagesContext.builder().userId(context.getUserKey()).locType(getStatisticalType().name()).build();
            HeatMapResponse heatMapResponse = customizationMapClient.monthlyFlowHeatMap(mapRequest,messagesContext);
            processResidentHeatMapResponse(context, heatMapResponse);
        } catch (Exception e) {
            log.error("处理热力图数据PassengerPopulationHandler失败: error={}", e.getMessage(), e);
        }
    }



    protected MonthlyFlowHeatMapRequest createResidentHeatMapRequest(HotContext context, Integer currentPage, Integer pageSize, String timeType) {
        MonthlyFlowHeatMapRequest monthlyFlowHeatMapRequest = new MonthlyFlowHeatMapRequest();
        monthlyFlowHeatMapRequest.setType(context.getType());
        monthlyFlowHeatMapRequest.setTypeInfo(context.getTypeInfo());
        monthlyFlowHeatMapRequest.setLocType(LocType.ALL_DAY.getCode());
        monthlyFlowHeatMapRequest.setMonth(context.getRequest().getStatMonth());
        monthlyFlowHeatMapRequest.setScale(context.getScale());
        //monthlyFlowHeatMapRequest.setCurrentPage(currentPage);
        // monthlyFlowHeatMapRequest.setPageSize(pageSize);
        monthlyFlowHeatMapRequest.setTimeType(timeType);
        return monthlyFlowHeatMapRequest;
    }


    @Override
    public void dimensionUvHandle(HotContext context) {
        PassengerFlowThroughClearlyRequest passengerFlowThroughClearlyRequest = buildPassengerFlowThroughClearlyRequest(context);

        SendMessagesContext messagesContext = SendMessagesContext.builder().userId(context.getUserKey()).locType(getStatisticalType().name()).build();
        ThroughClearlyResponse response = customizationMapClient.passengerFlowThroughClearly(passengerFlowThroughClearlyRequest, messagesContext);

        isValidThroughClearlyResponse(response);

        Integer uv = response.getData().getQuery().getUv();
        createGridStatisticsDimension(context, uv);
    }

    protected PassengerFlowThroughClearlyRequest buildPassengerFlowThroughClearlyRequest(HotContext context){

        HotAdCodeRequest request = context.getRequest();

        PassengerFlowThroughClearlyRequest clearlyRequest = new PassengerFlowThroughClearlyRequest();
        clearlyRequest.setType(context.getType());
        clearlyRequest.setMonth(request.getStatMonth());
        clearlyRequest.setTypeInfo(context.getTypeInfo());
        clearlyRequest.setProfile("uv");
        clearlyRequest.setTimeType(context.getTimeType());
        clearlyRequest.setLocType(LocType.ALL_DAY.getCode());

        return clearlyRequest;

    }




    @Override
    public StatisticalType getStatisticalType() {
        return StatisticalType.PASSENGER_POPULATION;
    }

}
