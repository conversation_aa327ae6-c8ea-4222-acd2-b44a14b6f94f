package heran.media.management.platform.mediaorder.processor;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LockCalculateDataProcessor {

    /**
     * 处理锁定数据
     *
     * @param context 上下文
     */
    void execute(LockDataContext context);

    /**
     * 校验数据
     *
     * @param context 上下文
     * @param planIds 方案id
     */
    void validate(LockDataContext context, List<Integer> planIds);

    /**
     * 类型
     *
     * @return 类型
     */
    String getType();

}
