package heran.media.management.platform.mediaorder.sudomain.response;

import heran.media.sharelib.domain.db.mapper.main.MediaOrderMapper;
import heran.media.sharelib.domain.db.model.main.MediaOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GetCalculateRatioResponse {
    @ApiModelProperty("计算重叠比例状态")
    private Boolean calculateRatio;
    @ApiModelProperty("计算重叠比例系数")
    private Double ratioCoefficient;

    public GetCalculateRatioResponse(MediaOrder mediaOrder){
        this.calculateRatio = mediaOrder.getCalculateRatio();
        this.ratioCoefficient = mediaOrder.getRatioCoefficient();
    }
}
