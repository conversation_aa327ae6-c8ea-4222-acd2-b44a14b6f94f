package heran.media.management.platform.mediaorder.sudomain.dto;

import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseManLand;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MediumFormatCollectListData {
    private String categoryId;
    private String cityCode;
    private String regionName;
    private Integer deliveryDays;
    private BigDecimal placementCost;
    private Integer vacancyPosition;
    private Double overlapRatio;
    private BigDecimal pv;
    private BigDecimal uv;
    private BigDecimal af;
    private BigDecimal taUv;
    private BigDecimal ta;
    private BigDecimal reach;
    private BigDecimal cpm;
    private BigDecimal cpUv;
    private BigDecimal cpTa;

    public MediumFormatCollectListData(OrderEffectEstimateBaseManLand data){
        this.categoryId = data.getMediumId();
        this.cityCode = data.getRegionCode();
        this.regionName = data.getRegionCode();
        this.deliveryDays = Integer.parseInt(data.getMarketTime());
        this.placementCost = new BigDecimal(data.getPlacementCost());
        this.vacancyPosition = Integer.parseInt(data.getMarketPlacementCount());
        this.pv = new BigDecimal(data.getPv());
        this.uv = new BigDecimal(data.getUv());
        this.af = new BigDecimal(data.getAf());
        this.taUv = new BigDecimal(data.getImpTaUv());
        this.ta = new BigDecimal(data.getTa());
        this.reach = new BigDecimal(data.getReach());
        this.cpm = new BigDecimal(data.getCpm());
        this.cpUv = new BigDecimal(data.getCpuv());
        this.cpTa = new BigDecimal(data.getCpta());

    }
}
