package heran.media.management.platform.mediaorder.processor;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class LockCalculateDataProcessorFactory {
    private static final Map<String, LockCalculateDataProcessor> LOCK_PROCESSOR_MAP = new HashMap<>();

    public LockCalculateDataProcessorFactory(LockCalculateDataProcessor... processors) {
        for (LockCalculateDataProcessor lockCalculateDataProcessor : processors) {
            LOCK_PROCESSOR_MAP.put(lockCalculateDataProcessor.getType(), lockCalculateDataProcessor);
        }
    }

    public LockCalculateDataProcessor getProcessor(String type) {
        return LOCK_PROCESSOR_MAP.get(type);
    }

    public void execute(String type, LockDataContext lockDataContext) {
        this.getProcessor(type).execute(lockDataContext);
    }
}
