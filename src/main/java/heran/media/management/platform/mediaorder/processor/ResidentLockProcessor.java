package heran.media.management.platform.mediaorder.processor;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.mediaorder.sudomain.request.LockDataRequest;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.bo.TaStatisticalType;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import heran.media.sharelib.domain.dto.hot.MainCalendarPlacementData;
import heran.media.sharelib.utils.GridCircleCoverUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 常驻人口处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ResidentLockProcessor extends LockBaseProcessor {


    @Override
    public MainCalendarOrderKpi buildMainCalendarOrderKpi(LockDataContext context, MainCalendarPlacementData placement, Map<String, List<GridCircleCoverUtil.Grid>> hotMap) {
        String aoiCoordinates = placement.getAoiCoordinates();
        LockDataRequest request = context.getRequest();
        MainCalendarOrderKpi kpi = initializeMainCalendarOrderKpi(placement.getId(), request.getKpiType(), request.getStatMonth().get(0), request.getType(), context);
        if (StringUtils.isEmpty(aoiCoordinates)) {
            return kpi;
        }
        //处理曝光人群常驻接口
        kpi.setAoiResidentPopulation(setKpiValue(hotMap, StatisticalType.RESIDENT_POPULATION.name(), aoiCoordinates, placement.getAoiArea()));
        //处理AOI曝光工作人口
        kpi.setAoiWorkPopulation(setKpiValue(hotMap, StatisticalType.WORK_POPULATION.name(), aoiCoordinates, placement.getAoiArea()));
        //处理AOI曝光居住人口
        kpi.setAoiDwellPopulation(setKpiValue(hotMap, StatisticalType.DWELL_POPULATION.name(), aoiCoordinates, placement.getAoiArea()));
        //处理AOI曝光客流人口
        kpi.setAoiPassengerPopulation(setKpiValue(hotMap, StatisticalType.PASSENGER_POPULATION.name(), aoiCoordinates, placement.getAoiArea()));
        //处理AOI曝光客流小时级人口
        kpi.setAoiPassengerHourPopulation(setKpiValue(hotMap, StatisticalType.PASSENGER_HOUR_POPULATION.name(), aoiCoordinates, placement.getAoiArea()));
        //处理TA常驻热力人口
        kpi.setAoiTaCount(setKpiValue(hotMap, TaStatisticalType.TA_RESIDENT_POPULATION.name(), aoiCoordinates, placement.getAoiArea()));
        return kpi;
    }

    @Override
    public String getType() {
        return "RESIDENT";
    }
}
