package heran.media.management.platform.mediaorder.processor;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import heran.media.management.platform.mediaorder.sudomain.request.LockDataRequest;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.bo.TaStatisticalType;
import heran.media.sharelib.domain.db.mapper.main.MainCalendarOrderKpiMapper;
import heran.media.sharelib.domain.db.mapper.main.MainCalendarPlacementMapper;
import heran.media.sharelib.domain.db.model.main.MainCalendarOrderKpi;
import heran.media.sharelib.domain.dto.hot.MainCalendarPlacementData;
import heran.media.sharelib.utils.GridCircleCoverUtil;
import heran.media.sharelib.utils.date.DataTmeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 精确计算 常驻人口
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RefinedResidentLockProcessor extends LockBaseProcessor {

    private static final BigDecimal ZERO = BigDecimal.ZERO;

    @Resource
    private MainCalendarPlacementMapper mainCalendarPlacementMapper;
    @Resource
    private MainCalendarOrderKpiMapper mainCalendarOrderKpiMapper;

    @Override
    public String getType() {
        return "REFINED_RESIDENT";
    }

    @Override
    protected void placementData(LockDataContext context, List<Integer> selectionPlanId) {
        int pageNo = 1;
        List<MainCalendarPlacementData> placementList;
        do {
            PageHelper.startPage(pageNo, EXPORT_PAGE_LIMIT, false);
            placementList = mainCalendarPlacementMapper.getMainCalendarPlacementByPlanIds(context.getRequest().getCategoryId(), selectionPlanId);
            if (CollectionUtils.isEmpty(placementList)) {
                break;
            }
            //将点位的AOI数据与网格热力数据进行合并
            calendarOrderKpi(placementList, context);
            pageNo++;
        } while (!placementList.isEmpty());
    }

    protected void calendarOrderKpi(List<MainCalendarPlacementData> placementList, LockDataContext context) {
        List<MainCalendarOrderKpi> resultList = new ArrayList<>();
        Map<String, Map<String, List<GridCircleCoverUtil.Grid>>> hotMapCache = new HashMap<>(8);
        Map<String, Integer> taPopulationCache = new HashMap<>(8);
        for (MainCalendarPlacementData placement : placementList) {
            Map<String, Integer> indicators = calculatePopulationIndicators(
                    placement,
                    context,
                    Arrays.asList(
                            StatisticalType.RESIDENT_POPULATION.name(),
                            StatisticalType.WORK_POPULATION.name(),
                            StatisticalType.DWELL_POPULATION.name(),
                            StatisticalType.PASSENGER_POPULATION.name(),
                            StatisticalType.PASSENGER_HOUR_POPULATION.name(),
                            TaStatisticalType.TA_RESIDENT_POPULATION.name()
                    ),
                    hotMapCache,
                    taPopulationCache,
                    TaStatisticalType.TA_RESIDENT_POPULATION.name()
            );
            MainCalendarOrderKpi kpi = baseBuildMainCalendarOrderKpi(context, placement);
            kpi.setAoiResidentPopulation(indicators.get(StatisticalType.RESIDENT_POPULATION.name()));
            kpi.setAoiWorkPopulation(indicators.get(StatisticalType.WORK_POPULATION.name()));
            kpi.setAoiDwellPopulation(indicators.get(StatisticalType.DWELL_POPULATION.name()));
            kpi.setAoiPassengerPopulation(indicators.get(StatisticalType.PASSENGER_POPULATION.name()));
            kpi.setAoiPassengerHourPopulation(indicators.get(StatisticalType.PASSENGER_HOUR_POPULATION.name()));
            kpi.setAoiTaCount(indicators.get(TaStatisticalType.TA_RESIDENT_POPULATION.name()));
            kpi.setTaPopulaceCount(indicators.get(TaStatisticalType.TA_RESIDENT_POPULATION.name() + "_UV"));
            resultList.add(kpi);
        }
        if (CollectionUtils.isNotEmpty(resultList)) {
            mainCalendarOrderKpiMapper.batchInsertUpdate(resultList);
        }
    }


    protected BigDecimal calculatePopulationSum(
            Map<String, List<GridCircleCoverUtil.Grid>> hotMap,
            String aoiCoordinates,
            Double aoiArea,
            String type
    ) {
        GridCircleCoverUtil.GridData data = refinedSetKpiValue(aoiArea, hotMap, type, aoiCoordinates);
        if (data != null && data.getCellCount() > 0) {
            log.info("StatisticalType:{}覆盖的网格人口数：{}", type, data.getValue());
            return data.getValue();
        }
        return ZERO;
    }


    protected Map<String, List<GridCircleCoverUtil.Grid>> getHotMap(LockDataContext context, String regionCode, String statMonth) {
        Map<String, List<GridCircleCoverUtil.Grid>> listMap = buildGrid(statMonth, regionCode);
        Map<String, List<GridCircleCoverUtil.Grid>> taListMap = buildTaHotGrid(context, statMonth, regionCode);
        listMap.putAll(taListMap);
        return listMap;
    }

    protected GridCircleCoverUtil.GridData refinedSetKpiValue(Double aoiArea, Map<String, List<GridCircleCoverUtil.Grid>> hotMap, String statisticalType, String aoiCoordinates) {
        if (!hotMap.isEmpty() && hotMap.containsKey(statisticalType)) {
            List<GridCircleCoverUtil.Grid> gridList = hotMap.get(statisticalType);
            try {
                return GridCircleCoverUtil.areaCalculateCoveredValueCount(aoiArea, aoiCoordinates, gridList);
            } catch (Exception e) {
                log.error("AOI覆盖网格数据异常：{}", e.getMessage());
            }
        }
        return null;
    }


    @Override
    public MainCalendarOrderKpi buildMainCalendarOrderKpi(LockDataContext context, MainCalendarPlacementData placement, Map<String, List<GridCircleCoverUtil.Grid>> hotMap) {
        return null;
    }

    protected MainCalendarOrderKpi baseBuildMainCalendarOrderKpi(LockDataContext context, MainCalendarPlacementData placement) {
        LockDataRequest request = context.getRequest();
        Collections.sort(request.getStatMonth());
        return initializeMainCalendarOrderKpi(placement.getId(), request.getKpiType(), new Gson().toJson(request.getStatMonth()), request.getType(), context);
    }


    protected Map<String, Integer> calculatePopulationIndicators(
            MainCalendarPlacementData placement,
            LockDataContext context,
            List<String> requiredTypes,
            Map<String, Map<String, List<GridCircleCoverUtil.Grid>>> hotMapCache,
            Map<String, Integer> taPopulationCache,
            String taUvType
    ) {

        String taKey = taUvType + "_UV";
        Set<String> allTypes = new HashSet<>(requiredTypes);
        allTypes.add(taKey);

        Map<String, BigDecimal> totals = new HashMap<>(8);
        int totalDays = 0;
        // 初始化 totals
        for (String type : requiredTypes) {
            totals.put(type, BigDecimal.ZERO);
        }
        String aoi = placement.getAoiCoordinates();
        Double area = placement.getAoiArea();

        //因为是按照城市一个城市一个城市处理的 上一个城市处理完内存中的数据就可以释放了 减少内存使用
        hotMapCache.keySet().stream().findFirst().ifPresent(key -> {
            String[] keys = key.split("_");
            if (keys.length > 0 && !keys[0].equals(placement.getRegionCode())) {
                hotMapCache.clear();
                taPopulationCache.clear();
            }
        });

        for (String statMonth : context.getRequest().getStatMonth()) {
            int daysInMonth = DataTmeUtils.getDaysInMonth(statMonth);
            totalDays += daysInMonth;
            if (StringUtils.isNotEmpty(aoi)) {
                String cacheKey = placement.getRegionCode() + "_" + statMonth;
                Map<String, List<GridCircleCoverUtil.Grid>> hotMap =
                        hotMapCache.computeIfAbsent(cacheKey, m -> getHotMap(context, placement.getRegionCode(), statMonth));
                //处理TA城市人口taUvType
                Integer taPopulation = taPopulationCache.computeIfAbsent(cacheKey, k ->
                        getTaPopulation(context, placement.getRegionCode(), statMonth, taUvType)
                );
                if (taPopulation == null) {
                    taPopulation = 0;
                }
                totals.put(taKey, totals.getOrDefault(taKey, BigDecimal.ZERO).add(new BigDecimal(taPopulation).multiply(BigDecimal.valueOf(daysInMonth))));

                for (String type : requiredTypes) {
                    BigDecimal value = calculatePopulationSum(hotMap, aoi, area, type);
                    totals.put(type, totals.get(type).add(value.multiply(BigDecimal.valueOf(daysInMonth))));
                }

            }
        }
        // 平均值计算
        Map<String, Integer> result = new HashMap<>(8);
        for (String type : allTypes) {
            if (totalDays == 0) {
                result.put(type, 0);
            } else {
                result.put(type, totals.get(type).divide(BigDecimal.valueOf(totalDays), 2, RoundingMode.HALF_UP).intValue());
            }
        }
        return result;
    }


}
