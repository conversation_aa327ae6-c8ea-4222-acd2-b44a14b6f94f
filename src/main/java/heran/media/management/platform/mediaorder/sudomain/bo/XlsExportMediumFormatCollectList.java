package heran.media.management.platform.mediaorder.sudomain.bo;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.mediaorder.sudomain.dto.MediumFormatCollectListData;
import heran.media.sharelib.domain.db.model.main.MainResourceCategory;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class XlsExportMediumFormatCollectList {
    @XlsField(title = "媒介形式", columnIndex = 0)
    private String categoryName;
    @XlsField(title = "城市", columnIndex = 1)
    private String cityName;
    @XlsField(title = "投放周期 单位：天", columnIndex = 2)
    private Integer deliveryDays;
    @XlsField(title = "投放费用 单位：元", columnIndex = 3)
    private BigDecimal placementCost;
    @XlsField(title = "投放点位", columnIndex = 4)
    private Integer vacancyPosition;
    @XlsField(title = "曝光人次PV 单位：人次", columnIndex = 5)
    private BigDecimal pv;
    @XlsField(title = "曝光人数UV 单位：人", columnIndex = 6)
    private BigDecimal uv;
    @XlsField(title = "平均触达频次AF 单位：次", columnIndex = 7)
    private BigDecimal af;
    @XlsField(title = "曝光潜客人数TA UA 单位：人", columnIndex = 8)
    private BigDecimal taUv;
    @XlsField(title = "曝光潜客浓度TA%", columnIndex = 9)
    private BigDecimal ta;
    @XlsField(title = "城市渗透率Reach", columnIndex = 10)
    private BigDecimal reach;
    @XlsField(title = "千人成本CPM 单位：元", columnIndex = 11)
    private BigDecimal cpm;
    @XlsField(title = "单人成本CP UV 单位：元", columnIndex = 12)
    private BigDecimal cpUv;
    @XlsField(title = "单TA成本CP TA 单位：元", columnIndex = 13)
    private BigDecimal cpTa;

    public XlsExportMediumFormatCollectList(MediumFormatCollectListData data, Map<Integer, MainResourceCategory> categoryMap) {
        String categoryName = "汇总";
        MainResourceCategory categoryLv3 = categoryMap.get(Integer.valueOf(data.getCategoryId()));
        if (categoryLv3 != null){
            MainResourceCategory categoryLv2 = categoryMap.get(categoryLv3.getParentId());
            categoryName = categoryLv2.getCategoryName() + "-" + categoryLv3.getCategoryName();
        }
        this.categoryName = categoryName;
        this.cityName = StringUtils.isEmpty(data.getRegionName()) ? "汇总" : data.getRegionName();
        this.deliveryDays = data.getDeliveryDays();
        this.placementCost = data.getPlacementCost();
        this.vacancyPosition = data.getVacancyPosition();
        this.pv = data.getPv();
        this.uv = data.getUv();
        this.af = data.getAf();
        this.taUv = data.getTaUv();
        this.ta = data.getTa();
        this.reach = data.getReach();
        this.cpm = data.getCpm();
        this.cpUv = data.getCpUv();
        this.cpTa = data.getCpTa();
    }
}
