package heran.media.management.platform.mediaorder.processor;

import heran.media.sharelib.domain.bo.StatisticalType;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class HotAdCodeFactory {
    private static final Map<StatisticalType, HotAdCodeHandler> HOT_CODE_MAP = new HashMap<>();

    public HotAdCodeFactory(HotAdCodeHandler... hotAdCodeHandlers) {
        for (HotAdCodeHandler handler : hotAdCodeHandlers) {
            HOT_CODE_MAP.put(handler.getStatisticalType(), handler);
        }
    }

    public HotAdCodeHandler getProcessor(StatisticalType type) {
        return HOT_CODE_MAP.get(type);
    }

    public void execute(StatisticalType type, HotContext context) {
        this.getProcessor(type).handle(context);
    }

    public void executeUv(StatisticalType type, HotContext context) {
        this.getProcessor(type).dimensionUvHandle(context);
    }

}
