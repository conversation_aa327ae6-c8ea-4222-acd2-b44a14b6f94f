package heran.media.management.platform.mediaorder.resolver;

import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderEntityQueryMapper;
import heran.media.management.platform.mediaorder.sudomain.dto.OrderEstimateBaseLandData;
import heran.media.management.platform.mediaorder.sudomain.request.CreateOverviewDataRequest;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.db.mapper.main.OrderEffectEstimateBaseManLandMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
public class EffectAllCollectResolver extends EffectCollectResolver {

    @Resource
    private OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper;
    @Resource
    private MediaOrderEntityQueryMapper mediaOrderEntityQueryMapper;

    @Override
    public void resolver(CreateOverviewDataRequest request) {
        //加之前先删除一下之前的
        orderEffectEstimateBaseManLandMapper.deleteByType(request.getOrderId(), request.getBizType(), request.getDataType());
        OrderEstimateBaseLandData landData = mediaOrderEntityQueryMapper.selectOrderAllCollectData(request.getOrderId(), request.getBizType());
        if (landData == null) {
            return;
        }
        request.setCalculateRatio(false);
        //城市人口数
        String statisticalType = "RESIDENT".equals(landData.getPopulation()) ? StatisticalType.RESIDENT_POPULATION.name() : StatisticalType.PASSENGER_POPULATION.name();

        Map<String, Set<String>> cityUvSet = getCityUvSet(request);

        BigDecimal population = getPopulation(statisticalType, cityUvSet);

        collectInsertData(Collections.singletonList(landData), null, request, 1.0, population);
    }


    @Override
    String getType() {
        return "ALL_FORMAT";
    }
}
