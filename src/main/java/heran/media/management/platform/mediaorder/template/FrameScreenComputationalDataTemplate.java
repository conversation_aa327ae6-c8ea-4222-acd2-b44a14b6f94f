package heran.media.management.platform.mediaorder.template;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 框架/智慧屏/门禁/单元门禁/道闸/LCD
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FrameScreenComputationalDataTemplate extends LedScreenComputationalDataTemplate {

    @Override
    protected BigDecimal getUv(ComputationalDataContext kpiQueryContext, BigDecimal population, BigDecimal heaven, BigDecimal visibleAngle, String aoiCoordinates, String mileage) {
        return population.multiply(heaven);
    }

    @Override
    public String templateName() {
        return "FRAME_SCREEN";
    }
}
