package heran.media.management.platform.mediaorder.sudomain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class TMediaOrderRes {
    @ApiModelProperty("订单Id")
    private Long id;
    @ApiModelProperty("订单编号")
    private String orderSn;
    @ApiModelProperty("订单类型")
    private String orderType;
    @ApiModelProperty("订单名称")
    private String orderName;
    @ApiModelProperty("品牌名称")
    private String brandName;
    @ApiModelProperty("集团名称")
    private String groupName;
    @ApiModelProperty("投放起始时间")
    private Date deliveryStartTime;
    @ApiModelProperty("投放结束时间")
    private Date deliveryEndTime;
    @ApiModelProperty("订单状态")
    private String orderStatus;
    @ApiModelProperty("是否已上传排期")
    private Boolean isScheduling;
    @ApiModelProperty("是否已上传监测")
    private Boolean isUploadMonitoring;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("引用方案条数")
    private Integer planCount;
}
