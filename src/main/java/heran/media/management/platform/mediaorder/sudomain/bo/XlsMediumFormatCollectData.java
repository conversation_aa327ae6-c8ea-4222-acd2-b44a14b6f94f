package heran.media.management.platform.mediaorder.sudomain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseManLand;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class XlsMediumFormatCollectData {
    @XlsField(title = "媒介形式（如 高线城市-社区-电梯框架）", columnIndex = 0)
    @ExcelProperty(value = "媒介形式（如 高线城市-社区-电梯框架）", index = 0)
    private String categoryName;
    @XlsField(title = "省份 如汇总 填汇总", columnIndex = 1)
    @ExcelProperty(value = "省份 如汇总 填汇总", index = 1)
    private String province;
    @XlsField(title = "城市 如汇总 填汇总", columnIndex = 2)
    @ExcelProperty(value = "城市 如汇总 填汇总", index = 2)
    private String cityName;
    @XlsField(title = "投放周期 单位：天", columnIndex = 3)
    @ExcelProperty(value = "投放周期 单位：天", index = 3)
    private Integer deliveryDays;
    @XlsField(title = "投放费用 单位：元", columnIndex = 4)
    @ExcelProperty(value = "投放费用 单位：元", index = 4)
    private String placementCost;
    @XlsField(title = "投放点位", columnIndex = 5)
    @ExcelProperty(value = "投放点位", index = 5)
    private Integer vacancyPosition;
    @XlsField(title = "曝光人次PV 单位：人次", columnIndex = 6)
    @ExcelProperty(value = "曝光人次PV 单位：人次", index = 6)
    private String pv;
    @XlsField(title = "曝光人数UV 单位：人", columnIndex = 7)
    @ExcelProperty(value = "曝光人数UV 单位：人", index = 7)
    private String uv;
    @XlsField(title = "平均触达频次AF 单位：次", columnIndex = 8)
    @ExcelProperty(value = "平均触达频次AF 单位：次", index = 8)
    private String af;
    @XlsField(title = "曝光潜客人数TA UA 单位：人", columnIndex = 9)
    @ExcelProperty(value = "曝光潜客人数TA UA 单位：人", index = 9)
    private String taUv;
    @XlsField(title = "曝光潜客浓度TA%", columnIndex = 10)
    @ExcelProperty(value = "曝光潜客浓度TA%", index = 10)
    private String ta;
    @XlsField(title = "城市渗透率Reach", columnIndex = 11)
    @ExcelProperty(value = "城市渗透率Reach", index = 11)
    private String reach;
    @XlsField(title = "千人成本CPM 单位：元", columnIndex = 12)
    @ExcelProperty(value = "千人成本CPM 单位：元", index = 12)
    private String cpm;
    @XlsField(title = "单人成本CP UV 单位：元", columnIndex = 13)
    @ExcelProperty(value = "单人成本CP UV 单位：元", index = 13)
    private String cpUv;
    @XlsField(title = "单TA成本CP TA 单位：元", columnIndex = 14)
    @ExcelProperty(value = "单TA成本CP TA 单位：元", index = 14)
    private String cpTa;


    public OrderEffectEstimateBaseManLand advPo() {
        OrderEffectEstimateBaseManLand baseManLand = new OrderEffectEstimateBaseManLand();
        baseManLand.setMarketTime(deliveryDays.toString());
        baseManLand.setPlacementCost(placementCost);
        baseManLand.setMarketPlacementCount(vacancyPosition.toString());
        baseManLand.setPv(pv);
        baseManLand.setUv(uv);
        baseManLand.setAf(af);
        baseManLand.setImpTaUv(taUv);
        baseManLand.setTa(ta);
        baseManLand.setReach(reach);
        baseManLand.setCpm(cpm);
        baseManLand.setCpuv(cpUv);
        baseManLand.setCpta(cpTa);
        baseManLand.setConfirmTheData(false);
        return baseManLand;
    }
}
