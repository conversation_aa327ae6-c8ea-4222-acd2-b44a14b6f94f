package heran.media.management.platform.mediaorder.sudomain.response;

import heran.media.management.platform.mediaorder.sudomain.dto.OrderSmartDetailsCategoryListData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderSmartDetailsCategoryListDataResponse {
    @ApiModelProperty("分类id")
    public Integer categoryId;
    @ApiModelProperty("城市条数")
    public Integer cityCount;
    @ApiModelProperty("投放开始时间")
    private Date deliveryStartTime;
    @ApiModelProperty("投放结束时间")
    private Date deliveryEndTime;
    @ApiModelProperty("投放周期（天）")
    private Integer periodDays;
    @ApiModelProperty("点位总量（个）")
    private Integer placementCount;
    @ApiModelProperty("投放费用（元）")
    private BigDecimal periodAmount;
    @ApiModelProperty("更新时间")
    private Date updateTime;

    public OrderSmartDetailsCategoryListDataResponse(OrderSmartDetailsCategoryListData data) {
        this.categoryId = data.getCategoryId();
        this.cityCount = data.getCityCount();
        this.deliveryStartTime = data.getDeliveryStartTime();
        this.deliveryEndTime = data.getDeliveryEndTime();
        this.periodDays = data.getPeriodDays();
        this.placementCount = data.getPlacementCount();
        this.periodAmount = data.getPeriodAmount();
        this.updateTime = data.getUpdateTime();
    }
}
