package heran.media.management.platform.mediaorder.sudomain.request;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderPlacementListRequest {
    @ApiModelProperty("分类id")
    private Integer categoryId;
    @ApiModelProperty("指标类型ORDER(订单)，APPRAISAL(全域效果评估)")
    private String dataType;
    @ApiModelProperty("参数构建 必须要有 categoryId，orderId")
    private SearchCriteria criteria;
}
