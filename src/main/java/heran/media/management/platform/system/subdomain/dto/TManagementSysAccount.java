package heran.media.management.platform.system.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.management.platform.main.subdomain.dto.AccountApiMapQuotaData;
import heran.media.sharelib.domain.db.model.ManagementSysAccount;
import heran.media.sharelib.domain.db.model.ManagementSysRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TManagementSysAccount {

    @ApiModelProperty(value = "主键自增id")
    private Integer id;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "附件id")
    private Long attachmentId;

    @ApiModelProperty(value = "登录密码")
    private String password;

    @ApiModelProperty(value = "手机号")
    private String cellphone;

    @ApiModelProperty(value = "微信unionid")
    private String unionid;

    @ApiModelProperty("有效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validityStartTime;

    @ApiModelProperty("有效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validityEndTime;

    @ApiModelProperty(value = "是否启用")
    private Boolean isEnabled;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDeleted;

    @ApiModelProperty(value = "是否拥有所有订单方案权限")
    private Boolean allOrderPermissions;

    @ApiModelProperty(value = "使用端")
    private String platform;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "行记录创建者")
    private String creator;

    @ApiModelProperty(value = "行记录更新者")
    private String updator;

    @ApiModelProperty(value = "行记录创建时间")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间")
    private Date updateTime;

    private List<Integer> roleList;

    @ApiModelProperty(value = "用户高德接口配额")
    private List<AccountApiMapQuotaData> accountApiMapQuotas;


    public AppUser adaptToAppUser() {
        ManagementSysAccountData managementSysAccount = new ManagementSysAccountData();
        managementSysAccount.setId(id);
        managementSysAccount.setAttachmentId(attachmentId);
        managementSysAccount.setUserName(userName);
        managementSysAccount.setNickName(nickName);
        managementSysAccount.setPassword(password);
        managementSysAccount.setCellphone(cellphone);
        managementSysAccount.setUnionid(unionid);
        managementSysAccount.setIsEnabled(isEnabled);
        managementSysAccount.setAllOrderPermissions(allOrderPermissions);
        managementSysAccount.setPlatform(platform);
        managementSysAccount.setRemark(remark);
        managementSysAccount.setCreator(creator);
        managementSysAccount.setUpdater(updator);
        managementSysAccount.setCreateTime(createTime);
        managementSysAccount.setUpdateTime(updateTime);
        managementSysAccount.setValidityStartTime(validityStartTime);
        managementSysAccount.setValidityEndTime(validityEndTime);

        List<ManagementSysRole> roleList = new LinkedList<>();
        if (this.roleList != null) {
            for (Integer roleId : this.roleList) {
                ManagementSysRole sysRole = new ManagementSysRole();
                sysRole.setId(roleId);
                roleList.add(sysRole);
            }
        }
        return new AppUser(managementSysAccount, roleList);
    }
}
