package heran.media.management.platform.system.service;

import heran.media.management.platform.system.subdomain.ds.AuthorityBizMapper;
import heran.media.management.platform.system.subdomain.ds.ManagementSysMenuEntityQueryMapper;
import heran.media.management.platform.system.subdomain.dto.TManagementSysMenu;
import heran.media.management.platform.system.subdomain.dto.TMenuResp;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.db.mapper.ManagementSysMenuMapper;
import heran.media.sharelib.domain.db.model.ManagementSysMenu;
import heran.media.sharelib.domain.db.model.ManagementSysRoleAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RefreshScope
public class ManagementSysMenuService {

    @Autowired
    private ManagementSysMenuMapper managementSysMenuMapper;

    @Autowired
    private ManagementSysMenuEntityQueryMapper managementSysMenuEntityQueryMapper;

    @Autowired
    private AuthorityBizMapper authorityBizMapper;

    public List<TManagementSysMenu> list(SearchCriteria criteria) {
        List<ManagementSysMenu> sysMenus = managementSysMenuEntityQueryMapper.list(criteria);
        List<TManagementSysMenu> menuNodes = sysMenus.stream()
                .map(TManagementSysMenu::new)
                .collect(Collectors.toList());

        // 构建菜单树结构
        Map<Integer, TManagementSysMenu> menuMap = new HashMap<>();
        for (TManagementSysMenu menu : menuNodes) {
            menuMap.put(menu.getId(), menu);
        }
        List<TManagementSysMenu> rootMenus = new LinkedList<>();
        for (TManagementSysMenu menu : menuNodes) {
            Integer parentId = menu.getParentId();
            if (parentId == null) {
                rootMenus.add(menu);
            } else {
                TManagementSysMenu parent = menuMap.get(parentId);
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new LinkedList<>());
                    }
                    parent.getChildren().add(menu);
                }
            }
        }

        return rootMenus;
    }

    public List<TManagementSysMenu> getVisibleMenus(AuthUserInfo currentUser) {
        List<ManagementSysRoleAuth> managementSysMenusDefined = authorityBizMapper.getManagementSysRoleMenuAuthByAccount(currentUser.getIdentifier());
        Set<String> menuIdSet = new HashSet<>();
        Boolean isAuthAll = false;
        for (ManagementSysRoleAuth roleAuth : managementSysMenusDefined) {
            String[] datas = roleAuth.getAuthedData().split("\\|\\|");
            if (datas.length > 0) {
                if (datas.length == 1) {
                    if (datas[0].equals("*")) {
                        isAuthAll = true;
                        break;
                    }
                }
                menuIdSet.addAll(Arrays.stream(datas).map(String::valueOf).collect(Collectors.toSet()));
            }
        }

        List<ManagementSysMenu> sysMenus = isAuthAll ? managementSysMenuMapper.getAll() :
                (menuIdSet.size() > 0 ? authorityBizMapper.getMenuByCodes(menuIdSet) : new LinkedList<>());
        // 构建菜单节点列表
        List<TManagementSysMenu> menuNodes = sysMenus.stream()
                .map(TManagementSysMenu::new)
                .collect(Collectors.toList());

        // 构建菜单树结构
        Map<Integer, TManagementSysMenu> menuMap = new HashMap<>();
        for (TManagementSysMenu menu : menuNodes) {
            menuMap.put(menu.getId(), menu);
        }
        List<TManagementSysMenu> rootMenus = new LinkedList<>();
        for (TManagementSysMenu menu : menuNodes) {
            Integer parentId = menu.getParentId();
            if (parentId == null) {
                rootMenus.add(menu);
            } else {
                TManagementSysMenu parent = menuMap.get(parentId);
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new LinkedList<>());
                    }
                    parent.getChildren().add(menu);
                }
            }
        }

        return rootMenus;
    }

    public List<ManagementSysMenu> listMenu(SearchCriteria criteria) {
        return managementSysMenuEntityQueryMapper.list(criteria);
    }

    public List<TManagementSysMenu> getAll(String platform) {
        List<ManagementSysMenu> sysMenus = managementSysMenuEntityQueryMapper.getAll(platform);
        // 构建菜单节点列表
        List<TManagementSysMenu> menuNodes = sysMenus.stream()
                .map(TManagementSysMenu::new)
                .collect(Collectors.toList());

        // 构建菜单树结构
        Map<Integer, TManagementSysMenu> menuMap = new HashMap<>();
        for (TManagementSysMenu menu : menuNodes) {
            menuMap.put(menu.getId(), menu);
        }
        List<TManagementSysMenu> rootMenus = new LinkedList<>();
        for (TManagementSysMenu menu : menuNodes) {
            Integer parentId = menu.getParentId();
            if (parentId == null) {
                rootMenus.add(menu);
            } else {
                TManagementSysMenu parent = menuMap.get(parentId);
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new LinkedList<>());
                    }
                    parent.getChildren().add(menu);
                }
            }
        }

        return rootMenus;
    }
}
