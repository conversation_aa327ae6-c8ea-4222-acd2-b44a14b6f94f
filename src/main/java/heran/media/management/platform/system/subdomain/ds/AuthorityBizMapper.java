package heran.media.management.platform.system.subdomain.ds;

import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.db.model.*;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Set;

@Mapper
public interface AuthorityBizMapper {

    @Select({"<script>",
            "select * from management_sys_menu where id in ",
            "<foreach collection='ids' item='item' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    @Results(id = "managementSysMenu-mapping-base", value = {
            @Result(property = "id", column = "id"), @Result(property = "title", column = "title"), @Result(property = "menuCode", column = "menu_code"), @Result(property = "menuText", column = "menu_text"), @Result(property = "parentId", column = "parent_id"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    List<ManagementSysMenu> getMenuByIds(@Param("ids") Set<Integer> ids);

    @Select({"<script>",
            "select * from management_sys_menu where menu_code in ",
            "<foreach collection='menuCodes' item='item' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    @ResultMap(value = "managementSysMenu-mapping-base")
    List<ManagementSysMenu> getMenuByCodes(@Param("menuCodes") Set<String> menuCodes);

    @Select("select management_sys_role_auth.* from  management_sys_account inner join management_sys_role_account on management_sys_account.id = management_sys_role_account.account_id \n" +
            "inner join management_sys_role_auth on management_sys_role_account.role_id = management_sys_role_auth.role_id AND channel = 'ADMIN' \n" +
            "where management_sys_account.id=#{userId} and management_sys_role_auth.channel='ADMIN' and management_sys_role_auth.auth_type IN ('MENU','PERMISSION')")
    @Results(id = "managementSysRoleAuth-mapping-base", value = {
            @Result(property = "id", column = "id"), @Result(property = "authType", column = "auth_type"), @Result(property = "channel", column = "channel"), @Result(property = "authedData", column = "authed_data"), @Result(property = "roleId", column = "role_id"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    List<ManagementSysRoleAuth> getManagementSysRoleMenuAuthByAccount(@Param("userId") Integer userId);

    @Select("select management_sys_role_auth.auth_type authType,management_sys_role_auth.authed_data authedData from  management_sys_account inner join management_sys_role_account on management_sys_account.id = management_sys_role_account.account_id \n" +
            "inner join management_sys_role_auth on management_sys_role_account.role_id = management_sys_role_auth.role_id\n" +
            "where management_sys_account.id=#{userId} and management_sys_role_auth.channel='ADMIN'")
    List<AuthUserInfo.AuthDataDetail> getAuthPermissionDataByUser(@Param("userId") Integer userId);

    @Select("select management_sys_role_auth.* from  management_sys_role_auth  " +
                    "where management_sys_role_auth.role_id=#{roleId}  and management_sys_role_auth.channel='ADMIN' ")
    @ResultMap(value = "managementSysRoleAuth-mapping-base")
    List<ManagementSysRoleAuth> getManagementSysRoleAuthByRoleAndTypeAndChannel(@Param("roleId") Integer roleId);


    @Select("select management_sys_role.* from management_sys_role\n" +
            "inner join management_sys_role_account on management_sys_role.id = management_sys_role_account.role_id\n" +
            "where management_sys_role_account.account_id = #{userId}")
    @Results(id = "managementSysRole-mapping-base", value = {
            @Result(property = "id", column = "id"), @Result(property = "roleName", column = "role_name"), @Result(property = "roleType", column = "role_type"), @Result(property = "isEnabled", column = "is_enabled"), @Result(property = "canDelete", column = "can_delete"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    List<ManagementSysRole> getAuthedRoles(@Param("userId") Integer userId);

    @Update("update management_sys_account set `password` = #{password}, updater=#{updater} where id=#{userId}")
    int updateAccountPassword(@Param("userId") Integer userId, @Param("password") String password, @Param("updater") String updater);

    @Update("update management_sys_account set `is_deleted` = #{status}, updater=#{updater} where id=#{userId}")
    int updateAccountDeleteStatus(@Param("userId") Integer userId, @Param("status") Boolean status, @Param("updater") String updater);

    @Update("update management_sys_account set `is_enabled` = #{status}, updater=#{updater} where id=#{userId}")
    int updateAccountEnableStatus(@Param("userId") Integer userId, @Param("status") Boolean status, @Param("updater") String updater);


    @Update("UPDATE management_sys_account set staff_code=#{staffCode},user_name=#{userName},nick_name=#{nickName},cellphone=#{cellphone},unionid=#{unionid},remark=#{remark},updater=#{updater} WHERE id=#{id}")
    int updateAccountInfoByIdWithoutPassword(ManagementSysAccount managementSysAccount);


    @Select("SELECT * FROM management_sys_role_account WHERE account_id=#{userId} ")
    @Results(id = "managementSysRoleAccount-mapping-base", value = {
            @Result(property = "id", column = "id"), @Result(property = "roleId", column = "role_id"), @Result(property = "accountId", column = "account_id"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    })
    List<ManagementSysRoleAccount> getAuthRolesByUserId(@Param("userId") Integer userId);


    @Update("update management_sys_role set `is_enabled` = #{status}, updater=#{updater} where id=#{userId}")
    void updateRoleEnableStatus(@Param("userId") Integer userKey, @Param("status") Boolean status, @Param("updater") String toString);


    @Delete("delete FROM management_sys_role_auth WHERE auth_type=#{authType} and channel=#{channel} and role_id=#{roleId} ")
    int deleteRoleAuthByAuthTypeChannelRoleId(@Param("authType") String authType, @Param("channel") String channel, @Param("roleId") Integer roleId);
}
