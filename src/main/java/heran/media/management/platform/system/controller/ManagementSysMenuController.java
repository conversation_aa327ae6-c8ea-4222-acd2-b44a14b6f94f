package heran.media.management.platform.system.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.system.service.ManagementSysMenuService;
import heran.media.management.platform.system.subdomain.dto.TManagementSysMenu;
import heran.media.management.platform.system.subdomain.dto.TMenuResp;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.db.model.ManagementSysMenu;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("/management-sys-menu")
public class ManagementSysMenuController {

    private final ManagementSysMenuService managementSysMenuService;

    public ManagementSysMenuController(ManagementSysMenuService managementSysMenuService) {
        this.managementSysMenuService = managementSysMenuService;
    }

    @ApiLog(storageDescription = "获取当前用户可访问菜单")
    @ApiOperation(value = "获取当前用户可访问菜单", notes = "获取当前用户可访问菜单")
    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<TManagementSysMenu>> list() {
        InternalResponse<List<TManagementSysMenu>> internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        internalResponse.withBody(managementSysMenuService.getVisibleMenus(currentUser));
        return internalResponse;
    }

    @ApiLog(storageDescription = "获取菜单列表")
    @ApiOperation(value = "获取菜单列表", notes = "获取菜单列表")
    @RequestMapping(value = "/listMenu", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<TManagementSysMenu>> listMenu(@Param("platform")String platform) {
        InternalResponse<List<TManagementSysMenu>> internalResponse = InternalResponse.success();
        internalResponse.withBody(managementSysMenuService.getAll(platform));
        return internalResponse;
    }
}
