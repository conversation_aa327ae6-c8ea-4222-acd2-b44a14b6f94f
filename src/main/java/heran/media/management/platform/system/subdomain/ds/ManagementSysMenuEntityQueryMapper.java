package heran.media.management.platform.system.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.ManagementSysMenu;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface ManagementSysMenuEntityQueryMapper {
    @SelectProvider(type = ManagementSysMenuEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "title", column = "title"), @Result(property = "menuCode", column = "menu_code"), @Result(property = "menuText", column = "menu_text"), @Result(property = "parentId", column = "parent_id"), @Result(property = "creator", column = "creator"), @Result(property = "updator", column = "updator"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<ManagementSysMenu> list(SearchCriteria criteria);

    @Select("SELECT * FROM management_sys_menu WHERE platform =#{platform}")
    List<ManagementSysMenu> getAll(@Param("platform") String platform);
}