package heran.media.management.platform.system.service;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.system.errors.CantDelRoleException;
import heran.media.management.platform.system.subdomain.db.CommonMapper;
import heran.media.management.platform.system.subdomain.ds.AuthorityBizMapper;
import heran.media.management.platform.system.subdomain.ds.ManagementSysRoleEntityQueryMapper;
import heran.media.management.platform.system.subdomain.dto.TManagementSysRole;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.db.mapper.ManagementSysRoleAoiAuthGroupMapper;
import heran.media.sharelib.domain.db.mapper.ManagementSysRoleAoiAuthMapper;
import heran.media.sharelib.domain.db.mapper.ManagementSysRoleMapper;
import heran.media.sharelib.domain.db.model.ManagementSysRole;
import heran.media.sharelib.domain.db.model.ManagementSysRoleAoiAuth;
import heran.media.sharelib.domain.db.model.ManagementSysRoleAoiAuthGroup;
import heran.media.sharelib.domain.db.model.ManagementSysRoleAuth;
import heran.media.sharelib.domain.dto.role.CityAoiData;
import heran.media.sharelib.domain.dto.role.CityCbdInsightData;
import heran.media.sharelib.domain.dto.role.CityNumeracyTaData;
import heran.media.sharelib.domain.dto.role.DataRoleRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
@RefreshScope
public class ManagementSysRoleService {

    @Resource
    private ManagementSysRoleEntityQueryMapper managementSysRoleEntityQueryMapper;

    @Resource
    private ManagementSysRoleMapper managementSysRoleMapper;

    @Resource
    private CommonMapper commonMapper;

    @Resource
    private AuthorityBizMapper authorityBizMapper;
    @Resource
    public ManagementSysRoleAoiAuthMapper managementSysRoleAoiAuthMapper;
    @Resource
    private ManagementSysRoleAoiAuthGroupMapper managementSysRoleAoiAuthGroupMapper;

    public List<ManagementSysRole> list(SearchCriteria criteria) {
        SearchCriteria.Criteria notDeletedCriteria = new SearchCriteria.Criteria();
        notDeletedCriteria.setKey("isDeleted");
        notDeletedCriteria.setValue("false");
        criteria.getCriterias().add(notDeletedCriteria);
        return managementSysRoleEntityQueryMapper.list(criteria);
    }

    public ManagementSysRole getById(Integer id) {
        return managementSysRoleMapper.getByIdEX(id);
    }

    public void create(Integer userKey, ManagementSysRole managementSysRole) {
        managementSysRole.setId(null);
        managementSysRole.setCreator(String.valueOf(userKey));
        presetManagementSysRole(userKey, managementSysRole);
        managementSysRoleMapper.insertUpdateEntity(managementSysRole);
    }

    public void modify(Integer userKey, ManagementSysRole managementSysRole) {
        presetManagementSysRole(userKey, managementSysRole);
        managementSysRoleMapper.insertUpdateEntity(managementSysRole);
    }

    private void presetManagementSysRole(Integer userKey, ManagementSysRole managementSysRole) {
        managementSysRole.setRoleType("MANAGEMENT");
        managementSysRole.setCanDelete(true);
        managementSysRole.setIsDeleted(false);
        managementSysRole.setUpdater(String.valueOf(userKey));
    }

    public void delete(Object userKey, Integer id) {
        ManagementSysRole role = managementSysRoleMapper.getByIdEX(id);
        if (role.getCanDelete()) {
            managementSysRoleMapper.deleteByIdLogically(userKey, id);
        } else {
            throw new CantDelRoleException();
        }
    }

    public void updateUserStatus(AuthUserInfo currentUser, Integer userKey, Boolean status) {
        authorityBizMapper.updateRoleEnableStatus(userKey, status, currentUser.getIdentifier().toString());
    }

    public List<ManagementSysRole> listAll() {
        return commonMapper.selectRoleByEnabled();
    }

    public TManagementSysRole getUserContentAuth(Integer roleId) {
        ManagementSysRole role = managementSysRoleMapper.getByIdEX(roleId);
        if (role == null) {
            return null;
        }
        TManagementSysRole tManagementSysRole = new TManagementSysRole(role);
        //获取这个角色的c端权限
        ManagementSysRoleAuth roleIdByAuthAndAuthType = commonMapper.getRoleIdByAuthAndAuthType(roleId);
        if (roleIdByAuthAndAuthType == null) {
            return tManagementSysRole;
        }
        if (StringUtils.isNotEmpty(roleIdByAuthAndAuthType.getAuthedData())) {
            Gson gson = new Gson();
            DataRoleRequest dataRoleRequest = gson.fromJson(roleIdByAuthAndAuthType.getAuthedData(), DataRoleRequest.class);
            tManagementSysRole.setDataRole(dataRoleRequest);
            //要特殊处理AOI数据
            buildDataRoleRequest(roleId,dataRoleRequest);
        }
        return tManagementSysRole;
    }

    /**
     * 构建DataRoleRequest中的AOI相关权限数据
     *
     * @param roleId  角色id
     * @param request DataRoleRequest
     */
    private void buildDataRoleRequest(Integer roleId, DataRoleRequest request) {
        List<ManagementSysRoleAoiAuthGroup> authGroup =
                managementSysRoleAoiAuthGroupMapper.getRoleAoiAuthGroup(roleId);
        if (CollectionUtils.isEmpty(authGroup)) {
            return;
        }

        // authTarget -> List<Group>
        Map<String, List<ManagementSysRoleAoiAuthGroup>> groupedMap =
                authGroup.stream().collect(Collectors.groupingBy(ManagementSysRoleAoiAuthGroup::getAuthTarget));

        // dataGroupingId -> List<aoiId>
        Map<Long, List<Integer>> groupedIds = Optional
                .ofNullable(managementSysRoleAoiAuthMapper.getAoiIdByRoleId(roleId))
                .orElseGet(ArrayList::new)
                .stream()
                .collect(Collectors.groupingBy(
                        ManagementSysRoleAoiAuth::getDataGrouping,
                        Collectors.mapping(ManagementSysRoleAoiAuth::getAoiId, Collectors.toList())
                ));

        // 数智透视：AOI数据库
        fillCityGroupData(
                request.getDigitalAssetRoleData().getAoiDataBaseData().getCityAoiData(),
                groupedMap, groupedIds, "CITY_AOI",
                CityAoiData::getGroupKey,
                CityAoiData::setRegionCodes,
                CityAoiData::setAoiIds
        );

        // 数智人群洞察: 数智TA
        fillCityGroupData(
                request.getSmartMarketingData().getNumeracyTaData().getCityNumeracyTaData(),
                groupedMap, groupedIds, "TA_AOI",
                CityNumeracyTaData::getGroupKey,
                CityNumeracyTaData::setRegionCodes,
                CityNumeracyTaData::setAoiIds
        );

        // 营销场景洞察：商圈洞察
        fillCityGroupData(
                request.getSmartMarketingData().getCbdInsightData().getCityCbdInsightData(),
                groupedMap, groupedIds, "CBD_AOI",
                CityCbdInsightData::getGroupKey,
                CityCbdInsightData::setRegionCodes,
                CityCbdInsightData::setCbdIds
        );

        // 营销场景洞察：特定场景洞察
        fillCityGroupData(
                request.getSmartMarketingData().getSpecificContext().getCityCbdInsightData(),
                groupedMap, groupedIds, "INSIGHT_AOI",
                CityCbdInsightData::getGroupKey,
                CityCbdInsightData::setRegionCodes,
                CityCbdInsightData::setCbdIds
        );
    }

    /**
     * 通用填充：按 authTarget 将 regionCodes 与 aoiIds/cbdIds 写回到目标列表
     */
    private <T> void fillCityGroupData(
            List<T> targetList,
            Map<String, List<ManagementSysRoleAoiAuthGroup>> groupedMap,
            Map<Long, List<Integer>> groupedIds,
            String authTarget,
            Function<T, String> groupKeyGetter,
            BiConsumer<T, List<String>> regionSetter,
            BiConsumer<T, List<Integer>> idSetter
    ) {
        if (CollectionUtils.isEmpty(targetList)) {
            return;
        }
        List<ManagementSysRoleAoiAuthGroup> groups = groupedMap.get(authTarget);
        if (CollectionUtils.isEmpty(groups)) {
            return;
        }

        // groupingName -> Group
        Map<String, ManagementSysRoleAoiAuthGroup> groupByName = groups.stream()
                .collect(Collectors.toMap(
                        ManagementSysRoleAoiAuthGroup::getGroupingName,
                        Function.identity(),
                        (left, right) -> left
                ));

        for (T item : targetList) {
            String groupKey = groupKeyGetter.apply(item);
            ManagementSysRoleAoiAuthGroup grp = groupByName.get(groupKey);
            if (grp == null) {
                continue;
            }

            List<String> regions = Optional.ofNullable(grp.getRegionArray())
                    .map(s -> JSON.parseArray(s, String.class))
                    .orElse(Collections.emptyList());
            regionSetter.accept(item, regions);

            List<Integer> ids = Optional.ofNullable(groupedIds.get(grp.getId()))
                    .orElse(Collections.emptyList())
                    .stream()
                    .distinct()
                    .collect(Collectors.toList());

            idSetter.accept(item, ids);
        }
    }

}
