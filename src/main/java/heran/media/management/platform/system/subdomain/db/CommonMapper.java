package heran.media.management.platform.system.subdomain.db;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import heran.media.management.platform.mediaorder.sudomain.dto.ItemData;
import heran.media.management.platform.mediaorder.sudomain.dto.MediaTagData;
import heran.media.sharelib.domain.db.model.ManagementSysAccount;
import heran.media.sharelib.domain.db.model.ManagementSysMenu;
import heran.media.sharelib.domain.db.model.ManagementSysRole;
import heran.media.sharelib.domain.db.model.ManagementSysRoleAuth;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CommonMapper extends BaseMapper<ManagementSysRole> {


    @Select(" SELECT\n" +
            " management_sys_role.id,management_sys_role.role_name AS roleName\n" +
            "FROM\n" +
            "management_sys_role_account\n" +
            "inner JOIN management_sys_role ON  management_sys_role_account.role_id = management_sys_role.id\n" +
            "where  management_sys_role_account.account_id = #{id} ")
    List<ManagementSysRole> getManagementSysAccountByUserId(Integer id);

    @Select({
            "SELECT",
            "t3.id tagId ,t3.parent_id,t2.label parentName, t3.label,t4.id categoryId,t4.category_name, t4.group_label",
            "FROM main_media_tag t3 ",
            "INNER JOIN main_resource_category t4 ON t3.cagetory_id = t4.id",
            "LEFT JOIN main_media_tag t2 ON t2.id = t3.parent_id",
            "WHERE t4.group_label = #{groupLabel}"
    })
    @Results(value = {
            @Result(property = "tagId", column = "tagId"),
            @Result(property = "parentId", column = "parent_id"),
            @Result(property = "tagName", column = "label"),
            @Result(property = "parentName", column = "parentName"),
            @Result(property = "categoryId", column = "categoryId"),
            @Result(property = "categoryName", column = "category_name"),
            @Result(property = "tagCategory", column = "group_label")
    })
    List<MediaTagData> getTagsByGroupLabel(@Param("groupLabel") String groupLabel);

    @Delete("delete from management_sys_role_account where account_id = #{id}")
    void deleteAuthInfosByUser(ManagementSysAccount userAccount);

   @Select("SELECT\n" +
           "management_sys_role_auth.authed_data\n" +
           "FROM\n" +
           "management_sys_role_auth\n" +
           "LEFT JOIN management_sys_role ON  management_sys_role_auth.role_id = management_sys_role.id\n" +
           "where  management_sys_role.id = #{id}")
    List<String> getManagementSysRoleAuthByRoleId(Integer id);

    @Select("select * from management_sys_menu where id in (${menuIds})")
    List<ManagementSysMenu> selectMenuByAuthDate(String menuIds);

    @Select("DELETE FROM management_sys_role_auth where role_id = #{id}")
    void deleteByRoleId(Integer id);

    @Select("select * from management_sys_role_auth where role_id = #{id}")
    ManagementSysRoleAuth getRoleIdByAuth(Integer id);

    @Select("select count(id) from management_sys_role_account where role_id = #{id} ")
    int selectRoleAccountByRoleName(Integer id);

    @Delete("delete from management_sys_role_account where account_id = #{id}")
    void deleteByRlation(Integer id);

    @Select({"SELECT * FROM management_sys_account WHERE user_name=#{userName} and is_deleted = 0 "})
     ManagementSysAccount getByUserName(@Param("userName") String var1);

    @Select({"SELECT id,role_name,role_type,is_enabled,can_delete,created_by_user,updated_by_user,creator,updater,create_time,update_time,role_code FROM management_sys_role WHERE role_name=#{roleName} and is_deleted = 0 "})
    ManagementSysRole getByRoleName(@Param("roleName") String var1);

    @Select("select * from management_sys_role where is_enabled = 1 and is_deleted = 0")
    List<ManagementSysRole> selectRoleByEnabled();

    @Select("select count(0) from management_sys_account where user_name = #{userName} and id != #{id}")
    Integer selectByUserName(@Param("id")Integer id,@Param("userName") String userName);

   @Select("select count(0) from management_sys_account where cellphone = #{cellphone} and id != #{id}")
   Integer selectByCellPhone(@Param("id")Integer id, @Param("cellphone")String cellphone);

   @Select("select count(0) from management_sys_role where role_name = #{roleName} and id != #{id} and is_deleted = 0")
   Integer selectByRoleName(@Param("id")Integer id, @Param("roleName")String roleName);

    @Select("select count(0) from management_sys_account msa\n" +
            "inner join management_sys_role_account msra on msa.id = msra.account_id\n" +
            "inner join management_sys_role msr on msra.role_id = msr.id\n" +
            "where msa.id = #{id} and msr.is_enabled = 1 AND msa.validity_start_time <= NOW() AND msa.validity_end_time >= NOW() ")
    Integer selectRoleByAccountIdAndEnable(@Param("id")Integer id);

    @Select("SELECT\n" +
            "t1.id,\n" +
            "t1.item_name,\n" +
            "t1.item_type,\n" +
            "t2.data_label,\n" +
            "t2.data_value,\n" +
            "t2.is_default,\n" +
            "t2.remark,\n" +
            "t2.data_sort \n" +
            "FROM\n" +
            "main_dict_item_type t1\n" +
            "INNER JOIN main_dict_item_data t2 ON t1.id = t2.dict_type_id \n" +
            "WHERE\n" +
            "t1.item_group = #{itemGroup}")
    @Results(value = {
            @Result(property = "itemId", column = "id"),
            @Result(property = "itemName", column = "item_name"),
            @Result(property = "itemType", column = "item_type"),
            @Result(property = "labelName", column = "data_label"),
            @Result(property = "labelKey", column = "data_value"),
            @Result(property = "isDefault", column = "is_default"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "sort", column = "data_sort")
    })
    List<ItemData> getItemDataByItemGroup(@Param("itemGroup")String itemGroup);

    /**
     * 获取c端权限数据
     * @param id 角色id
     * @return ManagementSysRoleAuth
     */
    @Select("select * from management_sys_role_auth where role_id = #{id} AND auth_type = 'ROLE_DATA_C' LIMIT 1")
    ManagementSysRoleAuth getRoleIdByAuthAndAuthType(Integer id);
}
