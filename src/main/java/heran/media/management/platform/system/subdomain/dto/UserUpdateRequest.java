package heran.media.management.platform.system.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.ManagementSysAccount;
import heran.media.sharelib.domain.db.model.ManagementSysRole;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.LinkedList;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserUpdateRequest {
    @NotNull(message = "用户ID不能空")
    @Min(1L)
    private Integer userId;
	private Boolean isEnabled;
    private String cellphone;
	@NotEmpty(message = "密码不能空")
    private String password;

	private String userName;

	private String staffCode;

	private String nickName;

    private List<Integer> roleList;

    public AppUser adaptToAppUser() {
		ManagementSysAccountData account= new ManagementSysAccountData();
		account.setId(userId);
		account.setPassword(password);
		account.setCellphone(cellphone);
		account.setNickName(nickName);
		account.setUserName(userName);
		account.setStaffCode(staffCode);
		account.setIsEnabled(isEnabled);
	    List<ManagementSysRole> roleList = new LinkedList<>();
	    if (this.roleList != null) {
		    for (Integer roleId : this.roleList) {
				ManagementSysRole role = new ManagementSysRole();
				role.setId(roleId);
			    roleList.add(role);
		    }
	    }
	    return new AppUser(account,roleList);
    }
}
