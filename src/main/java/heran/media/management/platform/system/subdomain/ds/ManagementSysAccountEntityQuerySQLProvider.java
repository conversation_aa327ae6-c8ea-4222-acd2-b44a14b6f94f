package heran.media.management.platform.system.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class ManagementSysAccountEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("msa.*");
            FROM("management_sys_account msa");
            LEFT_OUTER_JOIN("management_sys_role_account msra on msa.id = msra.account_id");
            LEFT_OUTER_JOIN("management_sys_role msr on msra.role_id = msr.id");

            String orderSql = null;

            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (cri.getKey().equals("id")) {
                        WHERE("msa.id = " + cri.getValue());
                    } else if (cri.getKey().equals("staffCode") && cri.getValue() != null) {
                        WHERE("msa.staff_code like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("userName") && cri.getValue() != null) {
                        WHERE("msa.user_name like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("nickName") && cri.getValue() != null) {
                        WHERE("msa.nick_name like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("password") && cri.getValue() != null) {
                        WHERE("msa.password like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("cellphone") && cri.getValue() != null) {
                        WHERE("msa.cellphone like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("unionid") && cri.getValue() != null) {
                        WHERE("msa.unionid like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("organizationCode") && cri.getValue() != null) {
                        WHERE("msa.organization_code like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("avatar") && cri.getValue() != null) {
                        WHERE("msa.avatar like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("isEnabled") && cri.getValue() != null) {
                        WHERE("msa.is_enabled = '" + cri.getValue() + "'");
                    } else if (cri.getKey().equals("remark") && cri.getValue() != null) {
                        WHERE("msa.remark like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("isDeleted") && cri.getValue() != null) {
                        WHERE("msa.is_deleted like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("roleName") && cri.getValue() != null) {
                        String[] roleNames = cri.getValue().split(",");
                        StringBuilder roleNameBuilder = new StringBuilder();
                        for (int i = 0; i < roleNames.length; i++) {
                            if (i > 0) {
                                roleNameBuilder.append(", ");
                            }
                            roleNameBuilder.append("'").append(roleNames[i].trim()).append("'");
                        }
                        WHERE("msr.role_name IN (" + roleNameBuilder.toString() + ")");
                    } else if ("platform".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("msa.platform like '%" + cri.getValue() + "%'");
                    } else if ("userNameSort".equals(cri.getKey()) && cri.getValue() != null) {
                        if (orderSql == null) {
                            orderSql = "msa.user_name " + cri.getValue();
                        } else {
                            orderSql += ",msa.user_name " + cri.getValue();
                        }
                    } else if ("platformSort".equals(cri.getKey()) && cri.getValue() != null) {
                        if (orderSql == null) {
                            orderSql = "msa.platform " + cri.getValue();
                        } else {
                            orderSql += ",msa.platform " + cri.getValue();
                        }
                    } else if ("roleNameSort".equals(cri.getKey()) && cri.getValue() != null) {
                        if (orderSql == null) {
                            orderSql = "msr.role_name " + cri.getValue();
                        } else {
                            orderSql += ",msr.role_name " + cri.getValue();
                        }
                    } else if ("validityEndTimeSort".equals(cri.getKey()) && cri.getValue() != null) {
                        if (orderSql == null) {
                            orderSql = "msa.validity_end_time " + cri.getValue();
                        } else {
                            orderSql += ",msa.validity_end_time " + cri.getValue();
                        }
                    } else if ("isEnabledSort".equals(cri.getKey()) && cri.getValue() != null) {
                        if (orderSql == null) {
                            orderSql = "msa.is_enabled " + cri.getValue();
                        } else {
                            orderSql += ",msa.is_enabled " + cri.getValue();
                        }
                    } else if ("updateTimeSort".equals(cri.getKey()) && cri.getValue() != null) {
                        if (orderSql == null) {
                            orderSql = "msa.update_time " + cri.getValue();
                        } else {
                            orderSql += ",msa.update_time " + cri.getValue();
                        }
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }

            GROUP_BY("msa.id");

            if (StringUtils.isEmpty(orderSql)) {
                ORDER_BY("msa.create_time desc");
            } else {
                ORDER_BY(orderSql);
            }

        }}.toString();
    }
}
