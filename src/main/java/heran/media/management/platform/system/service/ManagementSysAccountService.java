package heran.media.management.platform.system.service;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.main.subdomain.ds.ApiMapLogEntityQueryMapper;
import heran.media.management.platform.main.subdomain.dto.AccountApiMapQuotaData;
import heran.media.management.platform.main.subdomain.dto.ApiMapUsedData;
import heran.media.management.platform.main.subdomain.dto.UserConfigResponse;
import heran.media.management.platform.system.errors.*;
import heran.media.management.platform.system.subdomain.db.CommonMapper;
import heran.media.management.platform.system.subdomain.ds.AuthorityBizMapper;
import heran.media.management.platform.system.subdomain.ds.ManagementSysAccountEntityQueryMapper;
import heran.media.management.platform.system.subdomain.dto.AppUser;
import heran.media.management.platform.system.subdomain.dto.ManagementSysAccountData;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.bo.UserConfigQuotaType;
import heran.media.sharelib.domain.db.mapper.ManagementSysAccountMapper;
import heran.media.sharelib.domain.db.mapper.ManagementSysRoleAccountMapper;
import heran.media.sharelib.domain.db.mapper.main.AccountApiMapQuotaMapper;
import heran.media.sharelib.domain.db.mapper.main.MainAttachementMapper;
import heran.media.sharelib.domain.db.model.ManagementSysAccount;
import heran.media.sharelib.domain.db.model.ManagementSysRole;
import heran.media.sharelib.domain.db.model.ManagementSysRoleAccount;
import heran.media.sharelib.domain.db.model.main.AccountApiMapQuota;
import heran.media.sharelib.domain.db.model.main.MainAttachement;
import heran.media.sharelib.utils.EncodeUtils;
import heran.media.sharelib.utils.upload.AliYunOssRedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;


@Slf4j
@Service
@RefreshScope
public class ManagementSysAccountService extends BaseCallServiceImpl {

    @Resource
    private ManagementSysAccountEntityQueryMapper managementSysAccountEntityQueryMapper;
    @Resource
    private ManagementSysAccountMapper managementSysAccountMapper;
    @Resource
    private AuthorityBizMapper authorityBizMapper;
    @Resource
    private ManagementSysRoleAccountMapper managementSysRoleAccountMapper;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private MainAttachementMapper mainAttachementMapper;
    @Resource
    private AliYunOssRedisUtils aliYunOssRedisUtils;
    @Resource
    private AccountApiMapQuotaMapper accountApiMapQuotaMapper;
    @Resource
    private ApiMapLogEntityQueryMapper apiMapLogEntityQueryMapper;


    public List<AppUser> list(SearchCriteria criteria) {
        SearchCriteria.Criteria notDeletedCriteria = new SearchCriteria.Criteria();
        notDeletedCriteria.setKey("isDeleted");
        notDeletedCriteria.setValue("0");
        criteria.getCriterias().add(notDeletedCriteria);
        Map<Integer, AppUser> userMap = new HashMap<>();
        List<ManagementSysAccountData> lists = managementSysAccountEntityQueryMapper.list(criteria);
        for (ManagementSysAccountData account : lists) {
            if (account.getAttachmentId() != null) {
                MainAttachement mainAttachement = mainAttachementMapper.getByIdFilterIsDeleted(account.getAttachmentId());
                if (mainAttachement != null) {
                    aliYunOssRedisUtils.setTempUrl(mainAttachement);
                    account.setPicture(mainAttachement.getUrl());
                }
            }
            if (!userMap.containsKey(account.getId())) {
                userMap.put(account.getId(), new AppUser(account, new LinkedList<>()));
            }
            AppUser appUser = userMap.get(account.getId());
            List<ManagementSysRole> roles = commonMapper.getManagementSysAccountByUserId(account.getId());
            appUser.setRoleList(roles);
        }

        List<AppUser> appUserList = new LinkedList<>(userMap.values());
        appUserList.sort(((o1, o2) -> o1.getAccount().getCreateTime().before(o2.getAccount().getCreateTime()) ? 1 : -1));
        return appUserList;
    }

    public ManagementSysAccount getById(Integer id) {
        return managementSysAccountMapper.getByIdEX(id);
    }


    @Transactional
    public void create(Integer userKey, AppUser appUser, List<AccountApiMapQuotaData> accountApiMapQuotas) {
        ManagementSysAccount account = appUser.getAccount();
        account.setId(null);
        account.setIsDeleted(false);
        account.setAllOrderPermissions(false);
        String code = UUID.randomUUID().toString().replace("-", "");
        account.setStaffCode(code);
        account.setPassword(EncodeUtils.getDBPassword(account.getPassword()));
        account.setCreator(String.valueOf(userKey));
        account.setUpdater(String.valueOf(userKey));
        account.setUpdatedByUser(userKey);
        account.setCreatedByUser(userKey);
        if (account.getUserName().length() > 64) {
            throw new UserNameIsToLongException();
        }
//        if (account.getNickName().length() > 64) {
//            throw new NickNameIsToLongException();
//        }
        if (commonMapper.getByUserName(account.getUserName()) != null) {
            throw new UserNameExitsException();
        }
        managementSysAccountMapper.insertUpdateEntity(account);
        for (ManagementSysRole role : appUser.getRoleList()) {
            ManagementSysRoleAccount roleAccount = new ManagementSysRoleAccount();
            roleAccount.setAccountId(account.getId());
            roleAccount.setRoleId(role.getId());
            roleAccount.setUpdatedByUser(userKey);
            roleAccount.setCreatedByUser(userKey);
            roleAccount.setCreator(userKey.toString());
            roleAccount.setUpdater(userKey.toString());
            managementSysRoleAccountMapper.insertIgnoreEntity(roleAccount);
        }

        if (accountApiMapQuotas != null && accountApiMapQuotas.size() > 0) {
            List<AccountApiMapQuota> accountApiMapQuotaList = new ArrayList<>();
            for (AccountApiMapQuotaData currQuota : accountApiMapQuotas) {
                AccountApiMapQuota currAccountApiMapQuota = new AccountApiMapQuota();
                currAccountApiMapQuota.setId(null);
                currAccountApiMapQuota.setAccountId(account.getId());
                currAccountApiMapQuota.setQuota(currQuota.getQuota());
                currAccountApiMapQuota.setBizType(currQuota.getBizType());
                currAccountApiMapQuota.setCreateTime(new Date());
                setCreatorInfo(userKey, currAccountApiMapQuota);
                setUpdaterInfo(userKey, currAccountApiMapQuota);
                accountApiMapQuotaList.add(currAccountApiMapQuota);
            }
            accountApiMapQuotaMapper.batchInsertEntities(accountApiMapQuotaList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void modify(Integer userKey, AppUser appUser, List<AccountApiMapQuotaData> accountApiMapQuotas) {
        ManagementSysAccount account = appUser.getAccount();
        ManagementSysAccount accountUser = managementSysAccountMapper.getByIdEX(account.getId());
        if (accountUser == null) {
            throw new NoSuchUserException(account.getId(), "");
        }
        //将前端传来的密文密码进行BCrypt处理,如果不传则不处理
        if (StringUtils.isNotEmpty(account.getPassword())) {
            accountUser.setPassword(EncodeUtils.getDBPassword(account.getPassword()));
        }

        Integer userNameCount = commonMapper.selectByUserName(account.getId(), account.getUserName());
        if (userNameCount > 0) {
            throw new UserNameExitsException();
        }
        //处理头像
        if (account.getAttachmentId() != null) {
            if (accountUser.getAttachmentId() == null || !accountUser.getAttachmentId().equals(account.getAttachmentId())) {
                accountUser.setAttachmentId(account.getAttachmentId());
                //删除原来的附件
                mainAttachementMapper.deleteByIdLogically(userKey, accountUser.getAttachmentId());
            }
        } else {
            //删除原来的附件
            mainAttachementMapper.deleteByIdLogically(userKey, accountUser.getAttachmentId());
        }

        accountUser.setUserName(account.getUserName());
        accountUser.setNickName(account.getNickName());
        accountUser.setPlatform(account.getPlatform());
        accountUser.setCellphone(account.getCellphone());
        accountUser.setIsEnabled(account.getIsEnabled());
        if (account.getAllOrderPermissions() != null) {
            accountUser.setAllOrderPermissions(account.getAllOrderPermissions());
        } else {
            accountUser.setAllOrderPermissions(false);
        }
        accountUser.setValidityStartTime(account.getValidityStartTime());
        accountUser.setValidityEndTime(account.getValidityEndTime());
        accountUser.setUpdater(userKey.toString());
        accountUser.setCreator(userKey.toString());
        accountUser.setCreatedByUser(userKey);
        accountUser.setUpdatedByUser(userKey);
        if (account.getUserName().length() > 64) {
            throw new UserNameIsToLongException();
        }
//        if (account.getNickName().length() > 64) {
//            throw new NickNameIsToLongException();
//        }
        managementSysAccountMapper.updateByEntity(accountUser);
        commonMapper.deleteAuthInfosByUser(accountUser);
        for (ManagementSysRole role : appUser.getRoleList()) {
            ManagementSysRoleAccount roleAccount = new ManagementSysRoleAccount();
            roleAccount.setAccountId(accountUser.getId());
            roleAccount.setRoleId(role.getId());
            roleAccount.setCreatedByUser(userKey);
            roleAccount.setUpdatedByUser(userKey);
            roleAccount.setCreator(userKey.toString());
            roleAccount.setUpdater(userKey.toString());
            managementSysRoleAccountMapper.insertIgnoreEntity(roleAccount);
        }

        if (accountApiMapQuotas != null && accountApiMapQuotas.size() > 0) {
            accountApiMapQuotaMapper.deleteByAccountIdEX(accountUser.getId());
            List<AccountApiMapQuota> accountApiMapQuotaList = new ArrayList<>();
            for (AccountApiMapQuotaData currQuota : accountApiMapQuotas) {
                AccountApiMapQuota currAccountApiMapQuota = new AccountApiMapQuota();
                currAccountApiMapQuota.setId(null);
                currAccountApiMapQuota.setAccountId(accountUser.getId());
                currAccountApiMapQuota.setQuota(currQuota.getQuota());
                currAccountApiMapQuota.setBizType(currQuota.getBizType());
                if (currQuota.getRemark() != null) {
                    currAccountApiMapQuota.setRemark(currQuota.getRemark());
                } else {
                    currAccountApiMapQuota.setRemark("");
                }

                currAccountApiMapQuota.setCreateTime(new Date());
                setCreatorInfo(userKey, currAccountApiMapQuota);
                setUpdaterInfo(userKey, currAccountApiMapQuota);
                accountApiMapQuotaList.add(currAccountApiMapQuota);
            }
            accountApiMapQuotaMapper.batchInsertEntities(accountApiMapQuotaList);
        }

    }

    @Transactional
    public void delete(Object userKey, Integer id) {
        managementSysAccountMapper.deleteByIdLogically(userKey, id);
        commonMapper.deleteByRlation(id);
    }

    public void changePassword(AuthUserInfo currentUser, Integer accountId, String newPassword) {
        ManagementSysAccount account = managementSysAccountMapper.getByIdEX(accountId);
        if (account != null) {
            String dbPassword = EncodeUtils.getDBPassword(newPassword);
            authorityBizMapper.updateAccountPassword(currentUser.getIdentifier(), dbPassword, String.valueOf(currentUser.getIdentifier()));
        } else {
            throw new RuntimeException("No such user");
        }
    }

    public void updateUserStatus(AuthUserInfo currentUser, Integer userKey, Boolean status) {
        authorityBizMapper.updateAccountEnableStatus(userKey, status, currentUser.getIdentifier().toString());
    }

    public void changePassword(AuthUserInfo userInfo, String oldPassword, String newPassword) {
        ManagementSysAccount account = managementSysAccountMapper.getByIdEX(userInfo.getIdentifier());
        if (account != null) {
            if (EncodeUtils.isDBPasswordMatched(oldPassword, account.getPassword())) {
                String dbPassword = EncodeUtils.getDBPassword(newPassword);
                authorityBizMapper.updateAccountPassword(userInfo.getIdentifier(), dbPassword, String.valueOf(userInfo.getIdentifier()));
            } else {
                throw new NotSamePasswordException();
            }

        } else {
            throw new AuthFailledException();
        }
    }

    public void deleteBatch(Integer identifier, Integer[] ids) {
        for (Integer id : ids) {
            managementSysAccountMapper.deleteByIdLogically(identifier, id);
            commonMapper.deleteByRlation(id);
        }
    }

    public UserConfigResponse getUserConfig(Integer accountId) {
        UserConfigResponse userConfigResponse = new UserConfigResponse();

        List<AccountApiMapQuota> accountQuota = accountApiMapQuotaMapper.getByAccountIdEX(accountId);
        List<ApiMapUsedData> todayUsed = apiMapLogEntityQueryMapper.getTodayUsed(accountId);
        List<AccountApiMapQuotaData> quotaList = new ArrayList<>();
        for (UserConfigQuotaType type : UserConfigQuotaType.values()) {
            AccountApiMapQuotaData quotaData = getAccountApiMapQuotaData(type, accountQuota, todayUsed);
            quotaList.add(quotaData);
        }
        userConfigResponse.setQuotaConfig(quotaList);
        return userConfigResponse;
    }

    private static AccountApiMapQuotaData getAccountApiMapQuotaData(UserConfigQuotaType type, List<AccountApiMapQuota> accountQuota, List<ApiMapUsedData> todayUsed) {
        AccountApiMapQuotaData quotaData = new AccountApiMapQuotaData();
        quotaData.setBizName(type.getName());
        quotaData.setBizType(type.name());
        quotaData.setTodayUsed(0);
        for (AccountApiMapQuota currAccountQuota : accountQuota) {
            if (currAccountQuota.getBizType().equals(type.name())) {
                quotaData.setQuota(currAccountQuota.getQuota());
                quotaData.setRemark(currAccountQuota.getRemark());
            }
        }
        for (ApiMapUsedData currTodayUsed : todayUsed) {
            if (currTodayUsed.getBizType().equals(type.name())) {
                quotaData.setTodayUsed(currTodayUsed.getTodayUsed());
            }
        }
        return quotaData;
    }
}
