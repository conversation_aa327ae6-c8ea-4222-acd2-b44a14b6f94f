package heran.media.management.platform.system.subdomain.dto;

import heran.media.sharelib.domain.db.model.ManagementSysAccount;
import heran.media.sharelib.domain.db.model.ManagementSysRole;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.LinkedList;
import java.util.List;

@Data
public class UserCreateRequest {
	@NotEmpty(message = "用户名称不能空")
	private String userName;
	@NotEmpty(message = "密码不能空")
	private String password;
	private String cellphone;
	private String nickName;
	private String department;
	private List<Integer> roleList;

	public AppUser adaptToAppUser(){
		ManagementSysAccountData account= new ManagementSysAccountData();
		account.setUserName(userName);
		account.setPassword(password);
		account.setCellphone(cellphone);
		account.setNickName(nickName);
		account.setIsEnabled(true);
		account.setRemark(department);
		List<ManagementSysRole> roleList = new LinkedList<>();
		if (this.roleList != null) {
			for (Integer roleId : this.roleList) {
				ManagementSysRole role = new ManagementSysRole();
				role.setId(roleId);
				roleList.add(role);
			}
		}
		return new AppUser(account,roleList);
	}
}
