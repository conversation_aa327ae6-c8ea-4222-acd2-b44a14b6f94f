package heran.media.management.platform.system.subdomain.dto;

import heran.media.sharelib.domain.db.model.ManagementSysMenu;
import heran.media.sharelib.domain.db.model.ManagementSysRole;
import heran.media.sharelib.domain.dto.role.DataRoleRequest;
import lombok.Data;

import java.util.List;

@Data
public class TRoleAuthResp {
    private List<ManagementSysMenu> menuCodes;
    private List<ManagementSysMenu> buttonMenuCodes;
    private DataRoleRequest dataRole;
    private ManagementSysRole role;
    private Boolean isCityAoiUpdate;
    private Boolean isTaAoiUpdate;
    private Boolean isCbdAoiUpdate;
    private Boolean isInsightAoiUpdate;

    public TRoleAuthResp(ManagementSysRole managementSysRole,
                         List<ManagementSysMenu> menuCodeList,
                         List<ManagementSysMenu> buttonCodeList,
                         DataRoleRequest dataRole,Boolean isCityAoiUpdate,
                         Boolean isTaAoiUpdate,
                         Boolean isCbdAoiUpdate,
                         Boolean isInsightAoiUpdate) {
        this.role = managementSysRole;
        this.menuCodes = menuCodeList;
        this.buttonMenuCodes = buttonCodeList;
        this.dataRole = dataRole;
        this.isCityAoiUpdate = isCityAoiUpdate;
        this.isTaAoiUpdate = isTaAoiUpdate;
        this.isCbdAoiUpdate = isCbdAoiUpdate;
        this.isInsightAoiUpdate = isInsightAoiUpdate;
    }
}
