package heran.media.management.platform.system.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.system.service.ManagementSysRoleService;
import heran.media.management.platform.system.subdomain.dto.TManagementSysRole;
import heran.media.management.platform.system.subdomain.dto.TRoleStatusChangedReq;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.db.model.ManagementSysRole;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("/management-sys-role")
public class ManagementSysRoleController {

    private final ManagementSysRoleService managementSysRoleService;

    public ManagementSysRoleController(ManagementSysRoleService managementSysRoleService) {
        this.managementSysRoleService = managementSysRoleService;
    }

    @ApiLog(storageDescription = "查询角色列表")
    @ApiOperation(value = "列表查询", notes = "查询字段可包含【id(主键自增id),roleName(角色名称),roleType(角色类型，0（管理平台），1（AI供应链），2（AI伙伴）),isEnabled(是否启用),canDelete(能否删除，系统内置角色不可删除),creator(行记录创建者),updator(行记录更新者),createTime(行记录创建时间),updateTime(行记录更新时间),】")
    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<TManagementSysRole> list(@RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<ManagementSysRole> entities = managementSysRoleService.list(criteria);
        PageResponse<TManagementSysRole> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(entities.stream().map(TManagementSysRole::new).collect(Collectors.toList()));
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog(storageDescription = "查询角色明细")
    @ApiOperation(value = "单个查询", notes = "根据id获取对象详情")
    @RequestMapping(value = "/getById/{id}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<TManagementSysRole> getById(@PathVariable Integer id) {
        InternalResponse internalResponse = null;
        ManagementSysRole managementSysRole = managementSysRoleService.getById(id);
        internalResponse = InternalResponse.success().withBody(managementSysRole);
        return internalResponse;
    }

    @ApiLog(storageDescription = "角色启用与禁用")
    @ApiOperation(value = "角色启用与禁用")
    @RequestMapping(value = "/status-change", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse statusChange(@Valid @RequestBody TRoleStatusChangedReq req) {
        InternalResponse<String> internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysRoleService.updateUserStatus(currentUser, req.getUserKey(), req.getStatus());
        return internalResponse;
    }

    @ApiOperation(value = "新建角色")
    @ApiLog(storage = true, storageDescription = "新建角色")
    @RequestMapping(value = "/create", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse create(@Valid @RequestBody TManagementSysRole managementSysRole) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysRoleService.create(currentUser.getIdentifier(), managementSysRole.adapToPO());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "修改角色")
    @ApiLog(storage = true, storageDescription = "修改角色")
    @RequestMapping(value = "/modify", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse modify(@Valid @RequestBody TManagementSysRole managementSysRole) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysRoleService.modify(currentUser.getIdentifier(), managementSysRole.adapToPO());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "删除角色")
    @ApiLog(storage = true, storageDescription = "删除角色")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @ResponseBody
    public InternalResponse delete(@RequestParam("id") Integer id) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysRoleService.delete(currentUser.getIdentifier(), id);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }


    @ApiLog(storageDescription = "查询c端角色信息")
    @ApiOperation(value = "查询c端角色信息", notes = "根据id获取对象详情")
    @RequestMapping(value = "/getUserContentAuth/{id}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<TManagementSysRole> getUserContentAuth(@PathVariable Integer id) {
        InternalResponse internalResponse = null;
        TManagementSysRole managementSysRole = managementSysRoleService.getUserContentAuth(id);
        internalResponse = InternalResponse.success().withBody(managementSysRole);
        return internalResponse;
    }
}
