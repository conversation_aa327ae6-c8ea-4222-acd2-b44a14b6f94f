package heran.media.management.platform.system.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.system.service.ManagementSysMenuService;
import heran.media.management.platform.system.service.ManagementSysRoleAuthService;
import heran.media.management.platform.system.subdomain.dto.*;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.db.model.ManagementSysMenu;
import heran.media.sharelib.domain.db.model.ManagementSysRoleAuth;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/management-sys-role-auth")
public class ManagementSysRoleAuthController {

    @Autowired
    private ManagementSysRoleAuthService managementSysRoleAuthService;

    @Autowired
    private ManagementSysMenuService managementSysMenuService;


    @ApiLog(storageDescription = "授权账号角色", storage = true)
    @ApiOperation(value = "授权角色功能权限")
    @RequestMapping(value = "/auth-functions", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse authRoles(@Valid @RequestBody TAuthFunctionsReq tAuthFunctionsReq) {
        InternalResponse internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysRoleAuthService.authFunctions(currentUser, tAuthFunctionsReq.getRoleId(), tAuthFunctionsReq.getAuthedFunctions());
        return internalResponse;
    }

    @ApiLog(storageDescription = "获取角色已授权的菜单功能列表")
    @ApiOperation(value = "获取角色已授权的菜单功能列表")
    @RequestMapping(value = "/list-authed-function-menu", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<TAuthedFunctionsMenuResp> listAuthedFunctionMenu(@RequestParam("roleKey") Integer roleKey) {
        InternalResponse<TAuthedFunctionsMenuResp> internalResponse = InternalResponse.success();
        List<ManagementSysRoleAuth> auths = managementSysRoleAuthService.listAuthedFunctions(roleKey);
        List<TManagementSysMenu> allMenus = managementSysMenuService.list(new SearchCriteria());
        internalResponse.withBody(new TAuthedFunctionsMenuResp("ADMIN", auths == null || auths.size() == 0 ? null : auths, allMenus));
        return internalResponse;
    }

//    @ApiLog(storageDescription = "查询角色授权列表")
//    @ApiOperation(value = "列表查询", notes = "查询字段可包含【id(主键自增id),authType(角色类型，MENU（菜单）),channel(角色可用渠道, ADMIN),authedData(多个时逗号分隔),roleId(角色id),creator(行记录创建者),updator(行记录更新者),createTime(行记录创建时间),updateTime(行记录更新时间),】")
//    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json")
//    @ResponseBody
//    public InternalResponse<TManagementSysRoleAuth> list(@RequestBody SearchCriteria criteria) {
//        InternalResponse internalResponse = null;
//        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
//        List<ManagementSysRoleAuth> entities = managementSysRoleAuthService.list(criteria);
//        PageResponse<TManagementSysRoleAuth> pageResponse = new PageResponse<>();
//        pageResponse.setCount(page.getTotal());
//        pageResponse.setCurrentPageNumber(criteria.getPage());
//        pageResponse.setResults(entities.stream().map(TManagementSysRoleAuth::new).collect(Collectors.toList()));
//        internalResponse = InternalResponse.success().withBody(pageResponse);
//        return internalResponse;
//    }

    @ApiLog(storageDescription = "查询角色授权情况")
    @ApiOperation(value = "单个查询", notes = "根据角色id获取对象详情")
    @RequestMapping(value = "/getById/{id}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<TManagementSysRoleAuth> getById(@PathVariable Integer id) {
        InternalResponse internalResponse = null;
        ManagementSysRoleAuth managementSysRoleAuth = managementSysRoleAuthService.getById(id);
        internalResponse = InternalResponse.success().withBody(managementSysRoleAuth);
        return internalResponse;
    }

    @ApiLog(storageDescription = "角色权限列表")
    @ApiOperation(value = "角色权限列表查询", notes = "查询字段可包含【isEnabled(状态，是否启用),roleName(角色),authedData(权限),roleNameSort(角色名称排序),roleTypeSort(使用端排序),updateTimeSort(更新时间排序)】")
    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<TManagementSysAccount> list(@RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<AppUser> entities = managementSysRoleAuthService.listAuth(criteria);
        PageResponse<AppUser> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(entities);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiOperation(value = "新增角色权限")
    @ApiLog(storage = true, storageDescription = "新增角色权限")
    @RequestMapping(value = "/create", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'SYSTEMAUTHORITY_INSERT')")
    @ResponseBody
    public InternalResponse create(@Valid @RequestBody TManagementSysRole tManagementSysRole) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysRoleAuthService.create(currentUser.getIdentifier(), tManagementSysRole.adaptRoleMenu());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "修改角色权限")
    @ApiLog(storage = true, storageDescription = "修改角色权限")
    @RequestMapping(value = "/modify", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'SYSTEMAUTHORITY_UPDATE')")
    @ResponseBody
    public InternalResponse modify(@Valid @RequestBody TManagementSysRole tManagementSysRole) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysRoleAuthService.modify(currentUser.getIdentifier(), tManagementSysRole.adaptRoleMenu());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "删除角色权限")
    @ApiLog(storage = true, storageDescription = "删除角色权限")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @PreAuthorize("hasPermission(null,'SYSTEMAUTHORITY_DELETE')")
    @ResponseBody
    public InternalResponse delete(@RequestParam("id") Integer id) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysRoleAuthService.delete(currentUser.getIdentifier(), id);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "批量删除角色权限")
    @ApiLog(storage = true, storageDescription = "批量删除角色权限")
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.DELETE)
    @ResponseBody
    public InternalResponse deleteBatch(@RequestParam("roleIds") Integer[] roleIds) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysRoleAuthService.deleteBatch(currentUser.getIdentifier(), roleIds);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "patch角色权限表数据")
    @ApiLog(storage = true, storageDescription = "patch角色权限表数据")
    @RequestMapping(value = "/patch", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse patch(@RequestParam("patchId") Integer patchId) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysRoleAuthService.patch(currentUser.getIdentifier(), patchId);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

}
