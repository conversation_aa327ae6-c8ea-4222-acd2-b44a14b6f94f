package heran.media.management.platform.system.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.MainMediaCbdList;
import heran.media.management.platform.main.subdomain.dto.UserConfigResponse;
import heran.media.management.platform.system.service.ManagementSysAccountService;
import heran.media.management.platform.system.subdomain.dto.AppUser;
import heran.media.management.platform.system.subdomain.dto.TAccountPasswordChangeReq;
import heran.media.management.platform.system.subdomain.dto.TAccountStatusChangedReq;
import heran.media.management.platform.system.subdomain.dto.TManagementSysAccount;
import heran.media.management.platform.system.subdomain.dto.TPwdChangeReq;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.LinkedList;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/management-sys-account")
public class ManagementSysAccountController {

    private final ManagementSysAccountService managementSysAccountService;


    public ManagementSysAccountController(ManagementSysAccountService managementSysAccountService) {
        this.managementSysAccountService = managementSysAccountService;
    }

    @ApiLog(storage = true, storageDescription = "修改当前用户个人密码")
    @ApiOperation(value = "修改当前用户个人密码")
    @RequestMapping(value = "/change-password", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse changePassword(@Valid @RequestBody TPwdChangeReq TPwdChangeReq) {
        InternalResponse<String> internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysAccountService.changePassword(currentUser, TPwdChangeReq.getOldPassword(), TPwdChangeReq.getNewPassword());
        return internalResponse;
    }

    @ApiLog(storageDescription = "获取当前操作的用户信息")
    @ApiOperation(value = "获取当前操作的用户信息")
    @RequestMapping(value = "/get-user-info", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<TManagementSysAccount> getCurrentUserInfo() {
        InternalResponse<TManagementSysAccount> internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        SearchCriteria criteria = new SearchCriteria();
        criteria.setCriterias(new LinkedList<>());
        SearchCriteria.Criteria idCri = new SearchCriteria.Criteria();
        idCri.setKey("id");
        idCri.setValue(currentUser.getIdentifier().toString());
        criteria.getCriterias().add(idCri);
        List<AppUser> managementSysAccounts = managementSysAccountService.list(criteria);
        if (managementSysAccounts.size() > 0) {
            TManagementSysAccount account = new TManagementSysAccount();
            account.setUserName(managementSysAccounts.get(0).getAccount().getUserName());
            internalResponse.withBody(account);
        } else {
            log.warn("Can not find current user info");
        }
        return internalResponse;
    }

    @ApiLog(storageDescription = "用户列表查询")
    @ApiOperation(value = "用户列表查询", notes = "查询字段可包含【userName(姓名),nickName(昵称),isEnabled(状态，是否启用),roleName(角色),platform(使用端),userNameSort(用户名称排序),platformSort(使用端排序),roleNameSort(角色名称排序),validityEndTimeSort(有效期排序),isEnabledSort(是否启动排序),updateTimeSort(修改时间排序)】")
    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<TManagementSysAccount> list(@RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<AppUser> entities = managementSysAccountService.list(criteria);
        PageResponse<AppUser> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(entities);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiOperation(value = "新建用户")
    @ApiLog(storage = true, storageDescription = "新建账号")
    @RequestMapping(value = "/create", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'SYSTEMUSER_INSERT')")
    @ResponseBody
    public InternalResponse create(@Valid @RequestBody TManagementSysAccount managementSysAccount) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysAccountService.create(currentUser.getIdentifier(), managementSysAccount.adaptToAppUser(), managementSysAccount.getAccountApiMapQuotas());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "修改用户")
    @ApiLog(storage = true, storageDescription = "修改账号")
    @RequestMapping(value = "/modify", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'SYSTEMUSER_UPDATE')")
    @ResponseBody
    public InternalResponse modify(@Valid @RequestBody TManagementSysAccount managementSysAccount) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysAccountService.modify(currentUser.getIdentifier(), managementSysAccount.adaptToAppUser(), managementSysAccount.getAccountApiMapQuotas());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "删除账号")
    @ApiLog(storage = true, storageDescription = "删除账号")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @PreAuthorize("hasPermission(null,'SYSTEMUSER_DELETE')")
    @ResponseBody
    public InternalResponse delete(@RequestParam("id") Integer id) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysAccountService.delete(currentUser.getIdentifier(), id);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }


    @ApiLog(storage = true, storageDescription = "重置用户账号密码", printOutputs = false)
    @ApiOperation(value = "重置用户账号密码")
    @RequestMapping(value = "/reset-password", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse changePassword(@Valid @RequestBody TAccountPasswordChangeReq pwdChangeReq) {
        InternalResponse<String> internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysAccountService.changePassword(currentUser, pwdChangeReq.getUserKey(), pwdChangeReq.getNewPassword());
        return internalResponse;
    }


    @ApiLog(storage = true, storageDescription = "启用禁用账号", printOutputs = false)
    @ApiOperation(value = "账号启用与禁用")
    @RequestMapping(value = "/status-change", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse statusChange(@Valid @RequestBody TAccountStatusChangedReq req) {
        InternalResponse<String> internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysAccountService.updateUserStatus(currentUser, req.getUserKey(), req.getStatus());
        return internalResponse;
    }

    @ApiOperation(value = "批量删除用户")
    @ApiLog(storage = true, storageDescription = "批量删除用户")
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.DELETE)
    @ResponseBody
    public InternalResponse deleteBatch(@RequestParam("ids") Integer[] ids) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        managementSysAccountService.deleteBatch(currentUser.getIdentifier(), ids);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "获取用户配置")
    @ApiLog(storage = true, storageDescription = "获取用户配置")
    @RequestMapping(value = "/getUserConfig", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<UserConfigResponse> getUserConfig(@RequestParam("accountId") Integer accountId) {
        InternalResponse<UserConfigResponse> internalResponse = InternalResponse.success();
        UserConfigResponse userConfigResponse = managementSysAccountService.getUserConfig(accountId);
        internalResponse.withBody(userConfigResponse);
        return internalResponse;

    }


}
