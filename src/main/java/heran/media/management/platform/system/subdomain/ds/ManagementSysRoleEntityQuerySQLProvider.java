package heran.media.management.platform.system.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.jdbc.SQL;
import org.apache.poi.util.StringUtil;

public class ManagementSysRoleEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("management_sys_role");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (cri.getKey().equals("id")) {
                        WHERE("id = " + cri.getValue());
                    } else if (cri.getKey().equals("roleName") && cri.getValue() != null) {
                        WHERE("role_name like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("roleType") && cri.getValue() != null) {
                        WHERE("role_type like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("isEnabled") && cri.getValue() != null) {
                        WHERE("is_enabled = '" + cri.getValue() + "'");
                    } else if (cri.getKey().equals("canDelete") && cri.getValue() != null) {
                        WHERE("can_delete = '" + cri.getValue() + "'");
                    } else if (cri.getKey().equals("isDeleted") && cri.getValue() != null) {
                        WHERE("is_deleted = '" + cri.getValue() + "'");
                    } else if (cri.getKey().equals("roleCode") && cri.getValue() != null) {
                        WHERE("role_code = '" + cri.getValue() + "'");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }

                }
            }
            ;
            ORDER_BY("update_time desc");
        }}.toString();
    }

    public String findAll(SearchCriteria criteria) {
        return new SQL() {
            {
                SELECT("r.*");
                FROM("management_sys_role r");
                LEFT_OUTER_JOIN("management_sys_role_auth ra on r.id=ra.role_id");

                String orderSql = null;

                if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                    String autherlData = "";
                    String isEnable = "";
                    String isDeleted = "";
                    String roleName = "";
                    String roleType = "";
                    String roleNameSort = "";
                    String roleTypeSort = "";
                    String updateTimeSort = "";
                    for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                        if ("authedData".equals(cri.getKey())) {
                            autherlData = cri.getValue();
                        } else if ("isEnabled".equals(cri.getKey())) {
                            isEnable = cri.getValue();
                        } else if ("isDeleted".equals(cri.getKey())) {
                            isDeleted = cri.getValue();
                        } else if ("roleName".equals(cri.getKey())) {
                            roleName = cri.getValue();
                        } else if ("roleType".equals(cri.getKey())) {
                            roleType = cri.getValue();
                        } else if ("roleNameSort".equals(cri.getKey())) {
                            roleNameSort = cri.getValue();
                        } else if ("roleTypeSort".equals(cri.getKey())) {
                            roleTypeSort = cri.getValue();
                        } else if ("updateTimeSort".equals(cri.getKey())) {
                            updateTimeSort = cri.getValue();
                        }
                    }
                    if (StringUtils.isNotBlank(autherlData) && StringUtils.isNotBlank(isEnable) && StringUtils.isNotBlank(roleName)) {
                        String[] roleNames = roleName.split(",");
                        StringBuilder roleNameBuilder = new StringBuilder();
                        for (int i = 0; i < roleNames.length; i++) {
                            if (i > 0) {
                                roleNameBuilder.append(", ");
                            }
                            roleNameBuilder.append("'").append(roleNames[i].trim()).append("'");
                        }
                        String[] authedDataValues = autherlData.split("\\|\\|");
                        for (int i = 0; i < authedDataValues.length; i++) {
                            WHERE("(CONCAT('||', ra.authed_data, '||') LIKE CONCAT('%||" + authedDataValues[i] + "||%') and r.is_deleted = " + isDeleted + " and r.role_name IN (" + roleNameBuilder.toString() + ")" + "and r.is_enabled =" + isEnable + ")");
                            if (i < authedDataValues.length - 1) {
                                OR();
                            }
                        }
                    } else if (StringUtils.isNotBlank(autherlData) && StringUtils.isNotBlank(isEnable)) {
                        String[] authedDataValues = autherlData.split("\\|\\|");
                        for (int i = 0; i < authedDataValues.length; i++) {
                            WHERE("(CONCAT('||', ra.authed_data, '||') LIKE CONCAT('%||" + authedDataValues[i] + "||%') and r.is_enabled = " + isEnable + ")");
                            if (i < authedDataValues.length - 1) {
                                OR();
                            }
                        }
                    } else if (StringUtils.isNotBlank(autherlData) && StringUtils.isNotBlank(roleName)) {
                        String[] roleNames = roleName.split(",");
                        StringBuilder roleNameBuilder = new StringBuilder();
                        for (int i = 0; i < roleNames.length; i++) {
                            if (i > 0) {
                                roleNameBuilder.append(", ");
                            }
                            roleNameBuilder.append("'").append(roleNames[i].trim()).append("'");
                        }
                        String[] authedDataValues = autherlData.split("\\|\\|");
                        for (int i = 0; i < authedDataValues.length; i++) {
                            WHERE("(CONCAT('||', ra.authed_data, '||') LIKE CONCAT('%||" + authedDataValues[i] + "||%') and r.is_deleted = " + isDeleted + " and r.role_name IN (" + roleNameBuilder.toString() + "))");
                            if (i < authedDataValues.length - 1) {
                                OR();
                            }
                        }
                    } else if (StringUtils.isNotBlank(autherlData) && StringUtils.isNotBlank(isDeleted)) {
                        String[] authedDataValues = autherlData.split("\\|\\|");
                        for (int i = 0; i < authedDataValues.length; i++) {
                            WHERE("(CONCAT('||', ra.authed_data, '||') LIKE CONCAT('%||" + authedDataValues[i] + "||%') and r.is_deleted = " + isDeleted + ")");
                            if (i < authedDataValues.length - 1) {
                                OR();
                            }
                        }

                    } else if (StringUtils.isNotBlank(roleName) && StringUtils.isNotBlank(isEnable)) {
                        String[] roleNames = roleName.split(",");
                        StringBuilder roleNameBuilder = new StringBuilder();
                        for (int i = 0; i < roleNames.length; i++) {
                            if (i > 0) {
                                roleNameBuilder.append(", ");
                            }
                            roleNameBuilder.append("'").append(roleNames[i].trim()).append("'");
                        }
                        WHERE("r.role_name IN (" + roleNameBuilder.toString() + ") and r.is_deleted =" + isDeleted + " and r.is_enabled =" + isEnable);
                    } else if (StringUtils.isNotBlank(isEnable) && StringUtils.isNotBlank(isDeleted)) {
                        WHERE("r.is_enabled = '" + isEnable + "' and r.is_deleted =" + isDeleted);
                    } else if (StringUtils.isNotBlank(roleType)) {
                        WHERE("r.role_type = '" + roleType + "'");
                    } else if (StringUtils.isNotBlank(roleName) && StringUtils.isNotBlank(isDeleted)) {
                        String[] roleNames = roleName.split(",");
                        StringBuilder roleNameBuilder = new StringBuilder();
                        for (int i = 0; i < roleNames.length; i++) {
                            if (i > 0) {
                                roleNameBuilder.append(", ");
                            }
                            roleNameBuilder.append("'").append(roleNames[i].trim()).append("'");
                        }
                        WHERE("r.role_name IN (" + roleNameBuilder.toString() + ") and r.is_deleted =" + isDeleted);
                    } else if (StringUtils.isNotBlank(isDeleted)) {
                        WHERE("r.is_deleted =" + isDeleted);
                    }

                    if (StringUtils.isNotBlank(roleNameSort)) {
                        orderSql = "r.role_name " + roleNameSort;
                    } else if (StringUtils.isNotBlank(roleTypeSort)) {
                        orderSql = "r.role_type " + roleTypeSort;
                    } else if (StringUtils.isNotBlank(updateTimeSort)) {
                        orderSql = "r.update_time " + updateTimeSort;
                    }


                    if (StringUtils.isEmpty(orderSql)) {
                        ORDER_BY("r.create_time desc");
                    } else {
                        ORDER_BY(orderSql);
                    }
                }
            }
        }.toString();
    }

}
