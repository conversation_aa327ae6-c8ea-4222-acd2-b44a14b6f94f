package heran.media.management.platform.system.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.ManagementSysMenu;
import heran.media.sharelib.domain.db.model.ManagementSysRole;
import heran.media.sharelib.domain.dto.role.DataRoleRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TManagementSysRole {

    @ApiModelProperty(value = "主键自增id")
    private Integer id;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色类型，0（管理平台），1（AI供应链），2（AI伙伴）")
    private String roleType;

    @ApiModelProperty(value = "是否启用")
    private Boolean isEnabled;

    @ApiModelProperty(value = "能否删除，系统内置角色不可删除")
    private Boolean canDelete;

    @ApiModelProperty(value = "角色编码")
    private String roleCode;

    @ApiModelProperty(value = "行记录创建者")
    private String creator;

    @ApiModelProperty(value = "行记录更新者")
    private String updator;

    @ApiModelProperty(value = "行记录创建时间")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "存放菜单的menuCode")
    private List<String> menuCodes;

    @ApiModelProperty(value = "存放按钮的menuCode")
    private List<String> buttonCodes;

    @ApiModelProperty(value = "存放c端数据权限")
    private DataRoleRequest dataRole;

    private String authedData;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "有效开始时间")
    private Date validityStartTime;
    @ApiModelProperty(value = "有效结束时间")
    private Date validityEndTime;

    @ApiModelProperty(value = "数智透视：AOI数据库 权限有无改动")
    private Boolean isCityAoiUpdate;
    @ApiModelProperty(value = "数智人群洞察: 数智TA 权限有无改动")
    private Boolean isTaAoiUpdate;
    @ApiModelProperty(value = "营销场景洞察：商圈洞察")
    private Boolean isCbdAoiUpdate;
    @ApiModelProperty(value = "营销场景洞察：特定场景洞察 权限有无改动")
    private Boolean isInsightAoiUpdate;

    public TManagementSysRole() {
    }

    public TManagementSysRole(ManagementSysRole managementSysRole) {
        this.id = managementSysRole.getId();
        this.roleName = managementSysRole.getRoleName();
        this.roleType = managementSysRole.getRoleType();
        this.isEnabled = managementSysRole.getIsEnabled();
        this.canDelete = managementSysRole.getCanDelete();
        this.creator = managementSysRole.getCreator();
        this.updator = managementSysRole.getUpdater();
        this.createTime = managementSysRole.getCreateTime();
        this.updateTime = managementSysRole.getUpdateTime();
        this.roleCode = managementSysRole.getRoleCode();
        this.remark = managementSysRole.getRemark();
        this.validityStartTime = managementSysRole.getValidityStartTime();
        this.validityEndTime = managementSysRole.getValidityEndTime();

    }

    public ManagementSysRole adapToPO() {
        ManagementSysRole managementSysRole = new ManagementSysRole();
        managementSysRole.setId(id);
        managementSysRole.setRoleName(roleName);
        managementSysRole.setRoleType(roleType);
        managementSysRole.setIsEnabled(isEnabled);
        managementSysRole.setCanDelete(canDelete);
        managementSysRole.setCreator(creator);
        managementSysRole.setUpdater(updator);
        managementSysRole.setRoleCode(roleCode);
        managementSysRole.setCreateTime(createTime);
        managementSysRole.setUpdateTime(updateTime);
        managementSysRole.setRemark(remark);
        managementSysRole.setValidityStartTime(validityStartTime);
        managementSysRole.setValidityEndTime(validityEndTime);

        return managementSysRole;
    }

    public TRoleAuthResp adaptRoleMenu() {
        ManagementSysRole managementSysRole = new ManagementSysRole();
        managementSysRole.setId(id);
        managementSysRole.setRoleName(roleName);
        managementSysRole.setRoleType(roleType);
        managementSysRole.setIsEnabled(isEnabled);
        managementSysRole.setCanDelete(canDelete);
        managementSysRole.setRoleCode(roleCode);
        managementSysRole.setCreator(creator);
        managementSysRole.setUpdater(updator);
        managementSysRole.setCreateTime(createTime);
        managementSysRole.setUpdateTime(updateTime);
        managementSysRole.setRemark(remark);
        managementSysRole.setValidityStartTime(validityStartTime);
        managementSysRole.setValidityEndTime(validityEndTime);

        List<ManagementSysMenu> menuCodeList = new LinkedList<>();
        List<ManagementSysMenu> buttonCodeList = new LinkedList<>();
        if (this.menuCodes != null) {
            for (String menuCode : this.menuCodes) {
                ManagementSysMenu sysMenu = new ManagementSysMenu();
                sysMenu.setMenuCode(menuCode);
                menuCodeList.add(sysMenu);
            }
        }
        if (this.buttonCodes != null) {
            for (String buttonCode : this.buttonCodes) {
                ManagementSysMenu menu = new ManagementSysMenu();
                menu.setMenuCode(buttonCode);
                buttonCodeList.add(menu);
            }
        }

        return new TRoleAuthResp(managementSysRole, menuCodeList, buttonCodeList, dataRole,
                isCityAoiUpdate, isTaAoiUpdate,
                isCbdAoiUpdate, isInsightAoiUpdate);
    }
}