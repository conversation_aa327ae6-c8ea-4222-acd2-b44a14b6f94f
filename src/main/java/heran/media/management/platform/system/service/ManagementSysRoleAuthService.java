package heran.media.management.platform.system.service;

import com.google.gson.Gson;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.system.errors.NoSuchUserException;
import heran.media.management.platform.system.errors.NotExitsException;
import heran.media.management.platform.system.errors.RoleNameExitsException;
import heran.media.management.platform.system.errors.RoleNameIsToLongException;
import heran.media.management.platform.system.subdomain.db.CommonMapper;
import heran.media.management.platform.system.subdomain.ds.AuthorityBizMapper;
import heran.media.management.platform.system.subdomain.ds.ManagementSysRoleAuthEntityQueryMapper;
import heran.media.management.platform.system.subdomain.ds.ManagementSysRoleEntityQueryMapper;
import heran.media.management.platform.system.subdomain.dto.AppUser;
import heran.media.management.platform.system.subdomain.dto.TAuthFunctions;
import heran.media.management.platform.system.subdomain.dto.TRoleAuthResp;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.db.mapper.*;
import heran.media.sharelib.domain.db.model.*;
import heran.media.sharelib.domain.dto.role.CityAoiData;
import heran.media.sharelib.domain.dto.role.CityCbdInsightData;
import heran.media.sharelib.domain.dto.role.CityNumeracyTaData;
import heran.media.sharelib.domain.dto.role.DataRoleRequest;
import heran.media.sharelib.errors.CustomerError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Service
@RefreshScope
public class ManagementSysRoleAuthService {

    @Resource
    private ManagementSysRoleAuthEntityQueryMapper managementSysRoleAuthEntityQueryMapper;
    @Resource
    private ManagementSysRoleAuthMapper managementSysRoleAuthMapper;
    @Resource
    private ManagementSysRoleEntityQueryMapper managementSysRoleEntityQueryMapper;
    @Resource
    private AuthorityBizMapper authorityBizMapper;
    @Resource
    private ManagementSysRoleMapper managementSysRoleMapper;
    @Resource
    private ManagementSysMenuMapper managementSysMenuMapper;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private ManagementSysRoleAoiAuthMapper managementSysRoleAoiAuthMapper;
    @Resource
    private ManagementSysRoleAoiAuthGroupMapper managementSysRoleAoiAuthGroupMapper;

    public ManagementSysRoleAuthService() {
    }

    public List<ManagementSysRoleAuth> list(SearchCriteria criteria) {
        return managementSysRoleAuthEntityQueryMapper.list(criteria);
    }

    public ManagementSysRoleAuth getById(Integer id) {
        return commonMapper.getRoleIdByAuth(id);
    }

    public void authFunctions(AuthUserInfo currentUser, Integer roleId, List<TAuthFunctions> authedFunctions) {
        for (TAuthFunctions functions : authedFunctions) {
            if (StringUtils.isNotBlank(functions.getRelatedIds())) {
                ManagementSysRoleAuth roleAuth = new ManagementSysRoleAuth();
                roleAuth.setRoleId(roleId);
                roleAuth.setChannel("ADMIN");
                roleAuth.setAuthType("MENU");
                roleAuth.setCreator(String.valueOf(currentUser.getIdentifier()));
                roleAuth.setUpdater(String.valueOf(currentUser.getIdentifier()));
                roleAuth.setCreatedByUser(currentUser.getIdentifier());
                roleAuth.setUpdatedByUser(currentUser.getIdentifier());

                Boolean isWildCard = false;
                String[] ids = functions.getRelatedIds().split(",");
                try {
                    for (String id : ids) {
                        if (!id.equals("*")) {
                            Integer.parseInt(id);
                        } else {
                            isWildCard = true;
                        }
                    }
                } catch (Exception e) {
                    throw new CustomerError("Not supported values");
                }
                roleAuth.setAuthedData(isWildCard ? "*" : functions.getRelatedIds().replace(",", "||"));
                managementSysRoleAuthMapper.insertUpdateEntity(roleAuth);
            } else {
                authorityBizMapper.deleteRoleAuthByAuthTypeChannelRoleId("MENU", "ADMIN", roleId);
            }
        }
    }


    public List<ManagementSysRoleAuth> listAuthedFunctions(Integer roleKey) {
        return authorityBizMapper.getManagementSysRoleAuthByRoleAndTypeAndChannel(roleKey);
    }

    public List<AppUser> listAuth(SearchCriteria criteria) {
        SearchCriteria.Criteria notDeletedCriteria = new SearchCriteria.Criteria();
        notDeletedCriteria.setKey("isDeleted");
        notDeletedCriteria.setValue("0");
        criteria.getCriterias().add(notDeletedCriteria);
//      if (criteria.getCriterias().size() == 0){
//          SearchCriteria.Criteria notDeletedCriteria = new SearchCriteria.Criteria();
//          notDeletedCriteria.setValue(null);
//          criteria.getCriterias().add(notDeletedCriteria);
//      }
//        String value = criteria.getCriterias().get(0).getValue();
//        String  key  = criteria.getCriterias().get(0).getKey();
        Map<Integer, AppUser> userMap = new HashMap<>();
        List<ManagementSysRole> lists = managementSysRoleEntityQueryMapper.listSysRoleMenuAuth(criteria);
        List<ManagementSysMenu> menuList = managementSysMenuMapper.getAll();
        Map<String, ManagementSysMenu> menuMap = menuList.stream().collect(Collectors.toMap(ManagementSysMenu::getMenuCode, Function.identity()));
        for (ManagementSysRole role : lists) {
            if (!userMap.containsKey(role.getId())) {
                userMap.put(role.getId(), new AppUser(role, new HashSet<>()));
            }
            AppUser appUser = userMap.get(role.getId());
            List<String> managementSysRoleAuthByRoles = commonMapper.getManagementSysRoleAuthByRoleId(role.getId());
            for (String managementSysRoleAuthByRole : managementSysRoleAuthByRoles) {
                if (managementSysRoleAuthByRole != null) {
                    String[] authDataArray = managementSysRoleAuthByRole.split("\\|\\|");
                    for (String key : authDataArray) {
                        ManagementSysMenu menu = menuMap.get(key);
                        if (menu != null) {
                            appUser.getFunctionList().add(menu);
                        }
                    }
                }
            }

        }

        List<AppUser> appUserList = new LinkedList<>(userMap.values());
        appUserList.sort(((o1, o2) -> o1.getRole().getCreateTime().before(o2.getRole().getCreateTime()) ? 1 : -1));
        return appUserList;
    }

    public Integer getRole(ManagementSysRole role, Integer identifier) {
        String code = UUID.randomUUID().toString().replace("-", "");
        role.setId(null);
        role.setRoleName(role.getRoleName());
        role.setRoleType(role.getRoleType());
        role.setIsDeleted(false);
        role.setCanDelete(true);
        role.setRoleCode(code);
        role.setIsEnabled(role.getIsEnabled());
        role.setCreatedByUser(identifier);
        role.setUpdatedByUser(identifier);
        role.setCreator(identifier.toString());
        role.setUpdater(identifier.toString());
        role.setValidityStartTime(role.getValidityStartTime());
        role.setValidityEndTime(role.getValidityEndTime());
        if (role.getRoleName().length() > 64) {
            throw new RoleNameIsToLongException();
        }
        if (commonMapper.getByRoleName(role.getRoleName()) != null) {
            throw new RoleNameExitsException();
        }
        managementSysRoleMapper.insertIgnoreEntity(role);
        return role.getId();
    }

    public void getMenuCodes(List<ManagementSysMenu> menuList, Integer roleId, Integer identifier, String authType) {
        ManagementSysRoleAuth roleAuth = new ManagementSysRoleAuth();
        StringBuilder stringBuilder = new StringBuilder();
        for (ManagementSysMenu sysMenu : menuList) {
            roleAuth.setRoleId(roleId);
            roleAuth.setChannel("ADMIN");
            roleAuth.setAuthType(authType);
            roleAuth.setAuthedData(sysMenu.getMenuCode().toString());
            roleAuth.setCreator(identifier.toString());
            roleAuth.setUpdater(identifier.toString());
            roleAuth.setCreatedByUser(identifier);
            roleAuth.setUpdatedByUser(identifier);
            stringBuilder.append(sysMenu.getMenuCode()).append(",");
        }
        if (stringBuilder.length() > 0) {
            String authDate = stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString();
            roleAuth.setAuthedData(authDate.replace(",", "||"));
        }
        if (StringUtils.isNotEmpty(roleAuth.getAuthType()) && !"ROLE_DATA_C".equals(roleAuth.getAuthType())) {
            managementSysRoleAuthMapper.insertIgnoreEntity(roleAuth);
        }
    }

    public void saveRoleData(TRoleAuthResp resp, Integer roleId, Integer userKey, String authType) {
        DataRoleRequest request = resp.getDataRole();
        if (request == null) {
            return;
        }

        saveManagementSysRoleAoi(roleId, userKey, request, resp);

        ManagementSysRoleAuth roleAuth = new ManagementSysRoleAuth();
        roleAuth.setRoleId(roleId);
        roleAuth.setChannel("CONTENT");
        roleAuth.setAuthType(authType);
        roleAuth.setAuthedData(new Gson().toJson(request));
        roleAuth.setCreator(userKey.toString());
        roleAuth.setUpdater(userKey.toString());
        roleAuth.setCreatedByUser(userKey);
        roleAuth.setUpdatedByUser(userKey);
        managementSysRoleAuthMapper.insertIgnoreEntity(roleAuth);
    }


    /**
     * 特殊处理下AOI 数据 因为数据量过大
     *
     * @param request DataRoleRequest
     */
    public void saveManagementSysRoleAoi(Integer roleId, Integer userKey, DataRoleRequest request, TRoleAuthResp resp) {
        deleteRoleData(resp);
        Integer id = resp.getRole().getId();
        // 数智透视：AOI数据库
        if (resp.getIsCityAoiUpdate() != null && resp.getIsCityAoiUpdate()) {
            managementSysRoleAoiAuthGroupMapper.deleteByRoleTarget(id, "CITY_AOI");
            managementSysRoleAoiAuthMapper.deleteByRoleTarget(id, "CITY_AOI");
            handleAoiAuth(
                    roleId,
                    userKey,
                    "CITY_AOI",
                    request.getDigitalAssetRoleData().getAoiDataBaseData().getCityAoiData(),
                    CityAoiData::getRegionCodes,
                    CityAoiData::getAoiIds,
                    (data) -> data.setAoiIds(new ArrayList<>()),
                    (list) -> request.getDigitalAssetRoleData().getAoiDataBaseData().setCityAoiData(list),
                    CityAoiData::setGroupKey
            );
        }

        // 数智人群洞察: 数智TA
        if (resp.getIsTaAoiUpdate() != null && resp.getIsTaAoiUpdate()) {
            managementSysRoleAoiAuthGroupMapper.deleteByRoleTarget(id, "TA_AOI");
            managementSysRoleAoiAuthMapper.deleteByRoleTarget(id, "TA_AOI");
            handleAoiAuth(
                    roleId,
                    userKey,
                    "TA_AOI",
                    request.getSmartMarketingData().getNumeracyTaData().getCityNumeracyTaData(),
                    CityNumeracyTaData::getRegionCodes,
                    CityNumeracyTaData::getAoiIds,
                    (data) -> data.setAoiIds(new ArrayList<>()),
                    (list) -> request.getSmartMarketingData().getNumeracyTaData().setCityNumeracyTaData(list),
                    CityNumeracyTaData::setGroupKey
            );
        }

        // 营销场景洞察：商圈洞察
        if (resp.getIsCbdAoiUpdate() != null && resp.getIsCbdAoiUpdate()) {
            managementSysRoleAoiAuthGroupMapper.deleteByRoleTarget(id, "CBD_AOI");
            managementSysRoleAoiAuthMapper.deleteByRoleTarget(id, "CBD_AOI");
            handleAoiAuth(
                    roleId,
                    userKey,
                    "CBD_AOI",
                    request.getSmartMarketingData().getCbdInsightData().getCityCbdInsightData(),
                    CityCbdInsightData::getRegionCodes,
                    CityCbdInsightData::getCbdIds,
                    (data) -> data.setCbdIds(new ArrayList<>()),
                    (list) -> request.getSmartMarketingData().getCbdInsightData().setCityCbdInsightData(list),
                    CityCbdInsightData::setGroupKey
            );
        }

        // 营销场景洞察：特定场景洞察
        if (resp.getIsInsightAoiUpdate() != null && resp.getIsInsightAoiUpdate()) {
            managementSysRoleAoiAuthGroupMapper.deleteByRoleTarget(id, "INSIGHT_AOI");
            managementSysRoleAoiAuthMapper.deleteByRoleTarget(id, "INSIGHT_AOI");
            handleAoiAuth(
                    roleId,
                    userKey,
                    "INSIGHT_AOI",
                    request.getSmartMarketingData().getSpecificContext().getCityCbdInsightData(),
                    CityCbdInsightData::getRegionCodes,
                    CityCbdInsightData::getCbdIds,
                    (data) -> data.setCbdIds(new ArrayList<>()),
                    (list) -> request.getSmartMarketingData().getSpecificContext().setCityCbdInsightData(list),
                    CityCbdInsightData::setGroupKey
            );
        }

    }

    /**
     * 通用处理 AOI 授权逻辑
     */
    private <T> void handleAoiAuth(Integer roleId,
                                   Integer userKey,
                                   String type,
                                   List<T> dataList,
                                   Function<T, List<String>> regionGetter,
                                   Function<T, List<Integer>> aoiGetter,
                                   Consumer<T> clearAoiIds,
                                   Consumer<List<T>> saveBack,
                                   BiConsumer<T, String> groupKeySetter) {

        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        List<T> resultList = new ArrayList<>();

        for (T data : dataList) {
            ManagementSysRoleAoiAuthGroup authGroup =
                    buildManagementSysRoleAoiAuthGroup(userKey, roleId, type, regionGetter.apply(data));
            managementSysRoleAoiAuthGroupMapper.insertIgnoreEntity(authGroup);

            List<Integer> aoiIds = aoiGetter.apply(data);
            if (aoiIds.contains(0)) {
                cityAngleCityAoiData(regionGetter.apply(data), userKey, roleId, type, authGroup.getId());
            } else {
                aoiAngleCityAoiData(aoiIds, userKey, roleId, type, authGroup.getId());
            }

            groupKeySetter.accept(data, authGroup.getGroupingName());

            clearAoiIds.accept(data);
            resultList.add(data);
        }
        // 回写处理后的数据
        saveBack.accept(resultList);
    }

    private ManagementSysRoleAoiAuth buildManagementSysRoleAoiAuth(Integer userKey, Integer aoiId, Integer roleId, String regionCode, String authTarget, Long dataGrouping) {
        ManagementSysRoleAoiAuth aoiAuth = new ManagementSysRoleAoiAuth();
        aoiAuth.setAoiId(aoiId);
        aoiAuth.setRoleId(roleId);
        aoiAuth.setRegionCode(regionCode);
        aoiAuth.setAuthTarget(authTarget);
        aoiAuth.setDataGrouping(dataGrouping);
        aoiAuth.setCreator(userKey.toString());
        aoiAuth.setUpdater(userKey.toString());
        aoiAuth.setCreatedByUser(userKey);
        aoiAuth.setUpdatedByUser(userKey);
        return aoiAuth;
    }

    private ManagementSysRoleAoiAuthGroup buildManagementSysRoleAoiAuthGroup(Integer userKey, Integer roleId, String authTarget, List<String> regionCodeList) {
        ManagementSysRoleAoiAuthGroup authGroup = new ManagementSysRoleAoiAuthGroup();
        authGroup.setRoleId(roleId);
        authGroup.setGroupingName(String.valueOf(System.currentTimeMillis()));
        authGroup.setRegionArray(new Gson().toJson(regionCodeList));
        authGroup.setAuthTarget(authTarget);
        authGroup.setCreator(userKey.toString());
        authGroup.setUpdater(userKey.toString());
        authGroup.setCreatedByUser(userKey);
        authGroup.setUpdatedByUser(userKey);
        return authGroup;
    }

    /**
     * 以城市为维度维护AOI权限数据
     *
     * @param regionCodeList regionCodeList
     * @param userKey        用户id
     * @param roleId         roleId
     * @param authTarget     数据目标
     * @param dataGrouping   分组id
     */
    private void cityAngleCityAoiData(List<String> regionCodeList, Integer userKey, Integer roleId, String authTarget, Long dataGrouping) {
        //城市为维度 说明当前AOIid 是全部的
        List<ManagementSysRoleAoiAuth> aoiAuths = new ArrayList<>();
        for (String regionCode : regionCodeList) {
            //因为以城市为维度去插入的数据的话都aoiId 都是-1
            ManagementSysRoleAoiAuth managementSysRoleAoiAuth = buildManagementSysRoleAoiAuth(userKey, 0, roleId, regionCode, authTarget, dataGrouping);
            aoiAuths.add(managementSysRoleAoiAuth);
        }
        if (CollectionUtils.isNotEmpty(aoiAuths)) {
            managementSysRoleAoiAuthMapper.batchInsertIgnoreEntity(aoiAuths);
        }
    }

    /**
     * 以AOI为维度维护AOI权限数据
     *
     * @param aoiIds       aoiIds
     * @param userKey      用户id
     * @param roleId       角色id
     * @param authTarget   权限目标
     * @param dataGrouping 分组id
     */
    private void aoiAngleCityAoiData(List<Integer> aoiIds, Integer userKey, Integer roleId, String authTarget, Long dataGrouping) {
        //aoi为维度 所传的城市不重要了 到时候查询的时候还是要根据AOI表中的城市去控制数据
        List<ManagementSysRoleAoiAuth> aoiAuths = new ArrayList<>();
        for (Integer aoiId : aoiIds) {
            //城市不中要就给个code
            ManagementSysRoleAoiAuth managementSysRoleAoiAuth = buildManagementSysRoleAoiAuth(userKey, aoiId, roleId, "CODE", authTarget, dataGrouping);
            aoiAuths.add(managementSysRoleAoiAuth);
        }
        //绑定的AOI有概率会很多 分批处理
        saveManagementSysRoleAoiAuth(aoiAuths);
    }


    private void saveManagementSysRoleAoiAuth(List<ManagementSysRoleAoiAuth> authList) {
        if (CollectionUtils.isNotEmpty(authList)) {
            // 分批大小，可以根据实际情况调整
            int batchSize = 1000;
            // 分批处理
            for (int i = 0; i < authList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, authList.size());
                List<ManagementSysRoleAoiAuth> batch = authList.subList(i, endIndex);
                // 执行批量插入
                managementSysRoleAoiAuthMapper.batchInsertIgnoreEntity(batch);
                // 可选：添加日志记录进度
                log.info("批量插入进度: {}/{}", endIndex, authList.size());
            }
        }
    }

    private void deleteRoleData(TRoleAuthResp resp) {
        Integer id = resp.getRole().getId();
        if (resp.getIsCityAoiUpdate() != null && resp.getIsCityAoiUpdate()) {
            managementSysRoleAoiAuthGroupMapper.deleteByRoleTarget(id, "CITY_AOI");
            managementSysRoleAoiAuthMapper.deleteByRoleTarget(id, "CITY_AOI");
        }
        if (resp.getIsTaAoiUpdate() != null && resp.getIsTaAoiUpdate()) {
            managementSysRoleAoiAuthGroupMapper.deleteByRoleTarget(id, "TA_AOI");
            managementSysRoleAoiAuthMapper.deleteByRoleTarget(id, "TA_AOI");
        }
        if (resp.getIsCbdAoiUpdate() != null && resp.getIsCbdAoiUpdate()) {
            managementSysRoleAoiAuthGroupMapper.deleteByRoleTarget(id, "CBD_AOI");
            managementSysRoleAoiAuthMapper.deleteByRoleTarget(id, "CBD_AOI");
        }
        if (resp.getIsInsightAoiUpdate() != null && resp.getIsInsightAoiUpdate()) {
            managementSysRoleAoiAuthGroupMapper.deleteByRoleTarget(id, "INSIGHT_AOI");
            managementSysRoleAoiAuthMapper.deleteByRoleTarget(id, "INSIGHT_AOI");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void create(Integer identifier, TRoleAuthResp adaptRoleMenu) {
        Integer roleId = getRole(adaptRoleMenu.getRole(), identifier);
        getMenuCodes(adaptRoleMenu.getMenuCodes(), roleId, identifier, "MENU");
        getMenuCodes(adaptRoleMenu.getButtonMenuCodes(), roleId, identifier, "PERMISSION");
        saveRoleData(adaptRoleMenu, roleId, identifier, "ROLE_DATA_C");
    }

    @Transactional(rollbackFor = Exception.class)
    public void modify(Integer identifier, TRoleAuthResp adaptRoleMenu) {
        Integer roleId = modifyRole(identifier, adaptRoleMenu.getRole());
        modifyMenuCode(identifier, adaptRoleMenu.getMenuCodes(), roleId, "MENU");
        modifyMenuCode(identifier, adaptRoleMenu.getButtonMenuCodes(), roleId, "PERMISSION");
        saveRoleData(adaptRoleMenu, roleId, identifier, "ROLE_DATA_C");
    }

    public Integer modifyRole(Integer identifier, ManagementSysRole role) {
        ManagementSysRole roles = managementSysRoleMapper.getByIdEX(role.getId());
        if (roles == null) {
            throw new NoSuchUserException(role.getId(), "");
        }

        Integer roleNameCount = commonMapper.selectByRoleName(role.getId(), role.getRoleName());
        if (roleNameCount > 0) {
            throw new RoleNameExitsException();
        }
        roles.setRoleName(role.getRoleName());
        roles.setUpdater(identifier.toString());
        roles.setCreator(identifier.toString());
        roles.setCreatedByUser(identifier);
        roles.setUpdatedByUser(identifier);
        roles.setIsEnabled(role.getIsEnabled());
        roles.setValidityStartTime(role.getValidityStartTime());
        roles.setValidityEndTime(role.getValidityEndTime());
        roles.setRemark(role.getRemark());
        if (role.getRoleName().length() > 64) {
            throw new RoleNameIsToLongException();
        }
        managementSysRoleMapper.insertIgnoreEntity(role);
        managementSysRoleMapper.updateByEntity(roles);
        commonMapper.deleteByRoleId(role.getId());
        return role.getId();
    }

    public void modifyMenuCode(Integer identifier, List<ManagementSysMenu> menuList, Integer roleId, String authType) {
        ManagementSysRoleAuth roleAuth = new ManagementSysRoleAuth();
        StringBuilder stringBuilder = new StringBuilder();
        for (ManagementSysMenu sysMenu : menuList) {
            roleAuth.setRoleId(roleId);
            roleAuth.setChannel("ADMIN");
            roleAuth.setAuthType(authType);
            roleAuth.setAuthedData(sysMenu.getMenuCode().toString());
            roleAuth.setCreator(identifier.toString());
            roleAuth.setUpdater(identifier.toString());
            roleAuth.setCreatedByUser(identifier);
            roleAuth.setUpdatedByUser(identifier);
            stringBuilder.append(sysMenu.getMenuCode()).append(",");
        }
        if (stringBuilder.length() > 0) {
            String authDate = stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString();
            roleAuth.setAuthedData(authDate.replace(",", "||"));
        }
        if (StringUtils.isNotEmpty(roleAuth.getAuthType()) && !"ROLE_DATA_C".equals(roleAuth.getAuthType())) {
            managementSysRoleAuthMapper.insertUpdateEntity(roleAuth);
        }
    }

    @Transactional
    public void delete(Integer identifier, Integer id) {
        int accountNum = commonMapper.selectRoleAccountByRoleName(id);
        if (accountNum > 0) {
            throw new NotExitsException();
        }
        managementSysRoleMapper.deleteByIdLogically(identifier, id);
        commonMapper.deleteByRoleId(id);
    }

    @Transactional
    public void deleteBatch(Integer identifier, Integer[] roleIds) {
        for (Integer roleId : roleIds) {
            int accountNum = commonMapper.selectRoleAccountByRoleName(roleId);
            if (accountNum > 0) {
                throw new NotExitsException();
            }
            managementSysRoleMapper.deleteByIdLogically(identifier, roleId);
            commonMapper.deleteByRoleId(roleId);
        }
    }

    @Transactional
    public void patch(Integer currentUserIdentifier, Integer patchId) {
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder stringBuilder2 = new StringBuilder();
        String authDate1 = "";
        String authDate2 = "";
        List<ManagementSysRoleAuth> managementSysRoleAuths = managementSysRoleAuthEntityQueryMapper.patch();
        if (!managementSysRoleAuths.isEmpty() && patchId == 1) {
            for (ManagementSysRoleAuth managementSysRoleAuth : managementSysRoleAuths) {
                stringBuilder.setLength(0);
                stringBuilder2.setLength(0);
                Set<String> set = Stream.of(managementSysRoleAuth.getAuthedData().replace("||", ",").split(",")).collect(Collectors.toSet());
                List<ManagementSysMenu> menuByIds = managementSysRoleAuthEntityQueryMapper.getMenuByIds(set);
                if (!menuByIds.isEmpty()) {
                    for (ManagementSysMenu menu : menuByIds) {
                        List<ManagementSysMenu> menuList = managementSysRoleAuthEntityQueryMapper.getParentId(menu.getId());
                        if (!menuList.isEmpty()) {
                            for (ManagementSysMenu sysMenu : menuList) {
                                stringBuilder.append(sysMenu.getMenuCode()).append("||");
                            }
                        }
                        stringBuilder2.append(menu.getMenuCode()).append("||");
                    }
                }
                authDate1 = stringBuilder.delete(stringBuilder.length() - 2, stringBuilder.length()).toString();
                authDate2 = stringBuilder2.delete(stringBuilder2.length() - 2, stringBuilder2.length()).toString();
                managementSysRoleAuth.setAuthedData(authDate2);
                managementSysRoleAuthMapper.updateByEntity(managementSysRoleAuth);

                managementSysRoleAuth.setAuthedData(authDate1);
                managementSysRoleAuth.setId(null);
                managementSysRoleAuth.setAuthType("PERMISSION");
                managementSysRoleAuthMapper.insert(managementSysRoleAuth);
            }
        }
    }
}
