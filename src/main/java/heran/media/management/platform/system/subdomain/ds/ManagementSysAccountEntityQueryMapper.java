package heran.media.management.platform.system.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.system.subdomain.dto.ManagementSysAccountData;
import heran.media.sharelib.domain.db.model.ManagementSysAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface ManagementSysAccountEntityQueryMapper {
    @SelectProvider(type = ManagementSysAccountEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "staffCode", column = "staff_code"),
            @Result(property = "userName", column = "user_name"),
            @Result(property = "nickName", column = "nick_name"),
            @Result(property = "password", column = "password"),
            @Result(property = "cellphone", column = "cellphone"),
            @Result(property = "unionid", column = "unionid"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updator", column = "updator"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "platform", column = "platform")
    }
    )
    List<ManagementSysAccountData> list(SearchCriteria criteria);
}
