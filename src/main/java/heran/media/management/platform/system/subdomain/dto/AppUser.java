package heran.media.management.platform.system.subdomain.dto;

import heran.media.sharelib.domain.db.model.ManagementSysMenu;
import heran.media.sharelib.domain.db.model.ManagementSysRole;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class AppUser {

    private ManagementSysAccountData account;
    private List<ManagementSysRole> roleList;
    private Set<ManagementSysMenu> functionList;
    private ManagementSysRole role;

    public AppUser() {

    }

    public AppUser(ManagementSysAccountData account, List<ManagementSysRole> roleList) {
        this.account = account;
        this.roleList = roleList;
    }

    public AppUser(ManagementSysRole role, Set<ManagementSysMenu> functionList) {
        this.role = role;
        this.functionList = functionList;
    }
}
