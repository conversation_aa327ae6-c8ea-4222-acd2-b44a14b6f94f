package heran.media.management.platform.preference.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SelectListData {
    private String regionNameLv1;
    private String regionNameLv2;
    private String regionNameLv3;
    private String regionCode;
    private String headingName;
    private String headingValue;
    private String mediumName;
    private String mediumValue;
    private String statMonth;
    private Boolean headingIsUpdate;
    private Boolean mediumIsUpdate;
    private String headingUpdateValue;
    private String mediumUpdateValue;

}
