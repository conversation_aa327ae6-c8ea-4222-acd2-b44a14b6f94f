package heran.media.management.platform.preference.mapper;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.preference.dto.CollectDataListData;
import heran.media.management.platform.preference.dto.CollectHeadingDataListData;
import heran.media.management.platform.preference.dto.CollectStatMonthDataListData;
import heran.media.management.platform.preference.dto.SelectListData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PoiOfflineSceneDetailMapper {

    /**
     * 城市汇总查询
     *
     * @param criteria SearchCriteria
     * @return List<CollectDataListData>
     */
    @SelectProvider(type = PoiOfflineSceneDetailEntityQuerySQLProvider.class, method = "collectDataList")
    @Results(value = {
            @Result(property = "regionCode", column = "region_code"),
            @Result(property = "timeCount", column = "timeCount"),
            @Result(property = "updateTime", column = "updateTime")
    }
    )
    List<CollectDataListData> collectDataList(SearchCriteria criteria);

    /**
     * 根据城市展开时间列表
     *
     * @param criteria SearchCriteria
     * @return List<CollectStatMonthDataListData>
     */
    @SelectProvider(type = PoiOfflineSceneDetailEntityQuerySQLProvider.class, method = "collectStatMonthDataList")
    @Results(value = {
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "updateTime", column = "updateTime")
    }
    )
    List<CollectStatMonthDataListData> collectStatMonthDataList(SearchCriteria criteria);

    /**
     * 根据城市 时间 获取到一级分类
     *
     * @param criteria SearchCriteria
     * @return List<CollectHeadingDataListData>
     */
    @SelectProvider(type = PoiOfflineSceneDetailEntityQuerySQLProvider.class, method = "collectHeadingDataListDataList")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "tagName", column = "tag_name"),
            @Result(property = "tagValue", column = "tag_value"),
            @Result(property = "updateTagValue", column = "update_tag_value"),
            @Result(property = "isUpdate", column = "is_update"),
            @Result(property = "updateTime", column = "update_time"),
    }
    )
    List<CollectHeadingDataListData> collectHeadingDataListDataList(SearchCriteria criteria);

    /**
     * 恢复初始化数据
     *
     * @param criteria SearchCriteria
     */
    @SelectProvider(type = PoiOfflineSceneDetailEntityQuerySQLProvider.class, method = "recoverData")
    void recoverData(SearchCriteria criteria);

    /**
     * 获取导出数据
     *
     * @param criteria SearchCriteria
     * @return List<SelectListData>
     */
    @SelectProvider(type = PoiOfflineSceneDetailEntityQuerySQLProvider.class, method = "selectList")
    @Results(value = {
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "regionNameLv1", column = "regionNameLv1"),
            @Result(property = "regionNameLv2", column = "regionNameLv2"),
            @Result(property = "regionNameLv3", column = "regionNameLv3"),
            @Result(property = "regionCode", column = "region_code"),
            @Result(property = "headingName", column = "headingName"),
            @Result(property = "headingValue", column = "headingValue"),
            @Result(property = "mediumName", column = "mediumName"),
            @Result(property = "mediumValue", column = "mediumValue"),
            @Result(property = "headingIsUpdate", column = "headingIsUpdate"),
            @Result(property = "mediumIsUpdate", column = "mediumIsUpdate"),
            @Result(property = "headingUpdateValue", column = "headingUpdateValue"),
            @Result(property = "mediumUpdateValue", column = "mediumUpdateValue"),
    }
    )
    List<SelectListData> selectList(SearchCriteria criteria);
}
