package heran.media.management.platform.preference.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CollectHeadingDataListDataResponse {
    @ApiModelProperty("主键")
    private Integer id;
    @ApiModelProperty("场景大类")
    private String tagName;
    @ApiModelProperty("场景大类占比")
    private String tagValue;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("是否修改过")
    private Boolean isUpdate;

    public CollectHeadingDataListDataResponse(CollectHeadingDataListData data, Boolean isBackups) {
        this.id = data.getId();
        this.tagName = data.getTagName();
        if (isBackups) {
            this.tagValue = data.getTagValue();
        } else {
            this.tagValue = data.getIsUpdate() ? data.getUpdateTagValue() : data.getTagValue();
        }
        this.updateTime = data.getUpdateTime();
        this.isUpdate = data.getIsUpdate();
    }
}
