package heran.media.management.platform.preference.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.management.platform.effectestimate.error.HeadMapDataException;
import heran.media.management.platform.mediaorder.sudomain.bo.EasyXlsMatchDetail;
import heran.media.management.platform.mediaorder.sudomain.dto.ResultMatchDetailErrorData;
import heran.media.management.platform.preference.bo.XlsSceneData;
import heran.media.sharelib.domain.db.mapper.main.PoiCityOfflineSceneDetailMapper;
import heran.media.sharelib.domain.db.model.MainDictRegionData;
import heran.media.sharelib.domain.db.model.main.MainDictRegion;
import heran.media.sharelib.domain.db.model.main.MediaOrderMatchDetail;
import heran.media.sharelib.domain.db.model.main.PoiCityOfflineSceneDetail;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class EasyExcelPoiCityOfflineSceneListener extends AnalysisEventListener<XlsSceneData> {


    private final Map<String, MainDictRegionData> regionMap;
    private final String dataType;
    private final String appName;
    private final Integer userKey;

    private final PoiCityOfflineSceneDetailMapper poiCityOfflineSceneDetailMapper;

    /**
     * 错误信息
     */
    private final List<String> errorMsgList = new ArrayList<>();
    private final List<ResultMatchDetailErrorData> errorResultDataList = new ArrayList<>();

    private final List<XlsSceneData> insertDataList = new ArrayList<>();


    public EasyExcelPoiCityOfflineSceneListener(Map<String, MainDictRegionData> regionMap, String dataType, String appName, Integer userKey, PoiCityOfflineSceneDetailMapper poiCityOfflineSceneDetailMapper) {
        this.regionMap = regionMap;
        this.dataType = dataType;
        this.appName = appName;
        this.userKey = userKey;
        this.poiCityOfflineSceneDetailMapper = poiCityOfflineSceneDetailMapper;
    }

    @Override
    public void invoke(XlsSceneData xlsSceneData, AnalysisContext context) {
        XlsSceneData data = validate(xlsSceneData, context.readSheetHolder().getRowIndex());
        if (data != null) {
            insertDataList.add(data);
        }
    }


    private XlsSceneData validate(XlsSceneData data, Integer rowIndex) {
        if (StringUtils.isEmpty(data.getCityCode())) {
            errorMsgList.add("adcode不能为空");
        } else {
            MainDictRegionData mainDictRegion = regionMap.get(data.getCityCode());
            if (mainDictRegion == null) {
                errorMsgList.add("adcode不合法");
            }
        }
        if (StringUtils.isEmpty(data.getHeadingName())) {
            errorMsgList.add("大类不能为空");
        }
        if (StringUtils.isEmpty(data.getHeadingRatio())) {
            errorMsgList.add("大类占比不能为空");
        } else {
            Double value = parsePercentage(data.getHeadingRatio());
            if (value == null) {
                errorMsgList.add("大类占比不合法");
            } else {
                BigDecimal divide = new BigDecimal(value).divide(new BigDecimal(100), 5, RoundingMode.HALF_UP);
                data.setHeadingRatio(divide.toString());
            }
        }
        if (StringUtils.isEmpty(data.getMediumName())) {
            errorMsgList.add("中类不能为空");
        }
        if (StringUtils.isEmpty(data.getMediumRatio())) {
            errorMsgList.add("中类占比不能为空");
        } else {
            Double value = parsePercentage(data.getMediumRatio());
            if (value == null) {
                errorMsgList.add("中类占比不合法");
            } else {
                BigDecimal divide = new BigDecimal(value).divide(new BigDecimal(100), 5, RoundingMode.HALF_UP);
                data.setMediumRatio(divide.toString());
            }
        }
        if (StringUtils.isEmpty(data.getStatMonth())) {
            errorMsgList.add("查询时间不能为空");
        } else {
            String month = convertToYearMonth(data.getStatMonth());
            if (StringUtils.isEmpty(month)) {
                errorMsgList.add("查询时间不合法");
            } else {
                data.setStatMonth(month);
            }
        }

        if (!errorMsgList.isEmpty()) {
            ResultMatchDetailErrorData errorData = new ResultMatchDetailErrorData(rowIndex, errorMsgList);
            errorMsgList.clear();
            errorResultDataList.add(errorData);
            return null;
        }
        return data;
    }

    public static String convertToYearMonth(String input) {
        try {
            // 允许中文格式，如 "2025年1月"
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy年M月", Locale.CHINA);
            YearMonth ym = YearMonth.parse(input, inputFormatter);
            // 格式化为 "yyyyMM"
            return ym.format(DateTimeFormatter.ofPattern("yyyyMM"));
        } catch (DateTimeParseException e) {
            // 输入格式不合法
            return null;
        }
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollectionUtils.isNotEmpty(insertDataList)) {
            //已城市为维度进行分组
            Map<String, List<XlsSceneData>> groupedMap = insertDataList.stream().collect(Collectors.groupingBy(XlsSceneData::getCityCode));
            for (Map.Entry<String, List<XlsSceneData>> entry : groupedMap.entrySet()) {
                List<XlsSceneData> value = entry.getValue();
                Map<String, List<XlsSceneData>> statMonthMap = insertDataList.stream().collect(Collectors.groupingBy(XlsSceneData::getStatMonth));
                for (Map.Entry<String, List<XlsSceneData>> dataScene : statMonthMap.entrySet()) {
                    List<XlsSceneData> sceneData = dataScene.getValue();
                    //获取大类数据
                    Map<String, XlsSceneData> result = sceneData.stream()
                            .collect(Collectors.toMap(
                                    data -> data.getHeadingName() + "_" + data.getStatMonth(),
                                    Function.identity(),
                                    (existing, replacement) -> existing
                            ));
                    List<XlsSceneData> resultList = new ArrayList<>(result.values());
                    //覆盖 先删除一下之前的数据
                    XlsSceneData data = resultList.get(0);
                    poiCityOfflineSceneDetailMapper.deletePoiCityOfflineSceneDetail(data.getStatMonth(), data.getCityCode(), dataType);
                    buildHeadingData(resultList);
                    //处理中类
                    buildMediumData(value, value.get(0));
                }
            }
            insertDataList.clear();
            regionMap.clear();
        }
        //判断错误数据是否为null 不为null 下载错误信息表格
        if (!errorResultDataList.isEmpty()) {
            XLSWriter.export(errorResultDataList, ResultMatchDetailErrorData.class, "导入失败数据", Objects.requireNonNull(((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getResponse()));
        }
    }

    private void buildMediumData(List<XlsSceneData> sceneData, XlsSceneData data) {
        //查询一下分类建立关系
        List<PoiCityOfflineSceneDetail> detailList = poiCityOfflineSceneDetailMapper
                .getPoiCityOfflineSceneDetailList(data.getStatMonth(), data.getCityCode(), dataType);
        Map<String, PoiCityOfflineSceneDetail> detailMap = detailList.stream()
                .collect(Collectors.toMap(
                        PoiCityOfflineSceneDetail::getTagName,
                        detail -> detail,
                        (existing, replacement) -> replacement
                ));

        List<PoiCityOfflineSceneDetail> details = new ArrayList<>();
        for (XlsSceneData detail : sceneData) {
            PoiCityOfflineSceneDetail headingDetail = detailMap.get(detail.getHeadingName());
            PoiCityOfflineSceneDetail buildData = buildData();
            buildData.setStatMonth(detail.getStatMonth());
            buildData.setRegionCode(detail.getCityCode());
            buildData.setTagName(detail.getMediumName());
            buildData.setTagValue(detail.getMediumRatio());
            buildData.setParentId(headingDetail.getId());
            buildData.setLevel(2);
            details.add(buildData);
        }
        if (CollectionUtils.isNotEmpty(details)) {
            poiCityOfflineSceneDetailMapper.batchInsertIgnore(details);
        }
    }


    private void buildHeadingData(List<XlsSceneData> resultList) {
        List<PoiCityOfflineSceneDetail> details = new ArrayList<>();
        for (XlsSceneData xlsSceneData : resultList) {
            PoiCityOfflineSceneDetail detail = buildData();
            detail.setStatMonth(xlsSceneData.getStatMonth());
            detail.setRegionCode(xlsSceneData.getCityCode());
            detail.setTagName(xlsSceneData.getHeadingName());
            detail.setTagValue(xlsSceneData.getHeadingRatio());
            detail.setLevel(1);
            details.add(detail);
        }
        if (CollectionUtils.isNotEmpty(details)) {
            poiCityOfflineSceneDetailMapper.batchInsertIgnore(details);
        }
    }

    private PoiCityOfflineSceneDetail buildData() {
        PoiCityOfflineSceneDetail detail = new PoiCityOfflineSceneDetail();
        detail.setDataType(dataType);
        detail.setIsDeleted(false);
        detail.setIsUpdate(false);
        detail.setCreatedByUser(userKey);
        detail.setUpdatedByUser(userKey);
        detail.setCreator(appName);
        detail.setUpdater(appName);
        return detail;
    }

    public static Double parsePercentage(String input) {
        if (input == null) {
            return null;
        }
        // 去掉空格和 %
        String cleaned = input.trim().replace("%", "");
        try {
            return Double.parseDouble(cleaned);
        } catch (NumberFormatException e) {
            // 不是合法数字
            return null;
        }
    }


    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        //获取 接受的表头数据
        Field[] fields = XlsSceneData.class.getDeclaredFields();
        //如果不等于 说明表头与要求的表头不一致
        if (headMap.size() != fields.length) {
            //返回错误信息
            throw new HeadMapDataException();
        }
        for (Field field : fields) {
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (!headMap.get(annotation.index()).equals(annotation.value()[0])) {
                //列对应不上
                throw new HeadMapDataException();
            }
        }
    }
}
