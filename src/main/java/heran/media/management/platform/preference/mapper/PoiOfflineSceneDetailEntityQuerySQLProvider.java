package heran.media.management.platform.preference.mapper;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 */
public class PoiOfflineSceneDetailEntityQuerySQLProvider {

    public String collectDataList(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("region_code,COUNT( DISTINCT stat_month ) timeCount,MAX(update_time) updateTime");
            FROM("poi_city_offline_scene_detail");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("region_code IN(" + cri.getValue() + ")");
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("stat_month = '" + cri.getValue() + "'");
                    } else if ("dataType".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("data_type = '" + cri.getValue() + "'");
                    } else if ("isDeleted".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("is_deleted = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            GROUP_BY("region_code");
        }}.toString();
    }

    public String collectStatMonthDataList(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("stat_month,MAX(update_time) updateTime");
            FROM("poi_city_offline_scene_detail");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    } else if ("isDeleted".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("is_deleted = " + cri.getValue());
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("region_code IN(" + cri.getValue() + ")");
                    } else if ("dataType".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("data_type = '" + cri.getValue() + "'");
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("stat_month = '" + cri.getValue() + "'");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            WHERE("is_deleted = FALSE ");
            GROUP_BY("stat_month");
        }}.toString();
    }

    public String collectHeadingDataListDataList(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("id,tag_name,tag_value,update_time,is_update,update_tag_value");
            FROM("poi_city_offline_scene_detail");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("region_code IN(" + cri.getValue() + ")");
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("stat_month = '" + cri.getValue() + "'");
                    } else if ("dataType".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("data_type = '" + cri.getValue() + "'");
                    } else if ("level".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("level = " + cri.getValue());
                    } else if ("isDeleted".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("is_deleted = " + cri.getValue());
                    }  else if ("parentId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("parent_id = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
        }}.toString();
    }

    public String recoverData(SearchCriteria criteria) {
        return new SQL() {{
            UPDATE("poi_city_offline_scene_detail");
            SET("update_tag_value = NULL");
            SET("is_update = 0");
            SET("is_deleted = 0");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("region_code IN(" + cri.getValue() + ")");
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("stat_month = '" + cri.getValue() + "'");
                    } else if ("dataType".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("data_type = '" + cri.getValue() + "'");
                    } else if ("ids".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("id iN(" + cri.getValue() + ")");
                    } else if ("isDeleted".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("is_deleted = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
        }}.toString();
    }


    public String selectList(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t1.stat_month,t2.tag_name headingName,t1.tag_name mediumName,t1.is_update mediumIsUpdate ,t1.update_tag_value mediumUpdateValue,t1.region_code,t5.region_name regionNameLv1,t4.region_name regionNameLv2,t3.region_name regionNameLv3,t1.tag_value mediumValue,t2.tag_value headingValue,t2.is_update headingIsUpdate,t2.update_tag_value headingUpdateValue");
            FROM("poi_city_offline_scene_detail t1 ");
            JOIN("poi_city_offline_scene_detail t2 ON t1.parent_id = t2.id");
            LEFT_OUTER_JOIN("main_dict_region t3 ON t1.region_code = t3.`code`");
            LEFT_OUTER_JOIN("main_dict_region t4 ON t3.parent_code = t4.`code`");
            LEFT_OUTER_JOIN("main_dict_region t5 ON t4.parent_code = t5.`code`");
            if (criteria.getCriterias() != null && !criteria.getCriterias().isEmpty()) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.region_code IN(" + cri.getValue() + ")");
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.stat_month = '" + cri.getValue() + "'");
                    } else if ("dataType".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.data_type = '" + cri.getValue() + "'");
                    } else if ("isDeleted".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.is_deleted = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            WHERE("t1.`level` = 2");
            ORDER_BY("t1.region_code ASC ,t1.stat_month DESC");
        }}.toString();
    }

}
