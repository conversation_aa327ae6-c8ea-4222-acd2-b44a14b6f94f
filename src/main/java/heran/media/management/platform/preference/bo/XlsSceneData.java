package heran.media.management.platform.preference.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.preference.dto.SelectListData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XlsSceneData {
    @XlsField(title = "区域名称", columnIndex = 0)
    @ExcelProperty(value = "区域名称", index = 0)
    private String cityName;
    @XlsField(title = "adcode", columnIndex = 1)
    @ExcelProperty(value = "adcode", index = 1)
    private String cityCode;
    @XlsField(title = "人口口径", columnIndex = 2)
    @ExcelProperty(value = "人口口径", index = 2)
    private String population;
    @XlsField(title = "查询时间", columnIndex = 3)
    @ExcelProperty(value = "查询时间", index = 3)
    private String statMonth;
    @XlsField(title = "大类", columnIndex = 4)
    @ExcelProperty(value = "大类", index = 4)
    private String headingName;
    @XlsField(title = "大类占比", columnIndex = 5)
    @ExcelProperty(value = "大类占比", index = 5)
    private String headingRatio;
    @XlsField(title = "中类", columnIndex = 6)
    @ExcelProperty(value = "中类", index = 6)
    private String mediumName;
    @XlsField(title = "中类占比", columnIndex = 7)
    @ExcelProperty(value = "中类占比", index = 7)
    private String mediumRatio;


    public XlsSceneData(SelectListData data) {
        StringBuilder cityNameBuilder = new StringBuilder();
        if (StringUtils.isNotEmpty(data.getRegionNameLv1())) {
            cityNameBuilder.append(data.getRegionNameLv1()).append("/");
        }
        if (StringUtils.isNotEmpty(data.getRegionNameLv2())) {
            cityNameBuilder.append(data.getRegionNameLv2()).append("/");
        }
        if (StringUtils.isNotEmpty(data.getRegionNameLv3())) {
            cityNameBuilder.append(data.getRegionNameLv3());
        }
        this.cityName = cityNameBuilder.toString();

        this.cityCode = data.getRegionCode();
        this.population = "常驻";

        this.statMonth = convertWithDate(data.getStatMonth());

        String hadingValue = data.getHeadingIsUpdate() ? data.getHeadingUpdateValue() : data.getHeadingValue();
        String mediumValue = data.getMediumIsUpdate() ? data.getMediumUpdateValue() : data.getMediumValue();

        this.headingName = data.getHeadingName();
        this.mediumName = data.getMediumName();
        double headingRatioValue = new BigDecimal(hadingValue).multiply(new BigDecimal(100)).doubleValue();
        this.headingRatio = headingRatioValue + "%";
        double mediumRatioValue = new BigDecimal(mediumValue).multiply(new BigDecimal(100)).doubleValue();
        this.mediumRatio = mediumRatioValue + "%";
    }

    public String convertWithDate(String input) {
        try {
            DateTimeFormatter parser = DateTimeFormatter.ofPattern("yyyyMM");
            YearMonth ym = YearMonth.parse(input, parser);
            return ym.getYear() + "年" + ym.getMonthValue() + "月";
        } catch (Exception e) {
            return "非法格式";
        }
    }

}
