package heran.media.management.platform.preference.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RequestDataRequest {
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("查询城市")
    private List<String> regionCode;
    @ApiModelProperty("人口口径")
    private String poiPopulation;
    @ApiModelProperty("事件类型 POI(POI线下场景偏好)AOI(AOI线下场景偏好)")
    public String dataType;

    public String getDimensionAll(){
        return "poi_m_all_category,poi_m";
    }
    public String getDimensionCategory(){
        return "poi_m_all_category";
    }
    public String getDimensionPoi(){
        return "poi_m";
    }
}
