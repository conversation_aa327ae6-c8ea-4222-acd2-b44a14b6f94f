package heran.media.management.platform.preference.controller;

import com.alibaba.excel.EasyExcel;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.service.CommonService;
import heran.media.management.platform.common.utils.DownloadFileUtils;
import heran.media.management.platform.main.subdomain.response.MarketingSceneAoiOfflineSceneDataResponse;
import heran.media.management.platform.mediaorder.listener.EasyExcelMediumFormatCollectDataListener;
import heran.media.management.platform.mediaorder.sudomain.bo.XlsMediumFormatCollectData;
import heran.media.management.platform.preference.bo.XlsSceneData;
import heran.media.management.platform.preference.dto.*;
import heran.media.management.platform.preference.listener.EasyExcelPoiCityOfflineSceneListener;
import heran.media.management.platform.preference.service.PoiCityOfflineSceneDetailService;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.db.mapper.main.MainDictRegionMapper;
import heran.media.sharelib.domain.db.mapper.main.PoiCityOfflineSceneDetailMapper;
import heran.media.sharelib.domain.db.model.MainDictRegionData;
import heran.media.sharelib.domain.db.model.main.PoiCityOfflineSceneDetail;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "线下场景偏好控制器")
@Slf4j
@RestController
@RequestMapping("/poi_city_offline_scene_detail")
public class PoiCityOfflineSceneDetailController {

    @Value("${spring.application.name}")
    public String appName;

    @Resource
    private PoiCityOfflineSceneDetailService poiCityOfflineSceneDetailService;
    @Resource
    private CommonService commonService;
    @Resource
    private PoiCityOfflineSceneDetailMapper poiCityOfflineSceneDetailMapper;
    @Resource
    private MainDictRegionMapper mainDictRegionMapper;


    @ApiOperation(value = "请求数据")
    @ApiLog(storage = true, storageDescription = "请求数据")
    @RequestMapping(value = "/requestDate", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @PreAuthorize("hasPermission(null,'POI_CITY_OFFLINE_REQUEST_DATE')")
    public InternalResponse<Void> requestDate(@Valid @RequestBody RequestDataRequest request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        poiCityOfflineSceneDetailService.requestData(currentUser.getIdentifier(), request);
        return internalResponse;
    }

    @ApiOperation(value = "删除线下场景偏好")
    @ApiLog(storage = true, storageDescription = "删除线下场景偏好")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @ResponseBody
    public InternalResponse<Void> delete(@RequestParam("id") Integer id) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        poiCityOfflineSceneDetailService.delete(id);
        return internalResponse;
    }

    @ApiOperation(value = "根据城市编码删除")
    @ApiLog(storage = true, storageDescription = "根据城市编码删除")
    @RequestMapping(value = "/deleteByRegionCode", method = RequestMethod.DELETE)
    @ResponseBody
    public InternalResponse<Void> deleteByRegionCode(@RequestParam("regionCode") String regionCode,
                                                     @RequestParam("dataType") String dataType) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        poiCityOfflineSceneDetailService.deleteByRegionCode(regionCode, dataType);
        return internalResponse;
    }

    @ApiOperation(value = "根据城市编码月份删除")
    @ApiLog(storage = true, storageDescription = "根据城市编码月份删除")
    @RequestMapping(value = "/deleteByRegionCodeAndStatMonth", method = RequestMethod.DELETE)
    @ResponseBody
    public InternalResponse<Void> deleteByRegionCodeAndStatMonth(@RequestParam("regionCode") String regionCode,
                                                                 @RequestParam("dataType") String dataType,
                                                                 @RequestParam("statMonth") String statMonth) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        poiCityOfflineSceneDetailService.deleteByRegionCodeAndStatMonth(regionCode, dataType, statMonth);
        return internalResponse;
    }

    @ApiOperation(value = "修改线下场景偏好占比")
    @ApiLog(storage = true, storageDescription = "修改线下场景偏好占比")
    @RequestMapping(value = "/updateTagValue", method = RequestMethod.PUT)
    @ResponseBody
    public InternalResponse<Void> updateTagValue(@RequestParam("id") Integer id, @RequestParam("tagValue") String tagValue) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        poiCityOfflineSceneDetailService.updateTagValue(id, tagValue);
        return internalResponse;
    }


    @ApiLog(storageDescription = "城市汇总查询")
    @ApiOperation(value = "城市汇总查询", notes = "查询字段可包含【dataType(必传事件类型 POI(POI)AOI(AOI)),regionCode(城市查询),statMonth(查询时间),isDeleted(FALSE列表传false必传,原始列表不要穿这个参数)】")
    @RequestMapping(value = "/collectDataList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<CollectDataListDataResponse>> collectDataList(@Valid @RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        PageResponse<CollectDataListDataResponse> pageResponse = poiCityOfflineSceneDetailService.collectDataList(criteria);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog(storageDescription = "根据城市展开时间列表")
    @ApiOperation(value = "根据城市展开时间列表", notes = "查询字段可包含【dataType(必传事件类型 POI(POI)AOI(AOI)),regionCode(城市查询),statMonth(查询时间),isDeleted(FALSE列表传false必传,原始列表不要穿这个参数)】")
    @RequestMapping(value = "/collectStatMonthDataList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<CollectStatMonthDataListData>> collectStatMonthDataList(@Valid @RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        List<CollectStatMonthDataListData> pageResponse = poiCityOfflineSceneDetailService.collectStatMonthDataList(criteria);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog(storageDescription = "根据城市 时间 获取到分类")
    @ApiOperation(value = "根据城市 时间 获取到分类", notes = "查询字段可包含【dataType(必传事件类型 POI(POI)AOI(AOI)),regionCode(城市查询),statMonth(查询时间),level(级别 1(场景大类)2(场景中类)),isDeleted(FALSE列表传false必传,原始列表不要穿这个参数)】")
    @RequestMapping(value = "/collectHeadingDataListDataList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<CollectHeadingDataListDataResponse>> collectHeadingDataListDataList(@Valid @RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        List<CollectHeadingDataListDataResponse> pageResponse = poiCityOfflineSceneDetailService.collectHeadingDataListDataList(criteria);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog(storageDescription = "恢复初始化数据")
    @ApiOperation(value = "恢复初始化数据", notes = "查询字段可包含【dataType(必传事件类型 POI(POI)AOI(AOI)),regionCode(城市查询),statMonth(查询时间),isDeleted(FALSE列表传false必传),ids(选中的id集合)】")
    @RequestMapping(value = "/recoverData", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @PreAuthorize("hasPermission(null,'POI_CITY_OFFLINE_RECOVER_DATA')")
    public InternalResponse<Void> recoverData(@Valid @RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        poiCityOfflineSceneDetailService.recoverData(criteria);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiLog(storageDescription = "获取某个大类下的所有中类数据")
    @ApiOperation(value = "获取某个大类下的所有中类数据", notes = "根据id获取对象详情")
    @RequestMapping(value = "/getBySceneDataResponse/{id}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<MarketingSceneAoiOfflineSceneDataResponse> getBySceneDataResponse(@PathVariable Integer id) {
        InternalResponse<MarketingSceneAoiOfflineSceneDataResponse> internalResponse = InternalResponse.success();
        MarketingSceneAoiOfflineSceneDataResponse response = poiCityOfflineSceneDetailService.getBySceneDataResponse(id);
        internalResponse.withBody(response);
        return internalResponse;
    }

    @ApiLog(storageDescription = "修改页修改指标")
    @ApiOperation(value = "修改页修改指标")
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @PreAuthorize("hasPermission(null,'POI_CITY_OFFLINE_UPDATE')")
    public InternalResponse<Void> update(@Valid @RequestBody List<UpDateSceneDataRequest> request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        poiCityOfflineSceneDetailService.update(request);
        return internalResponse;
    }

    @ApiOperation(value = "下载线下场景偏好导入模板", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/downloadTemplate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadTemplate(HttpServletResponse resp) throws Exception {
        File file = commonService.generalTemplate("线下场景偏好导入模板", XlsSceneData.class);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "线下场景偏好导入模板");
    }

    @ApiOperation(value = "导入线下场景偏好", response = InternalResponse.class)
    @ApiLog(printInputs = false)
    @RequestMapping(value = "/importPoiCityOfflineSceneListener", method = RequestMethod.POST)
    @PreAuthorize("hasPermission(null,'POI_CITY_OFFLINE_IMPORT_POI_CITY_OFFLINE_SCENE')")
    public void importPoiCityOfflineSceneListener(@RequestPart("file") MultipartFile file, String dataType) throws Exception {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        //获取地区数据
        List<MainDictRegionData> dataList = mainDictRegionMapper.getAll();
        Map<String, MainDictRegionData> map = dataList.stream()
                .collect(Collectors.toMap(
                        MainDictRegionData::getCode,
                        data -> data,
                        (existing, d) -> d
                ));
        EasyExcel.read(file.getInputStream(),
                XlsSceneData.class,
                new EasyExcelPoiCityOfflineSceneListener(map, dataType, appName, currentUser.getIdentifier(), poiCityOfflineSceneDetailMapper)).sheet().doRead();
    }


    @ApiOperation(value = "根据条件导出所有记录，现在5W")
    @ApiLog(printOutputs = false, storage = true, storageDescription = "线下场景偏好导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @PreAuthorize("hasPermission(null,'POI_CITY_OFFLINE_EXPORT')")
    public void export(HttpServletResponse resp,
                       @RequestParam(value = "exportAll", required = false) Boolean exportAll,
                       @RequestBody SearchCriteria searchCriteria) throws Exception {
        exportAll = exportAll != null && exportAll;
        File file = poiCityOfflineSceneDetailService.exportData(searchCriteria, exportAll);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "线下场景偏好信息");
    }


}
