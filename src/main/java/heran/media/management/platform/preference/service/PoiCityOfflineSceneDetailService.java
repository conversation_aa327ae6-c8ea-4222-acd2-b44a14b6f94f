package heran.media.management.platform.preference.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.error.RegionCodeNotLegalException;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.management.platform.main.subdomain.bo.XlsMediaTaCrowdPack;
import heran.media.management.platform.main.subdomain.dto.MediaTaCrowdPackData;
import heran.media.management.platform.main.subdomain.response.MarketingSceneAoiOfflineSceneDataResponse;
import heran.media.management.platform.preference.bo.XlsSceneData;
import heran.media.management.platform.preference.dto.*;
import heran.media.management.platform.preference.mapper.PoiOfflineSceneDetailMapper;
import heran.media.sharelib.client.CustomizationMapClient;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.domain.db.mapper.main.PoiCityOfflineSceneDetailMapper;
import heran.media.sharelib.domain.db.model.main.MediaTaCrowdPack;
import heran.media.sharelib.domain.db.model.main.PoiCityOfflineSceneDetail;
import heran.media.sharelib.domain.dto.amap.request.SendMessagesContext;
import heran.media.sharelib.domain.dto.amap.request.ThroughClearlyRequest;
import heran.media.sharelib.domain.dto.amap.response.ThroughClearlyResponse;
import heran.media.sharelib.errors.CustomerError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static heran.media.management.platform.common.constants.AppConstants.REGION_CODES;
import static heran.media.management.platform.common.utils.xls.XLSWriter.getExportFileName;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PoiCityOfflineSceneDetailService extends BaseCallServiceImpl {

    private static int EXPORT_START_PAGE = 1;
    private static int EXPORT_PAGE_LIMIT = 5000;
    private static int EXPORT_LIMIT_LOOP_COUNT = 20;

    @Value("${path.export}")
    private String tempDataPath;


    @Resource
    private CustomizationMapClient customizationMapClient;
    @Resource
    private PoiCityOfflineSceneDetailMapper poiCityOfflineSceneDetailMapper;
    @Resource
    private PoiOfflineSceneDetailMapper poiOfflineSceneDetailMapper;

    /**
     * 请求线下场景偏好
     *
     * @param userKey 用户id
     * @param request RequestDataRequest
     */
    public void requestData(Integer userKey, RequestDataRequest request) {
        //判断是否包含直辖市区code
        for (String code : request.getRegionCode()) {
            if (REGION_CODES.contains(code)){
                throw new RegionCodeNotLegalException();
            }
        }

        //如果存在直接返回
        //poiCityOfflineSceneDetailMapper.deletePoiCityOfflineSceneDetail(request.getStatMonth(), request.getRegionCode(), request.getDataType());
        for (String code : request.getRegionCode()) {
            List<PoiCityOfflineSceneDetail> poiCityOfflineSceneDetailLevelList = poiCityOfflineSceneDetailMapper.getPoiCityOfflineSceneDetailLevelList(request.getStatMonth(), code, request.getDataType());
            if (CollectionUtils.isNotEmpty(poiCityOfflineSceneDetailLevelList)) {
                continue;
            }
            SendMessagesContext context = SendMessagesContext.builder().locType(request.getPoiPopulation()).userId(userKey).typeInfo(code).bizData("POI_CITY_OFFLINE_SCENE_DETAIL").build();
            ThroughClearlyRequest clearlyRequest = buildThroughClearlyRequest(request, code);
            ThroughClearlyResponse response = customizationMapClient.residentThroughClearly(clearlyRequest,context);
            verifyResponse(response);
            Map<String, Map<String, String>> dimensions = response.getData().getQuery().getDimensions();
            handle(userKey, request, dimensions, code);
        }

    }

    public void handle(Integer userKey, RequestDataRequest request, Map<String, Map<String, String>> dimensions, String code) {
        if (dimensions.isEmpty()) {
            return;
        }
        //大类
        Map<String, String> map = dimensions.get(request.getDimensionCategory());
        List<PoiCityOfflineSceneDetail> details = buildPoiCityOfflineSceneDetailBroadHeading(userKey, request, map, code);
        //批量增加
        poiCityOfflineSceneDetailMapper.batchInsertIgnore(details);
        //中类
        Map<String, String> mapPoi = dimensions.get(request.getDimensionPoi());
        List<PoiCityOfflineSceneDetail> sceneDetails = buildPoiCityOfflineSceneDetailMediumClass(code, userKey, request, mapPoi);
        //批量增加
        poiCityOfflineSceneDetailMapper.batchInsertIgnore(sceneDetails);
    }


    private List<PoiCityOfflineSceneDetail> buildPoiCityOfflineSceneDetailMediumClass(String code, Integer userKey, RequestDataRequest request, Map<String, String> map) {
        //查询一下分类建立关系
        List<PoiCityOfflineSceneDetail> detailList = poiCityOfflineSceneDetailMapper
                .getPoiCityOfflineSceneDetailList(request.getStatMonth(), code, request.getDataType());
        Map<String, PoiCityOfflineSceneDetail> detailMap = detailList.stream()
                .collect(Collectors.toMap(
                        PoiCityOfflineSceneDetail::getTagName,
                        detail -> detail,
                        (existing, replacement) -> replacement
                ));

        List<PoiCityOfflineSceneDetail> details = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            String[] split = key.split("-");
            PoiCityOfflineSceneDetail detail = detailMap.get(split[0]);
            PoiCityOfflineSceneDetail sceneDetail = new PoiCityOfflineSceneDetail();
            sceneDetail.setStatMonth(request.getStatMonth());
            sceneDetail.setRegionCode(code);
            sceneDetail.setLevel(2);
            sceneDetail.setParentId(detail.getId());
            sceneDetail.setIsDeleted(false);
            sceneDetail.setIsUpdate(false);
            sceneDetail.setTagValue(value);
            sceneDetail.setTagName(split.length == 2 ? split[1] : split[0]);
            sceneDetail.setDataType(request.getDataType());
            setCreatorInfo(userKey, sceneDetail);
            setUpdaterInfo(userKey, sceneDetail);
            details.add(sceneDetail);
        }
        return details;
    }


    private List<PoiCityOfflineSceneDetail> buildPoiCityOfflineSceneDetailBroadHeading(Integer userKey, RequestDataRequest request, Map<String, String> map, String code) {
        List<PoiCityOfflineSceneDetail> details = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            PoiCityOfflineSceneDetail sceneDetail = new PoiCityOfflineSceneDetail();
            sceneDetail.setStatMonth(request.getStatMonth());
            sceneDetail.setRegionCode(code);
            sceneDetail.setLevel(1);
            sceneDetail.setParentId(null);
            sceneDetail.setIsUpdate(false);
            sceneDetail.setIsDeleted(false);
            sceneDetail.setTagName(key);
            sceneDetail.setTagValue(value);
            sceneDetail.setDataType(request.getDataType());
            setCreatorInfo(userKey, sceneDetail);
            setUpdaterInfo(userKey, sceneDetail);
            details.add(sceneDetail);
        }
        return details;
    }

    private void verifyResponse(ThroughClearlyResponse response) {
        if (response == null || response.getData() == null || response.getData().getQuery() == null || response.getCode() != 0) {
            log.error("调用高德接口失败，response: {}", response);
            throw new CustomerError(response != null ? response.getMessage() : "调用高德接口失败");
        }
    }


    private ThroughClearlyRequest buildThroughClearlyRequest(RequestDataRequest request, String code) {
        ThroughClearlyRequest clearlyRequest = new ThroughClearlyRequest();
        clearlyRequest.setType("3");
        clearlyRequest.setMonth(request.getStatMonth());
        clearlyRequest.setTgi(false);
        clearlyRequest.setProfile(request.getDimensionAll());
        clearlyRequest.setTypeInfo(code);
        clearlyRequest.setLabType(StatisticalType.valueOf(request.getPoiPopulation()).getCode());
        return clearlyRequest;
    }

    /**
     * 删除
     *
     * @param id 主键id
     */
    public void delete(Integer id) {
        poiCityOfflineSceneDetailMapper.deleteById(id);
        //删除下面的子集  有就删 没有就不做操作
        poiCityOfflineSceneDetailMapper.deleteByParentId(id);
    }

    /**
     * 根据城市编码删除
     *
     * @param regionCode 城市编码
     * @param dataType   类型
     */
    public void deleteByRegionCode(String regionCode, String dataType) {
        poiCityOfflineSceneDetailMapper.deleteByRegionCode(regionCode, dataType);
    }

    /**
     * 根据城市编码月份删除
     *
     * @param regionCode 城市编码
     * @param dataType   类型
     * @param statMonth  月份
     */
    public void deleteByRegionCodeAndStatMonth(String regionCode, String dataType, String statMonth) {
        poiCityOfflineSceneDetailMapper.deleteByRegionCodeAndStatMonth(regionCode, dataType, statMonth);
    }

    /**
     * 修改指标
     *
     * @param id       主键
     * @param tagValue 占比
     */
    public void updateTagValue(Integer id, String tagValue) {
        BigDecimal decimal = new BigDecimal(tagValue).divide(new BigDecimal(100), 10, RoundingMode.HALF_UP);
        poiCityOfflineSceneDetailMapper.updateTagValue(id, decimal.toString());
    }

    /**
     * 城市汇总查询
     *
     * @param criteria SearchCriteria
     * @return PageResponse<CollectDataListDataResponse>
     */
    public PageResponse<CollectDataListDataResponse> collectDataList(SearchCriteria criteria) {
        Page<Object> page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<CollectDataListData> searchResult = poiOfflineSceneDetailMapper.collectDataList(criteria);
        PageResponse<CollectDataListDataResponse> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult.stream().map(CollectDataListDataResponse::new).collect(Collectors.toList()));
        return pageResponse;
    }

    /**
     * 根据城市展开时间列表
     *
     * @param criteria SearchCriteria
     * @return PageResponse<CollectDataListDataResponse>
     */
    public List<CollectStatMonthDataListData> collectStatMonthDataList(SearchCriteria criteria) {
        return poiOfflineSceneDetailMapper.collectStatMonthDataList(criteria);
    }

    /**
     * 根据城市 时间 获取到一级分类
     *
     * @param criteria SearchCriteria
     * @return List<CollectHeadingDataListData>
     */
    public List<CollectHeadingDataListDataResponse> collectHeadingDataListDataList(SearchCriteria criteria) {
        Map<String, SearchCriteria.Criteria> map = criteria.getCriterias().stream().collect(Collectors.toMap(SearchCriteria.Criteria::getKey, c -> c));
        SearchCriteria.Criteria isDeleted = map.get("isDeleted");
        List<CollectHeadingDataListData> searchResult = poiOfflineSceneDetailMapper.collectHeadingDataListDataList(criteria);
        return searchResult.stream().map(data -> new CollectHeadingDataListDataResponse(data, isDeleted == null)).collect(Collectors.toList());
    }

    /**
     * 恢复初始化数据
     *
     * @param criteria SearchCriteria
     */
    public void recoverData(SearchCriteria criteria) {
        poiOfflineSceneDetailMapper.recoverData(criteria);
    }

    /**
     * 获取某个大类下的所有中类数据
     *
     * @param id 大类id
     * @return MarketingSceneAoiOfflineSceneDataResponse
     */
    public MarketingSceneAoiOfflineSceneDataResponse getBySceneDataResponse(Integer id) {
        //获取一级分类数据
        PoiCityOfflineSceneDetail byIdFilterDeleted = poiCityOfflineSceneDetailMapper.getByIdFilterDeleted(id);
        if (byIdFilterDeleted == null) {
            return null;
        }
        MarketingSceneAoiOfflineSceneDataResponse dataResponse = new MarketingSceneAoiOfflineSceneDataResponse(byIdFilterDeleted, false);
        List<PoiCityOfflineSceneDetail> poiOfflineSceneDetailList = poiCityOfflineSceneDetailMapper.getPoiOfflineSceneDetailList(byIdFilterDeleted.getId());
        List<MarketingSceneAoiOfflineSceneDataResponse> collect = poiOfflineSceneDetailList.stream().map(d -> new MarketingSceneAoiOfflineSceneDataResponse(d, false)).collect(Collectors.toList());
        dataResponse.setResponses(collect);
        return dataResponse;
    }

    /**
     * 修改页修改指标
     *
     * @param request List<UpDataSceneDataRequest>
     */
    public void update(List<UpDateSceneDataRequest> request) {
        if (CollectionUtils.isEmpty(request)) {
            return;
        }
        for (UpDateSceneDataRequest dataRequest : request) {
            BigDecimal decimal = new BigDecimal(dataRequest.getValue()).divide(new BigDecimal(100), 10, RoundingMode.HALF_UP);
            poiCityOfflineSceneDetailMapper.updateTagValue(dataRequest.getId(), decimal.toString());
        }
    }

    /**
     * 导出数据
     *
     * @param criteria  SearchCriteria
     * @param exportAll exportAll
     * @return File
     * @throws IOException IOException
     */
    public File exportData(SearchCriteria criteria, boolean exportAll) throws IOException {
        if (exportAll) {
            criteria.setPage(EXPORT_START_PAGE);
            criteria.setLimit(EXPORT_PAGE_LIMIT);
        }
        int startPage = criteria.getPage();
        int pageLimit = criteria.getLimit();

        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        String fileName = getExportFileName(tempDataPath + "/", "CollectStatMonthDataListData");
        File newFile = new File(fileName);

        PageHelper.startPage(startPage, pageLimit, false);
        List<SelectListData> queryResult = poiOfflineSceneDetailMapper.selectList(criteria);
        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), XlsSceneData.class);
        while (!queryResult.isEmpty()) {
            List<XlsSceneData> appCallHistories = queryResult.stream().map(XlsSceneData::new).collect(Collectors.toList());
            workbook = XLSWriter.appendLineFromObject("sheet1", appCallHistories, XlsSceneData.class, workbook);

            if (exportAll) {
                if (startPage * pageLimit >= EXPORT_LIMIT_LOOP_COUNT * EXPORT_PAGE_LIMIT) {
                    log.warn("导出数据超过可导出限制（不符合实时导出场景），如需变更自行更改");
                    break;
                }
                startPage++;
                PageHelper.startPage(startPage, pageLimit, false);
                queryResult = poiOfflineSceneDetailMapper.selectList(criteria);
                continue;
            }
            break;
        }

        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }

}
