package heran.media.management.platform.preference.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CollectDataListDataResponse {
    @ApiModelProperty("城市code")
    private String regionCode;
    @ApiModelProperty("查询时间数量")
    private Integer timeCount;
    @ApiModelProperty("更新时间")
    private Date updateTime;

    public CollectDataListDataResponse(CollectDataListData data) {
        this.regionCode = data.getRegionCode();
        this.timeCount = data.getTimeCount();
        this.updateTime = data.getUpdateTime();
    }

}
