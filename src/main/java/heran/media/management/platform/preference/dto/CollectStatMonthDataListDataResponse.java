package heran.media.management.platform.preference.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CollectStatMonthDataListDataResponse {
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("更新时间")
    private Date updateTime;

    public CollectStatMonthDataListDataResponse(CollectStatMonthDataListData data){
        this.statMonth = data.getStatMonth();
        this.updateTime = data.getUpdateTime();
    }
}
