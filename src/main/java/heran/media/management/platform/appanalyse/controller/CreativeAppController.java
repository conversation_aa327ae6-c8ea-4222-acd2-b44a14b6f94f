package heran.media.management.platform.appanalyse.controller;

import heran.media.management.platform.appanalyse.dto.CreativeAppRequest;
import heran.media.management.platform.appanalyse.dto.CreativeAppResponse;
import heran.media.management.platform.appanalyse.dto.MarketingCaseIndustryListResponse;
import heran.media.management.platform.appanalyse.dto.MarketingCaseListResponse;
import heran.media.management.platform.appanalyse.service.CreativeAppService;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.TMainMediaTag;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 创意应用Controller
 * @Author: yzj
 * @Date: 2023/8/30 11:15
 * @Version: 1.0
 */
@ApiOperation(value = "创意应用相关接口")
@Slf4j
@RestController
@RequestMapping("/app-analyse-creative")
public class CreativeAppController {

    @Resource
    private CreativeAppService creativeAppService;

    @ApiLog
    @ApiOperation(value = "新增创意应用数据")
    @RequestMapping(value = "/insert", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'CREATIVEAPPLICATIONAS_INSERT')")
    @ResponseBody
    public InternalResponse<Void> insert(@RequestBody @Valid CreativeAppRequest creativeApp) {
        AuthUserInfo userInfo = AuthUtils.getUserInfo();
        creativeAppService.insertUpdate(creativeApp, userInfo.getIdentifier());
        return InternalResponse.success();
    }

    @ApiLog
    @ApiOperation(value = "编辑创意应用数据")
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'CREATIVEAPPLICATIONAS_UPDATE')")
    @ResponseBody
    public InternalResponse<Void> update(@RequestBody @Valid CreativeAppRequest creativeApp) {
        AuthUserInfo userInfo = AuthUtils.getUserInfo();
        creativeAppService.insertUpdate(creativeApp, userInfo.getIdentifier());
        return InternalResponse.success();
    }

    @ApiLog
    @ApiOperation(value = "条件查询", notes = "查询字段可包含【marketingScene(营销场景),creativeApp(创意应用)" +
            ",creativeTitle(创意标题),industryCategoryTagId(行业分类标签id),creativeFormTagId(创意形式标签id),caseId(案例id)】")
    @RequestMapping(value = "/conditionQuery", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<CreativeAppResponse>> conditionQuery(@RequestBody @Valid SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        PageResponse<CreativeAppResponse> pageResponse = creativeAppService.conditionQuery(criteria);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "查询详情", notes = "查询详情")
    @RequestMapping(value = "/getDetailByCaseId", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<CreativeAppResponse> getDetailByCaseId(@RequestParam("caseId") Integer caseId) {
        InternalResponse internalResponse = null;
        CreativeAppResponse pageResponse = creativeAppService.getDetailByCaseId(caseId);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "根据分类获取标签列表")
    @RequestMapping(value = "/getTagCategory", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<TMainMediaTag>> getTagCategory(@RequestParam("tagCategory") String tagCategory) {
        InternalResponse internalResponse = null;
        List<TMainMediaTag> tagList = creativeAppService.getTagCategory(tagCategory);
        internalResponse = InternalResponse.success().withBody(tagList);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "根据案例id删除相关数据")
    @RequestMapping(value = "/deleteByCaseId", method = RequestMethod.DELETE, produces = "application/json")
    @PreAuthorize("hasPermission(null,'CREATIVEAPPLICATIONAS_DELETE')")
    @ResponseBody
    public InternalResponse<Void> deleteByCaseId(@RequestParam("caseId") Integer caseId) {
        InternalResponse internalResponse = null;
        AuthUserInfo userInfo = AuthUtils.getUserInfo();
        creativeAppService.deleteByCaseId(caseId, userInfo.getIdentifier());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "获取默认排序")
    @RequestMapping(value = "/getDefaultPriority", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<Integer> getDefaultPriority() {
        InternalResponse internalResponse = null;
        Integer priority = creativeAppService.getDefaultPriority();
        internalResponse = InternalResponse.success().withBody(priority);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "按媒体场景获取所有的应用方案 groupLabel：CREATIVE_APPLICATIONS_SCENE")
    @RequestMapping(value = "/getCaseList", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<MarketingCaseListResponse>> getCaseList(@RequestParam("groupLabel") String groupLabel) {
        InternalResponse internalResponse = null;
        List<MarketingCaseListResponse> caseList = creativeAppService.getCaseList(groupLabel);
        internalResponse = InternalResponse.success().withBody(caseList);
        return internalResponse;
    }


    @ApiLog
    @ApiOperation(value = "按行业获取所有的应用方案  tagCategory：CREATIVE_APP_INDUSTRY_CATEGORY_TAG")
    @RequestMapping(value = "/getCaseIndustryList", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<MarketingCaseIndustryListResponse>> getCaseIndustryList(@RequestParam("tagCategory") String tagCategory) {
        InternalResponse internalResponse = null;
        List<MarketingCaseIndustryListResponse> caseList = creativeAppService.getCaseIndustryList(tagCategory);
        internalResponse = InternalResponse.success().withBody(caseList);
        return internalResponse;
    }


}
