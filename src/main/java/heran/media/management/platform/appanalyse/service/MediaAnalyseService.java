package heran.media.management.platform.appanalyse.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import heran.media.management.platform.appanalyse.dto.*;
import heran.media.management.platform.appanalyse.mapper.MediaAnalyseMapper;
import heran.media.management.platform.appanalyse.po.ComponentAttrContent;
import heran.media.management.platform.appanalyse.po.ComponentAttrRel;
import heran.media.management.platform.appanalyse.po.ComponentRelData;
import heran.media.management.platform.appanalyse.po.MediaAnalyseData;
import heran.media.management.platform.appanalyse.po.MediaAnalyseExtraInfo;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.error.DataModelAlreadyExistException;
import heran.media.management.platform.common.error.DataModelMediumNumLimitException;
import heran.media.management.platform.common.error.TemplateNotExistException;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.main.subdomain.dto.TMainTemplateComponent;
import heran.media.sharelib.domain.bo.MainTemplateComponentComponentDataType;
import heran.media.sharelib.domain.bo.MainTemplateComponentPositionType;
import heran.media.sharelib.domain.bo.OperationType;
import heran.media.sharelib.domain.db.mapper.main.*;
import heran.media.sharelib.domain.db.model.main.*;
import heran.media.sharelib.utils.upload.AliYunOssRedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 媒介分析Service
 * @Author: yzj
 * @Date: 2023/8/24 11:18
 * @Version: 1.0
 */
@Slf4j
@Service
public class MediaAnalyseService extends BaseCallServiceImpl {

    private static final String MEDIUM_NAME_KEY = "MEDIUM_NAME";
    private static final int DATA_MODEL_MEDIUM_MAX_NUM = 6;

    @Resource
    private MediaAnalyseMapper mediaAnalyseMapper;
    @Resource
    private MainComponentAttrMapper mainComponentAttrMapper;
    @Resource
    private MainTemplateGenerateConfigMapper configMapper;
    @Resource
    private MainResourceCategoryMapper mainResourceCategoryMapper;
    @Resource
    private MainResourceTemplateMapper mainResourceTemplateMapper;
    @Resource
    private MainTemplateComponentRelMapper mainTemplateComponentRelMapper;
    @Resource
    private MainAttachementMapper mainAttachementMapper;
    @Resource
    private AliYunOssRedisUtils aliYunOssRedisUtils;

    @Transactional(rollbackFor = Exception.class)
    public void insertUpdate(MediaAnalyseRequest mediaAnalyse, Integer operatorId, String optType) {
        List<TComponentRel> componentList = mediaAnalyse.getComponentRelList();
        if (CollectionUtils.isNotEmpty(componentList) && componentList.size()>DATA_MODEL_MEDIUM_MAX_NUM){
            throw new DataModelMediumNumLimitException();
        }
        Integer configId = insertConfig(operatorId, mediaAnalyse.getTemplateCode(), mediaAnalyse.getSecondCategoryId(),
                mediaAnalyse.getSecondCategoryName(),mediaAnalyse.getSceneId(),optType);
        if (CollectionUtils.isNotEmpty(componentList)){
            String inputComponentDataType = componentList.get(0).getComponentDataType();
            List<Integer> inputRelIdList = componentList.stream().map(TComponentRel::getId).collect(Collectors.toList());
            Integer curMaxDisplayPriority = 0;
            if (OperationType.UPDATE.name().equals(optType)) {
                //编辑,这里先删除不存在的卡片
                List<Integer> relIdList = new ArrayList<>();
                List<ComponentRelData> relDataList = mediaAnalyseMapper.getComponentRelDataByConfigId(configId);
                Optional<Integer> maxDisplayPriorityOptional = relDataList.stream()
                        .map(ComponentRelData::getDisplayPriority)
                        .max(Comparator.comparingInt(Integer::intValue));
                if (maxDisplayPriorityOptional.isPresent()){
                    curMaxDisplayPriority = maxDisplayPriorityOptional.get();
                }
                if (CollectionUtils.isNotEmpty(relDataList)) {
                    relIdList = relDataList.stream().map(ComponentRelData::getRelId).collect(Collectors.toList());
                    String originComponentDataType = relDataList.get(0).getComponentDataType();
                    //若原来的组件类型为IMAGE,则在删除时需更改图片附件的状态
                    if (MainTemplateComponentComponentDataType.IMAGE.name().equals(originComponentDataType)) {
                        //IMAGE数据类型时rel表数据和属性都只有一个所以直接拿
                        String attachmentId = relDataList.get(0).adaptToTComponentAttrList().get(0).getAttrValue().toString();
                        //IMAGE --> DATA_TEMPLATE
                        if (MainTemplateComponentComponentDataType.DATA_TEMPLATE.name().equals(inputComponentDataType)) {
                            //逻辑删除
                            mainAttachementMapper.deleteByIdLogically(operatorId, Long.valueOf(attachmentId));
                        } else {
                            //IMAGE --> IMAGE
                            String inputAttachmentId = mediaAnalyse.getComponentRelList().get(0).getContentList().get(0).getAttrValue();
                            if (!inputAttachmentId.equals(attachmentId)) {
                                mainAttachementMapper.deleteByIdLogically(operatorId, Long.valueOf(attachmentId));
                            }
                        }
                    }
                }
                List<Integer> relIdListToDel = new ArrayList<>(relIdList);
                relIdListToDel.removeAll(inputRelIdList);
                // 过滤掉null元素
                relIdListToDel = relIdListToDel.stream().filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(relIdListToDel)) {
                    mainTemplateComponentRelMapper.deleteBatchIds(relIdListToDel);
                }
            }
            insertUpdateBatchTemplateComponentRel(operatorId, configId,componentList,curMaxDisplayPriority,optType);
        }
    }

    private void insertUpdateBatchTemplateComponentRel(Integer operatorId, Integer configId, List<TComponentRel> componentList, Integer curMaxDisplayPriority, String optType) {
        List<MainTemplateComponentRel> relList = new ArrayList<>();
        for (TComponentRel componentRel : componentList) {
            MainTemplateComponentRel rel = new MainTemplateComponentRel();
            rel.setId(componentRel.getId());
            rel.setTemplateConfigId(configId);
            rel.setComponentId(componentRel.getComponentId());
            List<TComponentAttrContent> attrList = componentRel.getContentList();
            List<ComponentAttrContent> contentList = new ArrayList<>();
            String displayTitle = null;
            for (TComponentAttrContent componentAttr : attrList) {
                if (MEDIUM_NAME_KEY.equals(componentAttr.getAttrKey())) {
                    displayTitle = componentAttr.getAttrValue();
                }
                ComponentAttrContent content = new ComponentAttrContent(componentAttr.getAttrKey(), componentAttr.getAttrValue());
                contentList.add(content);
            }
            rel.setComponentContent(new Gson().toJson(contentList));
            rel.setDisplayTitle(displayTitle);
            //先设置一个不可能重复的值,后面再重新排序
            rel.setDisplayPriority(curMaxDisplayPriority+componentRel.getDisplayPriority());
            rel.setDisplayPosition(MainTemplateComponentPositionType.ANY.name());
            rel.setIsEnabled(true);
            setUpdaterInfo(operatorId, rel);
            setCreatorInfo(operatorId, rel);
            relList.add(rel);
        }
        mainTemplateComponentRelMapper.insertUpdateBatch(relList);
        if (OperationType.UPDATE.name().equals(optType)){
            //更新时重新排序
            List<MainTemplateComponentRel> collect = relList.stream().filter(s -> s.getId() == null).collect(Collectors.toList());
            Map<Integer, Integer> idMap = new HashMap<>(8);
            if (CollectionUtils.isNotEmpty(collect)){
                List<MainTemplateComponentRel> byConfigId = mainTemplateComponentRelMapper.getValidByTemplateConfigId(configId);
                idMap = byConfigId.stream().collect(Collectors.toMap(MainTemplateComponentRel::getDisplayPriority, MainTemplateComponentRel::getId));
            }
            relList.sort(Comparator.comparingInt(MainTemplateComponentRel::getDisplayPriority));
            int initDisplayPriority = 1;
            for (MainTemplateComponentRel rel : relList) {
                if (rel.getId()==null){
                    rel.setId(idMap.get(rel.getDisplayPriority()));
                }
                rel.setDisplayPriority(initDisplayPriority++);
            }
            mainTemplateComponentRelMapper.updateDisplayPriorityBatch(relList);
        }
    }

    private Integer insertConfig(Integer operatorId, String templateCode, Integer secondCategoryId, String categoryName, Integer parentId, String optType) {
        MainResourceTemplate template = mainResourceTemplateMapper.getByTemplateCode(templateCode);
        if (template == null){
            throw new TemplateNotExistException();
        }
        if (OperationType.INSERT.name().equals(optType)) {
            MainResourceCategory category = mainResourceCategoryMapper.getByGroupLabelCategoryNameParentId(templateCode, categoryName, parentId);
            if (category == null){
                //新增数据模型
                category = insertCategory(operatorId, templateCode, categoryName, parentId);
            }else {
                //使用已有数据模型
                MainTemplateGenerateConfig existConfig = configMapper.getByTemplateCodeAndSceneId(template.getId(), category.getId());
                if (existConfig != null){
                    //已被使用
                    throw new DataModelAlreadyExistException();
                }
            }
            secondCategoryId = category.getId();
        }else {
            try {
                mainResourceCategoryMapper.updateCategoryNameById(categoryName,secondCategoryId);
            } catch (DuplicateKeyException e) {
                throw new DataModelAlreadyExistException();
            }
        }
        MainTemplateGenerateConfig config = new MainTemplateGenerateConfig();
        config.setTemplateId(template.getId());
        config.setIsEnabled(true);
        config.setRelSceneCategoryId(secondCategoryId);
        config.setMediaCategoryId(-1);
        config.setRelRegion("-1");
        config.setUpdateTime(new Date());
        setCreatorInfo(operatorId, config);
        setUpdaterInfo(operatorId, config);
        configMapper.insertOrUpdate(config);
        return config.getId();
    }

    private MainResourceCategory insertCategory(Integer operatorId, String templateCode, String categoryName, Integer parentId){
        MainResourceCategory category = new MainResourceCategory();
        category.setCategoryName(categoryName);
        category.setParentId(parentId);
        category.setLevel(2);
        category.setIsLeaf(true);
        category.setGroupLabel(templateCode);
        setCreatorInfo(operatorId,category);
        setUpdaterInfo(operatorId,category);
        mainResourceCategoryMapper.insertUpdateEntity(category);
        return category;
    }


    public PageResponse<MediaAnalyseResponse> conditionQueryNew(SearchCriteria criteria, String templateCode) {
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<MediaAnalyseData> searchResult = mediaAnalyseMapper.listDataModel(criteria, templateCode);
        PageResponse<MediaAnalyseResponse> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        List<Integer> configIdList = searchResult.stream().map(MediaAnalyseData::getConfigId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(configIdList)){
            pageResponse.setResults(searchResult.stream()
                    .map(MediaAnalyseResponse::new)
                    .collect(Collectors.toList()));
        }
        return pageResponse;
    }

    private List<MediaAnalyseResponse> matchAttrValue(List<MediaAnalyseData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        List<Integer> componentIdList = dataList.stream().map(MediaAnalyseData::getComponentId).collect(Collectors.toList());
        List<MainComponentAttr> attrList = mainComponentAttrMapper.getByComponentIdList(componentIdList);
        List<MediaAnalyseResponse> resultList = new ArrayList<>();
        Map<String, MainComponentAttr> attrMap = new HashMap<>();
        for (MainComponentAttr mainComponentAttr : attrList) {
            attrMap.put(mainComponentAttr.getComponentId() + "_" + mainComponentAttr.getAttrKey(), mainComponentAttr);
        }
        dataList.sort(Comparator.comparingInt(MediaAnalyseData::getDisplayPriority));
        for (MediaAnalyseData data : dataList) {
            resultList.add(new MediaAnalyseResponse(data, attrMap, mainAttachementMapper, aliYunOssRedisUtils));
        }
        return resultList;
    }

    public List<ResourceCategoryResponse> categoryQuery(String groupLabel,String secondaryCategoryName) {
        List<MainResourceCategory> resourceCategoryList = mainResourceCategoryMapper.getByGroupLabel(groupLabel);
        if (resourceCategoryList == null) {
            return null;
        }
        List<MainResourceCategory> resultList;
        if (secondaryCategoryName!=null){
            resultList = new ArrayList<>();
            List<Integer> businessIdList = new ArrayList<>();
            List<Integer> parentIdList = new ArrayList<>();
            List<MainResourceCategory> businessList = new ArrayList<>();
            for (MainResourceCategory category : resourceCategoryList) {
                if (category.getCategoryName().equals(secondaryCategoryName)) {
                    businessIdList.add(category.getId());
                    businessList.add(category);
                    parentIdList.add(category.getParentId());
                }
            }
            List<MainResourceCategory> subAndParentList = resourceCategoryList.stream()
                    .filter(category -> businessIdList.contains(category.getParentId())||parentIdList.contains(category.getId()))
                    .collect(Collectors.toList());
            resultList.addAll(businessList);
            resultList.addAll(subAndParentList);
        }else {
            resultList = resourceCategoryList;
        }
        List<ResourceCategoryResponse> collect = resultList.stream().map(ResourceCategoryResponse::new)
                .collect(Collectors.toList());
        return assembleResourceCategories(collect);
    }

    public static List<ResourceCategoryResponse> assembleResourceCategories(List<ResourceCategoryResponse> inputList) {
        // 创建一个映射，将 parentId 映射到资源目录列表
        Map<Integer, List<ResourceCategoryResponse>> parentIdToCategories = new HashMap<>();
        for (ResourceCategoryResponse resourceCategoryResponse : inputList) {
            parentIdToCategories.computeIfAbsent(resourceCategoryResponse.getParentId(), k -> new ArrayList<>()).add(resourceCategoryResponse);
        }

        // 定义一个递归函数，用于构建目录层次结构
        Function<ResourceCategoryResponse, ResourceCategoryResponse> buildHierarchy = new Function<ResourceCategoryResponse, ResourceCategoryResponse>() {
            @Override
            public ResourceCategoryResponse apply(ResourceCategoryResponse category) {
                // 获取当前目录的子目录列表
                List<ResourceCategoryResponse> children = parentIdToCategories.get(category.getId());
                if (children != null) {
                    // 递归构建子目录的层次结构
                    category.setSecondaryCategoryList(children.stream()
                            .map(this)  // 使用递归调用
                            .collect(Collectors.toList()));
                }
                return category;
            }
        };

        // 构建树形层次结构
        List<ResourceCategoryResponse> rootCategories = parentIdToCategories.get(null);
        if (rootCategories != null) {
            return rootCategories.stream()
                    .map(buildHierarchy)
                    .collect(Collectors.toList());
        }
        // 如果没有根目录，则返回空列表
        return Collections.emptyList();
    }

    public void deleteMediaDataByRelId(Integer relId) {
        mainTemplateComponentRelMapper.deleteByIdEX(relId);
    }

    public List<TMainTemplateComponent> getComponentList(String componentGroup) {
        List<ComponentAttrRel> componentList = mediaAnalyseMapper.getComponentAttrRelByGroup(componentGroup);
        if (CollectionUtils.isNotEmpty(componentList)) {
            List<TMainTemplateComponent> resultList = new ArrayList<>();
            Map<Integer, List<ComponentAttrRel>> groupByComponentId = componentList.stream().collect(Collectors.groupingBy(ComponentAttrRel::getComponentId));
            for (Map.Entry<Integer, List<ComponentAttrRel>> entry : groupByComponentId.entrySet()) {
                resultList.add(new TMainTemplateComponent(entry.getValue()));
            }
            return resultList;
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteMediaDataByConfigId(Integer configId, Integer operatorId) {
        List<ComponentRelData> relDataList = mediaAnalyseMapper.getComponentRelDataByConfigId(configId);
        if (MainTemplateComponentComponentDataType.IMAGE.name().equals(relDataList.get(0).getComponentDataType())) {
            String attachmentId = relDataList.get(0).adaptToTComponentAttrList().get(0).getAttrValue().toString();
            mainAttachementMapper.deleteByIdLogically(operatorId, Long.valueOf(attachmentId));
        }
        mainTemplateComponentRelMapper.deleteByConfigId(configId);
        MainTemplateGenerateConfig byIdEX = configMapper.getByIdEX(configId);
        mainResourceCategoryMapper.deleteByIdEX(byIdEX.getRelSceneCategoryId());
        configMapper.deleteByIdEX(configId);
    }

    public List<MediaAnalyseResponse> getDetailByConfigId(Integer configId) {
        return matchAttrValue(mediaAnalyseMapper.list(getSingleSearchCriteria("configId", String.valueOf(configId)), null));
    }

    public List<MediaAnalyseListDataResponse> getMediaAnalyseListDataResponse(String templateCode) {
        // 获取一级类目（Scene 级别）
        List<MainResourceCategory> mainResourceCategories = mainResourceCategoryMapper
                .getByGroupLabelAndLevel("MEDIUM_ANALYSIS_SCENE", 1);

        // 获取模型数据明细
        List<DataModelDataDetails> dataModelDataDetails = mediaAnalyseMapper.getDataModelDataDetails(templateCode);

        // 分组：sceneId -> 明细列表
        Map<Integer, List<DataModelDataDetails>> map = dataModelDataDetails.stream()
                .collect(Collectors.groupingBy(DataModelDataDetails::getSceneId));

        List<MediaAnalyseListDataResponse> dataResponses = new ArrayList<>();

        for (MainResourceCategory category : mainResourceCategories) {
            MediaAnalyseListDataResponse mediaAnalyseListDataResponse = new MediaAnalyseListDataResponse();
            mediaAnalyseListDataResponse.setCategoryName(category.getCategoryName());

            // 安全获取列表，避免空指针
            List<DataModelDataDetails> details = map.getOrDefault(category.getId(), Collections.emptyList());

            // 映射成简化版模型数据
            List<DataModelData> dataList = details.stream()
                    .map(DataModelData::new)
                    .collect(Collectors.toList());

            mediaAnalyseListDataResponse.setModelDataList(dataList);
            dataResponses.add(mediaAnalyseListDataResponse);
        }

        return dataResponses;
    }

}
