package heran.media.management.platform.appanalyse.mapper;

import heran.media.management.platform.appanalyse.dto.MarketingCaseIndustryData;
import heran.media.management.platform.appanalyse.dto.MarketingCaseListData;
import heran.media.management.platform.appanalyse.po.CreativeAppDetailData;
import heran.media.management.platform.appanalyse.po.CreativeAppListData;
import heran.media.management.platform.appanalyse.po.MediaTag;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

/**
 * @Description:
 * @Author: yzj
 * @Date: 2023/8/30 17:38
 * @Version: 1.0
 */
@Mapper
public interface CreativeAppMapper {

    @SelectProvider(type= CreativeAppQuerySQLProvider.class,method="select")
    @Results(value = {
            @Result(property = "marketingScene", column = "sceneName"),
            @Result(property = "creativeAppCategoryId", column = "secondCategoryId"),
            @Result(property = "creativeApp", column = "creativeName"),
            @Result(property = "caseId", column = "caseId"),
            @Result(property = "creativeTitle", column = "case_title"),
            @Result(property = "creativeContent", column = "content"),
            @Result(property = "priority", column = "priority"),
            @Result(property = "executeStartTime", column = "execute_start_time"),
            @Result(property = "executeEndTime", column = "execute_end_time"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<CreativeAppListData> list(SearchCriteria criteria);


    @Select({
            "<script>",
            "SELECT",
            "t1.id caseId,t3.id, t3.label, t4.group_label",
            "FROM",
            "main_marketing_case t1",
            "INNER JOIN main_media_tag_rel t2 ON t1.id = t2.biz_id ",
            "INNER JOIN main_media_tag t3 ON t2.tag_id = t3.id",
            "INNER JOIN main_resource_category t4 ON t3.cagetory_id = t4.id",
            "WHERE t2.biz_type = 'CASE' ",
            "AND t1.id IN",
            "<foreach item='caseId' collection='caseIds' open='(' separator=',' close=')'>",
            "#{caseId}",
            "</foreach>",
            "</script>"
    })
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "caseId", column = "caseId"),
            @Result(property = "tagName", column = "label"),
            @Result(property = "tagCategory", column = "group_label")
    })
    List<MediaTag> getTagsByCaseIds(@Param("caseIds") List<Integer> caseIds);

    @Select("SELECT\n" +
            "t1.category_name sceneName,\n" +
            "t2.id secondCategoryId,\n" +
            "t2.category_name creativeName,\n" +
            "t3.id caseId,\n" +
            "t3.case_title,\n" +
            "t3.content,\n" +
            "t3.priority,\n" +
            "t3.execute_start_time,\n" +
            "t3.execute_end_time,\n" +
            "t3.main_drawing_attachment,\n" +
            "t5.id tagId,\n" +
            "t6.group_label,\n" +
            "t8.id albumDetailId,\n" +
            "t9.id attachmentId,\n" +
            "t9.storage_path,\n" +
            "t9.file_type,\n" +
            "t9.unique_file_name\n" +
            "FROM\n" +
            "main_resource_category t1\n" +
            "INNER JOIN main_resource_category t2 ON t1.id = t2.parent_id\n" +
            "INNER JOIN main_marketing_case t3 ON t2.id = t3.case_category_id\n" +
            "INNER JOIN main_media_tag_rel t4 ON t3.id = t4.biz_id\n" +
            "INNER JOIN main_media_tag t5 ON t4.tag_id = t5.id\n" +
            "INNER JOIN main_resource_category t6 ON t5.cagetory_id = t6.id\n" +
            "INNER JOIN main_media_album t7 ON t3.id = t7.biz_id\n" +
            "INNER JOIN main_media_album_detail t8 ON t7.id = t8.album_id\n" +
            "INNER JOIN main_attachement t9 ON t8.attachment_id = t9.id \n" +
            "WHERE\n" +
            "t3.id = #{caseId} \n" +
            "AND t3.is_enabled = 1 \n" +
            "AND t4.biz_type = 'CASE'\n" +
            "AND t7.album_type = 'MARKETING_CASE_ALBUM'")
    @Results(value = {
            @Result(property = "marketingScene", column = "sceneName"),
            @Result(property = "creativeAppCategoryId", column = "secondCategoryId"),
            @Result(property = "creativeApp", column = "creativeName"),
            @Result(property = "caseId", column = "caseId"),
            @Result(property = "creativeTitle", column = "case_title"),
            @Result(property = "creativeContent", column = "content"),
            @Result(property = "priority", column = "priority"),
            @Result(property = "executeStartTime", column = "execute_start_time"),
            @Result(property = "executeEndTime", column = "execute_end_time"),
            @Result(property = "mainDrawingAttachment", column = "main_drawing_attachment"),
            @Result(property = "tagId", column = "tagId"),
            @Result(property = "tagCategory", column = "group_label"),
            @Result(property = "albumDetailId", column = "albumDetailId"),
            @Result(property = "attachmentId", column = "attachmentId"),
            @Result(property = "storagePath", column = "storage_path"),
            @Result(property = "fileType", column = "file_type"),
            @Result(property = "uniqueFileName", column = "unique_file_name")
    }
    )
    List<CreativeAppDetailData> getDetailByCaseId(Integer caseId);

    @Select("SELECT MAX(priority)+1 FROM main_marketing_case WHERE case_type = 'CREATIVE_APPLICATIONS'")
    Integer getMaxPriority();

    @Select("SELECT t1.id,t1.case_category_id,t1.case_title FROM `main_marketing_case` t1 ")
    List<MarketingCaseListData> getMarketingCaseListData();

    @Select("SELECT t1.id,t1.case_title,t3.id tagId FROM `main_marketing_case` t1 JOIN main_media_tag_rel t2 ON t1.id = t2.biz_id AND t2.biz_type = 'CASE' JOIN main_media_tag t3 on t2.tag_id = t3.id GROUP BY t1.id")
    List<MarketingCaseIndustryData> getMarketingCaseIndustryData();
}
