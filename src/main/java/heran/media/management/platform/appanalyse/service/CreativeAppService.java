package heran.media.management.platform.appanalyse.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import heran.media.management.platform.appanalyse.dto.*;
import heran.media.management.platform.appanalyse.mapper.CreativeAppMapper;
import heran.media.management.platform.appanalyse.po.CreativeAppDetailData;
import heran.media.management.platform.appanalyse.po.CreativeAppListData;
import heran.media.management.platform.appanalyse.po.MediaTag;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.error.CreativeTitleRepeatException;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.main.subdomain.dto.TMainMediaTag;
import heran.media.sharelib.domain.bo.MainAttachementFileType;
import heran.media.sharelib.domain.bo.MainMarketingCaseCaseType;
import heran.media.sharelib.domain.bo.MainMediaAlbumAlbumType;
import heran.media.sharelib.domain.bo.MainMediaTagRelBizType;
import heran.media.sharelib.domain.db.mapper.main.*;
import heran.media.sharelib.domain.db.model.main.*;
import heran.media.sharelib.utils.upload.AliYunOssRedisUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 创意应用Service
 * @Author: yzj
 * @Date: 2023/8/30 11:18
 * @Version: 1.0
 */
@Service
@RefreshScope
public class CreativeAppService extends BaseCallServiceImpl {

    @Value("${useCDN}")
    private Boolean useCDN;
    @Value("${videoHost}")
    private String videoHost;

    private static final String INDUSTRY_CATEGORY = "CREATIVE_APP_INDUSTRY_CATEGORY_TAG";
    private static final String CREATIVE_FORM = "CREATIVE_APP_CREATIVE_FORM_TAG";
    private static final String MARKETING_MODE = "MARKETING_METHOD_LABEL";

    @Resource
    private CreativeAppMapper creativeAppMapper;
    @Resource
    private MainMarketingCaseMapper mainMarketingCaseMapper;
    @Resource
    private MainCaseTagRelMapper mainCaseTagRelMapper;
    @Resource
    private MainMediaAlbumMapper mainMediaAlbumMapper;
    @Resource
    private MainMediaAlbumDetailMapper mainMediaAlbumDetailMapper;
    @Resource
    private MainMediaTagMapper mainMediaTagMapper;
    @Resource
    private MainAttachementMapper mainAttachementMapper;
    @Resource
    private AliYunOssRedisUtils aliYunOssRedisUtils;
    @Resource
    private MainResourceCategoryMapper mainResourceCategoryMapper;


    @Transactional(rollbackFor = Exception.class)
    public void insertUpdate(CreativeAppRequest creativeApp, Integer identifier) {
        verifyData(creativeApp);
        Integer caseId = insertUpdateCase(creativeApp, identifier);
        insertOrDeleteCaseTagRel(identifier, caseId, creativeApp);
        insertUpdateAlbum(identifier, caseId, creativeApp);
    }

    private void verifyData(CreativeAppRequest creativeApp) {
        MainMarketingCase caseData = mainMarketingCaseMapper.getByCaseCategoryIdCaseTitle(creativeApp.getCreativeCategoryId(), creativeApp.getCreativeTitle());
        if (creativeApp.getCaseId() == null && caseData != null) {
            throw new CreativeTitleRepeatException();
        } else if (creativeApp.getCaseId() != null && caseData != null && !caseData.getId().equals(creativeApp.getCaseId())) {
            throw new CreativeTitleRepeatException();
        }
    }

    private void insertUpdateAlbum(Integer identifier, Integer caseId, CreativeAppRequest creativeApp) {
        MainMediaAlbum mediaAlbum = new MainMediaAlbum();
        mediaAlbum.setAlbumType(MainMediaAlbumAlbumType.MARKETING_CASE_ALBUM.name());
        mediaAlbum.setAlbumName(creativeApp.getCreativeTitle());
        mediaAlbum.setBizId(caseId);
        setCreatorInfo(identifier, mediaAlbum);
        setUpdaterInfo(identifier, mediaAlbum);
        mainMediaAlbumMapper.insertUpdateEntity(mediaAlbum);
        Long albumId = mediaAlbum.getId();
        if (albumId == null) {
            MainMediaAlbum album = mainMediaAlbumMapper.getByAlbumTypeBizIdCategoryId(mediaAlbum.getAlbumType(), caseId, -1);
            albumId = album.getId();
        }
        insertOrDeleteAlbumDetail(identifier, albumId.intValue(), creativeApp);
    }

    private void insertOrDeleteAlbumDetail(Integer identifier, Integer albumId, CreativeAppRequest creativeApp) {
        List<MainMediaAlbumDetail> detailList = mainMediaAlbumDetailMapper.getByAlbumId(albumId);
        List<Integer> attachmentIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(detailList)) {
            attachmentIdList = detailList.stream().map(MainMediaAlbumDetail::getAttachmentId).collect(Collectors.toList());
        }
        List<Integer> inputAttachmentIdList = creativeApp.getAttachmentIdList();
        //删除关联附件
        List<Integer> attachmentIdToDelete = new ArrayList<>(attachmentIdList);
        attachmentIdToDelete.removeAll(inputAttachmentIdList);
        // 过滤掉null元素
        attachmentIdToDelete = attachmentIdToDelete.stream().filter(Objects::nonNull).collect(Collectors.toList());
        //先删除原来所有附件关联关系
        mainMediaAlbumDetailMapper.deleteBatchByAlbumIds(Collections.singletonList(Long.valueOf(albumId)));
        //然后重新插入
        List<MainMediaAlbumDetail> detailListToAdd = new ArrayList<>();
        for (Integer attachmentId : inputAttachmentIdList) {
            MainMediaAlbumDetail mediaAlbumDetail = new MainMediaAlbumDetail();
            mediaAlbumDetail.setAlbumId(albumId);
            mediaAlbumDetail.setAttachmentId(attachmentId);
            setUpdaterInfo(identifier, mediaAlbumDetail);
            setCreatorInfo(identifier, mediaAlbumDetail);
            detailListToAdd.add(mediaAlbumDetail);
        }
        mainMediaAlbumDetailMapper.insertBatch(detailListToAdd);
        //逻辑删除附件
        if (CollectionUtils.isNotEmpty(attachmentIdToDelete)) {
            mainAttachementMapper.deleteByIdsLogicallyBatch(identifier, attachmentIdToDelete);
        }
    }

    private void insertOrDeleteCaseTagRel(Integer identifier, Integer caseId, CreativeAppRequest creativeApp) {
        List<MainMediaTagRel> relList = mainCaseTagRelMapper.getByCaseId(caseId, MainMediaTagRelBizType.CASE.name());
        List<Integer> inputTagIdList = new ArrayList<>(creativeApp.getCreativeFormTagIdList());
        inputTagIdList.add(creativeApp.getIndustryCategoryTagId());
        inputTagIdList.addAll(creativeApp.getMarketingModeTagIdList());
        List<Integer> tagIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relList)) {
            tagIdList = relList.stream().map(MainMediaTagRel::getTagId).collect(Collectors.toList());
        }
        //新增关联标签
        List<Integer> tagsToAdd = new ArrayList<>(inputTagIdList);
        //删除关联标签
        List<Integer> tagsToDelete = new ArrayList<>(tagIdList);
        tagsToAdd.removeAll(tagIdList);
        tagsToDelete.removeAll(inputTagIdList);
        // 过滤掉null元素
        tagsToAdd = tagsToAdd.stream().filter(Objects::nonNull).collect(Collectors.toList());
        tagsToDelete = tagsToDelete.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tagsToAdd)) {
            List<MainMediaTagRel> relListToAdd = new ArrayList<>();
            for (Integer tagId : tagsToAdd) {
                MainMediaTagRel rel = new MainMediaTagRel();
                rel.setBizId(caseId);
                rel.setTagId(tagId);
                rel.setBizType(MainMediaTagRelBizType.CASE.name());
                setCreatorInfo(identifier, rel);
                setUpdaterInfo(identifier, rel);
                relListToAdd.add(rel);
            }
            mainCaseTagRelMapper.insertBatch(relListToAdd);
        }
        if (CollectionUtils.isNotEmpty(tagsToDelete)) {
            List<MainMediaTagRel> relListToDel = new ArrayList<>();
            for (Integer tagId : tagsToDelete) {
                MainMediaTagRel rel = new MainMediaTagRel();
                rel.setBizId(caseId);
                rel.setTagId(tagId);
                rel.setBizType(MainMediaTagRelBizType.CASE.name());
                relListToDel.add(rel);
            }
            mainCaseTagRelMapper.deleteBatchByUnionKeys(relListToDel);
        }
    }

    private Integer insertUpdateCase(CreativeAppRequest creativeApp, Integer identifier) {
        MainMarketingCase marketingCase = new MainMarketingCase();
        marketingCase.setId(creativeApp.getCaseId());
        marketingCase.setCaseCategoryId(creativeApp.getCreativeCategoryId());
        marketingCase.setCaseTitle(creativeApp.getCreativeTitle());
        marketingCase.setCaseType(MainMarketingCaseCaseType.CREATIVE_APPLICATIONS.name());
        marketingCase.setContent(creativeApp.getCreativeContent());
        marketingCase.setMainDrawingAttachment(creativeApp.getAttachmentIdList().get(0));
        marketingCase.setIsEnabled(true);
        marketingCase.setPriority(creativeApp.getPriority());
        marketingCase.setExecuteStartTime(creativeApp.getExecuteStartTime());
        marketingCase.setExecuteEndTime(creativeApp.getExecuteEndTime());
        setCreatorInfo(identifier, marketingCase);
        setUpdaterInfo(identifier, marketingCase);
        marketingCase.setCreateTime(new Date());
        mainMarketingCaseMapper.insertUpdateEntity(marketingCase);
        return marketingCase.getId();
    }

    public PageResponse<CreativeAppResponse> conditionQuery(SearchCriteria criteria) {
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<CreativeAppListData> searchResult = creativeAppMapper.list(criteria);
        List<CreativeAppResponse> creativeAppResponseList = null;
        PageResponse<CreativeAppResponse> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        searchResult = searchResult.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(searchResult)) {
            return pageResponse;
        }
        List<Integer> caseIdList = searchResult.stream().map(CreativeAppListData::getCaseId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(caseIdList)) {
            creativeAppResponseList = searchResult.stream().map(CreativeAppResponse::new).collect(Collectors.toList());
            //补全标签列表
            List<MediaTag> mediaTags = creativeAppMapper.getTagsByCaseIds(caseIdList);
            if (CollectionUtils.isNotEmpty(mediaTags)) {
                Map<Integer, Map<String, List<MediaTag>>> tagMapByCase = mediaTags.stream().collect(Collectors.groupingBy(MediaTag::getCaseId, Collectors.groupingBy(MediaTag::getTagCategory)));
                for (CreativeAppResponse creativeAppResponse : creativeAppResponseList) {
                    Map<String, List<MediaTag>> tagMapByCategory = tagMapByCase.get(creativeAppResponse.getCaseId());
                    creativeAppResponse.setIndustryCategoryTag(new TMainMediaTag(tagMapByCategory.get(INDUSTRY_CATEGORY).get(0)));
                    creativeAppResponse.setCreativeFormTagList(tagMapByCategory.get(CREATIVE_FORM).stream().map(TMainMediaTag::new).collect(Collectors.toList()));
                    List<MediaTag> marketingModeTagList = tagMapByCategory.get(MARKETING_MODE);
                    if (CollectionUtils.isNotEmpty(marketingModeTagList)) {
                        creativeAppResponse.setMarketingModeTagList(marketingModeTagList.stream().map(TMainMediaTag::new).collect(Collectors.toList()));
                    }
                }
            }
        }
        pageResponse.setResults(creativeAppResponseList);
        return pageResponse;
    }

    public List<TMainMediaTag> getTagCategory(String tagCategory) {
        List<MainMediaTag> mainMediaTagList = mainMediaTagMapper.getByCategory(tagCategory);
        if (CollectionUtils.isNotEmpty(mainMediaTagList)) {
            return mainMediaTagList.stream().map(TMainMediaTag::new).collect(Collectors.toList());
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByCaseId(Integer caseId, Integer identifier) {
        MainMediaAlbum album = mainMediaAlbumMapper.getByAlbumTypeBizIdCategoryId(MainMediaAlbumAlbumType.MARKETING_CASE_ALBUM.name(), caseId, -1);
        if (album != null) {
            List<MainMediaAlbumDetail> detailList = mainMediaAlbumDetailMapper.getByAlbumId(album.getId().intValue());
            if (CollectionUtils.isNotEmpty(detailList)) {
                List<Integer> attachmentIdList = detailList.stream().map(MainMediaAlbumDetail::getAttachmentId).collect(Collectors.toList());
                mainMediaAlbumDetailMapper.deleteBatchByUnionKeys(detailList);
                mainAttachementMapper.deleteByIdsLogicallyBatch(identifier, attachmentIdList);
            }
            mainMediaAlbumMapper.deleteByIdEX(album.getId());
        }
        mainMarketingCaseMapper.deleteByIdEX(caseId);
    }

    public CreativeAppResponse getDetailByCaseId(Integer caseId) {
        List<CreativeAppDetailData> detailDataList = creativeAppMapper.getDetailByCaseId(caseId);
        if (CollectionUtils.isEmpty(detailDataList)) {
            return null;
        }
        CreativeAppResponse creativeAppResponse = new CreativeAppResponse();
        Map<Integer, List<CreativeAppDetailData>> collect = detailDataList.stream().collect(Collectors.groupingBy(CreativeAppDetailData::getTagId));
        List<TMainMediaTag> creativeFormTagList = new ArrayList<>();
        List<TMainMediaTag> marketingModeTagList = new ArrayList<>();
        for (Map.Entry<Integer, List<CreativeAppDetailData>> entry : collect.entrySet()) {
            CreativeAppDetailData detailData = entry.getValue().get(0);
            if (creativeAppResponse.getCaseId() == null) {
                creativeAppResponse = new CreativeAppResponse(detailData);
            }
            if (creativeAppResponse.getIndustryCategoryTag() == null && INDUSTRY_CATEGORY.equals(detailData.getTagCategory())) {
                creativeAppResponse.setIndustryCategoryTag(new TMainMediaTag(detailData.getTagId(), detailData.getTagName()));
            }
            if (CREATIVE_FORM.equals(detailData.getTagCategory())) {
                creativeFormTagList.add(new TMainMediaTag(detailData.getTagId(), detailData.getTagName()));
            }
            if (MARKETING_MODE.equals(detailData.getTagCategory())) {
                marketingModeTagList.add(new TMainMediaTag(detailData.getTagId(), detailData.getTagName()));
            }
            if (creativeAppResponse.getAttachmentList() == null) {
                List<MainAttachement> attachmentList = entry.getValue().stream()
                        .sorted(Comparator.comparingLong(CreativeAppDetailData::getAlbumDetailId))
                        .map(CreativeAppDetailData::adaptToMainAttachment)
                        .collect(Collectors.toList());
                aliYunOssRedisUtils.setTempUrlList(attachmentList);
                if (useCDN) {
                    for (MainAttachement attachment : attachmentList) {
                        if (Arrays.toString(MainAttachementFileType.VIDEO.desc).contains(attachment.getFileType())) {
                            String url = attachment.getUrl().replaceAll("://[a-zA-Z0-9.-]+/", "://" + videoHost + "/");
                            attachment.setUrl(url);
                        }
                    }
                }
                List<AttachmentResponse> attachmentResponseList = attachmentList.stream()
                        .map(AttachmentResponse::new)
                        .collect(Collectors.toList());
                creativeAppResponse.setAttachmentList(attachmentResponseList);
            }
        }
        creativeAppResponse.setMarketingModeTagList(marketingModeTagList);
        creativeAppResponse.setCreativeFormTagList(creativeFormTagList);
        Iterator<AttachmentResponse> iterator = creativeAppResponse.getAttachmentList().iterator();
        while (iterator.hasNext()) {
            AttachmentResponse attachmentResponse = iterator.next();
            if (attachmentResponse.getId().equals(detailDataList.get(0).getMainDrawingAttachment())) {
                creativeAppResponse.setMainAttachment(new AttachmentResponse(attachmentResponse));
                iterator.remove();
                break;
            }
        }

        return creativeAppResponse;
    }

    public Integer getDefaultPriority() {
        return creativeAppMapper.getMaxPriority();
    }

    public List<MarketingCaseListResponse> getCaseList(String groupLabel) {
        List<MainResourceCategory> resourceCategoryList = mainResourceCategoryMapper.getByGroupLabel(groupLabel);
        List<MarketingCaseListData> marketingCaseListData = creativeAppMapper.getMarketingCaseListData();

        // 分组：案例数据根据二级类目的 ID 分组
        Map<Integer, List<MarketingCaseListData>> caseList = marketingCaseListData.stream()
                .collect(Collectors.groupingBy(MarketingCaseListData::getCaseCategoryId));

        // 获取一级类目
        List<MainResourceCategory> levelOneList = resourceCategoryList.stream()
                .filter(item -> item.getLevel() == 1)
                .collect(Collectors.toList());

        // 获取二级类目，按 parentId 分组
        Map<Integer, List<MainResourceCategory>> level2GroupedByParentId = resourceCategoryList.stream()
                .filter(item -> item.getLevel() == 2)
                .collect(Collectors.groupingBy(MainResourceCategory::getParentId));

        List<MarketingCaseListResponse> marketingCaseListResponses = new ArrayList<>();

        for (MainResourceCategory category : levelOneList) {
            MarketingCaseListResponse marketingCaseListResponse = new MarketingCaseListResponse();
            marketingCaseListResponse.setId(category.getId());
            marketingCaseListResponse.setName(category.getCategoryName());
            marketingCaseListResponse.setIsCheck(false);

            List<MainResourceCategory> mainResourceCategories = level2GroupedByParentId.get(category.getId());
            if (mainResourceCategories == null) {
                continue; // 无二级类目直接跳过
            }

            List<MarketingCaseListResponse> marketingCaseListSubsetData = new ArrayList<>();

            for (MainResourceCategory mainResourceCategory : mainResourceCategories) {
                MarketingCaseListResponse data = new MarketingCaseListResponse();
                data.setId(mainResourceCategory.getId());
                data.setName(mainResourceCategory.getCategoryName());
                data.setParentId(category.getId());
                data.setIsCheck(false);

                List<MarketingCaseListData> listData = caseList.getOrDefault(mainResourceCategory.getId(), Collections.emptyList());
                if (CollectionUtils.isNotEmpty(listData)) {
                    List<MarketingCaseListResponse> listResponses = listData.stream().map(MarketingCaseListResponse::new).collect(Collectors.toList());
                    data.setSubsetData(listResponses);
                }
                marketingCaseListSubsetData.add(data);
            }

            marketingCaseListResponse.setSubsetData(marketingCaseListSubsetData);
            marketingCaseListResponses.add(marketingCaseListResponse);
        }
        return marketingCaseListResponses;
    }

    public List<MarketingCaseIndustryListResponse> getCaseIndustryList(String tagCategory) {
        List<MainMediaTag> mainMediaTagList = mainMediaTagMapper.getByCategory(tagCategory);
        List<MarketingCaseIndustryData> marketingCaseIndustryData = creativeAppMapper.getMarketingCaseIndustryData();


        Map<Integer, List<MarketingCaseIndustryData>> marketingCaseMap = marketingCaseIndustryData.stream()
                .collect(Collectors.groupingBy(MarketingCaseIndustryData::getTagId));

        List<MarketingCaseIndustryListResponse> responses = new ArrayList<>();

        for (MainMediaTag mainMediaTag : mainMediaTagList) {
            MarketingCaseIndustryListResponse response = new MarketingCaseIndustryListResponse();
            response.setId(mainMediaTag.getId());
            response.setName(mainMediaTag.getLabel());
            response.setIsCheck(false);

            List<MarketingCaseIndustryData> caseIndustryData = marketingCaseMap.get(mainMediaTag.getId());
            if (CollectionUtils.isNotEmpty(caseIndustryData)) {
                List<MarketingCaseIndustryListResponse> listResponses = caseIndustryData.stream().map(MarketingCaseIndustryListResponse::new).collect(Collectors.toList());
                response.setCaseIndustryData(listResponses);
            }

            responses.add(response);
        }
        return responses;
    }

}