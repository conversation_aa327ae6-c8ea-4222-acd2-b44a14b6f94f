package heran.media.management.platform.appanalyse.controller;

import heran.media.management.platform.appanalyse.dto.MediaAnalyseListDataResponse;
import heran.media.management.platform.appanalyse.dto.MediaAnalyseRequest;
import heran.media.management.platform.appanalyse.dto.MediaAnalyseResponse;
import heran.media.management.platform.appanalyse.dto.ResourceCategoryResponse;
import heran.media.management.platform.appanalyse.service.MediaAnalyseService;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.TMainTemplateComponent;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 媒介分析Controller
 * @Author: yzj
 * @Date: 2023/8/24 11:07
 * @Version: 1.0
 */
@ApiOperation(value = "媒介分析相关接口")
@Slf4j
@RestController
@RequestMapping("/app-analyse-media")
public class MediaAnalyseController {

    @Resource
    private MediaAnalyseService mediaAnalyseService;

    @ApiLog
    @ApiOperation(value = "新增媒介分析数据")
    @RequestMapping(value = "/insert", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'MEDIAANALYSIS_INSERT')")
    @ResponseBody
    public InternalResponse<Void> insert(@RequestBody @Valid MediaAnalyseRequest mediaAnalyse,@RequestParam("optType")String optType) {
        AuthUserInfo userInfo = AuthUtils.getUserInfo();
        mediaAnalyseService.insertUpdate(mediaAnalyse, userInfo.getIdentifier(),optType);
        return InternalResponse.success();
    }

    @ApiLog
    @ApiOperation(value = "编辑媒介分析数据")
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = "application/json")
    @PreAuthorize("hasPermission(null,'MEDIAANALYSIS_UPDATE')")
    @ResponseBody
    public InternalResponse<Void> update(@RequestBody @Valid MediaAnalyseRequest mediaAnalyse,@RequestParam("optType")String optType) {
        AuthUserInfo userInfo = AuthUtils.getUserInfo();
        mediaAnalyseService.insertUpdate(mediaAnalyse, userInfo.getIdentifier(),optType);
        return InternalResponse.success();
    }

    @ApiLog
    @ApiOperation(value = "条件查询", notes = "查询字段可包含【marketingScene(营销场景),dataModel(数据模型),media(媒介)】")
    @RequestMapping(value = "/conditionQuery", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<MediaAnalyseResponse>> conditionQuery(@RequestParam("templateCode") String templateCode,
                                                                               @RequestBody @Valid SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        PageResponse<MediaAnalyseResponse> pageResponse = mediaAnalyseService.conditionQueryNew(criteria, templateCode);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "根据配置id查询详情", notes = "根据配置id查询")
    @RequestMapping(value = "/getDetailByConfigId", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<MediaAnalyseResponse>> getDetailByConfigId(@RequestParam("configId") Integer configId) {
        InternalResponse internalResponse = null;
        List<MediaAnalyseResponse> mediaAnalyseResponseList = mediaAnalyseService.getDetailByConfigId(configId);
        internalResponse = InternalResponse.success().withBody(mediaAnalyseResponseList);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "目录查询")
    @RequestMapping(value = "/categoryQuery", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<ResourceCategoryResponse>> categoryQuery(@RequestParam("groupLabel") String groupLabel,
                                                                          @RequestParam(value = "secondaryCategoryName",required = false) String secondaryCategoryName) {
        InternalResponse internalResponse = null;
        List<ResourceCategoryResponse> categoryList = mediaAnalyseService.categoryQuery(groupLabel,secondaryCategoryName);
        internalResponse = InternalResponse.success().withBody(categoryList);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "根据关联id删除关联的媒介数据")
    @RequestMapping(value = "/deleteMediaDataByRelId", method = RequestMethod.DELETE, produces = "application/json")
    @PreAuthorize("hasPermission(null,'MEDIAANALYSIS_DELETE')")
    @ResponseBody
    public InternalResponse<Void> deleteMediaDataByRelId(@RequestParam("relId") Integer relId) {
        InternalResponse internalResponse = null;
        mediaAnalyseService.deleteMediaDataByRelId(relId);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "根据配置id删除关联的媒介数据")
    @RequestMapping(value = "/deleteMediaDataByConfigId", method = RequestMethod.DELETE, produces = "application/json")
    @PreAuthorize("hasPermission(null,'MEDIAANALYSIS_DELETE')")
    @ResponseBody
    public InternalResponse<Void> deleteMediaDataByConfigId(@RequestParam("configId") Integer configId) {
        InternalResponse internalResponse = null;
        AuthUserInfo userInfo = AuthUtils.getUserInfo();
        mediaAnalyseService.deleteMediaDataByConfigId(configId,userInfo.getIdentifier());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "获取组件列表")
    @RequestMapping(value = "/getComponentList", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<TMainTemplateComponent>> getComponentList(@RequestParam("componentGroup") String componentGroup) {
        InternalResponse internalResponse = null;
        List<TMainTemplateComponent> componentContentList = mediaAnalyseService.getComponentList(componentGroup);
        internalResponse = InternalResponse.success().withBody(componentContentList);
        return internalResponse;
    }


    @ApiLog
    @ApiOperation(value = "获取组件列表 templateCode:MEDIUM_ANALYSIS_SCENE")
    @RequestMapping(value = "/getMediaAnalyseListDataResponse", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<MediaAnalyseListDataResponse>> getMediaAnalyseListDataResponse(@RequestParam("templateCode") String templateCode) {
        InternalResponse internalResponse = null;
        List<MediaAnalyseListDataResponse> componentContentList = mediaAnalyseService.getMediaAnalyseListDataResponse(templateCode);
        internalResponse = InternalResponse.success().withBody(componentContentList);
        return internalResponse;
    }
}
