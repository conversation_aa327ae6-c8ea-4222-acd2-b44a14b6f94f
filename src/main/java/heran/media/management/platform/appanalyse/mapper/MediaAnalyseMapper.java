package heran.media.management.platform.appanalyse.mapper;


import heran.media.management.platform.appanalyse.dto.DataModelDataDetails;
import heran.media.management.platform.appanalyse.po.ComponentAttrRel;
import heran.media.management.platform.appanalyse.po.ComponentRelData;
import heran.media.management.platform.appanalyse.po.MediaAnalyseData;
import heran.media.management.platform.appanalyse.po.MediaAnalyseExtraInfo;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @Description:
 * @Author: yzj
 * @Date: 2023/8/24 15:43
 * @Version: 1.0
 */
@Mapper
public interface MediaAnalyseMapper {

    @SelectProvider(type = MediaAnalyseQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "configId", column = "configId"),
            @Result(property = "media", column = "display_title"),
            @Result(property = "displayPriority", column = "display_priority"),
            @Result(property = "componentRelId", column = "componentRelId"),
            @Result(property = "componentContent", column = "component_content"),
            @Result(property = "dataModel", column = "dataModel"),
            @Result(property = "dataModelId", column = "dataModelId"),
            @Result(property = "marketingScene", column = "category_name"),
            @Result(property = "sceneId", column = "sceneId"),
            @Result(property = "componentId", column = "componentId"),
            @Result(property = "componentName", column = "componentName"),
            @Result(property = "componentDataType", column = "component_data_type"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<MediaAnalyseData> list(SearchCriteria criteria, String templateCode);

    @SelectProvider(type = MediaAnalyseQuerySQLProvider.class, method = "selectDataModel")
    @Results(value = {
            @Result(property = "configId", column = "configId"),
            @Result(property = "dataModel", column = "dataModel"),
            @Result(property = "dataModelId", column = "dataModelId"),
            @Result(property = "marketingScene", column = "category_name"),
            @Result(property = "sceneId", column = "sceneId"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<MediaAnalyseData> listDataModel(SearchCriteria criteria, String templateCode);

    @Select("SELECT\n" +
            "t1.id componentId,\n" +
            "t1.component_name,\n" +
            "t1.component_code,\n" +
            "t1.component_group,\n" +
            "t1.default_title,\n" +
            "t1.position_type,\n" +
            "t1.component_template_html,\n" +
            "t1.component_data_type,\n" +
            "t1.data_handler,\n" +
            "t1.is_multiple,\n" +
            "t2.id attrId,\n" +
            "t2.attr_name,\n" +
            "t2.attr_key,\n" +
            "t2.data_type,\n" +
            "t2.optional_value,\n" +
            "t2.show_on_page, \n" +
            "t2.validation_regx, \n" +
            "t2.validation_tip \n" +
            "FROM\n" +
            "main_template_component t1\n" +
            "INNER JOIN main_component_attr t2 ON t1.id = t2.component_id \n" +
            "WHERE\n" +
            "t1.is_enabled = 1 and t2.is_enabled = 1 and \n" +
            "t1.component_group = #{group} ORDER BY t2.update_time asc ")
    @Results(id = "ComponentAttrRel-mapping", value = {
            @Result(property = "componentId", column = "componentId"),
            @Result(property = "componentName", column = "component_name"),
            @Result(property = "componentCode", column = "component_code"),
            @Result(property = "componentGroup", column = "component_group"),
            @Result(property = "defaultTitle", column = "default_title"),
            @Result(property = "positionType", column = "position_type"),
            @Result(property = "componentTemplateHtml", column = "component_template_html"),
            @Result(property = "componentDataType", column = "component_data_type"),
            @Result(property = "dataHandler", column = "data_handler"),
            @Result(property = "isMultiple", column = "is_multiple"),
            @Result(property = "attrId", column = "attrId"),
            @Result(property = "attrName", column = "attr_name"),
            @Result(property = "attrKey", column = "attr_key"),
            @Result(property = "dataType", column = "data_type"),
            @Result(property = "optionalValue", column = "optional_value"),
            @Result(property = "showOnPage", column = "show_on_page"),
            @Result(property = "validationRegx", column = "validation_regx"),
            @Result(property = "validationTip", column = "validation_tip"),
    })
    List<ComponentAttrRel> getComponentAttrRelByGroup(@Param("group") String group);

    @Select("SELECT\n" +
            "t1.id,\n" +
            "t1.component_content,t2.component_data_type,t1.display_priority \n" +
            "FROM\n" +
            "main_template_component_rel t1\n" +
            "INNER JOIN main_template_component t2 ON t1.component_id = t2.id \n" +
            "WHERE\n" +
            "t1.template_config_id = #{configId}")
    @Results(value = {
            @Result(property = "relId", column = "id"),
            @Result(property = "componentContent", column = "component_content"),
            @Result(property = "componentDataType", column = "component_data_type"),
            @Result(property = "displayPriority", column = "display_priority")
    }
    )
    List<ComponentRelData> getComponentRelDataByConfigId(@Param("configId") Integer configId);

    @Select("<script>" +
            "SELECT\n" +
            "template_config_id configId,\n" +
            "MAX( update_time ) updateTime\n" +
            "FROM\n" +
            "main_template_component_rel \n" +
            "WHERE\n" +
            "template_config_id IN " +
            "<foreach item='configId' collection='configIdList' open='(' separator=',' close=')'> \n" +
            "#{configId} " +
            "</foreach> \n" +
            "GROUP BY\n" +
            "template_config_id" +
            "</script>")
    @Results(value = {
            @Result(property = "configId", column = "configId"),
            @Result(property = "updateTime", column = "updateTime")
    })
    List<MediaAnalyseExtraInfo> getUpdateTimeByConfigIdList(@Param("configIdList") List<Integer> configIdList);


    @Select("SELECT t6.category_name modelName,t6.id modelId,t7.id sceneId " +
            "FROM main_template_generate_config t1 " +
            "JOIN main_resource_template t3 ON t1.template_id = t3.id " +
            "JOIN main_resource_category t6 ON t1.rel_scene_category_id = t6.id " +
            "JOIN main_resource_category t7 ON t6.parent_id = t7.id " +
            "WHERE t1.is_enabled = 1 AND t3.is_enabled = 1 AND t3.template_code = #{templateCode}")
    List<DataModelDataDetails> getDataModelDataDetails(@Param("templateCode") String templateCode);
}
