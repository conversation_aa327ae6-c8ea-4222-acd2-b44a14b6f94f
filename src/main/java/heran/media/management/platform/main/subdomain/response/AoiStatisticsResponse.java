package heran.media.management.platform.main.subdomain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AoiStatisticsResponse {
    @ApiModelProperty("AOI总量")
    private Integer allCount;
    @ApiModelProperty("城市总量")
    private Integer cityCount;
    @ApiModelProperty("城市aoi总量")
    private Integer cityAoiCount;
    @ApiModelProperty("标签aoi总量")
    private List<TagCountInfoDataResponse> tagAoiCount;
}
