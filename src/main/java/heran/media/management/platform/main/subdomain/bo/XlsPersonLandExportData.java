package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.main.subdomain.dto.TPersonLandExportData;
import heran.media.sharelib.domain.db.model.main.PersonLandData;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsPersonLandExportData {

    @XlsField(title = "城市", columnIndex = 0)
    private String cityName;

    @XlsField(title = "查询时间", columnIndex = 1)
    private String statMonthText;

    @XlsField(title = "居住人口统计/天级", columnIndex = 2)
    private Integer dwellPopulation;

    @XlsField(title = "工作人口统计/天级", columnIndex = 3)
    private Integer workPopulation;

    @XlsField(title = "常驻人口统计/天级", columnIndex = 4)
    private Integer residentPopulation;

    @XlsField(title = "客流人口统计/天级", columnIndex = 5)
    private Integer passengerPopulation;

    @XlsField(title = "客流人口统计/小时级", columnIndex = 6)
    private Integer passengerHourPopulation;

    @XlsField(title = "TA常驻人口数据", columnIndex = 7)
    private String crowdPack;

    @XlsField(title = "城市网络热力", columnIndex = 8)
    private String cityHotText;

    @XlsField(title = "城市网格人口",columnIndex = 9)
    private String wktTypes;

    @XlsField(title = "城市透视", columnIndex = 10)
    private String urbanPerspective;

    @XlsField(title = "更新时间", columnIndex = 11)
    private String updateTimeText;


    public XlsPersonLandExportData() {
    }

    public XlsPersonLandExportData(TPersonLandExportData personLandData) {
        this.cityName = personLandData.getCityName();
        this.statMonthText = personLandData.getStatMonthText();
        this.dwellPopulation = personLandData.getDwellPopulation();
        this.workPopulation = personLandData.getWorkPopulation();
        this.residentPopulation = personLandData.getResidentPopulation();
        this.passengerPopulation = personLandData.getPassengePopulation();
        this.passengerHourPopulation = personLandData.getPassengeHourPopulation();
        this.crowdPack = personLandData.getCrowdPack();
        this.updateTimeText = personLandData.getUpdateTimeText();
        this.cityHotText = personLandData.getCityHotText();
        this.urbanPerspective = personLandData.getUrbanPerspective();
        this.wktTypes = personLandData.getWktTypes();

    }



}
