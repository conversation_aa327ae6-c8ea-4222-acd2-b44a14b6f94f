package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class OrderEffectEstimateBaseCustomEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("order_effect_estimate_base_custom");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                     }
                    if(cri.getKey().equals("id")){
                        WHERE("id = "+ cri.getValue());
                    }
                    else if(cri.getKey().equalsIgnoreCase("orderId")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("order_id between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("order_id >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("order_id <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("order_id = "+ cri.getValue());
                    }else if(cri.getKey().equals("busCover") && cri.getValue()!=null){
                        WHERE("bus_cover like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("residenceCover")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("residence_cover between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("residence_cover >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("residence_cover <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("residence_cover = "+ cri.getValue());
                    }else if(cri.getKey().equals("officeBuildingCover") && cri.getValue()!=null){
                        WHERE("office_building_cover like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("cbdCover") && cri.getValue()!=null){
                        WHERE("cbd_cover like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("supermarketCover") && cri.getValue()!=null){
                        WHERE("supermarket_cover like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("universityCover") && cri.getValue()!=null){
                        WHERE("university_cover like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("administrativeRegionCover") && cri.getValue()!=null){
                        WHERE("administrative_region_cover like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("undergroundCover") && cri.getValue()!=null){
                        WHERE("underground_cover like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("hotelCover") && cri.getValue()!=null){
                        WHERE("hotel_cover like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("brandCover") && cri.getValue()!=null){
                        WHERE("brand_cover like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("isShow") && cri.getValue()!=null){
                        WHERE("is_show like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("remark") && cri.getValue()!=null){
                        WHERE("remark like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("extraAttrs") && cri.getValue()!=null){
                        WHERE("extra_attrs like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("createdByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("created_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("created_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("created_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("created_by_user = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("updatedByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("updated_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("updated_by_user = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }

                }
            };
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
