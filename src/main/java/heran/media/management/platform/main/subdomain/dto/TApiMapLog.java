package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.ApiMapLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TApiMapLog implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "171")
    private Long id;

    @ApiModelProperty(value = "请求参数", required = false, example = "请求参数")
    private String reqParam;

    @ApiModelProperty(value = "请求对象体", required = false, example = "请求对象体")
    private String reqBody;

    @ApiModelProperty(value = "是否成功", required = false, example = "72")
    private Integer isSuc;

    @ApiModelProperty(value = "错误码", required = false, example = "错误码")
    @Size(max = 64, message = "错误码长度不能超出64位")
    private String errCode;

    @ApiModelProperty(value = "错误信息", required = false, example = "错误信息")
    @Size(max = 512, message = "错误信息长度不能超出512位")
    private String errMessage;

    @ApiModelProperty(value = "响应体", required = false, example = "响应体")
    private String respData;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TApiMapLog() {
    }

    public TApiMapLog(ApiMapLog apiMapLog) {
        this.id = apiMapLog.getId();
        this.reqParam = apiMapLog.getReqParam();
        this.reqBody = apiMapLog.getReqBody();
        this.isSuc = apiMapLog.getIsSuc();
        this.errCode = apiMapLog.getErrCode();
        this.errMessage = apiMapLog.getErrMessage();
        this.respData = apiMapLog.getRespData();
        this.createdByUser = apiMapLog.getCreatedByUser();
        this.updatedByUser = apiMapLog.getUpdatedByUser();
        this.creator = apiMapLog.getCreator();
        this.updater = apiMapLog.getUpdater();
        this.createTime = apiMapLog.getCreateTime();
        this.updateTime = apiMapLog.getUpdateTime();

    }
}
