package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.main.MarketingSceneAoiDataDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface MarketingSceneAoiDataDetailEntityQueryMapper {
    @SelectProvider(type = MarketingSceneAoiDataDetailEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "marketingSceneAoiDataId", column = "marketing_scene_aoi_data_id"), @Result(property = "trafficStatistics", column = "traffic_statistics"), @Result(property = "passengerPortrayal", column = "passenger_portrayal"), @Result(property = "tripWay", column = "trip_way"), @Result(property = "passengerSource", column = "passenger_source"), @Result(property = "demographicStatistics", column = "demographic_statistics"), @Result(property = "crowdPortrayal", column = "crowd_portrayal"), @Result(property = "apiTripWay", column = "api_trip_way"), @Result(property = "crowdSource", column = "crowd_source"), @Result(property = "offlineScene", column = "offline_scene"), @Result(property = "remark", column = "remark"), @Result(property = "extraAttrs", column = "extra_attrs"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<MarketingSceneAoiDataDetail> list(SearchCriteria criteria);
}
