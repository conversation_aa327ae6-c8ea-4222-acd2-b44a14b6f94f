package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.ApiMapUsedData;
import heran.media.management.platform.main.subdomain.dto.MainMediaCbdList;
import heran.media.sharelib.domain.db.model.main.ApiMapLog;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface ApiMapLogEntityQueryMapper {
    @SelectProvider(type = ApiMapLogEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "reqParam", column = "req_param"), @Result(property = "reqBody", column = "req_body"), @Result(property = "isSuc", column = "is_suc"), @Result(property = "errCode", column = "err_code"), @Result(property = "errMessage", column = "err_message"), @Result(property = "respData", column = "resp_data"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<ApiMapLog> list(SearchCriteria criteria);

    @Select("SELECT biz_type,COUNT(id) as today_used FROM `api_map_log` WHERE DATE(create_time) = CURDATE() and created_by_user = #{accountId} GROUP BY biz_type  ")
    List<ApiMapUsedData> getTodayUsed(@Param("accountId") Integer accountId);
}
