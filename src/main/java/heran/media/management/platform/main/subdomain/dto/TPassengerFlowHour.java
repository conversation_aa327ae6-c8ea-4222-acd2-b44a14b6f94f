package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.PassengerFlowHour;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TPassengerFlowHour implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "165")
    private Integer id;

    @ApiModelProperty(value = "人地数据id", required = true, example = "216")
    @NotNull(message = "人地数据id不能为空")
    private Integer landDataId;

    @ApiModelProperty(value = "小时", required = true, example = "小时")
    @Size(max = 64, message = "小时长度不能超出64位")
    @NotBlank(message = "小时不能为空")
    private Integer dataHour;

    @ApiModelProperty(value = "客流人数", required = true, example = "250")
    @NotNull(message = "客流人数不能为空")
    private Integer passengerFlowCount;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date updateTime;


    public TPassengerFlowHour() {
    }

    public TPassengerFlowHour(PassengerFlowHour passengerFlowHour) {
        this.id = passengerFlowHour.getId();
        this.landDataId = passengerFlowHour.getLandDataId();
        this.dataHour = passengerFlowHour.getDataHour();
        this.passengerFlowCount = passengerFlowHour.getPassengerFlowCount();
        this.createdByUser = passengerFlowHour.getCreatedByUser();
        this.updatedByUser = passengerFlowHour.getUpdatedByUser();
        this.creator = passengerFlowHour.getCreator();
        this.updater = passengerFlowHour.getUpdater();
        this.createTime = passengerFlowHour.getCreateTime();
        this.updateTime = passengerFlowHour.getUpdateTime();

    }
}
