package heran.media.management.platform.main.subdomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SceneAoiMapDataRequest {
    @ApiModelProperty("查询月份")
    @NotNull(message = "查询月份不能为空")
    private String statMonth;
    @ApiModelProperty("洞察类型")
    @NotNull(message = "洞察类型不能为空 商圈洞察（CBD）特定场景洞察（AOI）")
    private String type;
    @ApiModelProperty("业务id AOIid")
    private List<Integer> bizIds;
    @ApiModelProperty("人口口径 DWELL_POPULATION（居住人口）RESIDENT_POPULATION（常驻人口）WORK_POPULATION（工作人口）PASSENGER_POPULATION（客流人口）WEEKDAY（工作日客流）ALL_DAY（日常客流）HOLIDAY（节假日客流）")
    @NotNull(message = "人口口径不能为空 ")
    private List<String> poiPopulation;
    @ApiModelProperty("请求内容 人口统计（DEMOGRAPHIC_STATISTICS）人群画像（PORTRAIT_CROWD）人群来源（SOURCE_POPULATION）流量统计（FLOW_STATISTICS）")
    @NotNull(message = "请求内容不能为空")
    private List<String> requestContent;
    @ApiModelProperty("热力值人口口径 DWELL_POPULATION（居住人口）RESIDENT_POPULATION（常驻人口）WORK_POPULATION（工作人口）PASSENGER_POPULATION（客流人口）WEEKDAY（工作日客流）ALL_DAY（日常客流）HOLIDAY（节假日客流）")
    @NotNull(message = "热力值人口口径不能为空")
    private List<String> hotPoiPopulation;
}
