package heran.media.management.platform.main.subdomain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class SmartCityDimensionTaskData {
    @ApiModelProperty("是否启用")
    private Boolean isEnabled;
    @ApiModelProperty("城市")
    private List<String> regionCodes;
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("人口口径")
    private List<String> poiPopulation;
    @ApiModelProperty(value = "洞察纬度", example = "age,sex,education")
    private String inSightDimension;
}
