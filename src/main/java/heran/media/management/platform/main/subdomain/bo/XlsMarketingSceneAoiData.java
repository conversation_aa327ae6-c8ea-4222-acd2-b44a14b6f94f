package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.MarketingSceneAoiData;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsMarketingSceneAoiData {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "查询月份", columnIndex = 1)
    private String statMonth;

    @XlsField(title = "省份编号", columnIndex = 2)
    private String regionCode;

    @XlsField(title = "bizId", columnIndex = 8)
    private Integer bizId;

    @XlsField(title = "数值", columnIndex = 13)
    private Integer aoiNum;

    @XlsField(title = "人口口径（到时候看可以不可以定义成字典数据）", columnIndex = 14)
    private String poiPopulation;

    @XlsField(title = "请求内容", columnIndex = 15)
    private String requestContent;

    @XlsField(title = "说明", columnIndex = 16)
    private String remark;

    @XlsField(title = "扩展字段", columnIndex = 17)
    private String extraAttrs;

    @XlsField(title = "行记录创建用户", columnIndex = 18)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 19)
    private Integer updatedByUser;


    public XlsMarketingSceneAoiData() {
    }

    public XlsMarketingSceneAoiData(MarketingSceneAoiData marketingSceneAoiData) {
        this.id = marketingSceneAoiData.getId();
        this.statMonth = marketingSceneAoiData.getStatMonth();
        this.bizId = marketingSceneAoiData.getBizId();
        this.aoiNum = marketingSceneAoiData.getAoiNum();
        this.poiPopulation = marketingSceneAoiData.getPoiPopulation();
        this.requestContent = marketingSceneAoiData.getRequestContent();
        this.remark = marketingSceneAoiData.getRemark();
        this.extraAttrs = marketingSceneAoiData.getExtraAttrs();
        this.createdByUser = marketingSceneAoiData.getCreatedByUser();
        this.updatedByUser = marketingSceneAoiData.getUpdatedByUser();

    }

    public MarketingSceneAoiData adapToPO(Integer userId) {
        MarketingSceneAoiData marketingSceneAoiData = new MarketingSceneAoiData();
        marketingSceneAoiData.setId(id);
        marketingSceneAoiData.setStatMonth(statMonth);
        marketingSceneAoiData.setRegionCode(regionCode);
        marketingSceneAoiData.setBizId(bizId);
        marketingSceneAoiData.setAoiNum(aoiNum);
        marketingSceneAoiData.setPoiPopulation(poiPopulation);
        marketingSceneAoiData.setRequestContent(requestContent);
        marketingSceneAoiData.setRemark(remark);
        marketingSceneAoiData.setExtraAttrs(extraAttrs);

        marketingSceneAoiData.setCreatedByUser(userId);
        marketingSceneAoiData.setUpdatedByUser(userId);
        return marketingSceneAoiData;
    }
}
