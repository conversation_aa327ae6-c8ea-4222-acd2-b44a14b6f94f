package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.DataTaskListData;
import heran.media.management.platform.main.subdomain.dto.StructureClearlyTaskData;
import heran.media.sharelib.domain.db.model.main.DataTask;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface DataTaskEntityQueryMapper {
    @SelectProvider(type = DataTaskEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "taskName", column = "task_name"), @Result(property = "taskStatus", column = "task_status"), @Result(property = "regionCode", column = "region_code"), @Result(property = "statMonth", column = "stat_month"), @Result(property = "disposeTime", column = "dispose_time"), @Result(property = "numberSmartCity", column = "number_smart_city"), @Result(property = "numberSmartCityRequest", column = "number_smart_city_request"), @Result(property = "numberSmartTa", column = "number_smart_ta"), @Result(property = "numberSmartTaRequest", column = "number_smart_ta_request"), @Result(property = "cbdInsight", column = "cbd_Insight"), @Result(property = "cbdInsightRequest", column = "cbd_Insight_request"), @Result(property = "specificSceneInsight", column = "specific_scene_Insight"), @Result(property = "specificSceneInsightRequest", column = "specific_scene_Insight_request"), @Result(property = "numberSmartCityPerspective", column = "number_smart_city_perspective"), @Result(property = "numberSmartCityPerspectiveRequest", column = "number_smart_city_perspective_request"), @Result(property = "remark", column = "remark"), @Result(property = "extraAttrs", column = "extra_attrs"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<DataTask> list(SearchCriteria criteria);


    /**
     * 列表数据
     *
     * @param criteria 查询条件
     * @return List<DataTaskListData>
     */
    @SelectProvider(type = DataTaskEntityQuerySQLProvider.class, method = "selectList")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "taskName", column = "task_name"),
            @Result(property = "taskStatus", column = "task_status"),
            @Result(property = "regionCodes", column = "region_code_all"),
            @Result(property = "statMonth", column = "stat_month_all"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<DataTaskListData> selectList(SearchCriteria criteria);

    @Select({
            "<script>",
            "SELECT stat_month,region_code,statistical_type,dimension_name  FROM statistics_dimension ",
            "WHERE stat_month = #{statMonth} ",
            "<if test='regionCodeList != null and regionCodeList.size() > 0'>",
            "AND region_code IN ",
            "<foreach item='regionCode' collection='regionCodeList' open='(' separator=',' close=')'>",
            "#{regionCode}",
            "</foreach>",
            "</if>",
            "<if test='dimensionList != null and dimensionList.size() > 0'>",
            "AND dimension_name IN ",
            "<foreach item='dimension' collection='dimensionList' open='(' separator=',' close=')'>",
            "#{dimension}",
            "</foreach>",
            "</if>",
            "</script>"
    })
    List<StructureClearlyTaskData> getStructureClearlyTaskDataList(
            @Param("statMonth") String statMonth,
            @Param("regionCodeList") List<String> regionCodeList,
            @Param("dimensionList") List<String> dimensionList
    );
}
