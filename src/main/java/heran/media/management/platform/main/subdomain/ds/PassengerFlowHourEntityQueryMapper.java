package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.TPassengerFlowHour;
import heran.media.sharelib.domain.db.model.main.PassengerFlowHour;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PassengerFlowHourEntityQueryMapper {
    @SelectProvider(type = PassengerFlowHourEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "landDataId", column = "land_data_id"), @Result(property = "dataHour", column = "data_hour"), @Result(property = "passengerFlowCount", column = "passenger_flow_count"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<PassengerFlowHour> list(SearchCriteria criteria);


    @Insert({"<script>",
            "INSERT INTO passenger_flow_hour(id,land_data_id,data_hour, passenger_flow_count, created_by_user, updated_by_user, creator, updater,create_time)",
            "VALUES ",
            "<foreach item='item' collection='batchInsertList' separator=','>",
            "(#{item.id},#{item.landDataId},#{item.dataHour}, #{item.passengerFlowCount},  #{item.createdByUser}, #{item.updatedByUser}, #{item.creator}, #{item.updater},#{item.createTime})",
            "</foreach>",

            "</script>"
    })
    void batchInsertEntities(List<PassengerFlowHour> batchInsertList);

    @Delete("DELETE FROM passenger_flow_hour WHERE land_data_id= #{landDataId}")
    void deleteByLandDataId(@Param("landDataId") int landDataId);

    @Select("select * from passenger_flow_hour where land_data_id = #{landDataId} order by data_hour asc")
    List<TPassengerFlowHour> getListByLandDataId(@Param("landDataId") int landDataId);
}
