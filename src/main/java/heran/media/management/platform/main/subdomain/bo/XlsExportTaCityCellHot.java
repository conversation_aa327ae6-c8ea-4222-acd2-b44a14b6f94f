package heran.media.management.platform.main.subdomain.bo;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.main.subdomain.dto.ExportCityCellHotData;
import heran.media.management.platform.main.subdomain.dto.ExportTaCityCellHot;
import heran.media.sharelib.domain.db.model.main.CellHotValueWktConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XlsExportTaCityCellHot {
    @XlsField(title = "查询时间", columnIndex = 0)
    private String statMonth;
    @XlsField(title = "adcode", columnIndex = 1)
    private String code;
    @XlsField(title = "城市", columnIndex = 2)
    private String cityName;
    @XlsField(title = "1000网格ID", columnIndex = 3)
    private String cellId;
    @XlsField(title = "中心点坐标", columnIndex = 4)
    private String center;
    @XlsField(title = "左上顶点", columnIndex = 5)
    private String upLeft;
    @XlsField(title = "左下顶点", columnIndex = 6)
    private String lowerLeft;
    @XlsField(title = "右下顶点", columnIndex = 7)
    private String lowerRight;
    @XlsField(title = "右上顶点", columnIndex = 8)
    private String upRight;
    @XlsField(title = "汇总100网格人口", columnIndex = 9)
    private String peopleCount;
    @XlsField(title = "包含100网格数量", columnIndex = 10)
    private String cellCount;
    @XlsField(title = "TA常驻热力", columnIndex = 11)
    private String taResidentHotValue;
    @XlsField(title = "TA常驻人口", columnIndex = 12)
    private String taResidentPeopleValue;
    @XlsField(title = "TA客流热力", columnIndex = 13)
    private String taPassengerHotValue;
    @XlsField(title = "TA客流人口", columnIndex = 14)
    private String taPassengerPeopleValue;

    public XlsExportTaCityCellHot(String statMonth, String regionCode, ExportTaCityCellHot data,
                                  CellHotValueWktConfig residentConfigData,
                                  CellHotValueWktConfig passengerConfigData,
                                  String regionName) {
        this.statMonth = statMonth;
        this.code = regionCode;
        this.cityName = regionName;
        if (StringUtils.isNotEmpty(data.getCellId())) {
            String cellId = data.getCellId();
            this.cellId = cellId.length() >= 6 ? cellId.substring(0, 6) : cellId;
        }
        this.center = data.getCenter();
        this.upLeft = data.getUpLeft();
        this.lowerLeft = data.getLowerLeft();
        this.lowerRight = data.getLowerRight();
        this.upRight = data.getUpRight();
        this.peopleCount = data.getPeopleCount();
        this.cellCount = data.getCellCount();
        this.taResidentHotValue = data.getResidentHeat();
        if (residentConfigData != null && residentConfigData.getMaxUv() != null && StringUtils.isNotEmpty(data.getResidentHeat())) {
            this.taResidentPeopleValue = new BigDecimal(residentConfigData.getMaxUv())
                    .multiply(new BigDecimal(data.getResidentHeat())).divide(new BigDecimal(100), 0, BigDecimal.ROUND_HALF_UP).toString();
        }
        this.taPassengerHotValue = data.getPassengerHeat();
        if (passengerConfigData != null && passengerConfigData.getMaxUv() != null && StringUtils.isNotEmpty(data.getPassengerHeat())) {
            this.taPassengerPeopleValue = new BigDecimal(passengerConfigData.getMaxUv())
                    .multiply(new BigDecimal(data.getPassengerHeat())).divide(new BigDecimal(100), 0, BigDecimal.ROUND_HALF_UP).toString();
        }
    }
}
