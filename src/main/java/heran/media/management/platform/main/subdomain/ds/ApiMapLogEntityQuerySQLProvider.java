package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class ApiMapLogEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("api_map_log");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                     }
                    if(cri.getKey().equals("id")){
                        WHERE("id = "+ cri.getValue());
                    }
                    else if(cri.getKey().equals("reqParam") && cri.getValue()!=null){
                        WHERE("req_param like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("reqBody") && cri.getValue()!=null){
                        WHERE("req_body like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("isSuc")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("is_suc between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("is_suc >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("is_suc <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("is_suc = "+ cri.getValue());
                    }else if(cri.getKey().equals("errCode") && cri.getValue()!=null){
                        WHERE("err_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("errMessage") && cri.getValue()!=null){
                        WHERE("err_message like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("respData") && cri.getValue()!=null){
                        WHERE("resp_data like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("createdByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("created_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("created_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("created_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("created_by_user = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("updatedByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("updated_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("updated_by_user = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }

                }
            };
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
