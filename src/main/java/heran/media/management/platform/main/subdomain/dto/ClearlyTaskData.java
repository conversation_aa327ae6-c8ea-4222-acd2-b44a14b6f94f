package heran.media.management.platform.main.subdomain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ClearlyTaskData {
    @ApiModelProperty("是否启用")
    private Boolean isEnabled;
    @ApiModelProperty("城市")
    private List<String> regionCodes;
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("人口口径")
    private List<String> poiPopulation;
    @ApiModelProperty("热力值人口口径")
    private List<String> hotPopulation;
    @ApiModelProperty("aoiId")
    private List<Integer> bizId;
}
