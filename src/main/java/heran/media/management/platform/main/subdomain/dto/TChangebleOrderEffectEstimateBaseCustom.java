package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseCustom;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleOrderEffectEstimateBaseCustom implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "101")
    private Integer id;

    @ApiModelProperty(value = "订单id", required = true, example = "110")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "公交车覆盖", required = true, example = "公交车覆盖")
    @Size(max = 64, message = "公交车覆盖长度不能超出64位")
    @NotBlank(message = "公交车覆盖不能为空")
    private String busCover;

    @ApiModelProperty(value = "住宅区覆盖", required = false, example = "243")
    private Integer residenceCover;

    @ApiModelProperty(value = "写字楼覆盖", required = false, example = "写字楼覆盖")
    @Size(max = 64, message = "写字楼覆盖长度不能超出64位")
    private String officeBuildingCover;

    @ApiModelProperty(value = "商圈覆盖", required = false, example = "商圈覆盖")
    @Size(max = 64, message = "商圈覆盖长度不能超出64位")
    private String cbdCover;

    @ApiModelProperty(value = "超市覆盖", required = false, example = "超市覆盖")
    @Size(max = 64, message = "超市覆盖长度不能超出64位")
    private String supermarketCover;

    @ApiModelProperty(value = "大学覆盖", required = false, example = "大学覆盖")
    @Size(max = 64, message = "大学覆盖长度不能超出64位")
    private String universityCover;

    @ApiModelProperty(value = "行政区覆盖", required = false, example = "行政区覆盖")
    @Size(max = 64, message = "行政区覆盖长度不能超出64位")
    private String administrativeRegionCover;

    @ApiModelProperty(value = "地铁站覆盖", required = false, example = "地铁站覆盖")
    @Size(max = 64, message = "地铁站覆盖长度不能超出64位")
    private String undergroundCover;

    @ApiModelProperty(value = "星级酒店覆盖", required = false, example = "星级酒店覆盖")
    @Size(max = 64, message = "星级酒店覆盖长度不能超出64位")
    private String hotelCover;

    @ApiModelProperty(value = "品牌零售覆盖", required = false, example = "品牌零售覆盖")
    @Size(max = 64, message = "品牌零售覆盖长度不能超出64位")
    private String brandCover;

    @ApiModelProperty(value = "是否显示", required = false, example = "是否显示")
    @Size(max = 64, message = "是否显示长度不能超出64位")
    private String isShow;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangebleOrderEffectEstimateBaseCustom() {
    }

    public OrderEffectEstimateBaseCustom adapToPO() {
        OrderEffectEstimateBaseCustom orderEffectEstimateBaseCustom = new OrderEffectEstimateBaseCustom();
        orderEffectEstimateBaseCustom.setId(id);
        orderEffectEstimateBaseCustom.setOrderId(orderId);
        orderEffectEstimateBaseCustom.setBusCover(busCover);
        orderEffectEstimateBaseCustom.setResidenceCover(residenceCover);
        orderEffectEstimateBaseCustom.setOfficeBuildingCover(officeBuildingCover);
        orderEffectEstimateBaseCustom.setCbdCover(cbdCover);
        orderEffectEstimateBaseCustom.setSupermarketCover(supermarketCover);
        orderEffectEstimateBaseCustom.setUniversityCover(universityCover);
        orderEffectEstimateBaseCustom.setAdministrativeRegionCover(administrativeRegionCover);
        orderEffectEstimateBaseCustom.setUndergroundCover(undergroundCover);
        orderEffectEstimateBaseCustom.setHotelCover(hotelCover);
        orderEffectEstimateBaseCustom.setBrandCover(brandCover);
        orderEffectEstimateBaseCustom.setIsShow(isShow);
        orderEffectEstimateBaseCustom.setRemark(remark);
        orderEffectEstimateBaseCustom.setExtraAttrs(extraAttrs);

        return orderEffectEstimateBaseCustom;
    }
}
