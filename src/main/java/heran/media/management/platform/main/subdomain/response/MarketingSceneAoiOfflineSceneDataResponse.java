package heran.media.management.platform.main.subdomain.response;

import heran.media.sharelib.domain.db.model.main.PoiAoiOfflineSceneDetail;
import heran.media.sharelib.domain.db.model.main.PoiCityOfflineSceneDetail;
import heran.media.sharelib.domain.db.model.main.PoiTaskOfflineSceneDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MarketingSceneAoiOfflineSceneDataResponse {
    @ApiModelProperty("主键")
    private Integer id;
    @ApiModelProperty("场景大类")
    private String tagName;
    @ApiModelProperty("场景大类占比")
    private String tagValue;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("是否修改过")
    private Boolean isUpdate;
    @ApiModelProperty("场景中类")
    public List<MarketingSceneAoiOfflineSceneDataResponse> responses;

    public MarketingSceneAoiOfflineSceneDataResponse(PoiCityOfflineSceneDetail data, List<MarketingSceneAoiOfflineSceneDataResponse> responses, boolean isBackups) {
        this.id = data.getId();
        this.tagName = data.getTagName();
        if (isBackups) {
            this.tagValue = data.getTagValue();
        } else {
            this.tagValue = data.getIsUpdate() ? data.getUpdateTagValue() : data.getTagValue();
        }
        this.updateTime = data.getUpdateTime();
        this.isUpdate = data.getIsUpdate();
        this.responses = responses;
    }

    public MarketingSceneAoiOfflineSceneDataResponse(PoiAoiOfflineSceneDetail data, List<MarketingSceneAoiOfflineSceneDataResponse> responses, boolean isBackups) {
        this.id = data.getId();
        this.tagName = data.getTagName();
        if (isBackups) {
            this.tagValue = data.getTagValue();
        } else {
            this.tagValue = data.getIsUpdate() ? data.getUpdateTagValue() : data.getTagValue();
        }
        this.updateTime = data.getUpdateTime();
        this.isUpdate = data.getIsUpdate();
        this.responses = responses;
    }

    public MarketingSceneAoiOfflineSceneDataResponse(PoiCityOfflineSceneDetail data, boolean isBackups) {
        this.id = data.getId();
        this.tagName = data.getTagName();
        if (isBackups) {
            this.tagValue = data.getTagValue();
        } else {
            this.tagValue = data.getIsUpdate() ? data.getUpdateTagValue() : data.getTagValue();
        }
        this.updateTime = data.getUpdateTime();
        this.isUpdate = data.getIsUpdate();
    }

    public MarketingSceneAoiOfflineSceneDataResponse(PoiTaskOfflineSceneDetail data, boolean isBackups) {
        this.id = data.getId().intValue();
        this.tagName = data.getTagName();
        if (isBackups) {
            this.tagValue = data.getTagValue();
        } else {
            this.tagValue = data.getIsUpdate() ? data.getUpdateTagValue() : data.getTagValue();
        }
        this.updateTime = data.getUpdateTime();
        this.isUpdate = data.getIsUpdate();
    }
}
