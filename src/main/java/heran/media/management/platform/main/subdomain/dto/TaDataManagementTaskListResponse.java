package heran.media.management.platform.main.subdomain.dto;

import com.alibaba.nacos.common.utils.StringUtils;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TaDataManagementTaskListResponse {
    @ApiModelProperty(value = "主键", required = false, example = "6")
    private Integer id;
    @ApiModelProperty(value = "任务ID（不知是否有有特殊ID 预留字段）", required = true, example = "任务ID（不知是否有有特殊ID 预留字段）")
    private String taskCode;
    @ApiModelProperty(value = "任务名称", required = true, example = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "任务状态 PENDING_REQUEST（待请求）ON_REQUEST（请求中）FINISHED（已完成）", required = true, example = "任务状态 PENDING_REQUEST（待请求）ON_REQUEST（请求中）FINISHED（已完成）")
    private String taskStatus;
    @ApiModelProperty(value = "人群包id", required = true, example = "220")
    @NotNull(message = "人群包id不能为空")
    private Integer crowdPackId;
    @ApiModelProperty(value = "人群包名称", required = true, example = "220")
    private String crowdPackName;
    @ApiModelProperty("区域编码")
    private List<String> regionCode;
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("TA网格热力指数")
    private String taEnergetics;
    @ApiModelProperty("TA人口统计")
    private String taPopulation;
    @ApiModelProperty("TA洞察维度")
    private String taInsight;
    @ApiModelProperty("TA网格UV")
    private String taCellUvPopulation;
    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;
    @ApiModelProperty(value = "高德任务id", required = false, example = "高德任务id")
    private String mapTaskId;
    @ApiModelProperty("TA接口类型")
    private String taType;

    public TaDataManagementTaskListResponse(MediaTaDataManagementTaskData data) {
        this.id = data.getId();
        this.taskCode = data.getTaskCode();
        this.taskName = data.getTaskName();
        this.taskStatus = data.getTaskStatus();
        this.crowdPackId = data.getCrowdPackId();
        this.crowdPackName = data.getCrowdPackName();
        this.updateTime = data.getUpdateTime();
        this.mapTaskId = data.getMapTaskId();
        this.statMonth = data.getStatMonth();
        this.taEnergetics = data.getTaEnergetics();
        this.taPopulation = data.getTaPopulation();
        this.taInsight = data.getTaInsight();
        this.taCellUvPopulation = data.getTaEnergetics();
        this.taType = data.getTaType();
        if (StringUtils.isNotEmpty(data.getRegionCode())) {
            Gson gson = new Gson();
            this.regionCode = gson.fromJson(data.getRegionCode(), new TypeToken<List<String>>() {
            }.getType());
        }
    }
}
