package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.main.AoiDrawTagRel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface AoiDrawTagRelEntityQueryMapper {
    @SelectProvider(type = AoiDrawTagRelEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "aoiDrawId", column = "aoi_draw_id"), @Result(property = "tagId", column = "tag_id"), @Result(property = "remark", column = "remark"), @Result(property = "extraAttrs", column = "extra_attrs"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<AoiDrawTagRel> list(SearchCriteria criteria);
}
