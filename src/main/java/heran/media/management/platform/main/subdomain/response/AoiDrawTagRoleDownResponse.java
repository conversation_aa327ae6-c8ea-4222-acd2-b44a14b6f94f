package heran.media.management.platform.main.subdomain.response;

import heran.media.sharelib.domain.db.model.main.MainMediaTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AoiDrawTagRoleDownResponse {
    @ApiModelProperty("主键id")
    private String id;
    @ApiModelProperty("标签名称")
    private String label;
    @ApiModelProperty("父级标签")
    private String parentId;
    @ApiModelProperty("是否AOI")
    private Boolean isAoi;

    @ApiModelProperty("次级标签")
    private List<AoiDrawTagRoleDownResponse> secondaryAoiDrawTagData;

    public AoiDrawTagRoleDownResponse(MainMediaTag tag) {
        this.id = "TAG_" + tag.getId().toString();
        this.label = tag.getLabel();
        this.parentId = tag.getParentId() == null ? null : "TAG_" + tag.getParentId().toString();
        this.isAoi = false;
    }

    public AoiDrawTagRoleDownResponse(AoiDrawRoleDownResponse response) {
        this.id = response.getId().toString();
        this.label = response.getAoiName();
        this.parentId = response.getTagId() == null ? null : "TAG_" + response.getTagId().toString();
        this.isAoi = true;
    }
}
