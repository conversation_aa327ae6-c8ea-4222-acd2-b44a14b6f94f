package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.RouteSelectionSchemeLine;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TRouteSelectionSchemeLine implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "12")
    private Integer id;

    @ApiModelProperty(value = "公交智能计算选线方案id", required = true, example = "162")
    @NotNull(message = "公交智能计算选线方案id不能为空")
    private Integer schemeId;

    @ApiModelProperty(value = "区域code", required = true, example = "区域code")
    @Size(max = 64, message = "区域code长度不能超出64位")
    @NotBlank(message = "区域code不能为空")
    private String regionCode;

    @ApiModelProperty(value = "线路名称", required = true, example = "线路名称")
    @Size(max = 64, message = "线路名称长度不能超出64位")
    @NotBlank(message = "线路名称不能为空")
    private String lineName;

    @ApiModelProperty(value = "级别", required = false, example = "级别")
    @Size(max = 64, message = "级别长度不能超出64位")
    private String level;

    @ApiModelProperty(value = "投放数量", required = false, example = "91")
    private Integer launchCount;

    @ApiModelProperty(value = "投放周期", required = false, example = "73")
    private Integer launchPeriod;

    @ApiModelProperty(value = "制作费", required = false, example = "24")
    private BigDecimal productionCost;

    @ApiModelProperty(value = "发布费", required = false, example = "69")
    private BigDecimal issueCost;

    @ApiModelProperty(value = "费用总计", required = false, example = "128")
    private BigDecimal costAggregate;

    @ApiModelProperty(value = "POI", required = true, example = "POI")
    @Size(max = 64, message = "POI长度不能超出64位")
    @NotBlank(message = "POI不能为空")
    private String poi;

    @ApiModelProperty(value = "aoi", required = true, example = "aoi")
    @Size(max = 64, message = "aoi长度不能超出64位")
    @NotBlank(message = "aoi不能为空")
    private String aoi;

    @ApiModelProperty(value = "商圈", required = true, example = "商圈")
    @Size(max = 64, message = "商圈长度不能超出64位")
    @NotBlank(message = "商圈不能为空")
    private String cbd;

    @ApiModelProperty(value = "干道", required = true, example = "干道")
    @Size(max = 64, message = "干道长度不能超出64位")
    @NotBlank(message = "干道不能为空")
    private String mainLine;

    @ApiModelProperty(value = "热力", required = true, example = "热力")
    @Size(max = 64, message = "热力长度不能超出64位")
    @NotBlank(message = "热力不能为空")
    private String heatingPower;

    @ApiModelProperty(value = "TA热力", required = true, example = "TA热力")
    @Size(max = 64, message = "TA热力长度不能超出64位")
    @NotBlank(message = "TA热力不能为空")
    private String taHeatingPower;

    @ApiModelProperty(value = "是否勾选", required = true, example = "227")
    @NotNull(message = "是否勾选不能为空")
    private Integer pitchOn;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TRouteSelectionSchemeLine() {
    }

    public TRouteSelectionSchemeLine(RouteSelectionSchemeLine routeSelectionSchemeLine) {
        this.id = routeSelectionSchemeLine.getId();
        this.schemeId = routeSelectionSchemeLine.getSchemeId();
        this.lineName = routeSelectionSchemeLine.getLineName();
        this.level = routeSelectionSchemeLine.getLevel();
        this.launchCount = routeSelectionSchemeLine.getLaunchCount();
        this.launchPeriod = routeSelectionSchemeLine.getLaunchPeriod();
        this.productionCost = routeSelectionSchemeLine.getProductionCost();
        this.issueCost = routeSelectionSchemeLine.getIssueCost();
        this.costAggregate = routeSelectionSchemeLine.getCostAggregate();
        this.poi = routeSelectionSchemeLine.getPoi();
        this.aoi = routeSelectionSchemeLine.getAoi();
        this.cbd = routeSelectionSchemeLine.getCbd();
        this.mainLine = routeSelectionSchemeLine.getMainLine();
        this.pitchOn = routeSelectionSchemeLine.getPitchOn();
        this.remark = routeSelectionSchemeLine.getRemark();
        this.extraAttrs = routeSelectionSchemeLine.getExtraAttrs();
        this.createdByUser = routeSelectionSchemeLine.getCreatedByUser();
        this.updatedByUser = routeSelectionSchemeLine.getUpdatedByUser();
        this.creator = routeSelectionSchemeLine.getCreator();
        this.updater = routeSelectionSchemeLine.getUpdater();
        this.createTime = routeSelectionSchemeLine.getCreateTime();
        this.updateTime = routeSelectionSchemeLine.getUpdateTime();

    }
}
