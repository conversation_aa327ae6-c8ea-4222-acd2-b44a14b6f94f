package heran.media.management.platform.main.subdomain.response;

import heran.media.management.platform.main.subdomain.dto.TaResidentData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
@Data
public class TaResidentDataResponse {
    @ApiModelProperty("人群包id")
    private Integer packId;
    @ApiModelProperty("人群包名称")
    private String packName;
    @ApiModelProperty("人群包一级分类")
    private String categoryLv1Name;
    @ApiModelProperty("人群包二级分类")
    private String categoryLv2Name;
    @ApiModelProperty("TA常驻人口")
    private Integer populaceCount;
    @ApiModelProperty("占比")
    private String ratio;

    public TaResidentDataResponse(TaResidentData data, Integer residentCount) {
        this.packId = data.getCrowdPackId();
        this.packName = data.getPackName();
        this.categoryLv1Name = data.getCategoryLv1Name();
        this.categoryLv2Name = data.getCategoryLv2Name();
        this.populaceCount = data.getPopulaceCount();
        if (populaceCount != null) {
            BigDecimal bigDecimal = new BigDecimal(populaceCount);
            this.ratio = bigDecimal.divide(new BigDecimal(residentCount), 5, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).toString();
        }
    }
}
