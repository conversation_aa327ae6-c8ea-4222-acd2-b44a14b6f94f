package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.main.subdomain.dto.PoiAnalyseDetailListData;
import heran.media.sharelib.domain.db.model.main.PoiAnalyseDetail;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@ToString
@Data
public class XlsPoiAnalyseDetail {

    @XlsField(title = "省份", columnIndex = 0)
    private String provinceName;

    @XlsField(title = "城市", columnIndex = 1)
    private String cityName;

    @XlsField(title = "区域/县级市", columnIndex = 2)
    private String districtName;

    @XlsField(title = "品牌编号", columnIndex = 3)
    private String code;

    @XlsField(title = "品牌名称", columnIndex = 4)
    private String analyseName;

    @XlsField(title = "poiId", columnIndex = 5)
    private String poiId;

    @XlsField(title = "点位名称", columnIndex = 6)
    private String poiName;

    @XlsField(title = "点位地址", columnIndex = 7)
    private String address;

    @XlsField(title = "经度", columnIndex = 8)
    private String longitude;

    @XlsField(title = "维度", columnIndex = 9)
    private String latitude;

    @XlsField(title = "poi类型编码", columnIndex = 10)
    private String poiType;

    @XlsField(title = "更新时间", columnIndex = 11)
    private Date updateTime;

    public XlsPoiAnalyseDetail() {
    }

    public XlsPoiAnalyseDetail(PoiAnalyseDetailListData poiAnalyseDetail) {
        this.provinceName = poiAnalyseDetail.getProvinceName();
        this.cityName = poiAnalyseDetail.getCityName();
        this.districtName = poiAnalyseDetail.getDistrictName();
        this.analyseName = poiAnalyseDetail.getAnalyseName();
        this.code = poiAnalyseDetail.getCode();
        this.poiId = poiAnalyseDetail.getPoiId();
        this.poiName = poiAnalyseDetail.getPoiName();
        this.address = poiAnalyseDetail.getAddress();
        this.longitude = poiAnalyseDetail.getLongitude();
        this.latitude = poiAnalyseDetail.getLatitude();
        this.poiType = poiAnalyseDetail.getPoiType();
        this.updateTime = poiAnalyseDetail.getUpdateTime();
    }

    public PoiAnalyseDetail adapToPO(Integer userId) {
        PoiAnalyseDetail poiAnalyseDetail = new PoiAnalyseDetail();
        poiAnalyseDetail.setAnalyseName(analyseName);
        poiAnalyseDetail.setPoiType(poiType);
        poiAnalyseDetail.setAddress(address);
        poiAnalyseDetail.setLongitude(longitude);
        poiAnalyseDetail.setLatitude(latitude);
        poiAnalyseDetail.setCreatedByUser(userId);
        poiAnalyseDetail.setUpdatedByUser(userId);
        return poiAnalyseDetail;
    }
}
