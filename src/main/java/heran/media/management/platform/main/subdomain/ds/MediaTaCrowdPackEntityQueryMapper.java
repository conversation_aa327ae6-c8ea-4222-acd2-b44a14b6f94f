package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.MediaTaCrowdPackData;
import heran.media.management.platform.main.subdomain.response.CrowdPackDataAllResponse;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface MediaTaCrowdPackEntityQueryMapper {
    @SelectProvider(type = MediaTaCrowdPackEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "packName", column = "pack_name"),
            @Result(property = "packCode", column = "pack_code"),
            @Result(property = "categoryIdLv1", column = "category_id_lv1"),
            @Result(property = "categoryIdLv2", column = "category_id_lv2"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
            @Result(property = "createdByUser", column = "created_by_user"),
            @Result(property = "createdByUserName", column = "createdByUserName"),
            @Result(property = "updatedByUser", column = "updated_by_user"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<MediaTaCrowdPackData> list(SearchCriteria criteria);

    @Select("SELECT mr1.category_name as category_name_lv1,mr2.category_name as category_name_lv2,mt.id,mt.pack_name,mt.pack_code,mt.category_id_lv1,mt.category_id_lv2,mt.is_enabled,mt.remark,mt.extra_attrs,mt.created_by_user,mt.updated_by_user,mt.creator,mt.updater,mt.create_time,mt.update_time FROM media_ta_crowd_pack as mt left join main_resource_category mr1 ON mt.category_id_lv1 = mr1.id left join main_resource_category mr2 ON mt.category_id_lv2 = mr2.id WHERE mt.id=#{id} ")
    @Results(id = "mediaTaCrowdPack-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "packName", column = "pack_name"),
            @Result(property = "packCode", column = "pack_code"),
            @Result(property = "categoryIdLv1", column = "category_id_lv1"),
            @Result(property = "categoryIdLv2", column = "category_id_lv2"),
            @Result(property = "categoryNameLv1", column = "category_name_lv1"),
            @Result(property = "categoryNameLv2", column = "category_name_lv2"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
            @Result(property = "createdByUser", column = "created_by_user"),
            @Result(property = "updatedByUser", column = "updated_by_user"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    MediaTaCrowdPackData getByIdAddField(@Param("id") Integer id);


}
