package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class PersonLandDataEntityExportSQLProvider {

    public String select(SearchCriteria criteria) {
        String withPart = "WITH ta_task_pack AS(" +
                "SELECT t2.pack_id, t2.stat_month, t2.region_code, t3.pack_name  " +
                "FROM media_ta_data_management_task t1  " +
                "JOIN media_ta_data_management_task_detail t2 ON t1.id = t2.management_task_id " +
                "JOIN media_ta_crowd_pack t3 ON t3.id = t2.pack_id " +
                "WHERE t1.ta_population = 'TA_RESIDENT_POPULATION' " +
                "GROUP BY t2.stat_month, t2.region_code,t2.id)";
        return withPart + new SQL() {{
            SELECT("pl.region_code,pl.stat_month,pl.resident_population,pl.work_population,pl.dwell_population,pl.passenge_population passengePopulation,pl.passenge_hour_population passengeHourPopulation,pl.update_time,r1.region_name as one_name,r2.region_name as two_name,r3.region_name as three_name,COUNT(DISTINCT sd.id) as dimensions, gh_agg.types as types,ch.wktTypes ,GROUP_CONCAT(DISTINCT t4.pack_name SEPARATOR ',') AS crowdPack");
            FROM("person_land_data as pl");
            LEFT_OUTER_JOIN("main_dict_region r3 on pl.region_code = r3.code");
            LEFT_OUTER_JOIN("main_dict_region r2 on r3.parent_code = r2.code");
            LEFT_OUTER_JOIN("main_dict_region r1 on r2.parent_code = r1.code");
            LEFT_OUTER_JOIN("(SELECT stat_month, type_info, GROUP_CONCAT( DISTINCT statistical_type SEPARATOR ',' ) AS types FROM grid_hot_data WHERE data_type = 'person_land_data' AND hot_value = '100.0' GROUP BY stat_month, type_info) gh_agg ON pl.stat_month = gh_agg.stat_month AND pl.region_code = gh_agg.type_info");
            LEFT_OUTER_JOIN("statistics_dimension as sd ON pl.stat_month  = sd.stat_month and pl.region_code  = sd.region_code and sd.data_type = 'person_land_data'");
            LEFT_OUTER_JOIN("(SELECT stat_month, region_code, GROUP_CONCAT( DISTINCT statistical_type SEPARATOR ',' ) AS wktTypes FROM cell_hot_value_wkt_config WHERE data_type = 'BASE' GROUP BY stat_month, region_code) ch ON pl.stat_month = ch.stat_month AND pl.region_code = ch.region_code");
            LEFT_OUTER_JOIN("ta_task_pack t4 ON t4.stat_month = pl.stat_month AND t4.region_code = pl.region_code");
            String orderSql = null;
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                     }
                    if(cri.getKey().equals("id")){
                        WHERE("pl.id = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("statMonth")){
                        WHERE("pl.stat_month IN( " + cri.getValue() +")");
                    }else if(cri.getKey().equals("regionCode") && cri.getValue()!=null){
                        WHERE("pl.region_code IN (" + cri.getValue() + ")");
//                        WHERE("pl.region_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("crowdPack") && cri.getValue()!=null){
                        WHERE("t4.pack_id = "+ cri.getValue());
                    }else if(cri.getKey().equals("taResidentPopulation") && cri.getValue()!=null){
                        WHERE("pl.ta_resident_population like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("taPassengePopulation") && cri.getValue()!=null){
                        WHERE("pl.ta_passenge_population like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("createdByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("pl.created_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("pl.created_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("pl.created_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("pl.created_by_user = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("orderStatMonth") && (cri.getValue().equals("desc") || cri.getValue().equals("asc"))){
                        if (orderSql == null){
                            orderSql = "pl.stat_month " + cri.getValue();
                        }else{
                            orderSql += ",pl.stat_month " + cri.getValue();
                        }
                    }else if(cri.getKey().equalsIgnoreCase("orderUpdateTime") && (cri.getValue().equals("desc") || cri.getValue().equals("asc"))){
                        if (orderSql == null){
                            orderSql = "pl.update_time " + cri.getValue();
                        }else{
                            orderSql += ",pl.update_time " + cri.getValue();
                        }
                    }else if(cri.getKey().equalsIgnoreCase("updatedByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("pl.updated_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("pl.updated_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("pl.updated_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("pl.updated_by_user = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }

                }
            };
            GROUP_BY("pl.id");
            if (orderSql == null){
                ORDER_BY("pl.stat_month asc");
            }else{
                ORDER_BY(orderSql);
            }
        }}.toString();
    }
}
