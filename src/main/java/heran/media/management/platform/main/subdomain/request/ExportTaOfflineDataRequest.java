package heran.media.management.platform.main.subdomain.request;

import heran.media.management.platform.common.domain.dto.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExportTaOfflineDataRequest extends PageRequest {
    @ApiModelProperty("任务id")
    private Integer taskId;
    @ApiModelProperty("人群包id")
    private Integer packId;
    @ApiModelProperty("行政编码")
    private String regionCode;
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("是否是原始数据")
    private Boolean isBackups;
}
