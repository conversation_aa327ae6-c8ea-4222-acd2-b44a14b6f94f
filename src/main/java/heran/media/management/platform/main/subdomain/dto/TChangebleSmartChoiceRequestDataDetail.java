package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.SmartChoiceRequestDataDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleSmartChoiceRequestDataDetail implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "212")
    private Integer id;

    @ApiModelProperty(value = "智选方案id", required = false, example = "207")
    private Integer selectionPlanId;

    @ApiModelProperty(value = "点位编号", required = false, example = "点位编号")
    @Size(max = 64, message = "点位编号长度不能超出64位")
    private String placementCode;

    @ApiModelProperty(value = "省份编号", required = false, example = "省份编号")
    @Size(max = 64, message = "省份编号长度不能超出64位")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称", required = false, example = "省份名称")
    @Size(max = 64, message = "省份名称长度不能超出64位")
    private String province;

    @ApiModelProperty(value = "城市编号", required = false, example = "城市编号")
    @Size(max = 64, message = "城市编号长度不能超出64位")
    private String cityCode;

    @ApiModelProperty(value = "城市名称", required = false, example = "城市名称")
    @Size(max = 64, message = "城市名称长度不能超出64位")
    private String city;

    @ApiModelProperty(value = "区域/县级市编号", required = false, example = "区域/县级市编号")
    @Size(max = 64, message = "区域/县级市编号长度不能超出64位")
    private String districtCode;

    @ApiModelProperty(value = "区域/县级市名称", required = false, example = "区域/县级市名称")
    @Size(max = 64, message = "区域/县级市名称长度不能超出64位")
    private String district;

    @ApiModelProperty(value = "媒体形式", required = false, example = "媒体形式")
    @Size(max = 64, message = "媒体形式长度不能超出64位")
    private String mediumFormat;

    @ApiModelProperty(value = "媒体筛选条件", required = false, example = "媒体筛选条件")
    @Size(max = 64, message = "媒体筛选条件长度不能超出64位")
    private String mediumFiltra;

    @ApiModelProperty(value = "媒体名称", required = false, example = "媒体名称")
    @Size(max = 64, message = "媒体名称长度不能超出64位")
    private String mediumName;

    @ApiModelProperty(value = "媒体位置", required = false, example = "媒体位置")
    @Size(max = 64, message = "媒体位置长度不能超出64位")
    private String mediumAddress;

    @ApiModelProperty(value = "点位数据 ？？？？？这都具体到点位了为啥还有点位数量", required = false, example = "125")
    private Integer placementCount;

    @ApiModelProperty(value = "缓冲区距离", required = false, example = "73")
    private Integer bufferDistance;

    @ApiModelProperty(value = "TA浓度", required = false, example = "191")
    private Integer taConcentration;

    @ApiModelProperty(value = "浓度周期", required = false, example = "浓度周期")
    @Size(max = 64, message = "浓度周期长度不能超出64位")
    private String concentrationPeriod;

    @ApiModelProperty(value = "计划投放开始时间", required = false, example = "1741321657556")
    private Date planLaunchStartTime;

    @ApiModelProperty(value = "计划投放结束时间", required = false, example = "1741321657556")
    private Date planLaunchEndTime;


    public TChangebleSmartChoiceRequestDataDetail() {
    }

    public SmartChoiceRequestDataDetail adapToPO() {
        SmartChoiceRequestDataDetail smartChoiceRequestDataDetail = new SmartChoiceRequestDataDetail();
        smartChoiceRequestDataDetail.setId(id);
        smartChoiceRequestDataDetail.setSelectionPlanId(selectionPlanId);
        smartChoiceRequestDataDetail.setPlacementCode(placementCode);
        smartChoiceRequestDataDetail.setProvinceCode(provinceCode);
        smartChoiceRequestDataDetail.setProvince(province);
        smartChoiceRequestDataDetail.setCityCode(cityCode);
        smartChoiceRequestDataDetail.setCity(city);
        smartChoiceRequestDataDetail.setDistrictCode(districtCode);
        smartChoiceRequestDataDetail.setDistrict(district);
        smartChoiceRequestDataDetail.setMediumFormat(mediumFormat);
        smartChoiceRequestDataDetail.setMediumFiltra(mediumFiltra);
        smartChoiceRequestDataDetail.setMediumName(mediumName);
        smartChoiceRequestDataDetail.setMediumAddress(mediumAddress);
        smartChoiceRequestDataDetail.setPlacementCount(placementCount);
        smartChoiceRequestDataDetail.setBufferDistance(bufferDistance);
        smartChoiceRequestDataDetail.setTaConcentration(taConcentration);
        smartChoiceRequestDataDetail.setConcentrationPeriod(concentrationPeriod);
        smartChoiceRequestDataDetail.setPlanLaunchStartTime(planLaunchStartTime);
        smartChoiceRequestDataDetail.setPlanLaunchEndTime(planLaunchEndTime);

        return smartChoiceRequestDataDetail;
    }
}
