package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.AoiDrawTagRel;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsAoiDrawTagRel {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "AOI绘画id", columnIndex = 1)
    private Integer aoiDrawId;

    @XlsField(title = "标签id", columnIndex = 2)
    private Integer tagId;

    @XlsField(title = "说明", columnIndex = 3)
    private String remark;

    @XlsField(title = "扩展字段", columnIndex = 4)
    private String extraAttrs;

    @XlsField(title = "行记录创建用户", columnIndex = 5)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 6)
    private Integer updatedByUser;


    public XlsAoiDrawTagRel() {
    }

    public XlsAoiDrawTagRel(AoiDrawTagRel aoiDrawTagRel) {
        this.id = aoiDrawTagRel.getId();
        this.aoiDrawId = aoiDrawTagRel.getAoiDrawId();
        this.tagId = aoiDrawTagRel.getTagId();
        this.remark = aoiDrawTagRel.getRemark();
        this.extraAttrs = aoiDrawTagRel.getExtraAttrs();
        this.createdByUser = aoiDrawTagRel.getCreatedByUser();
        this.updatedByUser = aoiDrawTagRel.getUpdatedByUser();

    }

    public AoiDrawTagRel adapToPO(Integer userId) {
        AoiDrawTagRel aoiDrawTagRel = new AoiDrawTagRel();
        aoiDrawTagRel.setId(id);
        aoiDrawTagRel.setAoiDrawId(aoiDrawId);
        aoiDrawTagRel.setTagId(tagId);
        aoiDrawTagRel.setRemark(remark);
        aoiDrawTagRel.setExtraAttrs(extraAttrs);

        aoiDrawTagRel.setCreatedByUser(userId);
        aoiDrawTagRel.setUpdatedByUser(userId);
        return aoiDrawTagRel;
    }
}
