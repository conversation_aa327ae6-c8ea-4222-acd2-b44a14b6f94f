package heran.media.management.platform.main.subdomain.dao;

import heran.media.management.platform.mediaorder.sudomain.ds.MediaOrderEntityQueryMapper;
import heran.media.sharelib.domain.bo.TaStatisticalType;
import heran.media.sharelib.domain.db.mapper.main.GridHotDataMapper;
import heran.media.sharelib.domain.db.mapper.main.TaGridTaskHeatDataMapper;
import heran.media.sharelib.domain.dto.hot.GridHotDto;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class CacheableGridHotDao {

    @Resource
    private MediaOrderEntityQueryMapper mediaOrderEntityQueryMapper;
    @Resource
    private TaGridTaskHeatDataMapper taGridTaskHeatDataMapper;
    @Resource
    private GridHotDataMapper gridHotDataMapper;

    @Cacheable(cacheNames = "GET_GRID_HOT_DATA", key = "#statisticalType + '_' + #regionCode+ '_' + #statMonth")
    public Map<String, String> getGridHotData(String statisticalType, String regionCode, String statMonth, Integer packId) {
        if (statisticalType.equals(TaStatisticalType.TA_PASSENGER_POPULATION.name()) ||
                statisticalType.equals(TaStatisticalType.TA_RESIDENT_POPULATION.name())) {
            //TA处理
            Integer taskId = mediaOrderEntityQueryMapper.getTaskId(packId, regionCode, statMonth, statisticalType);
            if (taskId != null) {
                List<GridHotDto> taGridHotDto = taGridTaskHeatDataMapper.getTaGridHotDto(statMonth, regionCode, taskId, statisticalType);
                return taGridHotDto.stream()
                        .collect(Collectors.toMap(
                                dto -> dto.getLongitude() + "," + dto.getLatitude(),
                                GridHotDto::getHotValue
                        ));
            }
        }

        List<GridHotDto> gridHotDto = gridHotDataMapper.getGridHotDto(statMonth, regionCode, statisticalType);
        return gridHotDto.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getLongitude() + "," + dto.getLatitude(),
                        GridHotDto::getHotValue,
                        (v1, v2) -> v1
                ));
    }
}
