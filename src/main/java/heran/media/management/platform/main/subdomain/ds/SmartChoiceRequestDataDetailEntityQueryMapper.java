package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.main.SmartChoiceRequestDataDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface SmartChoiceRequestDataDetailEntityQueryMapper {
    @SelectProvider(type = SmartChoiceRequestDataDetailEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "selectionPlanId", column = "selection_plan_id"), @Result(property = "placementCode", column = "placement_code"), @Result(property = "provinceCode", column = "province_code"), @Result(property = "province", column = "province"), @Result(property = "cityCode", column = "city_code"), @Result(property = "city", column = "city"), @Result(property = "districtCode", column = "district_code"), @Result(property = "district", column = "district"), @Result(property = "mediumFormat", column = "medium_format"), @Result(property = "mediumFiltra", column = "medium_filtra"), @Result(property = "mediumName", column = "medium_name"), @Result(property = "mediumAddress", column = "medium_address"), @Result(property = "placementCount", column = "placement_count"), @Result(property = "bufferDistance", column = "buffer_distance"), @Result(property = "taConcentration", column = "ta_concentration"), @Result(property = "concentrationPeriod", column = "concentration_period"), @Result(property = "planLaunchStartTime", column = "plan_launch_start_time"), @Result(property = "planLaunchEndTime", column = "plan_launch_end_time"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<SmartChoiceRequestDataDetail> list(SearchCriteria criteria);
}
