package heran.media.management.platform.main.subdomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GetPackCategoryRequest {
    @ApiModelProperty("分组")
    private String groupLabel;
    @ApiModelProperty("人群包名称")
    private String packName;
    @ApiModelProperty("二级分类id数组")
    private List<Integer> categoryIds;
}
