package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class StatisticsDimensionEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("statistics_dimension");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if (cri.getKey().equals("id")) {
                        WHERE("id = " + cri.getValue());
                    } else if (cri.getKey().equals("statMonth") && cri.getValue() != null) {
                        WHERE("stat_month like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("regionCode") && cri.getValue() != null) {
                        WHERE("region_code like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("statisticalType") && cri.getValue() != null) {
                        WHERE("statistical_type like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("dimensionName") && cri.getValue() != null) {
                        WHERE("dimension_name like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("dimensionValue") && cri.getValue() != null) {
                        WHERE("dimension_value like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("createdByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("created_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("created_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("created_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("created_by_user = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("updatedByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("updated_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("updated_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("updated_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("updated_by_user = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }

                }
            }
            ;
            ORDER_BY("update_time desc");
        }}.toString();
    }


    public String selectList(String regionCode, String statMonth) {
        return new SQL() {{
            SELECT("t1.stat_month, t2.region_name lv3Name, t3.region_name lv2Name, t4.region_name lv1Name,t1.statistical_type, t1.dimension_name, t1.dimension_value,t5.dict_name,t5.header ");
            FROM("`statistics_dimension` t1 ");
            JOIN("main_dict_region t2 ON t1.region_code = t2.`code`");
            LEFT_OUTER_JOIN("main_dict_region t3 ON t3.`code` = t2.`parent_code`");
            LEFT_OUTER_JOIN("main_dict_region t4 ON t4.`code` = t3.`parent_code`");
            JOIN("map_resource_dict t5 ON t1.dimension_name = t5.dict_value AND t5.group_label = 'PROFILE_TYPE' AND t5.level = 2");

            if (StringUtils.isNotEmpty(regionCode)) {
                WHERE("t1.region_code = '" + regionCode + "'");
            }
            if (StringUtils.isNotEmpty(statMonth)) {
                WHERE("t1.stat_month = '" + statMonth + "'");
            }
            WHERE("t1.data_type = 'person_land_data'");
        }}.toString();
    }
}
