package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.main.MainMediaSchedulePlacement;
import heran.media.sharelib.domain.db.model.main.MainResourceCategory;
import heran.media.sharelib.domain.db.model.main.MediaTaPackTagRel;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface MediaTaPackTagRelEntityQueryMapper {
    @SelectProvider(type = MediaTaPackTagRelEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "tagId", column = "tag_id"), @Result(property = "crowdPackId", column = "crowd_pack_id"), @Result(property = "remark", column = "remark"), @Result(property = "extraAttrs", column = "extra_attrs"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<MediaTaPackTagRel> list(SearchCriteria criteria);

    @Insert({"<script>",
            "INSERT INTO media_ta_pack_tag_rel(id,tag_id,crowd_pack_id, remark, extra_attrs, created_by_user, updated_by_user, creator, updater,create_time)",
            "VALUES ",
            "<foreach item='item' collection='batchInsertList' separator=','>",
            "(#{item.id},#{item.tagId},#{item.crowdPackId}, #{item.remark}, #{item.extraAttrs},  #{item.createdByUser}, #{item.updatedByUser}, #{item.creator}, #{item.updater},#{item.createTime})",
            "</foreach>",

            "</script>"
    })
    void batchInsertEntities(List<MediaTaPackTagRel> batchInsertList);

    @Delete("DELETE FROM media_ta_pack_tag_rel WHERE crowd_pack_id= #{crowdPackId}")
    void deleteByCrowdPackId(@Param("crowdPackId") int crowdPackId);

    @Select("select tag_id from media_ta_pack_tag_rel where crowd_pack_id = #{crowdPackId}")
    List<Integer> getIdsByCrowdPackId(@Param("crowdPackId") int crowdPackId);


    @Select({
            "<script>",
            "SELECT * FROM main_resource_category",
            "WHERE group_label = #{groupLabel}",
            "<if test='ids != null and ids.size() > 0'>",
            "  AND id IN",
            "  <foreach collection='ids' item='id' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach>",
            "</if>",
            "</script>"
    })
    List<MainResourceCategory> getPackCategoryByIds(
            @Param("groupLabel") String groupLabel,
            @Param("ids") List<Integer> ids
    );




}
