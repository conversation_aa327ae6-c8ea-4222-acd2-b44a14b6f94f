package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class MediaTaCrowdPackEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("mt.*,t7.user_name createdByUserName,mr1.category_name as category_name_lv1,mr2.category_name as category_name_lv2," +
                    "GROUP_CONCAT(\n" +
                    "    IF(\n" +
                    "        CONCAT_WS('|', m6.label, m5.label, m4.label) != '',\n" +
                    "        TRIM(BOTH '|' FROM CONCAT_WS('|', m6.label, m5.label, m4.label)),\n" +
                    "        NULL\n" +
                    "    )\n" +
                    "    SEPARATOR ';'\n" +
                    ") AS tag_names");
            FROM("media_ta_crowd_pack as mt");
            LEFT_OUTER_JOIN("main_resource_category mr1 ON mt.category_id_lv1 = mr1.id");
            LEFT_OUTER_JOIN("main_resource_category mr2 ON mt.category_id_lv2 = mr2.id");
            LEFT_OUTER_JOIN("media_ta_pack_tag_rel m3 ON mt.id = m3.crowd_pack_id");
            LEFT_OUTER_JOIN("main_media_tag m4 on m3.tag_id = m4.id");
            LEFT_OUTER_JOIN("main_media_tag m5 on m4.parent_id = m5.id");
            LEFT_OUTER_JOIN("main_media_tag m6 on m5.parent_id = m6.id");
            LEFT_OUTER_JOIN("management_sys_account t7 ON mt.created_by_user = t7.id");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                     }
                    if(cri.getKey().equals("id")){
                        WHERE("mt.id = "+ cri.getValue());
                    }
                    else if(cri.getKey().equals("packName") && cri.getValue()!=null){
                        WHERE("mt.pack_name like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("packCode") && cri.getValue()!=null){
                        WHERE("mt.pack_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("categoryIdLv1")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("mt.category_id_lv1 between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("mt.category_id_lv1 >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("mt.category_id_lv1 <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("mt.category_id_lv1 = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("categoryIdLv2")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("mt.category_id_lv2 between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("mt.category_id_lv2 >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("mt.category_id_lv2 <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("mt.category_id_lv2 = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("isEnabled")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("mt.is_enabled between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("mt.is_enabled >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("mt.is_enabled <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("mt.is_enabled = "+ cri.getValue());
                    }else if(cri.getKey().equals("remark") && cri.getValue()!=null){
                        WHERE("mt.remark like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("extraAttrs") && cri.getValue()!=null){
                        WHERE("mt.extra_attrs like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("createdByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("mt.created_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("mt.created_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("mt.created_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("mt.created_by_user = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("updatedByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("mt.updated_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("mt.updated_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("mt.updated_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("mt.updated_by_user = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }

                }
            };
            GROUP_BY("mt.id");
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
