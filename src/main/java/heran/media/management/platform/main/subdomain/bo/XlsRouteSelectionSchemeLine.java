package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.RouteSelectionSchemeLine;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@ToString
@Data
public class XlsRouteSelectionSchemeLine {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "公交智能计算选线方案id", columnIndex = 1)
    private Integer schemeId;

    @XlsField(title = "区域code", columnIndex = 2)
    private String regionCode;

    @XlsField(title = "线路名称", columnIndex = 3)
    private String lineName;

    @XlsField(title = "级别", columnIndex = 4)
    private String level;

    @XlsField(title = "投放数量", columnIndex = 5)
    private Integer launchCount;

    @XlsField(title = "投放周期", columnIndex = 6)
    private Integer launchPeriod;

    @XlsField(title = "制作费", columnIndex = 7)
    private BigDecimal productionCost;

    @XlsField(title = "发布费", columnIndex = 8)
    private BigDecimal issueCost;

    @XlsField(title = "费用总计", columnIndex = 9)
    private BigDecimal costAggregate;

    @XlsField(title = "POI", columnIndex = 10)
    private String poi;

    @XlsField(title = "aoi", columnIndex = 11)
    private String aoi;

    @XlsField(title = "商圈", columnIndex = 12)
    private String cbd;

    @XlsField(title = "干道", columnIndex = 13)
    private String mainLine;

    @XlsField(title = "热力", columnIndex = 14)
    private String heatingPower;

    @XlsField(title = "TA热力", columnIndex = 15)
    private String taHeatingPower;

    @XlsField(title = "是否勾选", columnIndex = 16)
    private Integer pitchOn;

    @XlsField(title = "说明", columnIndex = 17)
    private String remark;

    @XlsField(title = "扩展字段", columnIndex = 18)
    private String extraAttrs;

    @XlsField(title = "行记录创建用户", columnIndex = 19)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 20)
    private Integer updatedByUser;


    public XlsRouteSelectionSchemeLine() {
    }

    public XlsRouteSelectionSchemeLine(RouteSelectionSchemeLine routeSelectionSchemeLine) {
        this.id = routeSelectionSchemeLine.getId();
        this.schemeId = routeSelectionSchemeLine.getSchemeId();
        this.lineName = routeSelectionSchemeLine.getLineName();
        this.level = routeSelectionSchemeLine.getLevel();
        this.launchCount = routeSelectionSchemeLine.getLaunchCount();
        this.launchPeriod = routeSelectionSchemeLine.getLaunchPeriod();
        this.productionCost = routeSelectionSchemeLine.getProductionCost();
        this.issueCost = routeSelectionSchemeLine.getIssueCost();
        this.costAggregate = routeSelectionSchemeLine.getCostAggregate();
        this.poi = routeSelectionSchemeLine.getPoi();
        this.aoi = routeSelectionSchemeLine.getAoi();
        this.cbd = routeSelectionSchemeLine.getCbd();
        this.mainLine = routeSelectionSchemeLine.getMainLine();
        this.pitchOn = routeSelectionSchemeLine.getPitchOn();
        this.remark = routeSelectionSchemeLine.getRemark();
        this.extraAttrs = routeSelectionSchemeLine.getExtraAttrs();
        this.createdByUser = routeSelectionSchemeLine.getCreatedByUser();
        this.updatedByUser = routeSelectionSchemeLine.getUpdatedByUser();

    }

    public RouteSelectionSchemeLine adapToPO(Integer userId) {
        RouteSelectionSchemeLine routeSelectionSchemeLine = new RouteSelectionSchemeLine();
        routeSelectionSchemeLine.setId(id);
        routeSelectionSchemeLine.setSchemeId(schemeId);
        routeSelectionSchemeLine.setLineName(lineName);
        routeSelectionSchemeLine.setLevel(level);
        routeSelectionSchemeLine.setLaunchCount(launchCount);
        routeSelectionSchemeLine.setLaunchPeriod(launchPeriod);
        routeSelectionSchemeLine.setProductionCost(productionCost);
        routeSelectionSchemeLine.setIssueCost(issueCost);
        routeSelectionSchemeLine.setCostAggregate(costAggregate);
        routeSelectionSchemeLine.setPoi(poi);
        routeSelectionSchemeLine.setAoi(aoi);
        routeSelectionSchemeLine.setCbd(cbd);
        routeSelectionSchemeLine.setMainLine(mainLine);
        routeSelectionSchemeLine.setPitchOn(pitchOn);
        routeSelectionSchemeLine.setRemark(remark);
        routeSelectionSchemeLine.setExtraAttrs(extraAttrs);

        routeSelectionSchemeLine.setCreatedByUser(userId);
        routeSelectionSchemeLine.setUpdatedByUser(userId);
        return routeSelectionSchemeLine;
    }
}
