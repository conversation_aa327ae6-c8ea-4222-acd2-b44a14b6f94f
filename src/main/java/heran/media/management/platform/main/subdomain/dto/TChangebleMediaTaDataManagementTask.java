package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.Gson;
import heran.media.sharelib.domain.db.model.main.MediaTaDataManagementTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleMediaTaDataManagementTask implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "41")
    private Integer id;

    @ApiModelProperty(value = "查询月份", required = true, example = "查询月份")
    @Size(max = 64, message = "查询月份长度不能超出64位")
    @NotBlank(message = "查询月份不能为空")
    private String statMonth;

    @ApiModelProperty(value = "查询城市", required = true, example = "查询城市")
    @Size(max = 32, message = "查询城市长度不能超出32位")
    private List<String> regionCode;

    @ApiModelProperty(value = "任务ID（不知是否有有特殊ID 预留字段）", required = true, example = "任务ID（不知是否有有特殊ID 预留字段）")
    @Size(max = 64, message = "任务ID（不知是否有有特殊ID 预留字段）长度不能超出64位")
    private String taskCode;

    @ApiModelProperty(value = "任务名称", required = true, example = "任务名称")
    @Size(max = 64, message = "任务名称长度不能超出64位")
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    @ApiModelProperty(value = "人群包id", required = true, example = "91")
    @NotNull(message = "人群包id不能为空")
    private Integer crowdPackId;

    @ApiModelProperty(value = "高德任务id", required = false, example = "高德任务id")
    @Size(max = 64, message = "高德任务id长度不能超出64位")
    private String mapTaskId;

    @ApiModelProperty(value = "TA人口统计", required = false, example = "TA人口统计")
    @Size(max = 64, message = "TA人口统计长度不能超出64位")
    private String taPopulation;

    @ApiModelProperty(value = "TA洞察维度", required = false, example = "TA洞察维度")
    private String taInsight;

    @ApiModelProperty(value = "TA网格热力指数", required = false, example = "TA网格热力指数")
    @Size(max = 64, message = "TA网格热力指数长度不能超出64位")
    private String taEnergetics;

    @ApiModelProperty("接口类型")
    private String taType;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangebleMediaTaDataManagementTask() {
    }

    public MediaTaDataManagementTask adapToPO() {
        MediaTaDataManagementTask mediaTaDataManagementTask = new MediaTaDataManagementTask();
        mediaTaDataManagementTask.setId(id);
        mediaTaDataManagementTask.setStatMonth(statMonth);
        Gson gson = new Gson();
        mediaTaDataManagementTask.setRegionCode(gson.toJson(regionCode));
        if (StringUtils.isNotEmpty(taskCode)) {
            mediaTaDataManagementTask.setTaskCode(taskCode);
        } else {
            mediaTaDataManagementTask.setTaskCode(UUID.randomUUID().toString().replace("_", ""));
        }
        mediaTaDataManagementTask.setTaskName(taskName);
        mediaTaDataManagementTask.setCrowdPackId(crowdPackId);
        mediaTaDataManagementTask.setTaskStatus("PENDING_REQUEST");
        mediaTaDataManagementTask.setMapTaskId(mapTaskId);
        mediaTaDataManagementTask.setTaPopulation(taPopulation);
        mediaTaDataManagementTask.setTaInsight(taInsight);
        mediaTaDataManagementTask.setTaEnergetics(taEnergetics);
        mediaTaDataManagementTask.setTaType(taType);
        mediaTaDataManagementTask.setRemark(remark);
        mediaTaDataManagementTask.setExtraAttrs(extraAttrs);

        return mediaTaDataManagementTask;
    }
}
