package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.PoiAnalyseDetailListData;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PoiAnalyseDetailEntityQueryMapper {

    /**
     * 列表数据
     *
     * @param criteria 查询条件
     * @return List<PoiAnalyseDetail>
     */
    @SelectProvider(type = PoiAnalyseDetailEntityQuerySQLProvider.class, method = "selectList")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "analyseName", column = "analyse_name"),
            @Result(property = "poiId", column = "poi_id"),
            @Result(property = "poiName", column = "poi_name"),
            @Result(property = "code", column = "code"),
            @Result(property = "analyseType", column = "analyse_type"),
            @Result(property = "poiType", column = "poi_type"),
            @Result(property = "provinceCode", column = "province_code"),
            @Result(property = "cityCode", column = "city_code"),
            @Result(property = "districtCode", column = "district_code"),
            @Result(property = "provinceName", column = "provinceName"),
            @Result(property = "cityName", column = "cityName"),
            @Result(property = "districtName", column = "districtName"),
            @Result(property = "address", column = "address"),
            @Result(property = "longitude", column = "longitude"),
            @Result(property = "latitude", column = "latitude"),
            @Result(property = "targetAddress", column = "target_address"),
            @Result(property = "geofencing", column = "geofencing"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
            @Result(property = "createdByUser", column = "created_by_user"),
            @Result(property = "updatedByUser", column = "updated_by_user"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<PoiAnalyseDetailListData> list(SearchCriteria criteria);

    /**
     * 获取每个类型的条件
     *
     * @param analyseType 类型
     * @return Integer
     */
    @Select("SELECT COUNT(*) FROM `poi_analyse_detail` WHERE analyse_type = #{analyseType}")
    Integer getPoiAnalyseData(@Param("analyseType") String analyseType);
}
