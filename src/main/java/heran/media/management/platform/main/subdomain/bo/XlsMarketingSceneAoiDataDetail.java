package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.MarketingSceneAoiDataDetail;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsMarketingSceneAoiDataDetail {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "营销场景AOI数据id", columnIndex = 1)
    private Integer marketingSceneAoiDataId;

    @XlsField(title = "流量统计", columnIndex = 2)
    private String trafficStatistics;

    @XlsField(title = "客流画像", columnIndex = 3)
    private String passengerPortrayal;

    @XlsField(title = "出行方式", columnIndex = 4)
    private String tripWay;

    @XlsField(title = "客流来源", columnIndex = 5)
    private String passengerSource;

    @XlsField(title = "人口统计", columnIndex = 6)
    private String demographicStatistics;

    @XlsField(title = "人群画像", columnIndex = 7)
    private String crowdPortrayal;

    @XlsField(title = "AOI洞察出行方式", columnIndex = 8)
    private String apiTripWay;

    @XlsField(title = "人群来源", columnIndex = 9)
    private String crowdSource;

    @XlsField(title = "线下场景偏好", columnIndex = 10)
    private String offlineScene;

    @XlsField(title = "说明", columnIndex = 11)
    private String remark;

    @XlsField(title = "扩展字段", columnIndex = 12)
    private String extraAttrs;

    @XlsField(title = "行记录创建用户", columnIndex = 13)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 14)
    private Integer updatedByUser;


    public XlsMarketingSceneAoiDataDetail() {
    }

    public XlsMarketingSceneAoiDataDetail(MarketingSceneAoiDataDetail marketingSceneAoiDataDetail) {
        this.id = marketingSceneAoiDataDetail.getId();
        this.marketingSceneAoiDataId = marketingSceneAoiDataDetail.getMarketingSceneAoiDataId();
        this.trafficStatistics = marketingSceneAoiDataDetail.getTrafficStatistics();
        this.passengerPortrayal = marketingSceneAoiDataDetail.getPassengerPortrayal();
        this.tripWay = marketingSceneAoiDataDetail.getTripWay();
        this.passengerSource = marketingSceneAoiDataDetail.getPassengerSource();
        this.demographicStatistics = marketingSceneAoiDataDetail.getDemographicStatistics();
        this.crowdPortrayal = marketingSceneAoiDataDetail.getCrowdPortrayal();
        this.apiTripWay = marketingSceneAoiDataDetail.getApiTripWay();
        this.crowdSource = marketingSceneAoiDataDetail.getCrowdSource();
        this.offlineScene = marketingSceneAoiDataDetail.getOfflineScene();
        this.remark = marketingSceneAoiDataDetail.getRemark();
        this.extraAttrs = marketingSceneAoiDataDetail.getExtraAttrs();
        this.createdByUser = marketingSceneAoiDataDetail.getCreatedByUser();
        this.updatedByUser = marketingSceneAoiDataDetail.getUpdatedByUser();

    }

    public MarketingSceneAoiDataDetail adapToPO(Integer userId) {
        MarketingSceneAoiDataDetail marketingSceneAoiDataDetail = new MarketingSceneAoiDataDetail();
        marketingSceneAoiDataDetail.setId(id);
        marketingSceneAoiDataDetail.setMarketingSceneAoiDataId(marketingSceneAoiDataId);
        marketingSceneAoiDataDetail.setTrafficStatistics(trafficStatistics);
        marketingSceneAoiDataDetail.setPassengerPortrayal(passengerPortrayal);
        marketingSceneAoiDataDetail.setTripWay(tripWay);
        marketingSceneAoiDataDetail.setPassengerSource(passengerSource);
        marketingSceneAoiDataDetail.setDemographicStatistics(demographicStatistics);
        marketingSceneAoiDataDetail.setCrowdPortrayal(crowdPortrayal);
        marketingSceneAoiDataDetail.setApiTripWay(apiTripWay);
        marketingSceneAoiDataDetail.setCrowdSource(crowdSource);
        marketingSceneAoiDataDetail.setOfflineScene(offlineScene);
        marketingSceneAoiDataDetail.setRemark(remark);
        marketingSceneAoiDataDetail.setExtraAttrs(extraAttrs);

        marketingSceneAoiDataDetail.setCreatedByUser(userId);
        marketingSceneAoiDataDetail.setUpdatedByUser(userId);
        return marketingSceneAoiDataDetail;
    }
}
