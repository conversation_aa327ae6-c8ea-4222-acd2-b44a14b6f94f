package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class MainMediaOrderManLandPlacementEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("main_media_order_man_land_placement");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                     }
                    if(cri.getKey().equals("id")){
                        WHERE("id = "+ cri.getValue());
                    }
                    else if(cri.getKey().equals("placementType") && cri.getValue()!=null){
                        WHERE("placement_type like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("orderId")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("order_id between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("order_id >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("order_id <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("order_id = "+ cri.getValue());
                    }else if(cri.getKey().equals("placementCode") && cri.getValue()!=null){
                        WHERE("placement_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("mediumFormat") && cri.getValue()!=null){
                        WHERE("medium_format like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("provinceCode") && cri.getValue()!=null){
                        WHERE("province_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("province") && cri.getValue()!=null){
                        WHERE("province like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("cityCode") && cri.getValue()!=null){
                        WHERE("city_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("city") && cri.getValue()!=null){
                        WHERE("city like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("districtCode") && cri.getValue()!=null){
                        WHERE("district_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("district") && cri.getValue()!=null){
                        WHERE("district like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("mediumName") && cri.getValue()!=null){
                        WHERE("medium_name like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("lineLevel") && cri.getValue()!=null){
                        WHERE("line_level like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("launchPeriod") && cri.getValue()!=null){
                        WHERE("launch_period like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("launchPeriodDay")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("launch_period_day between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("launch_period_day >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("launch_period_day <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("launch_period_day = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("neutralPosition")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("neutral_position between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("neutral_position >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("neutral_position <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("neutral_position = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("publishCost")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("publish_cost between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("publish_cost >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("publish_cost <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("publish_cost = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("makeCost")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("make_cost between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("make_cost >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("make_cost <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("make_cost = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("bufferDistance")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("buffer_distance between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("buffer_distance >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("buffer_distance <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("buffer_distance = "+ cri.getValue());
                    }else if(cri.getKey().equals("bufferWktCoordinate") && cri.getValue()!=null){
                        WHERE("buffer_wkt_coordinate like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("poi")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("poi between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("poi >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("poi <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("poi = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("aoi")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("aoi between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("aoi >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("aoi <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("aoi = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("cbdName")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("cbd_name between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("cbd_name >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("cbd_name <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("cbd_name = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("arterialTraffic")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("arterial_traffic between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("arterial_traffic >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("arterial_traffic <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("arterial_traffic = "+ cri.getValue());
                    }else if(cri.getKey().equals("heatingIndex") && cri.getValue()!=null){
                        WHERE("heating_index like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("heatingPeriod") && cri.getValue()!=null){
                        WHERE("heating_period like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("address") && cri.getValue()!=null){
                        WHERE("address like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("createdByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("created_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("created_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("created_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("created_by_user = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("updatedByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("updated_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("updated_by_user = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }

                }
            };
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
