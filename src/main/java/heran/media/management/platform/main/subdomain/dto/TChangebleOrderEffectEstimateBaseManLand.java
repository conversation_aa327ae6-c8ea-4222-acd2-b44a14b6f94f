package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseManLand;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleOrderEffectEstimateBaseManLand implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "221")
    private Integer id;

    @ApiModelProperty(value = "订单id", required = true, example = "123")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "曝光类型 MEDIUM_FORMAT(媒介曝光) CITY_FORMAT(城市曝光)", required = true, example = "")
    @Size(max = 64, message = "曝光类型 MEDIUM_FORMAT(媒介曝光) CITY_FORMAT(城市曝光)长度不能超出64位")
    @NotBlank(message = "曝光类型 MEDIUM_FORMAT(媒介曝光) CITY_FORMAT(城市曝光)不能为空")
    private String exposureType;

    @ApiModelProperty(value = "媒介id", required = false, example = "51")
    private String mediumId;

    @ApiModelProperty(value = "城市行政编码", required = false, example = "城市行政编码")
    @Size(max = 64, message = "城市行政编码长度不能超出64位")
    private String regionCode;

    @ApiModelProperty(value = "投放时间", required = false, example = "投放时间")
    @Size(max = 64, message = "投放时间长度不能超出64位")
    private String marketTime;

    @ApiModelProperty(value = "投放点位数量", required = false, example = "投放点位数量")
    @Size(max = 64, message = "投放点位数量长度不能超出64位")
    private String marketPlacementCount;

    @ApiModelProperty(value = "曝光人数PV(人次)", required = false, example = "曝光人数PV(人次)")
    @Size(max = 64, message = "曝光人数PV(人次)长度不能超出64位")
    private String pv;

    @ApiModelProperty(value = "曝光人数UV(人)", required = false, example = "曝光人数UV(人)")
    @Size(max = 64, message = "曝光人数UV(人)长度不能超出64位")
    private String uv;

    @ApiModelProperty(value = "广告平均触发频次", required = false, example = "广告平均触发频次")
    @Size(max = 64, message = "广告平均触发频次长度不能超出64位")
    private String af;

    @ApiModelProperty(value = "曝光潜客人数(人)", required = false, example = "曝光潜客人数(人)")
    @Size(max = 64, message = "曝光潜客人数(人)长度不能超出64位")
    private String impTaUv;

    @ApiModelProperty(value = "城市渗透率", required = false, example = "城市渗透率")
    @Size(max = 64, message = "城市渗透率长度不能超出64位")
    private String reach;

    @ApiModelProperty(value = "曝光潜客浓度(%)", required = false, example = "曝光潜客浓度(%)")
    @Size(max = 64, message = "曝光潜客浓度(%)长度不能超出64位")
    private String ta;

    @ApiModelProperty(value = "千人成本", required = false, example = "千人成本")
    @Size(max = 64, message = "千人成本长度不能超出64位")
    private String cpm;

    @ApiModelProperty(value = "单人成本", required = false, example = "单人成本")
    @Size(max = 64, message = "单人成本长度不能超出64位")
    private String cpuv;

    @ApiModelProperty(value = "单TA成本", required = false, example = "单TA成本")
    @Size(max = 64, message = "单TA成本长度不能超出64位")
    private String cpta;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangebleOrderEffectEstimateBaseManLand() {
    }

    public OrderEffectEstimateBaseManLand adapToPO() {
        OrderEffectEstimateBaseManLand orderEffectEstimateBaseManLand = new OrderEffectEstimateBaseManLand();
        orderEffectEstimateBaseManLand.setId(id);
        orderEffectEstimateBaseManLand.setOrderId(orderId);
        orderEffectEstimateBaseManLand.setExposureType(exposureType);
        orderEffectEstimateBaseManLand.setMediumId(mediumId);
        orderEffectEstimateBaseManLand.setRegionCode(regionCode);
        orderEffectEstimateBaseManLand.setMarketTime(marketTime);
        orderEffectEstimateBaseManLand.setMarketPlacementCount(marketPlacementCount);
        orderEffectEstimateBaseManLand.setPv(pv);
        orderEffectEstimateBaseManLand.setUv(uv);
        orderEffectEstimateBaseManLand.setAf(af);
        orderEffectEstimateBaseManLand.setImpTaUv(impTaUv);
        orderEffectEstimateBaseManLand.setReach(reach);
        orderEffectEstimateBaseManLand.setTa(ta);
        orderEffectEstimateBaseManLand.setCpm(cpm);
        orderEffectEstimateBaseManLand.setCpuv(cpuv);
        orderEffectEstimateBaseManLand.setCpta(cpta);
        orderEffectEstimateBaseManLand.setRemark(remark);
        orderEffectEstimateBaseManLand.setExtraAttrs(extraAttrs);

        return orderEffectEstimateBaseManLand;
    }
}
