package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.AoiDraw;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TAoiDrawMarket implements Serializable {

    private Integer id;

    private Integer regionId;

    private String regionCode;

    private String oneName;

    private String twoName;

    private String threeName;

    private String cityName;

    private String labelLv1;

    private String labelLv2;

    private String labelLv3;

    private String aoiName;

    private Integer quoteCbd;

    private Integer cbdId;

    private String aoiArea;

    private String aoiType;

    private String polyline;

    private String address;

    private String remark;

    private String extraAttrs;

    private Integer createdByUser;

    private Integer updatedByUser;

    private String creator;

    private String updater;

    private Date createTime;

    private Date updateTime;

    private Date maxUpdateTime;

    private Integer marketCounts;

    List<TagIdInfoData>  tagIdInfo;

    private String hotPopulation;


    public TAoiDrawMarket() {
    }


}
