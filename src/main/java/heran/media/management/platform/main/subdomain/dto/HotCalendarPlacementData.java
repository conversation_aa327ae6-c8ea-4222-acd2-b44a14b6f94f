package heran.media.management.platform.main.subdomain.dto;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.aioptionmode.po.CalendarPlacementData;
import heran.media.management.platform.common.utils.xls.annotation.MetaField;
import heran.media.sharelib.domain.bo.StatisticalType;
import heran.media.sharelib.utils.date.DataTmeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HotCalendarPlacementData extends CalendarPlacementData {
//    @MetaField(columnIndex = 20, labelCn = "缓冲区类型")
//    private String showBufferType;
//    @MetaField(columnIndex = 21, labelCn = "缓冲区半径(米)")
//    private Double showBufferArea;
//    @MetaField(columnIndex = 22, labelCn = "热力查询时间", width = 100)
//    private String showHotData;
//    @MetaField(columnIndex = 23, labelCn = "AOI热力口径")
//    private String showAoiCaliber;
//    @MetaField(columnIndex = 24, labelCn = "AOI热力")
//    private Double showAoiHotValue;
//    @MetaField(columnIndex = 25, labelCn = "AOI TA热力值")
//    private Double showTaHotValue;

//    public void setHotData() {
//        this.showBufferType = super.getBufferType();
//        this.showBufferArea = super.getBufferArea();
//        this.showAoiCaliber = super.getAoiCaliber();
//        if (StringUtils.isNotEmpty(super.getAoiCaliber())) {
//            StatisticalType type = StatisticalType.valueOf(super.getAoiCaliber());
//            this.showAoiCaliber = type.getName();
//        }
//        this.showAoiHotValue = super.getAoiHotValue();
//        this.showTaHotValue = super.getTaHotValue();
//        if (StringUtils.isNotEmpty(super.getStatMonth())) {
//            this.showHotData = DataTmeUtils.formatYearMonth(super.getStatMonth());
//        }
//    }
}
