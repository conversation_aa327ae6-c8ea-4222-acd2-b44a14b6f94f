package heran.media.management.platform.main.subdomain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ExportCityIncludeData {
    private Long id;
    private String areaId;
    private String areaCode;
    private String lv1Name;
    private String lv2Name;
    private String lv3Name;
    private String cellId;
    private String center1000;
    private String center100;
    private String wktUpLeft;
    private String wktLowerLeft;
    private String wktLowerRight;
    private String wktUpRight;
    private Integer peopleCount100;
    private Integer peopleCount1000;
}
