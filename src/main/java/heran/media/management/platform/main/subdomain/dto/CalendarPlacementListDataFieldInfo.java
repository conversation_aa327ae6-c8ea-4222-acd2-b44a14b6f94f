package heran.media.management.platform.main.subdomain.dto;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.annotation.MetaField;
import heran.media.management.platform.effectestimate.subdomian.dto.AppraisalMetaFieldInfo;
import heran.media.sharelib.domain.bo.OrderKpiType;
import heran.media.sharelib.utils.date.DataTmeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CalendarPlacementListDataFieldInfo extends CalendarAoiMetaFieldInfo {
    @MetaField(columnIndex = -1, labelCn = "订单编号", width = 200)
    private String orderSn;
    @MetaField(columnIndex = 35, labelCn = "曝光人群洞察查询时间", width = 100)
    private String sampleStatMonth;
    @MetaField(columnIndex = 35, labelCn = "曝光人群洞察")
    private String sample = "样本";
    @MetaField(columnIndex = 36, labelCn = "公交曝光POI查询时间")
    private String busSampleStatMonth;
    @MetaField(columnIndex = 37, labelCn = "公交曝光POI")
    private String busSample = "样本";

    @MetaField(columnIndex = -1, labelCn = "曝光人群样本id", hide = true)
    private String baseId;
    @MetaField(columnIndex = -1, labelCn = "曝光公交样本id", hide = true)
    private String baseBusId;
    @MetaField(columnIndex = -1, labelCn = "订单id", hide = true)
    private Integer orderId;


    public void setScheduleValue() {
        if (StringUtils.isNotEmpty(sampleStatMonth)) {
            this.sampleStatMonth = DataTmeUtils.formatYearMonth(sampleStatMonth);
        }
        if (StringUtils.isNotEmpty(busSampleStatMonth)) {
            this.busSampleStatMonth = DataTmeUtils.formatYearMonth(busSampleStatMonth);
        }
        if (baseId == null) {
            sample = "未被选为样本";
        }
        if (baseBusId == null) {
            busSample = "未被选为样本";
        }
    }
}
