package heran.media.management.platform.main.subdomain.request;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.google.gson.Gson;
import heran.media.management.platform.main.subdomain.dto.ClearlyTaskData;
import heran.media.management.platform.main.subdomain.dto.SmartCityDimensionTaskData;
import heran.media.management.platform.main.subdomain.dto.SmartCityTaskData;
import heran.media.sharelib.domain.bo.TaskStatusType;
import heran.media.sharelib.domain.db.model.main.DataTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SaveTaskDataRequest {
    @ApiModelProperty("主键")
    private Integer id;
    @ApiModelProperty("任务名称")
    private String taskName;
    @ApiModelProperty("批量选择城市区域")
    private List<String> regionCodes;
    @ApiModelProperty("批量选择查询时间")
    private String statMonth;
    @ApiModelProperty("商圈洞察")
    private ClearlyTaskData cbdClearlyTaskData;
    @ApiModelProperty("特定场景洞察")
    private ClearlyTaskData specificClearlyTaskData;
    @ApiModelProperty("数智城市")
    private SmartCityTaskData smartCityTaskData;
    @ApiModelProperty("数智城市透视")
    private SmartCityDimensionTaskData smartCityDimensionTaskData;

    public DataTask adaToPo() {
        Gson gson = new Gson();
        DataTask dataTask = new DataTask();
        dataTask.setId(id);
        dataTask.setTaskName(taskName);
        dataTask.setStatMonth(statMonth);
        dataTask.setTaskStatus(TaskStatusType.PENDING_REQUEST.name());
        dataTask.setRegionCode(gson.toJson(regionCodes));
        dataTask.setDisposeTime(new Date());
        dataTask.setCbdInsight(cbdClearlyTaskData.getIsEnabled());
        dataTask.setCbdInsightRequest(gson.toJson(cbdClearlyTaskData));
        dataTask.setSpecificSceneInsight(specificClearlyTaskData.getIsEnabled());
        dataTask.setSpecificSceneInsightRequest(gson.toJson(specificClearlyTaskData));
        dataTask.setNumberSmartCity(smartCityTaskData.getIsEnabled());
        dataTask.setNumberSmartCityRequest(gson.toJson(smartCityTaskData));
        dataTask.setNumberSmartCityPerspective(smartCityDimensionTaskData.getIsEnabled());
        dataTask.setNumberSmartCityPerspectiveRequest(gson.toJson(smartCityDimensionTaskData));

        HashSet<String> statMonthSet = new HashSet<>();
        HashSet<String> regionCodeSet = new HashSet<>();

        if (StringUtils.isNotEmpty(cbdClearlyTaskData.getStatMonth())) {
            statMonthSet.add(cbdClearlyTaskData.getStatMonth());
        }
        if (CollectionUtils.isNotEmpty(cbdClearlyTaskData.getRegionCodes())){
            regionCodeSet.addAll(cbdClearlyTaskData.getRegionCodes());
        }

        if (StringUtils.isNotEmpty(specificClearlyTaskData.getStatMonth())) {
            statMonthSet.add(specificClearlyTaskData.getStatMonth());
        }
        if (CollectionUtils.isNotEmpty(specificClearlyTaskData.getRegionCodes())){
            regionCodeSet.addAll(specificClearlyTaskData.getRegionCodes());
        }

        if (StringUtils.isNotEmpty(smartCityTaskData.getStatMonth())) {
            statMonthSet.add(smartCityTaskData.getStatMonth());
        }
        if (CollectionUtils.isNotEmpty(smartCityTaskData.getRegionCodes())){
            regionCodeSet.addAll(smartCityTaskData.getRegionCodes());
        }

        if (StringUtils.isNotEmpty(smartCityDimensionTaskData.getStatMonth())) {
            statMonthSet.add(smartCityDimensionTaskData.getStatMonth());
        }
        if (CollectionUtils.isNotEmpty(smartCityDimensionTaskData.getRegionCodes())){
            regionCodeSet.addAll(smartCityDimensionTaskData.getRegionCodes());
        }
        dataTask.setStatMonthAll(gson.toJson(statMonthSet));
        dataTask.setRegionCodeAll(gson.toJson(regionCodeSet));
        dataTask.setUpdateTime(new Date());
        return dataTask;
    }
}
