package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.mediaorder.sudomain.dto.AllCollectListData;
import heran.media.management.platform.mediaorder.sudomain.dto.MediumFormatCollectListData;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseManLand;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface OrderEffectEstimateBaseManLandEntityQueryMapper {
    @SelectProvider(type = OrderEffectEstimateBaseManLandEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "orderId", column = "order_id"), @Result(property = "exposureType", column = "exposure_type"), @Result(property = "mediumId", column = "medium_id"), @Result(property = "regionCode", column = "region_code"), @Result(property = "marketTime", column = "market_time"), @Result(property = "marketPlacementCount", column = "market_placement_count"), @Result(property = "pv", column = "pv"), @Result(property = "uv", column = "uv"), @Result(property = "af", column = "af"), @Result(property = "impTaUv", column = "imp_ta_uv"), @Result(property = "reach", column = "reach"), @Result(property = "ta", column = "ta"), @Result(property = "cpm", column = "cpm"), @Result(property = "cpuv", column = "cpuv"), @Result(property = "cpta", column = "cpta"), @Result(property = "remark", column = "remark"), @Result(property = "extraAttrs", column = "extra_attrs"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<OrderEffectEstimateBaseManLand> list(SearchCriteria criteria);


    @Select("SELECT t1.medium_id categoryId,t1.region_code cityCode,CONCAT_WS('/', t4.region_name, t3.region_name, t2.region_name) region_name,t1.market_time deliveryDays,t1.placement_cost placementCost,t1.market_placement_count vacancyPosition,t1.overlap_ratio overlapRatio,t1.pv,t1.uv,t1.af,t1.imp_ta_uv taUv,t1.ta,t1.reach,t1.cpm,t1.cpuv,t1.cpta " +
            "FROM `order_effect_estimate_base_man_land` t1 " +
            "LEFT JOIN main_dict_region t2 ON t1.region_code = t2.code " +
            "LEFT JOIN main_dict_region t3 ON t3.code = t2.parent_code " +
            "LEFT JOIN main_dict_region t4 ON t4.code = t3.parent_code " +
            "WHERE order_id = #{orderId} AND exposure_type = #{exposureType} AND biz_type = #{bizType}")
    @Results(value = {
            @Result(property = "categoryId", column = "categoryId"),
            @Result(property = "cityCode", column = "cityCode"),
            @Result(property = "regionName", column = "region_name"),
            @Result(property = "deliveryDays", column = "deliveryDays"),
            @Result(property = "placementCost", column = "placementCost"),
            @Result(property = "vacancyPosition", column = "vacancyPosition"),
            @Result(property = "overlapRatio", column = "overlapRatio"),
            @Result(property = "pv", column = "pv"),
            @Result(property = "uv", column = "uv"),
            @Result(property = "af", column = "af"),
            @Result(property = "taUv", column = "taUv"),
            @Result(property = "ta", column = "ta"),
            @Result(property = "reach", column = "reach"),
            @Result(property = "cpm", column = "cpm"),
            @Result(property = "cpUv", column = "cpuv"),
            @Result(property = "cpTa", column = "cpta"),
    }
    )
    List<MediumFormatCollectListData> getCollectListData(@Param("orderId") Integer orderId, @Param("exposureType") String exposureType, @Param("bizType") String bizType);

    @Select("SELECT t1.medium_id categoryId,t1.region_code cityCode,t2.region_name,t1.market_time deliveryDays,t1.placement_cost placementCost,t1.market_placement_count vacancyPosition,t1.overlap_ratio overlapRatio,t1.pv,t1.uv,t1.af,t1.imp_ta_uv taUv,t1.ta,t1.reach,t1.cpm,t1.cpuv,t1.cpta " +
            "FROM `order_effect_estimate_base_man_land` t1 " +
            "LEFT JOIN main_dict_region t2 ON t1.region_code = t2.code " +
            "WHERE order_id = #{orderId} AND exposure_type = 'MEDIUM_FORMAT_COLLECT' AND biz_type = #{bizType} AND medium_id = #{categoryId} LIMIT 1")
    @Results(value = {
            @Result(property = "categoryId", column = "categoryId"),
            @Result(property = "cityCode", column = "cityCode"),
            @Result(property = "regionName", column = "region_name"),
            @Result(property = "deliveryDays", column = "deliveryDays"),
            @Result(property = "placementCost", column = "placementCost"),
            @Result(property = "vacancyPosition", column = "vacancyPosition"),
            @Result(property = "overlapRatio", column = "overlapRatio"),
            @Result(property = "pv", column = "pv"),
            @Result(property = "uv", column = "uv"),
            @Result(property = "af", column = "af"),
            @Result(property = "taUv", column = "taUv"),
            @Result(property = "ta", column = "ta"),
            @Result(property = "reach", column = "reach"),
            @Result(property = "cpm", column = "cpm"),
            @Result(property = "cpUv", column = "cpuv"),
            @Result(property = "cpTa", column = "cpta"),
    }
    )
    MediumFormatCollectListData getMediumCollectListData(@Param("orderId") Integer orderId,
                                                               @Param("bizType") String bizType,
                                                               @Param("categoryId") Integer categoryId);

    @Select("SELECT t1.medium_count mediumCount,t1.city_count cityCount,t1.market_time deliveryDays,t1.placement_cost placementCost,t1.market_placement_count vacancyPosition,t1.overlap_ratio overlapRatio,t1.pv,t1.uv,t1.af,t1.imp_ta_uv taUv,t1.ta,t1.reach,t1.cpm,t1.cpuv,t1.cpta " +
            "FROM `order_effect_estimate_base_man_land` t1 " +
            "LEFT JOIN main_dict_region t2 ON t1.region_code = t2.code " +
            "WHERE order_id = #{orderId} AND exposure_type = #{exposureType} AND biz_type = #{bizType} LIMIT 1")
    @Results(value = {
            @Result(property = "mediumCount", column = "mediumCount"),
            @Result(property = "cityCount", column = "cityCount"),
            @Result(property = "vacancyPosition", column = "vacancyPosition"),
            @Result(property = "deliveryDays", column = "deliveryDays"),
            @Result(property = "pv", column = "pv"),
            @Result(property = "uv", column = "uv"),
            @Result(property = "af", column = "af"),
            @Result(property = "taUv", column = "taUv"),
            @Result(property = "ta", column = "ta"),
            @Result(property = "reach", column = "reach"),
            @Result(property = "cpm", column = "cpm"),
            @Result(property = "cpUv", column = "cpuv"),
            @Result(property = "cpTa", column = "cpta"),
    }
    )
    AllCollectListData getAllCollectListData(@Param("orderId") Integer orderId, @Param("exposureType") String exposureType, @Param("bizType") String bizType);

}
