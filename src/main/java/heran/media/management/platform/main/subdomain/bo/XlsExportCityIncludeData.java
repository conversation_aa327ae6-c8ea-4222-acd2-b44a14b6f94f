package heran.media.management.platform.main.subdomain.bo;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.main.subdomain.dto.ExportCityIncludeData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XlsExportCityIncludeData {
    @XlsField(title = "区域ID", columnIndex = 0)
    private String areaId;
    @XlsField(title = "adcode", columnIndex = 1)
    private String areaCode;
    @XlsField(title = "省/市/区县", columnIndex = 2)
    private String regionName;
    @XlsField(title = "1000网格ID", columnIndex = 3)
    private String cellId;
    @XlsField(title = "1000网格中心点", columnIndex = 4)
    private String center1000;
    @XlsField(title = "100网格ID", columnIndex = 3)
    private String cellId100;
    @XlsField(title = "100网格中心点", columnIndex = 5)
    private String center100;
    @XlsField(title = "100网格左上顶点", columnIndex = 6)
    private String wktUpLeft;
    @XlsField(title = "100网格左下顶点", columnIndex = 7)
    private String wktLowerLeft;
    @XlsField(title = "100网格右下顶点", columnIndex = 8)
    private String wktLowerRight;
    @XlsField(title = "100网格右上顶点", columnIndex = 9)
    private String wktUpRight;
    @XlsField(title = "人口类型", columnIndex = 10)
    private String type = "常驻";
    @XlsField(title = "100网格人口-202501", columnIndex = 11)
    private Integer peopleCount;

    // 动态字段，用于存储不同月份的人口数据
    private Map<String, Integer> monthlyPeopleCount;

    public XlsExportCityIncludeData(ExportCityIncludeData data) {
        this.areaId = data.getAreaId();
        this.areaCode = data.getAreaCode();

        if (StringUtils.isNotEmpty(data.getLv1Name())) {
            this.regionName = data.getLv1Name() + "/" + data.getLv2Name() + "/" + data.getLv3Name();
        } else {
            this.regionName = data.getLv2Name() + "/" + data.getLv1Name();
        }

        if (StringUtils.isNotEmpty(data.getCellId())) {
            String cellId = data.getCellId();
            this.cellId = cellId.length() >= 6 ? cellId.substring(0, 6) : cellId;
        }
        this.center1000 = data.getCenter1000();
        this.cellId100 = data.getCellId();
        this.center100 = data.getCenter100();
        this.wktUpLeft = data.getWktUpLeft();
        this.wktLowerLeft = data.getWktLowerLeft();
        this.wktLowerRight = data.getWktLowerRight();
        this.wktUpRight = data.getWktUpRight();
        this.peopleCount = data.getPeopleCount100();
    }

    // 动态生成表头的方法
    public static List<String> generateHeaders(List<String> statMonths) {
        List<String> headers = new ArrayList<>();
        headers.add("区域ID");
        headers.add("adcode");
        headers.add("省/市/区县");
        headers.add("1000网格ID");
        headers.add("1000网格中心点");
        headers.add("100网格ID");
        headers.add("100网格中心点");
        headers.add("100网格左上顶点");
        headers.add("100网格左下顶点");
        headers.add("100网格右下顶点");
        headers.add("100网格右上顶点");
        headers.add("人口类型");
        headers.add("100网格人口-202501");
        // 为每个月份添加表头
        for (String month : statMonths) {
            headers.add("100网格计算人口-" + month);
        }
        return headers;
    }

    // 根据月份获取人口数据
    public Integer getPeopleCountByMonth(String month) {
        return monthlyPeopleCount != null ? monthlyPeopleCount.get(month) : null;
    }
}
