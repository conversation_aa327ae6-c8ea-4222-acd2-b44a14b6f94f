package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MarketingSceneAoiData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleMarketingSceneAoiData implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "113")
    private Integer id;

    @ApiModelProperty(value = "查询月份", required = true, example = "查询月份")
    @Size(max = 64, message = "查询月份长度不能超出64位")
    @NotBlank(message = "查询月份不能为空")
    private String statMonth;

    @ApiModelProperty(value = "省份编号", required = false, example = "省份编号")
    @Size(max = 64, message = "省份编号长度不能超出64位")
    private String regionCode;

    @ApiModelProperty(value = "业务id", required = false, example = "AOI名称")
    @Size(max = 64, message = "业务id")
    private Integer bizId;

    @ApiModelProperty(value = "数值", required = false, example = "182")
    private Integer aoiNum;

    @ApiModelProperty(value = "人口口径（到时候看可以不可以定义成字典数据）", required = false, example = "人口口径（到时候看可以不可以定义成字典数据）")
    @Size(max = 64, message = "人口口径（到时候看可以不可以定义成字典数据）长度不能超出64位")
    private String poiPopulation;

    @ApiModelProperty(value = "请求内容", required = false, example = "请求内容")
    @Size(max = 64, message = "请求内容长度不能超出64位")
    private String requestContent;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangebleMarketingSceneAoiData() {
    }

    public MarketingSceneAoiData adapToPO() {
        MarketingSceneAoiData marketingSceneAoiData = new MarketingSceneAoiData();
        marketingSceneAoiData.setId(id);
        marketingSceneAoiData.setStatMonth(statMonth);
        marketingSceneAoiData.setRegionCode(regionCode);
        marketingSceneAoiData.setBizId(bizId);
        marketingSceneAoiData.setAoiNum(aoiNum);
        marketingSceneAoiData.setPoiPopulation(poiPopulation);
        marketingSceneAoiData.setRequestContent(requestContent);
        marketingSceneAoiData.setRemark(remark);
        marketingSceneAoiData.setExtraAttrs(extraAttrs);

        return marketingSceneAoiData;
    }
}
