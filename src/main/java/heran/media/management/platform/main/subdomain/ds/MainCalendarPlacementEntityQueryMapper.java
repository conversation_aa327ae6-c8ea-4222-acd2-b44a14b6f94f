package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.CalendarPlacementListDataFieldInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MainCalendarPlacementEntityQueryMapper {

    @SelectProvider(type = MainCalendarPlacementEntityQuerySQLProvider.class, method = "selectSchedulePlacementList")
    @Results(value = {
            @Result(property = "id", column = "calendarPlacementId"),
            @Result(property = "taUv", column = "tauv"),
            @Result(property = "cpUv", column = "cpuv"),
            @Result(property = "cpTa", column = "cpta")
    }
    )
    List<CalendarPlacementListDataFieldInfo> selectSchedulePlacementList(SearchCriteria criteria);
}
