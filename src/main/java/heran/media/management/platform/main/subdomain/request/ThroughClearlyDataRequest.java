package heran.media.management.platform.main.subdomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class ThroughClearlyDataRequest {
    @ApiModelProperty("aoiId")
    @NotNull(message = "aoiId不能为空")
    private Integer aoiId;
    @ApiModelProperty("月份")
    @NotNull(message = "月份不能为空")
    private String statMonth;
    @ApiModelProperty("人口口径")
    @NotNull(message = "人口口径不能为空")
    private String poiPopulation;
    @ApiModelProperty("洞察类型")
    @NotNull(message = "洞察类型不能为空 商圈洞察（CBD）特定场景洞察（AOI）")
    private String type;
}
