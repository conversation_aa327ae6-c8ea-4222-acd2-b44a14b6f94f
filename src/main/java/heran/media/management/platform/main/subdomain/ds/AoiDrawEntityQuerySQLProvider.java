package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class AoiDrawEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("ad.*,mr.code regionCode");
            FROM("aoi_draw as ad");
            LEFT_OUTER_JOIN("main_dict_region mr ON ad.region_id = mr.id");
            LEFT_OUTER_JOIN("aoi_draw_tag_rel as tr on ad.id = tr.aoi_draw_id ");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if (cri.getKey().equals("id")) {
                        WHERE("ad.id = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("regionCode")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("mr.code between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("mr.code >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("mr.code <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("mr.code = " + cri.getValue());
                    } else if (cri.getKey().equals("aoiName") && cri.getValue() != null) {
                        WHERE("ad.aoi_name like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("quoteCbd")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("ad.quote_cbd between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("ad.quote_cbd >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("ad.quote_cbd <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("ad.quote_cbd = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("cbdId")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("ad.cbd_id between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("ad.cbd_id >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("ad.cbd_id <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("ad.cbd_id = " + cri.getValue());
                    } else if (cri.getKey().equals("aoiArea") && cri.getValue() != null) {
                        WHERE("ad.aoi_area like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("aoiType") && cri.getValue() != null) {
                        WHERE("ad.aoi_type like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("polyline") && cri.getValue() != null) {
                        WHERE("ad.polyline like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("remark") && cri.getValue() != null) {
                        WHERE("ad.remark like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("extraAttrs") && cri.getValue() != null) {
                        WHERE("ad.extra_attrs like '%" + cri.getValue() + "%'");
                    } else if ("tagId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("tr.tag_id = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("createdByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("ad.created_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("ad.created_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("ad.created_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("ad.created_by_user = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("updatedByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("ad.updated_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("ad.updated_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("ad.updated_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("ad.updated_by_user = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }

                }
            }
            GROUP_BY("ad.id");
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
