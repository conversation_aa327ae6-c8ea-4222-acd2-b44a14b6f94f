package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MediaTaCrowdPack;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TMediaTaCrowdPack implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "10")
    private Integer id;

    @ApiModelProperty(value = "人群包名称", required = true, example = "人群包名称")
    @Size(max = 32, message = "人群包名称长度不能超出32位")
    @NotBlank(message = "人群包名称不能为空")
    private String packName;

    @ApiModelProperty(value = "人群包ID（不知是否有有特殊ID 有可能是对应高德或者自定义）", required = true, example = "人群包ID（不知是否有有特殊ID 有可能是对应高德或者自定义）")
    @Size(max = 32, message = "人群包ID（不知是否有有特殊ID 有可能是对应高德或者自定义）长度不能超出32位")
    @NotBlank(message = "人群包ID（不知是否有有特殊ID 有可能是对应高德或者自定义）不能为空")
    private String packCode;

    @ApiModelProperty(value = "一级分类", required = true, example = "180")
    @NotNull(message = "一级分类不能为空")
    private Integer categoryIdLv1;

    @ApiModelProperty(value = "二级分类", required = true, example = "182")
    @NotNull(message = "二级分类不能为空")
    private Integer categoryIdLv2;

    @ApiModelProperty(value = "一级分类名称", required = true, example = "名称")
    private String categoryNameLv1;

    @ApiModelProperty(value = "二级分类名称", required = true, example = "名称")
    private String categoryNameLv2;

    @ApiModelProperty(value = "是否启动", required = true, example = "251")
    @NotNull(message = "是否启动不能为空")
    private Integer isEnabled;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 500, message = "说明长度不能超出500位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录创建用户名称", required = false, example = "")
    private String createdByUserName;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;

    @ApiModelProperty(value = "标签", required = true, example = "[1,2,3]")
    private List<Integer> tagIds;

    @ApiModelProperty(value = "标签名称", required = true, example = "年龄段|25-30岁;年龄段|31-34岁;年龄段|25-40岁;年龄段")
    private String tagNames;


    public TMediaTaCrowdPack() {
    }

    public TMediaTaCrowdPack(MediaTaCrowdPack mediaTaCrowdPack) {
        this.id = mediaTaCrowdPack.getId();
        this.packName = mediaTaCrowdPack.getPackName();
        this.packCode = mediaTaCrowdPack.getPackCode();
        this.categoryIdLv1 = mediaTaCrowdPack.getCategoryIdLv1();
        this.categoryIdLv2 = mediaTaCrowdPack.getCategoryIdLv2();
        this.isEnabled = mediaTaCrowdPack.getIsEnabled();
        this.remark = mediaTaCrowdPack.getRemark();
        this.extraAttrs = mediaTaCrowdPack.getExtraAttrs();
        this.createdByUser = mediaTaCrowdPack.getCreatedByUser();
        this.updatedByUser = mediaTaCrowdPack.getUpdatedByUser();
        this.creator = mediaTaCrowdPack.getCreator();
        this.updater = mediaTaCrowdPack.getUpdater();
        this.createTime = mediaTaCrowdPack.getCreateTime();
        this.updateTime = mediaTaCrowdPack.getUpdateTime();

    }

    public TMediaTaCrowdPack(MediaTaCrowdPackData mediaTaCrowdPackData) {
        this.id = mediaTaCrowdPackData.getId();
        this.packName = mediaTaCrowdPackData.getPackName();
        this.packCode = mediaTaCrowdPackData.getPackCode();
        this.categoryIdLv1 = mediaTaCrowdPackData.getCategoryIdLv1();
        this.categoryIdLv2 = mediaTaCrowdPackData.getCategoryIdLv2();
        this.categoryNameLv1 = mediaTaCrowdPackData.getCategoryNameLv1();
        this.categoryNameLv2 = mediaTaCrowdPackData.getCategoryNameLv2();
        this.isEnabled = mediaTaCrowdPackData.getIsEnabled();
        this.remark = mediaTaCrowdPackData.getRemark();
        this.extraAttrs = mediaTaCrowdPackData.getExtraAttrs();
        this.createdByUser = mediaTaCrowdPackData.getCreatedByUser();
        this.createdByUserName = mediaTaCrowdPackData.getCreatedByUserName();
        this.updatedByUser = mediaTaCrowdPackData.getUpdatedByUser();
        this.creator = mediaTaCrowdPackData.getCreator();
        this.updater = mediaTaCrowdPackData.getUpdater();
        this.createTime = mediaTaCrowdPackData.getCreateTime();
        this.updateTime = mediaTaCrowdPackData.getUpdateTime();
        this.tagNames = mediaTaCrowdPackData.getTagNames();
    }
}
