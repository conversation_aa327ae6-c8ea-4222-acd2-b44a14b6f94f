package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.PoiAnalyseDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangeblePoiAnalyseDetail implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "43")
    private Integer id;

    @ApiModelProperty(value = "查询月份(预留字段需求中写到按月更新 但是页面没有月份选择器 感觉是实时的)", required = true, example = "查询月份(预留字段需求中写到按月更新 但是页面没有月份选择器 感觉是实时的)")
    @Size(max = 64, message = "查询月份(预留字段需求中写到按月更新 但是页面没有月份选择器 感觉是实时的)长度不能超出64位")
    @NotBlank(message = "查询月份(预留字段需求中写到按月更新 但是页面没有月份选择器 感觉是实时的)不能为空")
    private String statMonth;

    @ApiModelProperty(value = "分析名称", required = true, example = "分析名称")
    @Size(max = 64, message = "分析名称长度不能超出64位")
    @NotBlank(message = "分析名称不能为空")
    private String analyseName;

    @ApiModelProperty(value = "POI_STATISTICS（POI统计）POI_DELINEATION（POI圈定）", required = true, example = "")
    @Size(max = 32, message = "POI_STATISTICS（POI统计）POI_DELINEATION（POI圈定）长度不能超出32位")
    @NotBlank(message = "POI_STATISTICS（POI统计）POI_DELINEATION（POI圈定）不能为空")
    private String analyseType;

    @ApiModelProperty(value = "分析类型 BRAND（按品牌）INDUSTRY（按行业）KEYWORD（按关键字）", required = true, example = "")
    @Size(max = 32, message = "分析类型 BRAND（按品牌）INDUSTRY（按行业）KEYWORD（按关键字）长度不能超出32位")
    @NotBlank(message = "分析类型 BRAND（按品牌）INDUSTRY（按行业）KEYWORD（按关键字）不能为空")
    private String poiType;

    @ApiModelProperty(value = "省份编号", required = false, example = "省份编号")
    @Size(max = 64, message = "省份编号长度不能超出64位")
    private String provinceCode;

    @ApiModelProperty(value = "城市编号", required = false, example = "城市编号")
    @Size(max = 64, message = "城市编号长度不能超出64位")
    private String cityCode;

    @ApiModelProperty(value = "区域/县级市编号", required = false, example = "区域/县级市编号")
    @Size(max = 64, message = "区域/县级市编号长度不能超出64位")
    private String districtCode;

    @ApiModelProperty(value = "详情地址", required = false, example = "详情地址")
    @Size(max = 255, message = "详情地址长度不能超出255位")
    private String address;

    @ApiModelProperty(value = "所在经度", required = false, example = "所在经度")
    @Size(max = 64, message = "所在经度长度不能超出64位")
    private String longitude;

    @ApiModelProperty(value = "所在维度", required = false, example = "所在维度")
    @Size(max = 64, message = "所在维度长度不能超出64位")
    private String latitude;

    @ApiModelProperty(value = "目标地址", required = false, example = "目标地址")
    @Size(max = 255, message = "目标地址长度不能超出255位")
    private String targetAddress;

    @ApiModelProperty(value = "地理围栏", required = false, example = "地理围栏")
    @Size(max = 64, message = "地理围栏长度不能超出64位")
    private String geofencing;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangeblePoiAnalyseDetail() {
    }

    public PoiAnalyseDetail adapToPO() {
        PoiAnalyseDetail poiAnalyseDetail = new PoiAnalyseDetail();
        poiAnalyseDetail.setId(id);
        poiAnalyseDetail.setAnalyseName(analyseName);
        poiAnalyseDetail.setAnalyseType(analyseType);
        poiAnalyseDetail.setPoiType(poiType);
        poiAnalyseDetail.setProvinceCode(provinceCode);
        poiAnalyseDetail.setCityCode(cityCode);
        poiAnalyseDetail.setDistrictCode(districtCode);
        poiAnalyseDetail.setAddress(address);
        poiAnalyseDetail.setLongitude(longitude);
        poiAnalyseDetail.setLatitude(latitude);
        poiAnalyseDetail.setTargetAddress(targetAddress);
        poiAnalyseDetail.setGeofencing(geofencing);
        poiAnalyseDetail.setRemark(remark);
        poiAnalyseDetail.setExtraAttrs(extraAttrs);

        return poiAnalyseDetail;
    }
}
