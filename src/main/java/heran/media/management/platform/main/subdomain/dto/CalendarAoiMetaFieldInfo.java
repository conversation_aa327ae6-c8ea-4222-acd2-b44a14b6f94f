package heran.media.management.platform.main.subdomain.dto;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.annotation.MetaField;
import heran.media.sharelib.domain.bo.LockDataType;
import heran.media.sharelib.utils.date.DataTmeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.stream.Collectors;

import static heran.media.management.platform.effectestimate.subdomian.dto.ExposureMetaFieldInfo.parseStatMonth;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CalendarAoiMetaFieldInfo extends HotCalendarPlacementData {
    @MetaField(columnIndex = 40, labelCn = "订单曝光数据查询时间", width = 120)
    private String orderExposureStatMonth;
    @MetaField(columnIndex = 41, labelCn = "订单曝光人口口径", width = 120)
    private String population;
    @MetaField(columnIndex = 51, labelCn = "订单曝光人次PV（人次）")
    private Integer pv;
    @MetaField(columnIndex = 52, labelCn = "订单曝光人数UV（次）")
    private Integer uv;
    @MetaField(columnIndex = 53, labelCn = "订单平均触达频次AF（次）")
    private Double af;
    @MetaField(columnIndex = 54, labelCn = "订单曝光潜客人数TAUV（人）")
    private Integer taUv;
    @MetaField(columnIndex = 55, labelCn = "订单千人成本CPM（元）")
    private Double cpm;
    @MetaField(columnIndex = 56, labelCn = "订单单人成本CPUV（元）")
    private Double cpUv;
    @MetaField(columnIndex = 57, labelCn = "订单单TA成本CPTA（元）")
    private Double cpTa;
    @MetaField(columnIndex = 58, labelCn = "全域效果评估曝光数据查询时间", width = 120)
    private String appraisalStatMonth;
    @MetaField(columnIndex = 59, labelCn = "全域效果评估曝光人口口径", width = 120)
    private String appraisalPopulation;
    @MetaField(columnIndex = 60, labelCn = "全域效果评估曝光人次PV（人次）")
    private Integer appraisalPv;
    @MetaField(columnIndex = 61, labelCn = "全域效果评估曝光人数UV（次）")
    private Integer appraisalUv;
    @MetaField(columnIndex = 62, labelCn = "全域效果评估平均触达频次AF（次）")
    private Double appraisalAf;
    @MetaField(columnIndex = 63, labelCn = "全域效果评估曝光潜客人数TAUV（人）")
    private Integer appraisalTaUv;
    @MetaField(columnIndex = 64, labelCn = "全域效果评估千人成本CPM（元）")
    private Double appraisalCpm;
    @MetaField(columnIndex = 65, labelCn = "全域效果评估单人成本CPUV（元）")
    private Double appraisalCpUv;
    @MetaField(columnIndex = 66, labelCn = "全域效果评估单TA成本CPTA（元）")
    private Double appraisalCpTa;

    public void setPopulationName() {
        if (StringUtils.isNotEmpty(population)) {
            LockDataType lockDataType = LockDataType.valueOf(population);
            this.population = lockDataType.getName();
        }
        if (StringUtils.isNotEmpty(appraisalPopulation)) {
            LockDataType lockDataType = LockDataType.valueOf(appraisalPopulation);
            this.appraisalPopulation = lockDataType.getName();
        }
        if (StringUtils.isNotEmpty(orderExposureStatMonth)) {
            List<String> list = parseStatMonth(orderExposureStatMonth);
            List<String> stringList = list.stream().map(DataTmeUtils::formatYearMonth).collect(Collectors.toList());
            this.orderExposureStatMonth = String.join(",", stringList);
        }
        if (StringUtils.isNotEmpty(appraisalStatMonth)) {
            List<String> list = parseStatMonth(appraisalStatMonth);
            List<String> stringList = list.stream().map(DataTmeUtils::formatYearMonth).collect(Collectors.toList());
            this.appraisalStatMonth = String.join(",", stringList);
        }
    }
}
