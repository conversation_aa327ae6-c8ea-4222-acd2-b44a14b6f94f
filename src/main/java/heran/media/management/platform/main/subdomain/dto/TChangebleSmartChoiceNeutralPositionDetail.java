package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.SmartChoiceNeutralPositionDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleSmartChoiceNeutralPositionDetail implements Serializable {

    @ApiModelProperty(value = "主键id", required = false, example = "7")
    private Integer id;

    @ApiModelProperty(value = "智选方案id", required = false, example = "237")
    private Integer selectionPlanId;

    @ApiModelProperty(value = "点位编号", required = false, example = "点位编号")
    @Size(max = 64, message = "点位编号长度不能超出64位")
    private String placementCode;

    @ApiModelProperty(value = "省份编号", required = false, example = "省份编号")
    @Size(max = 64, message = "省份编号长度不能超出64位")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称", required = false, example = "省份名称")
    @Size(max = 64, message = "省份名称长度不能超出64位")
    private String province;

    @ApiModelProperty(value = "城市编号", required = false, example = "城市编号")
    @Size(max = 64, message = "城市编号长度不能超出64位")
    private String cityCode;

    @ApiModelProperty(value = "城市名称", required = false, example = "城市名称")
    @Size(max = 64, message = "城市名称长度不能超出64位")
    private String city;

    @ApiModelProperty(value = "区域/县级市编号", required = false, example = "区域/县级市编号")
    @Size(max = 64, message = "区域/县级市编号长度不能超出64位")
    private String districtCode;

    @ApiModelProperty(value = "区域/县级市名称", required = false, example = "区域/县级市名称")
    @Size(max = 64, message = "区域/县级市名称长度不能超出64位")
    private String district;

    @ApiModelProperty(value = "媒体形式", required = false, example = "媒体形式")
    @Size(max = 64, message = "媒体形式长度不能超出64位")
    private String mediumFormat;

    @ApiModelProperty(value = "媒体筛选条件", required = false, example = "媒体筛选条件")
    @Size(max = 64, message = "媒体筛选条件长度不能超出64位")
    private String mediumFiltra;

    @ApiModelProperty(value = "媒体名称", required = false, example = "媒体名称")
    @Size(max = 64, message = "媒体名称长度不能超出64位")
    private String mediumName;

    @ApiModelProperty(value = "媒体位置", required = false, example = "媒体位置")
    @Size(max = 64, message = "媒体位置长度不能超出64位")
    private String mediumAddress;

    @ApiModelProperty(value = "点位数据 ？？？？？这都具体到点位了为啥还有点位数量", required = false, example = "53")
    private Integer placementCount;

    @ApiModelProperty(value = "计划投放开始时间", required = false, example = "1741321657729")
    private Date planLaunchStartTime;

    @ApiModelProperty(value = "计划投放结束时间", required = false, example = "1741321657729")
    private Date planLaunchEndTime;


    public TChangebleSmartChoiceNeutralPositionDetail() {
    }

    public SmartChoiceNeutralPositionDetail adapToPO() {
        SmartChoiceNeutralPositionDetail smartChoiceNeutralPositionDetail = new SmartChoiceNeutralPositionDetail();
        smartChoiceNeutralPositionDetail.setId(id);
        smartChoiceNeutralPositionDetail.setSelectionPlanId(selectionPlanId);
        smartChoiceNeutralPositionDetail.setPlacementCode(placementCode);
        smartChoiceNeutralPositionDetail.setProvinceCode(provinceCode);
        smartChoiceNeutralPositionDetail.setProvince(province);
        smartChoiceNeutralPositionDetail.setCityCode(cityCode);
        smartChoiceNeutralPositionDetail.setCity(city);
        smartChoiceNeutralPositionDetail.setDistrictCode(districtCode);
        smartChoiceNeutralPositionDetail.setDistrict(district);
        smartChoiceNeutralPositionDetail.setMediumFormat(mediumFormat);
        smartChoiceNeutralPositionDetail.setMediumFiltra(mediumFiltra);
        smartChoiceNeutralPositionDetail.setMediumName(mediumName);
        smartChoiceNeutralPositionDetail.setMediumAddress(mediumAddress);
        smartChoiceNeutralPositionDetail.setPlacementCount(placementCount);
        smartChoiceNeutralPositionDetail.setPlanLaunchStartTime(planLaunchStartTime);
        smartChoiceNeutralPositionDetail.setPlanLaunchEndTime(planLaunchEndTime);

        return smartChoiceNeutralPositionDetail;
    }
}
