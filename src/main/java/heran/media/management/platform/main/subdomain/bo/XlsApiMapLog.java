package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.ApiMapLog;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsApiMapLog {
    @XlsField(title = "主键", columnIndex = 0)
    private Long id;

    @XlsField(title = "请求参数", columnIndex = 1)
    private String reqParam;

    @XlsField(title = "请求对象体", columnIndex = 2)
    private String reqBody;

    @XlsField(title = "是否成功", columnIndex = 3)
    private Integer isSuc;

    @XlsField(title = "错误码", columnIndex = 4)
    private String errCode;

    @XlsField(title = "错误信息", columnIndex = 5)
    private String errMessage;

    @XlsField(title = "响应体", columnIndex = 6)
    private String respData;

    @XlsField(title = "行记录创建用户", columnIndex = 7)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 8)
    private Integer updatedByUser;


    public XlsApiMapLog() {
    }

    public XlsApiMapLog(ApiMapLog apiMapLog) {
        this.id = apiMapLog.getId();
        this.reqParam = apiMapLog.getReqParam();
        this.reqBody = apiMapLog.getReqBody();
        this.isSuc = apiMapLog.getIsSuc();
        this.errCode = apiMapLog.getErrCode();
        this.errMessage = apiMapLog.getErrMessage();
        this.respData = apiMapLog.getRespData();
        this.createdByUser = apiMapLog.getCreatedByUser();
        this.updatedByUser = apiMapLog.getUpdatedByUser();

    }

    public ApiMapLog adapToPO(Integer userId) {
        ApiMapLog apiMapLog = new ApiMapLog();
        apiMapLog.setId(id);
        apiMapLog.setReqParam(reqParam);
        apiMapLog.setReqBody(reqBody);
        apiMapLog.setIsSuc(isSuc);
        apiMapLog.setErrCode(errCode);
        apiMapLog.setErrMessage(errMessage);
        apiMapLog.setRespData(respData);

        apiMapLog.setCreatedByUser(userId);
        apiMapLog.setUpdatedByUser(userId);
        return apiMapLog;
    }
}
