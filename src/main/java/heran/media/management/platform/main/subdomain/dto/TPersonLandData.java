package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.PersonLandData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TPersonLandData implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "55")
    private Integer id;

    @ApiModelProperty(value = "查询月份", required = true, example = "查询月份")
    @Size(max = 64, message = "查询月份长度不能超出64位")
    @NotBlank(message = "查询月份不能为空")
    private String statMonth;

    @ApiModelProperty(value = "查询城市", required = false, example = "查询城市")
    @Size(max = 64, message = "查询城市长度不能超出64位")
    private String regionCode;

    @ApiModelProperty(value = "居住人口统计/天级", required = false, example = "184")
    private Integer dwellPopulation;

    @ApiModelProperty(value = "工作人口统计/天级", required = false, example = "31")
    private Integer workPopulation;

    @ApiModelProperty(value = "常驻人口统计/天级", required = false, example = "51")
    private Integer residentPopulation;

    @ApiModelProperty(value = "客流人口统计/天级", required = false, example = "132")
    private Integer passengePopulation;

    @ApiModelProperty(value = "客流人口统计/小时级", required = false, example = "113")
    private Integer passengeHourPopulation;

    @ApiModelProperty(value = "TA常驻人口统计", required = false, example = "TA常驻人口统计")
    @Size(max = 64, message = "TA常驻人口统计长度不能超出64位")
    private String taResidentPopulation;

    @ApiModelProperty(value = "城市网格热力", required = false, example = "常驻，居住")
    private String cityHotText;

    @ApiModelProperty(value = "城市网格人口", required = false, example = "常驻，居住")
    private String wktTypes;

    @ApiModelProperty(value = "城市透视", required = false, example = "是或否")
    private String urbanPerspective;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;

    public TPersonLandData() {
    }

    public TPersonLandData(PersonLandData personLandData) {
        this.id = personLandData.getId();
        this.statMonth = personLandData.getStatMonth();
        this.regionCode = personLandData.getRegionCode();
        this.dwellPopulation = personLandData.getDwellPopulation();
        this.workPopulation = personLandData.getWorkPopulation();
        this.residentPopulation = personLandData.getResidentPopulation();
        this.passengePopulation = personLandData.getPassengePopulation();
        this.passengeHourPopulation = personLandData.getPassengeHourPopulation();
        this.taResidentPopulation = personLandData.getTaResidentPopulation();
        this.updateTime = personLandData.getUpdateTime();
    }

    public TPersonLandData(PersonLandDataList personLandData) {
        this.id = personLandData.getId();
        this.statMonth = personLandData.getStatMonth();
        this.regionCode = personLandData.getRegionCode();
        this.dwellPopulation = personLandData.getDwellPopulation();
        this.workPopulation = personLandData.getWorkPopulation();
        this.residentPopulation = personLandData.getResidentPopulation();
        this.passengePopulation = personLandData.getPassengePopulation();
        this.passengeHourPopulation = personLandData.getPassengeHourPopulation();
        this.updateTime = personLandData.getUpdateTime();
        this.wktTypes = personLandData.getWktTypes();
        this.cityHotText = personLandData.getCityHotText();
        this.urbanPerspective = personLandData.getUrbanPerspective();
        this.taResidentPopulation = personLandData.getTaResidentData();
    }
}
