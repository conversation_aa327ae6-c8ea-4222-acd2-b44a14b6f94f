package heran.media.management.platform.main.subdomain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class MainMediaTagDataList {
    @ApiModelProperty("一级标签id")
    private Integer lv1Id;
    @ApiModelProperty("二级标签id")
    private Integer lv2Id;
    @ApiModelProperty("三级标签id")
    private Integer lv3Id;
    @ApiModelProperty("一级标签名称")
    private String tagLv1Name;
    @ApiModelProperty("二级标签名称")
    private String tagLv2Name;
    @ApiModelProperty("三级标签名称")
    private String tagLv3Name;
    @ApiModelProperty("说明")
    private String remark;
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
