package heran.media.management.platform.main.subdomain.response;

import heran.media.sharelib.domain.db.model.main.PassengerFlowHour;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PassengerFlowHourResponse {
    @ApiModelProperty("小时数")
    private Integer dataHour;
    @ApiModelProperty("人口数")
    private Integer passengerFlowCount;

    public PassengerFlowHourResponse(PassengerFlowHour data){
        this.dataHour = data.getDataHour();
        this.passengerFlowCount = data.getPassengerFlowCount();
    }
}
