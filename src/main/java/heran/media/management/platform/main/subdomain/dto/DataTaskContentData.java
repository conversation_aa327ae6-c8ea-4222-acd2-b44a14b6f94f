package heran.media.management.platform.main.subdomain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataTaskContentData {
    @ApiModelProperty("洞察维度名称")
    private String contentName;
    @ApiModelProperty("洞察维度编码")
    private String contentCode;
    @ApiModelProperty("洞察维度状态呀")
    private Boolean status;
}
