package heran.media.management.platform.main.subdomain.response;

import com.alibaba.nacos.common.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import heran.media.management.platform.main.subdomain.dto.GetMarketingSceneAoiData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class GetMarketingSceneAoiDataResponse {
    @ApiModelProperty("流量统计")
    private Map<String, String> trafficStatistics;
    @ApiModelProperty("客流画像")
    private Map<String, String> passengerPortrayal;
    @ApiModelProperty("出行方式")
    private Map<String, String> tripWay;
    @ApiModelProperty("客流来源")
    private Map<String, String> passengerSource;
    @ApiModelProperty("人口统计")
    private Map<String, String> demographicStatistics;
    @ApiModelProperty("人群画像")
    private Map<String, String> crowdPortrayal;
    @ApiModelProperty("AOI洞察出行方式")
    private Map<String, String> apiTripWay;
    @ApiModelProperty("人群来源")
    private Map<String, String> crowdSource;
    @ApiModelProperty("线下场景偏好")
    private List<MarketingSceneAoiOfflineSceneDataResponse> offlineScene;

    public GetMarketingSceneAoiDataResponse(GetMarketingSceneAoiData data) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            this.trafficStatistics = parseJson(data.getTrafficStatistics(), objectMapper);
            this.passengerPortrayal = parseJson(data.getPassengerPortrayal(), objectMapper);
            this.tripWay = parseJson(data.getTripWay(), objectMapper);
            this.passengerSource = parseJson(data.getPassengerSource(), objectMapper);
            if (StringUtils.isNotEmpty(data.getDemographicStatistics())) {
                Map<String, String> stringMap = parseJson(data.getDemographicStatistics(), objectMapper);
                if (!stringMap.isEmpty()) {
                    this.demographicStatistics = stringMap.entrySet().stream()
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey,
                                    e -> new BigDecimal(e.getValue())
                                            .divide(new BigDecimal(10000), 5, RoundingMode.HALF_UP)
                                            .stripTrailingZeros()
                                            .toPlainString()
                            ));
                }
            }
            this.crowdPortrayal = parseJson(data.getCrowdPortrayal(), objectMapper);
            this.apiTripWay = parseJson(data.getApiTripWay(), objectMapper);
            this.crowdSource = parseJson(data.getCrowdSource(), objectMapper);
        } catch (JsonProcessingException e) {
            log.error("JSON转对象发生错误：{}", e.getMessage());
        }
    }

    /**
     * 解析JSON字符串为Map
     */
    private Map<String, String> parseJson(String json, ObjectMapper objectMapper) throws JsonProcessingException {
        if (json == null) {
            return null;
        }
        return objectMapper.readValue(json, new TypeReference<Map<String, String>>() {
        });
    }
}
