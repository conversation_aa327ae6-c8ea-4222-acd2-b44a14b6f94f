package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseCustom;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TOrderEffectEstimateBaseCustom implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "144")
    private Integer id;

    @ApiModelProperty(value = "订单id", required = true, example = "136")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "公交车覆盖", required = true, example = "公交车覆盖")
    @Size(max = 64, message = "公交车覆盖长度不能超出64位")
    @NotBlank(message = "公交车覆盖不能为空")
    private String busCover;

    @ApiModelProperty(value = "住宅区覆盖", required = false, example = "211")
    private Integer residenceCover;

    @ApiModelProperty(value = "写字楼覆盖", required = false, example = "写字楼覆盖")
    @Size(max = 64, message = "写字楼覆盖长度不能超出64位")
    private String officeBuildingCover;

    @ApiModelProperty(value = "商圈覆盖", required = false, example = "商圈覆盖")
    @Size(max = 64, message = "商圈覆盖长度不能超出64位")
    private String cbdCover;

    @ApiModelProperty(value = "超市覆盖", required = false, example = "超市覆盖")
    @Size(max = 64, message = "超市覆盖长度不能超出64位")
    private String supermarketCover;

    @ApiModelProperty(value = "大学覆盖", required = false, example = "大学覆盖")
    @Size(max = 64, message = "大学覆盖长度不能超出64位")
    private String universityCover;

    @ApiModelProperty(value = "行政区覆盖", required = false, example = "行政区覆盖")
    @Size(max = 64, message = "行政区覆盖长度不能超出64位")
    private String administrativeRegionCover;

    @ApiModelProperty(value = "地铁站覆盖", required = false, example = "地铁站覆盖")
    @Size(max = 64, message = "地铁站覆盖长度不能超出64位")
    private String undergroundCover;

    @ApiModelProperty(value = "星级酒店覆盖", required = false, example = "星级酒店覆盖")
    @Size(max = 64, message = "星级酒店覆盖长度不能超出64位")
    private String hotelCover;

    @ApiModelProperty(value = "品牌零售覆盖", required = false, example = "品牌零售覆盖")
    @Size(max = 64, message = "品牌零售覆盖长度不能超出64位")
    private String brandCover;

    @ApiModelProperty(value = "是否显示", required = false, example = "是否显示")
    @Size(max = 64, message = "是否显示长度不能超出64位")
    private String isShow;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TOrderEffectEstimateBaseCustom() {
    }

    public TOrderEffectEstimateBaseCustom(OrderEffectEstimateBaseCustom orderEffectEstimateBaseCustom) {
        this.id = orderEffectEstimateBaseCustom.getId();
        this.orderId = orderEffectEstimateBaseCustom.getOrderId();
        this.busCover = orderEffectEstimateBaseCustom.getBusCover();
        this.residenceCover = orderEffectEstimateBaseCustom.getResidenceCover();
        this.officeBuildingCover = orderEffectEstimateBaseCustom.getOfficeBuildingCover();
        this.cbdCover = orderEffectEstimateBaseCustom.getCbdCover();
        this.supermarketCover = orderEffectEstimateBaseCustom.getSupermarketCover();
        this.universityCover = orderEffectEstimateBaseCustom.getUniversityCover();
        this.administrativeRegionCover = orderEffectEstimateBaseCustom.getAdministrativeRegionCover();
        this.undergroundCover = orderEffectEstimateBaseCustom.getUndergroundCover();
        this.hotelCover = orderEffectEstimateBaseCustom.getHotelCover();
        this.brandCover = orderEffectEstimateBaseCustom.getBrandCover();
        this.isShow = orderEffectEstimateBaseCustom.getIsShow();
        this.remark = orderEffectEstimateBaseCustom.getRemark();
        this.extraAttrs = orderEffectEstimateBaseCustom.getExtraAttrs();
        this.createdByUser = orderEffectEstimateBaseCustom.getCreatedByUser();
        this.updatedByUser = orderEffectEstimateBaseCustom.getUpdatedByUser();
        this.creator = orderEffectEstimateBaseCustom.getCreator();
        this.updater = orderEffectEstimateBaseCustom.getUpdater();
        this.createTime = orderEffectEstimateBaseCustom.getCreateTime();
        this.updateTime = orderEffectEstimateBaseCustom.getUpdateTime();

    }
}
