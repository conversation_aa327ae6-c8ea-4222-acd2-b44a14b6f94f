package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.StatisticsDimension;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleStatisticsDimension implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "161")
    private Integer id;

    @ApiModelProperty(value = "查询月份", required = true, example = "查询月份")
    @Size(max = 64, message = "查询月份长度不能超出64位")
    @NotBlank(message = "查询月份不能为空")
    private String statMonth;

    @ApiModelProperty(value = "查询城市", required = true, example = "查询城市")
    @Size(max = 32, message = "查询城市长度不能超出32位")
    @NotBlank(message = "查询城市不能为空")
    private String regionCode;

    @ApiModelProperty(value = "统计类型DWELL_POPULATION（居住人口）WORK_POPULATION(常驻人口)WORK_POPULATION(工作人口)PASSENGE_POPULATION(客流人口)", required = false, example = "")
    @Size(max = 32, message = "统计类型DWELL_POPULATION（居住人口）WORK_POPULATION(常驻人口)WORK_POPULATION(工作人口)PASSENGE_POPULATION(客流人口)长度不能超出32位")
    private String statisticalType;

    @ApiModelProperty(value = "统计维度名称", required = true, example = "统计维度名称")
    @Size(max = 64, message = "统计维度名称长度不能超出64位")
    @NotBlank(message = "统计维度名称不能为空")
    private String dimensionName;

    @ApiModelProperty(value = "统计维度值 JOIN", required = true, example = "统计维度值 JOIN")
    @NotBlank(message = "统计维度值 JOIN不能为空")
    private String dimensionValue;


    public TChangebleStatisticsDimension() {
    }

    public StatisticsDimension adapToPO() {
        StatisticsDimension statisticsDimension = new StatisticsDimension();
        statisticsDimension.setId(id);
        statisticsDimension.setStatMonth(statMonth);
        statisticsDimension.setRegionCode(regionCode);
        statisticsDimension.setStatisticalType(statisticalType);
        statisticsDimension.setDimensionName(dimensionName);
        statisticsDimension.setDimensionValue(dimensionValue);

        return statisticsDimension;
    }
}
