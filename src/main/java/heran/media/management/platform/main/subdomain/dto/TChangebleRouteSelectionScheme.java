package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.RouteSelectionScheme;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleRouteSelectionScheme implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "73")
    private Integer id;

    @ApiModelProperty(value = "媒体智能选点方案id", required = true, example = "125")
    @NotNull(message = "媒体智能选点方案id不能为空")
    private Integer selectionPlanId;

    @ApiModelProperty(value = "区域code", required = true, example = "区域code")
    @Size(max = 64, message = "区域code长度不能超出64位")
    @NotBlank(message = "区域code不能为空")
    private String regionCode;

    @ApiModelProperty(value = "过滤条件 json", required = false, example = "过滤条件 json")
    @Size(max = 255, message = "过滤条件 json长度不能超出255位")
    private String filterCondition;

    @ApiModelProperty(value = "状态 UNFINISHED(未完成)，FINISHED(已完成)", required = true, example = "状态 UNFINISHED(未完成)，FINISHED(已完成)")
    @Size(max = 64, message = "状态 UNFINISHED(未完成)，FINISHED(已完成)长度不能超出64位")
    @NotBlank(message = "状态 UNFINISHED(未完成)，FINISHED(已完成)不能为空")
    private String status;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangebleRouteSelectionScheme() {
    }

    public RouteSelectionScheme adapToPO() {
        RouteSelectionScheme routeSelectionScheme = new RouteSelectionScheme();
        routeSelectionScheme.setId(id);
        routeSelectionScheme.setSelectionPlanId(selectionPlanId);
        routeSelectionScheme.setFilterCondition(filterCondition);
        routeSelectionScheme.setStatus(status);
        routeSelectionScheme.setRemark(remark);
        routeSelectionScheme.setExtraAttrs(extraAttrs);

        return routeSelectionScheme;
    }
}
