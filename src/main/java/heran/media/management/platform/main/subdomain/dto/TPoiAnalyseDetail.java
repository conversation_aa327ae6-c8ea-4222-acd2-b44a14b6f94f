package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.PoiAnalyseDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TPoiAnalyseDetail implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "234")
    private Integer id;

    @ApiModelProperty(value = "分析名称", required = true, example = "分析名称")
    @Size(max = 64, message = "分析名称长度不能超出64位")
    @NotBlank(message = "分析名称不能为空")
    private String analyseName;

    @ApiModelProperty(value = "poiId", required = true, example = "poiId")
    private String poiId;

    @ApiModelProperty(value = "poiName", required = true, example = "poiName")
    private String poiName;

    @ApiModelProperty(value = "code", required = true, example = "code")
    private String code;

    @ApiModelProperty(value = "POI_STATISTICS（POI统计）POI_DELINEATION（POI圈定）", required = true, example = "")
    @Size(max = 32, message = "POI_STATISTICS（POI统计）POI_DELINEATION（POI圈定）长度不能超出32位")
    @NotBlank(message = "POI_STATISTICS（POI统计）POI_DELINEATION（POI圈定）不能为空")
    private String analyseType;

    @ApiModelProperty(value = "分析类型 BRAND（按品牌）INDUSTRY（按行业）KEYWORD（按关键字）", required = true, example = "")
    @Size(max = 32, message = "分析类型 BRAND（按品牌）INDUSTRY（按行业）KEYWORD（按关键字）长度不能超出32位")
    @NotBlank(message = "分析类型 BRAND（按品牌）INDUSTRY（按行业）KEYWORD（按关键字）不能为空")
    private String poiType;

    @ApiModelProperty(value = "省份编号", required = false, example = "省份编号")
    @Size(max = 64, message = "省份编号长度不能超出64位")
    private String provinceCode;

    @ApiModelProperty(value = "省份", required = false, example = "省份")
    @Size(max = 64, message = "省份长度不能超出64位")
    private String provinceName;

    @ApiModelProperty(value = "城市编号", required = false, example = "城市编号")
    @Size(max = 64, message = "城市编号长度不能超出64位")
    private String cityCode;

    @ApiModelProperty(value = "城市", required = false, example = "城市")
    @Size(max = 64, message = "城市长度不能超出64位")
    private String cityName;

    @ApiModelProperty(value = "区域/县级市编号", required = false, example = "区域/县级市编号")
    @Size(max = 64, message = "区域/县级市编号长度不能超出64位")
    private String districtCode;

    @ApiModelProperty(value = "区域/县级市", required = false, example = "区域/县级市")
    @Size(max = 64, message = "区域/县级市长度不能超出64位")
    private String districtName;

    @ApiModelProperty(value = "详情地址", required = false, example = "详情地址")
    @Size(max = 255, message = "详情地址长度不能超出255位")
    private String address;

    @ApiModelProperty(value = "所在经度", required = false, example = "所在经度")
    @Size(max = 64, message = "所在经度长度不能超出64位")
    private String longitude;

    @ApiModelProperty(value = "所在维度", required = false, example = "所在维度")
    @Size(max = 64, message = "所在维度长度不能超出64位")
    private String latitude;

    @ApiModelProperty(value = "目标地址", required = false, example = "目标地址")
    @Size(max = 255, message = "目标地址长度不能超出255位")
    private String targetAddress;

    @ApiModelProperty(value = "地理围栏", required = false, example = "地理围栏")
    @Size(max = 64, message = "地理围栏长度不能超出64位")
    private String geofencing;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TPoiAnalyseDetail() {
    }

    public TPoiAnalyseDetail(PoiAnalyseDetailListData poiAnalyseDetail) {
        this.id = poiAnalyseDetail.getId();
        this.analyseName = poiAnalyseDetail.getAnalyseName();
        this.poiId = poiAnalyseDetail.getPoiId();
        this.poiName = poiAnalyseDetail.getPoiName();
        this.code = poiAnalyseDetail.getCode();
        this.analyseType = poiAnalyseDetail.getAnalyseType();
        this.poiType = poiAnalyseDetail.getPoiType();
        this.provinceCode = poiAnalyseDetail.getProvinceCode();
        this.cityCode = poiAnalyseDetail.getCityCode();
        this.districtCode = poiAnalyseDetail.getDistrictCode();
        this.provinceName = poiAnalyseDetail.getProvinceName();
        this.cityName = poiAnalyseDetail.getCityName();
        this.districtName = poiAnalyseDetail.getDistrictName();
        this.address = poiAnalyseDetail.getAddress();
        this.longitude = poiAnalyseDetail.getLongitude();
        this.latitude = poiAnalyseDetail.getLatitude();
        this.targetAddress = poiAnalyseDetail.getTargetAddress();
        this.geofencing = poiAnalyseDetail.getGeofencing();
        this.remark = poiAnalyseDetail.getRemark();
        this.extraAttrs = poiAnalyseDetail.getExtraAttrs();
        this.createdByUser = poiAnalyseDetail.getCreatedByUser();
        this.updatedByUser = poiAnalyseDetail.getUpdatedByUser();
        this.creator = poiAnalyseDetail.getCreator();
        this.updater = poiAnalyseDetail.getUpdater();
        this.createTime = poiAnalyseDetail.getCreateTime();
        this.updateTime = poiAnalyseDetail.getUpdateTime();

    }


    public TPoiAnalyseDetail(PoiAnalyseDetail poiAnalyseDetail) {
        this.id = poiAnalyseDetail.getId();
        this.analyseName = poiAnalyseDetail.getAnalyseName();
        this.analyseType = poiAnalyseDetail.getAnalyseType();
        this.poiType = poiAnalyseDetail.getPoiType();
        this.provinceCode = poiAnalyseDetail.getProvinceCode();
        this.cityCode = poiAnalyseDetail.getCityCode();
        this.districtCode = poiAnalyseDetail.getDistrictCode();
        this.address = poiAnalyseDetail.getAddress();
        this.longitude = poiAnalyseDetail.getLongitude();
        this.latitude = poiAnalyseDetail.getLatitude();
        this.targetAddress = poiAnalyseDetail.getTargetAddress();
        this.geofencing = poiAnalyseDetail.getGeofencing();
        this.remark = poiAnalyseDetail.getRemark();
        this.extraAttrs = poiAnalyseDetail.getExtraAttrs();
        this.createdByUser = poiAnalyseDetail.getCreatedByUser();
        this.updatedByUser = poiAnalyseDetail.getUpdatedByUser();
        this.creator = poiAnalyseDetail.getCreator();
        this.updater = poiAnalyseDetail.getUpdater();
        this.createTime = poiAnalyseDetail.getCreateTime();
        this.updateTime = poiAnalyseDetail.getUpdateTime();

    }
}
