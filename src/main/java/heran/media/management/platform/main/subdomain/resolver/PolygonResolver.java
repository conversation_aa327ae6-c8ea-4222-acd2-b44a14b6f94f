package heran.media.management.platform.main.subdomain.resolver;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.WktUtils;
import heran.media.sharelib.domain.db.model.main.AoiDraw;
import org.springframework.stereotype.Component;

/**
 * 多边形
 *
 * <AUTHOR>
 */
@Component
public class PolygonResolver implements AoiWktResolver {

    @Override
    public void getAoiWkt(Integer userKey, AoiDraw aoiDraw) {
        String polyline = aoiDraw.getPolyline();
        String wkt = polylineWktString(polyline);
        aoiDraw.setAoiWkt(wkt);
    }

    @Override
    public String type() {
        return "POLYGON";
    }

    protected String polylineWktString(String polyline) {
        if (StringUtils.isNotEmpty(polyline)) {
            return WktUtils.formatCoordinates(polyline);
        }
        return null;
    }
}
