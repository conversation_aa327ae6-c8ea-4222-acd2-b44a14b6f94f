package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.preference.dto.SelectListData;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 */
public class MainCalendarPlacementEntityQuerySQLProvider {
    public String selectSchedulePlacementList(SearchCriteria criteria) {
        return new SQL() {{
            //点位信息
            SELECT("t1.id calendarPlacementId, t1.placement_sn,t1.media_name,t1.media_position,t1.resource_provider,t1.resource_provider_type,t1.province_id,t1.city_id,t1.district_id,t1.medium_lv1_category_id,t1.medium_lv2_category_id,t1.medium_lv3_category_id,t1.pack_id,t1.pack_name,t1.aoi_coordinates,t1.vacancy_position,t1.buffer_area,t1.delivery_start_time,t1.delivery_end_time,t1.placement_cost,t1.ta_hot_value,t1.aoi_area,t1.extend_aoi_coordinates,t1.aoi_caliber,t1.aoi_hot_value");
            //订单指标数据
            SELECT("t6.stat_month orderExposureStatMonth,t6.population,t6.pv,t6.uv,t6.af,t6.tauv,t6.cpm,t6.cpuv,t6.cpta");
            //全域效果评估指标数据
            SELECT("t7.stat_month appraisalStatMonth ,t7.population appraisalPopulation ,t7.pv appraisalPv,t7.uv appraisalUv,t7.af appraisalAf,t7.tauv appraisalTaUv,t7.cpm appraisalCpm,t7.cpuv appraisalCpUv,t7.cpta appraisalCpTa");
            //区域数据
            SELECT("t3.region_name provinceName, t4.region_name cityName, t5.region_name districtName");
            //全域效果评估曝光数据
            SELECT("t8.id baseId,t8.stat_month sampleStatMonth");
            //全域效果评估公交曝光数据
            SELECT("t9.id baseBusId,t9.stat_month busSampleStatMonth");
            //订单信息
            SELECT("sp.order_id orderId,sp.order_sn orderSn");
            //热力数据
            SELECT("t10.stat_month statMonth,t10.statistical_type statisticalType");
            //扩展数据
            SELECT("t11.extra_attrs");
            FROM("media_order_refer_selection_plan sp");
            JOIN("main_calendar_placement t1 ON sp.selection_plan_id = t1.plan_id");
            JOIN("main_aoi_calendar_placement t2 ON t1.plan_id = t2.plan_id AND t1.placement_sn = t2.placement_sn AND t1.resource_provider = t2.resource_provider");
            JOIN("main_dict_region t3 ON t3.id = t1.province_id");
            JOIN("main_dict_region t4 ON t4.id = t1.city_id");
            LEFT_OUTER_JOIN("main_dict_region t5 ON t5.id = t1.district_id");
            LEFT_OUTER_JOIN("main_calendar_order_kpi t6 ON t6.calendar_placement_id = t1.id AND t6.kpi_type = 'ORDER' AND t6.order_id = sp.order_id");
            LEFT_OUTER_JOIN("main_calendar_order_kpi t7 ON t7.calendar_placement_id = t1.id AND t7.kpi_type = 'APPRAISAL' AND t7.order_id = sp.order_id");
            LEFT_OUTER_JOIN("order_effect_estimate_base_sample t8 ON t1.id = t8.placement_id AND t8.sample_type = 'EXPOSURE' AND t8.order_id = sp.order_id");
            LEFT_OUTER_JOIN("order_effect_estimate_base_sample t9 ON t1.id = t9.placement_id AND t9.sample_type = 'BUS_REPORT' AND t9.order_id = sp.order_id ");
            LEFT_OUTER_JOIN("selection_plan_hottype_detail t10 ON t10.plan_id = t1.plan_id");
            LEFT_OUTER_JOIN("main_calendar_placement_ext t11 ON t11.calendar_placement_id = t1.id");
            if (CollectionUtils.isNotEmpty(criteria.getCriterias())) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    switch (cri.getKey()) {
                        case "area":
                            if (cri.getValue() != null) {
                                WHERE("(t3.code IN(" + cri.getValue() + ") OR t4.code IN(" + cri.getValue() + ") OR t5.code IN(" + cri.getValue() + "))");
                            }
                            break;
                        case "categoryId":
                            if (cri.getValue() != null) {
                                WHERE("t1.medium_lv3_category_id = " + cri.getValue());
                            }
                            break;
                        case "mediaName":
                            if (cri.getValue() != null) {
                                WHERE("t1.media_name like '%" + cri.getValue() + "%'");
                            }
                            break;
                        case "hotStatMonth":
                            if (cri.getValue() != null) {
                                WHERE("t1.hot_stat_month = " + cri.getValue());
                            }
                            break;
                        case "exampleStatMonth":
                            if (cri.getValue() != null) {
                                WHERE("t8.stat_month = " + cri.getValue());
                            }
                            break;
                        case "busStatMonth":
                            if (cri.getValue() != null) {
                                WHERE("t9.stat_month = " + cri.getValue());
                            }
                            break;
                        default:
                            throw new RuntimeException("Not supported properties");
                    }
                }
            }
            WHERE("(t6.id IS NOT NULL OR t7.id IS NOT NULL)");
        }}.toString();
    }
}
