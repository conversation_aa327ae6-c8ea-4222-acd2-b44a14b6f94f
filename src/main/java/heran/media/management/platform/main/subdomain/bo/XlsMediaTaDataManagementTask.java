package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.MediaTaDataManagementTask;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsMediaTaDataManagementTask {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "查询月份", columnIndex = 1)
    private String statMonth;

    @XlsField(title = "查询城市", columnIndex = 2)
    private String regionCode;

    @XlsField(title = "任务ID（不知是否有有特殊ID 预留字段）", columnIndex = 3)
    private String taskCode;

    @XlsField(title = "任务名称", columnIndex = 4)
    private String taskName;

    @XlsField(title = "人群包id", columnIndex = 5)
    private Integer crowdPackId;

    @XlsField(title = "任务状态 PENDING_REQUEST（待请求）ON_REQUEST（请求中）FINISHED（已完成）", columnIndex = 6)
    private String taskStatus;

    @XlsField(title = "高德任务id", columnIndex = 7)
    private String mapTaskId;

    @XlsField(title = "TA人口统计", columnIndex = 8)
    private String taPopulation;

    @XlsField(title = "TA洞察维度", columnIndex = 9)
    private String taInsight;

    @XlsField(title = "TA网格热力指数", columnIndex = 10)
    private String taEnergetics;

    @XlsField(title = "说明", columnIndex = 11)
    private String remark;

    @XlsField(title = "扩展字段", columnIndex = 12)
    private String extraAttrs;

    @XlsField(title = "行记录创建用户", columnIndex = 13)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 14)
    private Integer updatedByUser;


    public XlsMediaTaDataManagementTask() {
    }

    public XlsMediaTaDataManagementTask(MediaTaDataManagementTask mediaTaDataManagementTask) {
        this.id = mediaTaDataManagementTask.getId();
        this.statMonth = mediaTaDataManagementTask.getStatMonth();
        this.regionCode = mediaTaDataManagementTask.getRegionCode();
        this.taskCode = mediaTaDataManagementTask.getTaskCode();
        this.taskName = mediaTaDataManagementTask.getTaskName();
        this.crowdPackId = mediaTaDataManagementTask.getCrowdPackId();
        this.taskStatus = mediaTaDataManagementTask.getTaskStatus();
        this.mapTaskId = mediaTaDataManagementTask.getMapTaskId();
        this.taPopulation = mediaTaDataManagementTask.getTaPopulation();
        this.taInsight = mediaTaDataManagementTask.getTaInsight();
        this.taEnergetics = mediaTaDataManagementTask.getTaEnergetics();
        this.remark = mediaTaDataManagementTask.getRemark();
        this.extraAttrs = mediaTaDataManagementTask.getExtraAttrs();
        this.createdByUser = mediaTaDataManagementTask.getCreatedByUser();
        this.updatedByUser = mediaTaDataManagementTask.getUpdatedByUser();

    }

    public MediaTaDataManagementTask adapToPO(Integer userId) {
        MediaTaDataManagementTask mediaTaDataManagementTask = new MediaTaDataManagementTask();
        mediaTaDataManagementTask.setId(id);
        mediaTaDataManagementTask.setStatMonth(statMonth);
        mediaTaDataManagementTask.setRegionCode(regionCode);
        mediaTaDataManagementTask.setTaskCode(taskCode);
        mediaTaDataManagementTask.setTaskName(taskName);
        mediaTaDataManagementTask.setCrowdPackId(crowdPackId);
        mediaTaDataManagementTask.setTaskStatus(taskStatus);
        mediaTaDataManagementTask.setMapTaskId(mapTaskId);
        mediaTaDataManagementTask.setTaPopulation(taPopulation);
        mediaTaDataManagementTask.setTaInsight(taInsight);
        mediaTaDataManagementTask.setTaEnergetics(taEnergetics);
        mediaTaDataManagementTask.setRemark(remark);
        mediaTaDataManagementTask.setExtraAttrs(extraAttrs);

        mediaTaDataManagementTask.setCreatedByUser(userId);
        mediaTaDataManagementTask.setUpdatedByUser(userId);
        return mediaTaDataManagementTask;
    }
}
