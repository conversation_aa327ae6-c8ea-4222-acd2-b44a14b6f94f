package heran.media.management.platform.main.subdomain.response;

import heran.media.management.platform.main.subdomain.dto.TaOfflineSceneDataList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TaOfflineSceneDataListResponse {
    @ApiModelProperty("主键id")
    private Integer id;
    @ApiModelProperty("人群包id")
    private Integer packId;
    @ApiModelProperty("人群包名称")
    private String packName;
    @ApiModelProperty("区域编号")
    private String regionCode;
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("指标名称")
    private String tagName;
    @ApiModelProperty("指标值")
    private String tagValue;
    @ApiModelProperty("是否修改")
    private Boolean isUpdate;
    @ApiModelProperty("人群包名称")
    private Date updateTime;

    public TaOfflineSceneDataListResponse(TaOfflineSceneDataList data,Boolean isBackups) {
        this.id = data.getId();
        this.packId = data.getPackId();
        this.packName = data.getPackName();
        this.regionCode = data.getRegionCode();
        this.statMonth = data.getStatMonth();
        this.tagName = data.getTagName();
        if (isBackups) {
            this.tagValue = data.getTagValue();
        } else {
            this.tagValue = data.getIsUpdate() ? data.getUpdateTagValue() : data.getTagValue();
        }
        this.isUpdate = data.getIsUpdate();
        this.updateTime = data.getUpdateTime();
    }
}
