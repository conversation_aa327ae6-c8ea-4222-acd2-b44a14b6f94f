package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseCustom;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsOrderEffectEstimateBaseCustom {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "订单id", columnIndex = 1)
    private Integer orderId;

    @XlsField(title = "公交车覆盖", columnIndex = 2)
    private String busCover;

    @XlsField(title = "住宅区覆盖", columnIndex = 3)
    private Integer residenceCover;

    @XlsField(title = "写字楼覆盖", columnIndex = 4)
    private String officeBuildingCover;

    @XlsField(title = "商圈覆盖", columnIndex = 5)
    private String cbdCover;

    @XlsField(title = "超市覆盖", columnIndex = 6)
    private String supermarketCover;

    @XlsField(title = "大学覆盖", columnIndex = 7)
    private String universityCover;

    @XlsField(title = "行政区覆盖", columnIndex = 8)
    private String administrativeRegionCover;

    @XlsField(title = "地铁站覆盖", columnIndex = 9)
    private String undergroundCover;

    @XlsField(title = "星级酒店覆盖", columnIndex = 10)
    private String hotelCover;

    @XlsField(title = "品牌零售覆盖", columnIndex = 11)
    private String brandCover;

    @XlsField(title = "是否显示", columnIndex = 12)
    private String isShow;

    @XlsField(title = "说明", columnIndex = 13)
    private String remark;

    @XlsField(title = "扩展字段", columnIndex = 14)
    private String extraAttrs;

    @XlsField(title = "行记录创建用户", columnIndex = 15)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 16)
    private Integer updatedByUser;


    public XlsOrderEffectEstimateBaseCustom() {
    }

    public XlsOrderEffectEstimateBaseCustom(OrderEffectEstimateBaseCustom orderEffectEstimateBaseCustom) {
        this.id = orderEffectEstimateBaseCustom.getId();
        this.orderId = orderEffectEstimateBaseCustom.getOrderId();
        this.busCover = orderEffectEstimateBaseCustom.getBusCover();
        this.residenceCover = orderEffectEstimateBaseCustom.getResidenceCover();
        this.officeBuildingCover = orderEffectEstimateBaseCustom.getOfficeBuildingCover();
        this.cbdCover = orderEffectEstimateBaseCustom.getCbdCover();
        this.supermarketCover = orderEffectEstimateBaseCustom.getSupermarketCover();
        this.universityCover = orderEffectEstimateBaseCustom.getUniversityCover();
        this.administrativeRegionCover = orderEffectEstimateBaseCustom.getAdministrativeRegionCover();
        this.undergroundCover = orderEffectEstimateBaseCustom.getUndergroundCover();
        this.hotelCover = orderEffectEstimateBaseCustom.getHotelCover();
        this.brandCover = orderEffectEstimateBaseCustom.getBrandCover();
        this.isShow = orderEffectEstimateBaseCustom.getIsShow();
        this.remark = orderEffectEstimateBaseCustom.getRemark();
        this.extraAttrs = orderEffectEstimateBaseCustom.getExtraAttrs();
        this.createdByUser = orderEffectEstimateBaseCustom.getCreatedByUser();
        this.updatedByUser = orderEffectEstimateBaseCustom.getUpdatedByUser();

    }

    public OrderEffectEstimateBaseCustom adapToPO(Integer userId) {
        OrderEffectEstimateBaseCustom orderEffectEstimateBaseCustom = new OrderEffectEstimateBaseCustom();
        orderEffectEstimateBaseCustom.setId(id);
        orderEffectEstimateBaseCustom.setOrderId(orderId);
        orderEffectEstimateBaseCustom.setBusCover(busCover);
        orderEffectEstimateBaseCustom.setResidenceCover(residenceCover);
        orderEffectEstimateBaseCustom.setOfficeBuildingCover(officeBuildingCover);
        orderEffectEstimateBaseCustom.setCbdCover(cbdCover);
        orderEffectEstimateBaseCustom.setSupermarketCover(supermarketCover);
        orderEffectEstimateBaseCustom.setUniversityCover(universityCover);
        orderEffectEstimateBaseCustom.setAdministrativeRegionCover(administrativeRegionCover);
        orderEffectEstimateBaseCustom.setUndergroundCover(undergroundCover);
        orderEffectEstimateBaseCustom.setHotelCover(hotelCover);
        orderEffectEstimateBaseCustom.setBrandCover(brandCover);
        orderEffectEstimateBaseCustom.setIsShow(isShow);
        orderEffectEstimateBaseCustom.setRemark(remark);
        orderEffectEstimateBaseCustom.setExtraAttrs(extraAttrs);

        orderEffectEstimateBaseCustom.setCreatedByUser(userId);
        orderEffectEstimateBaseCustom.setUpdatedByUser(userId);
        return orderEffectEstimateBaseCustom;
    }
}
