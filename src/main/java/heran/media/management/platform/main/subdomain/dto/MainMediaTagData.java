package heran.media.management.platform.main.subdomain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MainMediaTagData {
    private Integer id;
    private String lv1Name;
    private String lv2Name;
    private String lv3Name;

    public MainMediaTagData(String lv1Name,String lv2Name,Integer id){
        this.id = id;
        this.lv1Name = lv1Name;
        this.lv2Name = lv2Name;
    }

    public MainMediaTagData(String lv1Name,String lv2Name,String lv3Name,Integer id){
        this.id = id;
        this.lv1Name = lv1Name;
        this.lv2Name = lv2Name;
        this.lv3Name = lv3Name;
    }
}
