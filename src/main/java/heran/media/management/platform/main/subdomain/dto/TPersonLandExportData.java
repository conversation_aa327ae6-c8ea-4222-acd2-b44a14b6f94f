package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.PersonLandData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TPersonLandExportData implements Serializable {

    private Integer id;

    private String statMonth;

    private String statMonthText;

    private String regionCode;

    private String oneName;

    private String twoName;

    private String threeName;

    private String cityName;

    private Integer dwellPopulation;

    private Integer workPopulation;

    private Integer residentPopulation;

    private Integer passengePopulation;

    private Integer passengeHourPopulation;

    private String crowdPack;

    private String taResidentPopulation;

    private String taPassengePopulation;

    private Integer createdByUser;

    private Integer updatedByUser;

    private String creator;

    private String updater;

    private Date createTime;

    private Date updateTime;

    private String updateTimeText;

    private String cityHotText;

    private String urbanPerspective;

    private String types;

    private Integer dimensions;

    private String wktTypes;

}
