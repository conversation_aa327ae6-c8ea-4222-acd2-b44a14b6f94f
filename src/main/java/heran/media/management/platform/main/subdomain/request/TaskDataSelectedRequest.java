package heran.media.management.platform.main.subdomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TaskDataSelectedRequest {
    @ApiModelProperty("城市编号")
    private List<String> regionCodeList;
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("人口口径")
    private List<String> poiPopulationList;
    @ApiModelProperty("事物id")
    private List<Integer> bizId;
    @ApiModelProperty("类型 SPECIFIC_CLEARLY(特定场景洞察),CBD_CLEARLY(商圈洞察洞察),SMART_CITY(数智城市),SMART_CITY_DIMENSION(数智城市透视)")
    private String type;
}
