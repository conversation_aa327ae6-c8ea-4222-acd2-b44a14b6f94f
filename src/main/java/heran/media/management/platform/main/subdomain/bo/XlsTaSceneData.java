package heran.media.management.platform.main.subdomain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.main.subdomain.request.ExportTaOfflineDataRequest;
import heran.media.sharelib.domain.db.model.main.PoiTaskOfflineSceneDetail;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class XlsTaSceneData {
    @XlsField(title = "大类", columnIndex = 0)
    @ExcelProperty(value = "大类", index = 0)
    private String headingName;
    @XlsField(title = "大类占比", columnIndex = 1)
    @ExcelProperty(value = "大类占比", index = 1)
    private String headingRatio;
    @XlsField(title = "中类", columnIndex = 2)
    @ExcelProperty(value = "中类", index = 2)
    private String mediumName;
    @XlsField(title = "中类占比", columnIndex = 3)
    @ExcelProperty(value = "中类占比", index = 3)
    private String mediumRatio;

    public PoiTaskOfflineSceneDetail adPo(Integer taskId, String regionCode, String statMonth, String appName, Integer userKey,Long parentId) {
        PoiTaskOfflineSceneDetail detail = new PoiTaskOfflineSceneDetail();
        detail.setTagName(mediumName);
        detail.setTagValue(mediumRatio);
        detail.setIsDeleted(false);
        detail.setLevel(2);
        detail.setParentId(parentId);
        detail.setIsUpdate(false);
        detail.setStatMonth(statMonth);
        detail.setRegionCode(regionCode);
        detail.setTaskId(taskId);
        detail.setCreatedByUser(userKey);
        detail.setUpdatedByUser(userKey);
        detail.setCreator(appName);
        detail.setUpdater(appName);
        return detail;
    }
}
