package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MediaTaDataManagementTaskDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TMediaTaDataManagementTaskDetail implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "39")
    private Integer id;

    @ApiModelProperty(value = "TA数据管理任务id", required = true, example = "9")
    @NotNull(message = "TA数据管理任务id不能为空")
    private Integer managementTaskId;

    @ApiModelProperty(value = "区域code", required = true, example = "区域code")
    @Size(max = 32, message = "区域code")
    private String regionCode;

    @ApiModelProperty(value = "人口数", required = false, example = "人口数")
    private Integer populaceCount;

    @ApiModelProperty(value = "省份名称", required = false, example = "省份名称")
    @Size(max = 64, message = "省份名称长度不能超出64位")
    private String province;

    @ApiModelProperty(value = "城市编号", required = false, example = "城市编号")
    @Size(max = 64, message = "城市编号长度不能超出64位")
    private String cityCode;

    @ApiModelProperty(value = "城市名称", required = false, example = "城市名称")
    @Size(max = 64, message = "城市名称长度不能超出64位")
    private String city;

    @ApiModelProperty(value = "区域/县级市编号", required = false, example = "区域/县级市编号")
    @Size(max = 64, message = "区域/县级市编号长度不能超出64位")
    private String districtCode;

    @ApiModelProperty(value = "区域/县级市名称", required = false, example = "区域/县级市名称")
    @Size(max = 64, message = "区域/县级市名称长度不能超出64位")
    private String district;

    @ApiModelProperty(value = "媒体形式", required = true, example = "媒体形式")
    @Size(max = 64, message = "媒体形式长度不能超出64位")
    @NotBlank(message = "媒体形式不能为空")
    private String mediumFormat;

    @ApiModelProperty(value = "媒体筛选条件", required = true, example = "媒体筛选条件")
    @Size(max = 64, message = "媒体筛选条件长度不能超出64位")
    @NotBlank(message = "媒体筛选条件不能为空")
    private String mediumFiltrate;

    @ApiModelProperty(value = "媒体名称", required = true, example = "媒体名称")
    @Size(max = 64, message = "媒体名称长度不能超出64位")
    @NotBlank(message = "媒体名称不能为空")
    private String mediumName;

    @ApiModelProperty(value = "媒体位置", required = false, example = "媒体位置")
    @Size(max = 64, message = "媒体位置长度不能超出64位")
    private String mediumAddress;

    @ApiModelProperty(value = "点位数量", required = false, example = "78")
    private Integer placementCount;

    @ApiModelProperty(value = "计划投放开始时间", required = false, example = "1741321657676")
    private Date planLaunchStartTime;

    @ApiModelProperty(value = "计划投放结束时间", required = false, example = "1741321657676")
    private Date planLaunchEndTime;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TMediaTaDataManagementTaskDetail() {
    }

    public TMediaTaDataManagementTaskDetail(MediaTaDataManagementTaskDetail mediaTaDataManagementTaskDetail) {
        this.id = mediaTaDataManagementTaskDetail.getId();
        this.managementTaskId = mediaTaDataManagementTaskDetail.getManagementTaskId();
        this.regionCode = mediaTaDataManagementTaskDetail.getRegionCode();
        this.populaceCount = mediaTaDataManagementTaskDetail.getPopulaceCount();
        this.remark = mediaTaDataManagementTaskDetail.getRemark();
        this.extraAttrs = mediaTaDataManagementTaskDetail.getExtraAttrs();
        this.createdByUser = mediaTaDataManagementTaskDetail.getCreatedByUser();
        this.updatedByUser = mediaTaDataManagementTaskDetail.getUpdatedByUser();
        this.creator = mediaTaDataManagementTaskDetail.getCreator();
        this.updater = mediaTaDataManagementTaskDetail.getUpdater();
        this.createTime = mediaTaDataManagementTaskDetail.getCreateTime();
        this.updateTime = mediaTaDataManagementTaskDetail.getUpdateTime();

    }
}
