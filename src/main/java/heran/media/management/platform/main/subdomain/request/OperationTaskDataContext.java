package heran.media.management.platform.main.subdomain.request;

import com.google.gson.Gson;
import heran.media.management.platform.main.subdomain.dto.ClearlyTaskData;
import heran.media.management.platform.main.subdomain.dto.SmartCityDimensionTaskData;
import heran.media.management.platform.main.subdomain.dto.SmartCityTaskData;
import heran.media.sharelib.domain.db.model.main.DataTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */
@Data
public class OperationTaskDataContext {

    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 商圈洞察
     */
    private ClearlyTaskData cbdClearlyTaskData;
    /**
     * 特定场景洞察
     */
    private ClearlyTaskData specificClearlyTaskData;
    /**
     * 数智城市
     */
    private SmartCityTaskData smartCityTaskData;
    /**
     * 数智城市透视
     */
    private SmartCityDimensionTaskData smartCityDimensionTaskData;


    public OperationTaskDataContext(DataTask dataTask) {
        Gson gson = new Gson();
        this.taskId = dataTask.getId();
        this.cbdClearlyTaskData = StringUtils.isEmpty(dataTask.getCbdInsightRequest()) ? null :
                gson.fromJson(dataTask.getCbdInsightRequest(), ClearlyTaskData.class);
        this.specificClearlyTaskData = StringUtils.isEmpty(dataTask.getSpecificSceneInsightRequest()) ? null :
                gson.fromJson(dataTask.getSpecificSceneInsightRequest(), ClearlyTaskData.class);
        this.smartCityTaskData = StringUtils.isEmpty(dataTask.getNumberSmartCityRequest()) ? null :
                gson.fromJson(dataTask.getNumberSmartCityRequest(), SmartCityTaskData.class);
        this.smartCityDimensionTaskData = StringUtils.isEmpty(dataTask.getNumberSmartCityPerspectiveRequest()) ? null :
                gson.fromJson(dataTask.getNumberSmartCityPerspectiveRequest(), SmartCityDimensionTaskData.class);

    }

}
