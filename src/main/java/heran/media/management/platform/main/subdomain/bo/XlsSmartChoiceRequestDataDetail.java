package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.SmartChoiceRequestDataDetail;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@ToString
@Data
public class XlsSmartChoiceRequestDataDetail {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "智选方案id", columnIndex = 1)
    private Integer selectionPlanId;

    @XlsField(title = "点位编号", columnIndex = 2)
    private String placementCode;

    @XlsField(title = "省份编号", columnIndex = 3)
    private String provinceCode;

    @XlsField(title = "省份名称", columnIndex = 4)
    private String province;

    @XlsField(title = "城市编号", columnIndex = 5)
    private String cityCode;

    @XlsField(title = "城市名称", columnIndex = 6)
    private String city;

    @XlsField(title = "区域/县级市编号", columnIndex = 7)
    private String districtCode;

    @XlsField(title = "区域/县级市名称", columnIndex = 8)
    private String district;

    @XlsField(title = "媒体形式", columnIndex = 9)
    private String mediumFormat;

    @XlsField(title = "媒体筛选条件", columnIndex = 10)
    private String mediumFiltra;

    @XlsField(title = "媒体名称", columnIndex = 11)
    private String mediumName;

    @XlsField(title = "媒体位置", columnIndex = 12)
    private String mediumAddress;

    @XlsField(title = "点位数据 ？？？？？这都具体到点位了为啥还有点位数量", columnIndex = 13)
    private Integer placementCount;

    @XlsField(title = "缓冲区距离", columnIndex = 14)
    private Integer bufferDistance;

    @XlsField(title = "TA浓度", columnIndex = 15)
    private Integer taConcentration;

    @XlsField(title = "浓度周期", columnIndex = 16)
    private String concentrationPeriod;

    @XlsField(title = "计划投放开始时间", columnIndex = 17)
    private Date planLaunchStartTime;

    @XlsField(title = "计划投放结束时间", columnIndex = 18)
    private Date planLaunchEndTime;

    @XlsField(title = "行记录创建用户", columnIndex = 19)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 20)
    private Integer updatedByUser;


    public XlsSmartChoiceRequestDataDetail() {
    }

    public XlsSmartChoiceRequestDataDetail(SmartChoiceRequestDataDetail smartChoiceRequestDataDetail) {
        this.id = smartChoiceRequestDataDetail.getId();
        this.selectionPlanId = smartChoiceRequestDataDetail.getSelectionPlanId();
        this.placementCode = smartChoiceRequestDataDetail.getPlacementCode();
        this.provinceCode = smartChoiceRequestDataDetail.getProvinceCode();
        this.province = smartChoiceRequestDataDetail.getProvince();
        this.cityCode = smartChoiceRequestDataDetail.getCityCode();
        this.city = smartChoiceRequestDataDetail.getCity();
        this.districtCode = smartChoiceRequestDataDetail.getDistrictCode();
        this.district = smartChoiceRequestDataDetail.getDistrict();
        this.mediumFormat = smartChoiceRequestDataDetail.getMediumFormat();
        this.mediumFiltra = smartChoiceRequestDataDetail.getMediumFiltra();
        this.mediumName = smartChoiceRequestDataDetail.getMediumName();
        this.mediumAddress = smartChoiceRequestDataDetail.getMediumAddress();
        this.placementCount = smartChoiceRequestDataDetail.getPlacementCount();
        this.bufferDistance = smartChoiceRequestDataDetail.getBufferDistance();
        this.taConcentration = smartChoiceRequestDataDetail.getTaConcentration();
        this.concentrationPeriod = smartChoiceRequestDataDetail.getConcentrationPeriod();
        this.planLaunchStartTime = smartChoiceRequestDataDetail.getPlanLaunchStartTime();
        this.planLaunchEndTime = smartChoiceRequestDataDetail.getPlanLaunchEndTime();
        this.createdByUser = smartChoiceRequestDataDetail.getCreatedByUser();
        this.updatedByUser = smartChoiceRequestDataDetail.getUpdatedByUser();

    }

    public SmartChoiceRequestDataDetail adapToPO(Integer userId) {
        SmartChoiceRequestDataDetail smartChoiceRequestDataDetail = new SmartChoiceRequestDataDetail();
        smartChoiceRequestDataDetail.setId(id);
        smartChoiceRequestDataDetail.setSelectionPlanId(selectionPlanId);
        smartChoiceRequestDataDetail.setPlacementCode(placementCode);
        smartChoiceRequestDataDetail.setProvinceCode(provinceCode);
        smartChoiceRequestDataDetail.setProvince(province);
        smartChoiceRequestDataDetail.setCityCode(cityCode);
        smartChoiceRequestDataDetail.setCity(city);
        smartChoiceRequestDataDetail.setDistrictCode(districtCode);
        smartChoiceRequestDataDetail.setDistrict(district);
        smartChoiceRequestDataDetail.setMediumFormat(mediumFormat);
        smartChoiceRequestDataDetail.setMediumFiltra(mediumFiltra);
        smartChoiceRequestDataDetail.setMediumName(mediumName);
        smartChoiceRequestDataDetail.setMediumAddress(mediumAddress);
        smartChoiceRequestDataDetail.setPlacementCount(placementCount);
        smartChoiceRequestDataDetail.setBufferDistance(bufferDistance);
        smartChoiceRequestDataDetail.setTaConcentration(taConcentration);
        smartChoiceRequestDataDetail.setConcentrationPeriod(concentrationPeriod);
        smartChoiceRequestDataDetail.setPlanLaunchStartTime(planLaunchStartTime);
        smartChoiceRequestDataDetail.setPlanLaunchEndTime(planLaunchEndTime);

        smartChoiceRequestDataDetail.setCreatedByUser(userId);
        smartChoiceRequestDataDetail.setUpdatedByUser(userId);
        return smartChoiceRequestDataDetail;
    }
}
