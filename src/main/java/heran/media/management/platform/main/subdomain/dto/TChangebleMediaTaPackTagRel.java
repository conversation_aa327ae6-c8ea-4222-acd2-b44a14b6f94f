package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MediaTaPackTagRel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleMediaTaPackTagRel implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "107")
    private Integer id;

    @ApiModelProperty(value = "标签id", required = true, example = "20")
    @NotNull(message = "标签id不能为空")
    private Integer tagId;

    @ApiModelProperty(value = "人群包id", required = true, example = "76")
    @NotNull(message = "人群包id不能为空")
    private Integer crowdPackId;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangebleMediaTaPackTagRel() {
    }

    public MediaTaPackTagRel adapToPO() {
        MediaTaPackTagRel mediaTaPackTagRel = new MediaTaPackTagRel();
        mediaTaPackTagRel.setId(id);
        mediaTaPackTagRel.setTagId(tagId);
        mediaTaPackTagRel.setCrowdPackId(crowdPackId);
        mediaTaPackTagRel.setRemark(remark);
        mediaTaPackTagRel.setExtraAttrs(extraAttrs);

        return mediaTaPackTagRel;
    }
}
