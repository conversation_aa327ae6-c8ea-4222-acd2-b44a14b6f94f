package heran.media.management.platform.main.subdomain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DataTaskListData {
    @ApiModelProperty(value = "主键", required = false, example = "134")
    private Integer id;
    @ApiModelProperty(value = "任务名称", required = true, example = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "任务状态 PENDING_REQUEST（待处理）ON_REQUEST（处理中）FINISHED（已完成）", required = true, example = "任务状态 PENDING_REQUEST（待处理）ON_REQUEST（处理中）FINISHED（已完成）")
    private String taskStatus;
    @ApiModelProperty("城市code")
    private String regionCodes;
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
