package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MarketingSceneAoiDataDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TMarketingSceneAoiDataDetail implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "55")
    private Integer id;

    @ApiModelProperty(value = "营销场景AOI数据id", required = true, example = "170")
    @NotNull(message = "营销场景AOI数据id不能为空")
    private Integer marketingSceneAoiDataId;

    @ApiModelProperty(value = "流量统计", required = false, example = "流量统计")
    private String trafficStatistics;

    @ApiModelProperty(value = "客流画像", required = false, example = "客流画像")
    private String passengerPortrayal;

    @ApiModelProperty(value = "出行方式", required = false, example = "出行方式")
    private String tripWay;

    @ApiModelProperty(value = "客流来源", required = false, example = "客流来源")
    private String passengerSource;

    @ApiModelProperty(value = "人口统计", required = false, example = "人口统计")
    private String demographicStatistics;

    @ApiModelProperty(value = "人群画像", required = false, example = "人群画像")
    private String crowdPortrayal;

    @ApiModelProperty(value = "AOI洞察出行方式", required = false, example = "AOI洞察出行方式")
    private String apiTripWay;

    @ApiModelProperty(value = "人群来源", required = false, example = "人群来源")
    private String crowdSource;

    @ApiModelProperty(value = "线下场景偏好", required = false, example = "线下场景偏好")
    private String offlineScene;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TMarketingSceneAoiDataDetail() {
    }

    public TMarketingSceneAoiDataDetail(MarketingSceneAoiDataDetail marketingSceneAoiDataDetail) {
        this.id = marketingSceneAoiDataDetail.getId();
        this.marketingSceneAoiDataId = marketingSceneAoiDataDetail.getMarketingSceneAoiDataId();
        this.trafficStatistics = marketingSceneAoiDataDetail.getTrafficStatistics();
        this.passengerPortrayal = marketingSceneAoiDataDetail.getPassengerPortrayal();
        this.tripWay = marketingSceneAoiDataDetail.getTripWay();
        this.passengerSource = marketingSceneAoiDataDetail.getPassengerSource();
        this.demographicStatistics = marketingSceneAoiDataDetail.getDemographicStatistics();
        this.crowdPortrayal = marketingSceneAoiDataDetail.getCrowdPortrayal();
        this.apiTripWay = marketingSceneAoiDataDetail.getApiTripWay();
        this.crowdSource = marketingSceneAoiDataDetail.getCrowdSource();
        this.offlineScene = marketingSceneAoiDataDetail.getOfflineScene();
        this.remark = marketingSceneAoiDataDetail.getRemark();
        this.extraAttrs = marketingSceneAoiDataDetail.getExtraAttrs();
        this.createdByUser = marketingSceneAoiDataDetail.getCreatedByUser();
        this.updatedByUser = marketingSceneAoiDataDetail.getUpdatedByUser();
        this.creator = marketingSceneAoiDataDetail.getCreator();
        this.updater = marketingSceneAoiDataDetail.getUpdater();
        this.createTime = marketingSceneAoiDataDetail.getCreateTime();
        this.updateTime = marketingSceneAoiDataDetail.getUpdateTime();

    }
}
