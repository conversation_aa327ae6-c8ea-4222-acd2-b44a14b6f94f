package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MediaTaDataManagementTaskDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleMediaTaDataManagementTaskDetail implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "55")
    private Integer id;

    @ApiModelProperty(value = "TA数据管理任务id", required = true, example = "225")
    @NotNull(message = "TA数据管理任务id不能为空")
    private Integer managementTaskId;

    @ApiModelProperty(value = "区域编码", required = true, example = "区域编码")
    private String regionCode;

    @ApiModelProperty(value = "人口数", required = false, example = "人口数")
    private Integer populaceCount;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangebleMediaTaDataManagementTaskDetail() {
    }

    public MediaTaDataManagementTaskDetail adapToPO() {
        MediaTaDataManagementTaskDetail mediaTaDataManagementTaskDetail = new MediaTaDataManagementTaskDetail();
        mediaTaDataManagementTaskDetail.setId(id);
        mediaTaDataManagementTaskDetail.setManagementTaskId(managementTaskId);
        mediaTaDataManagementTaskDetail.setRegionCode(regionCode);
        mediaTaDataManagementTaskDetail.setPopulaceCount(populaceCount);
        mediaTaDataManagementTaskDetail.setRemark(remark);
        mediaTaDataManagementTaskDetail.setExtraAttrs(extraAttrs);

        return mediaTaDataManagementTaskDetail;
    }
}
