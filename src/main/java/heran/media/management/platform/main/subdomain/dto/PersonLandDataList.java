package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class PersonLandDataList implements Serializable {

    private Integer id;

    private String statMonth;

    private String regionCode;

    private Integer dwellPopulation;

    private Integer workPopulation;

    private Integer residentPopulation;

    private Integer passengePopulation;

    private Integer passengeHourPopulation;

    private String cityHotText;

    private String urbanPerspective;

    private String types;

    private Integer dimensions;

    private Date updateTime;

    private String taResidentData;

    private String wktTypes;


}
