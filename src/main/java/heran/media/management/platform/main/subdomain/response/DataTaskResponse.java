package heran.media.management.platform.main.subdomain.response;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import heran.media.management.platform.main.subdomain.dto.DataTaskListData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DataTaskResponse {
    @ApiModelProperty(value = "主键", required = false, example = "134")
    private Integer id;
    @ApiModelProperty(value = "任务名称", required = true, example = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "任务状态 PENDING_REQUEST（待处理）ON_REQUEST（处理中）FINISHED（已完成）")
    private String taskStatus;
    @ApiModelProperty("城市code")
    private List<String> regionCodes;
    @ApiModelProperty("查询时间")
    private List<String> statMonth;
    @ApiModelProperty("更新时间")
    private Date updateTime;

    public DataTaskResponse(DataTaskListData data) {
        Gson gson = new Gson();
        this.id = data.getId();
        this.taskName = data.getTaskName();
        this.taskStatus = data.getTaskStatus();
        this.regionCodes = StringUtils.isEmpty(data.getRegionCodes()) ? null : gson.fromJson(data.getRegionCodes(), new TypeToken<List<String>>() {
        }.getType());
        this.statMonth = StringUtils.isEmpty(data.getStatMonth()) ? null : gson.fromJson(data.getStatMonth(), new TypeToken<List<String>>() {
        }.getType());
        this.updateTime = data.getUpdateTime();
    }
}
