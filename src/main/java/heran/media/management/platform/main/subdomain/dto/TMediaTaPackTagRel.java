package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MediaTaPackTagRel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TMediaTaPackTagRel implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "103")
    private Integer id;

    @ApiModelProperty(value = "标签id", required = true, example = "248")
    @NotNull(message = "标签id不能为空")
    private Integer tagId;

    @ApiModelProperty(value = "人群包id", required = true, example = "62")
    @NotNull(message = "人群包id不能为空")
    private Integer crowdPackId;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TMediaTaPackTagRel() {
    }

    public TMediaTaPackTagRel(MediaTaPackTagRel mediaTaPackTagRel) {
        this.id = mediaTaPackTagRel.getId();
        this.tagId = mediaTaPackTagRel.getTagId();
        this.crowdPackId = mediaTaPackTagRel.getCrowdPackId();
        this.remark = mediaTaPackTagRel.getRemark();
        this.extraAttrs = mediaTaPackTagRel.getExtraAttrs();
        this.createdByUser = mediaTaPackTagRel.getCreatedByUser();
        this.updatedByUser = mediaTaPackTagRel.getUpdatedByUser();
        this.creator = mediaTaPackTagRel.getCreator();
        this.updater = mediaTaPackTagRel.getUpdater();
        this.createTime = mediaTaPackTagRel.getCreateTime();
        this.updateTime = mediaTaPackTagRel.getUpdateTime();

    }
}
