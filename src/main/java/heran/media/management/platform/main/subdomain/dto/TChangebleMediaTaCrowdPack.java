package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MediaTaCrowdPack;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleMediaTaCrowdPack implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "26")
    private Integer id;

    @ApiModelProperty(value = "人群包名称", required = true, example = "人群包名称")
    @Size(max = 32, message = "人群包名称长度不能超出32位")
    @NotBlank(message = "人群包名称不能为空")
    private String packName;

//    @ApiModelProperty(value = "人群包ID（不知是否有有特殊ID 有可能是对应高德或者自定义）", required = false, example = "人群包ID（不知是否有有特殊ID 有可能是对应高德或者自定义）")
//    @Size(max = 32, message = "人群包ID（不知是否有有特殊ID 有可能是对应高德或者自定义）长度不能超出32位")
//    @NotBlank(message = "人群包ID（不知是否有有特殊ID 有可能是对应高德或者自定义）不能为空")
//    private String packCode;

    @ApiModelProperty(value = "一级分类", required = true, example = "65")
    @NotNull(message = "一级分类不能为空")
    private Integer categoryIdLv1;

    @ApiModelProperty(value = "二级分类", required = true, example = "2")
    @NotNull(message = "二级分类不能为空")
    private Integer categoryIdLv2;

    @ApiModelProperty(value = "是否启动", required = true, example = "52")
    @NotNull(message = "是否启动不能为空")
    private Integer isEnabled;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 500, message = "说明长度不能超出500位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "标签", required = true, example = "[1,2,3]")
    @NotNull(message = "标签不能为空")
    private List<Integer> tagIds;

    public TChangebleMediaTaCrowdPack() {
    }

    public MediaTaCrowdPack adapToPO() {
        MediaTaCrowdPack mediaTaCrowdPack = new MediaTaCrowdPack();
        mediaTaCrowdPack.setId(id);
        mediaTaCrowdPack.setPackName(packName);
//        mediaTaCrowdPack.setPackCode(packCode);
        mediaTaCrowdPack.setCategoryIdLv1(categoryIdLv1);
        mediaTaCrowdPack.setCategoryIdLv2(categoryIdLv2);
        mediaTaCrowdPack.setIsEnabled(isEnabled);
        mediaTaCrowdPack.setRemark(remark);
        mediaTaCrowdPack.setExtraAttrs(extraAttrs);

        return mediaTaCrowdPack;
    }
}
