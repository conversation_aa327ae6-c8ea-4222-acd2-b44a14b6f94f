package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.AoiDrawTagRel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleAoiDrawTagRel implements Serializable {

    @ApiModelProperty(value = "主键", required = true, example = "238")
    @NotNull(message = "主键不能为空")
    private Integer id;

    @ApiModelProperty(value = "AOI绘画id", required = true, example = "142")
    @NotNull(message = "AOI绘画id不能为空")
    private Integer aoiDrawId;

    @ApiModelProperty(value = "标签id", required = true, example = "19")
    @NotNull(message = "标签id不能为空")
    private Integer tagId;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangebleAoiDrawTagRel() {
    }

    public AoiDrawTagRel adapToPO() {
        AoiDrawTagRel aoiDrawTagRel = new AoiDrawTagRel();
        aoiDrawTagRel.setId(id);
        aoiDrawTagRel.setAoiDrawId(aoiDrawId);
        aoiDrawTagRel.setTagId(tagId);
        aoiDrawTagRel.setRemark(remark);
        aoiDrawTagRel.setExtraAttrs(extraAttrs);

        return aoiDrawTagRel;
    }
}
