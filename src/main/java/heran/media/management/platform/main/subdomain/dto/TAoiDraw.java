package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.management.platform.main.subdomain.response.AoiDrawResponse;
import heran.media.sharelib.domain.db.model.main.AoiDraw;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TAoiDraw implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "15")
    private Integer id;

    @ApiModelProperty(value = "城市id(如果能划分三级的话则增加省市区的id字段便于搜索)", required = true, example = "200")
    @NotNull(message = "城市id(如果能划分三级的话则增加省市区的id字段便于搜索)不能为空")
    private Integer regionId;

    @ApiModelProperty("区域code")
    private String regionCode;

    @ApiModelProperty(value = "AOI名称", required = true, example = "AOI名称")
    @Size(max = 64, message = "AOI名称长度不能超出64位")
    @NotBlank(message = "AOI名称不能为空")
    private String aoiName;

    @ApiModelProperty(value = "引用媒介商圈概括", required = true, example = "154")
    @NotNull(message = "引用媒介商圈概括不能为空")
    private Integer quoteCbd;

    @ApiModelProperty(value = "商圈id", required = false, example = "234")
    private Integer cbdId;

    @ApiModelProperty(value = "面积", required = false, example = "面积")
    @Size(max = 64, message = "面积长度不能超出64位")
    private String aoiArea;

    @ApiModelProperty(value = "aoi类型", required = false, example = "")
    @Size(max = 64, message = "aoi类型长度不能超出64位")
    private String aoiType;

    @ApiModelProperty(value = "坐标串", required = true, example = "坐标串")
    @NotBlank(message = "坐标串不能为空")
    private String polyline;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "详细地址", required = false, example = "详细地址")
    private String address;

    @ApiModelProperty(value = "图形类型", required = false, example = "图形类型")
    private String graphType;

    @ApiModelProperty(value = "半径", required = false, example = "半径")
    private Double radius;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;

    @ApiModelProperty(value = "标签id的信息", required = false, example = "")
    List<TagIdInfoData>  tagIdInfo;


    public TAoiDraw() {
    }

    public TAoiDraw(AoiDraw aoiDraw) {
        this.id = aoiDraw.getId();
        this.regionId = aoiDraw.getRegionId();
        this.aoiName = aoiDraw.getAoiName();
        this.quoteCbd = aoiDraw.getQuoteCbd();
        this.cbdId = aoiDraw.getCbdId();
        this.aoiArea = aoiDraw.getAoiArea();
        this.aoiType = aoiDraw.getAoiType();
        this.polyline = aoiDraw.getPolyline();
        this.address = aoiDraw.getAddress();
        this.graphType = aoiDraw.getGraphType();
        this.radius = aoiDraw.getRadius();
        this.remark = aoiDraw.getRemark();
        this.extraAttrs = aoiDraw.getExtraAttrs();
        this.createdByUser = aoiDraw.getCreatedByUser();
        this.updatedByUser = aoiDraw.getUpdatedByUser();
        this.creator = aoiDraw.getCreator();
        this.updater = aoiDraw.getUpdater();
        this.createTime = aoiDraw.getCreateTime();
        this.updateTime = aoiDraw.getUpdateTime();

    }

    public TAoiDraw(AoiDrawResponse aoiDraw) {
        this.id = aoiDraw.getId();
        this.regionId = aoiDraw.getRegionId();
        this.aoiName = aoiDraw.getAoiName();
        this.quoteCbd = aoiDraw.getQuoteCbd();
        this.cbdId = aoiDraw.getCbdId();
        this.aoiArea = aoiDraw.getAoiArea();
        this.aoiType = aoiDraw.getAoiType();
        this.polyline = aoiDraw.getPolyline();
        this.address = aoiDraw.getAddress();
        this.graphType = aoiDraw.getGraphType();
        this.radius = aoiDraw.getRadius();
        this.remark = aoiDraw.getRemark();
        this.extraAttrs = aoiDraw.getExtraAttrs();
        this.createdByUser = aoiDraw.getCreatedByUser();
        this.updatedByUser = aoiDraw.getUpdatedByUser();
        this.creator = aoiDraw.getCreator();
        this.updater = aoiDraw.getUpdater();
        this.createTime = aoiDraw.getCreateTime();
        this.updateTime = aoiDraw.getUpdateTime();
        this.regionCode = aoiDraw.getRegionCode();
    }
}
