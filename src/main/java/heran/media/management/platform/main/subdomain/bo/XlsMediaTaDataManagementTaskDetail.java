package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.MediaTaDataManagementTaskDetail;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsMediaTaDataManagementTaskDetail {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "TA数据管理任务id", columnIndex = 1)
    private Integer managementTaskId;

    @XlsField(title = "区域编号", columnIndex = 2)
    private String regionCode;

    @XlsField(title = "人口数", columnIndex = 3)
    private Integer populaceCount;

    @XlsField(title = "说明", columnIndex = 16)
    private String remark;

    @XlsField(title = "扩展字段", columnIndex = 17)
    private String extraAttrs;

    @XlsField(title = "行记录创建用户", columnIndex = 18)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 19)
    private Integer updatedByUser;


    public XlsMediaTaDataManagementTaskDetail() {
    }

    public XlsMediaTaDataManagementTaskDetail(MediaTaDataManagementTaskDetail mediaTaDataManagementTaskDetail) {
        this.id = mediaTaDataManagementTaskDetail.getId();
        this.managementTaskId = mediaTaDataManagementTaskDetail.getManagementTaskId();
        this.regionCode = mediaTaDataManagementTaskDetail.getRegionCode();
        this.populaceCount = mediaTaDataManagementTaskDetail.getPopulaceCount();
        this.remark = mediaTaDataManagementTaskDetail.getRemark();
        this.extraAttrs = mediaTaDataManagementTaskDetail.getExtraAttrs();
        this.createdByUser = mediaTaDataManagementTaskDetail.getCreatedByUser();
        this.updatedByUser = mediaTaDataManagementTaskDetail.getUpdatedByUser();

    }

    public MediaTaDataManagementTaskDetail adapToPO(Integer userId) {
        MediaTaDataManagementTaskDetail mediaTaDataManagementTaskDetail = new MediaTaDataManagementTaskDetail();
        mediaTaDataManagementTaskDetail.setId(id);
        mediaTaDataManagementTaskDetail.setManagementTaskId(managementTaskId);
        mediaTaDataManagementTaskDetail.setRegionCode(regionCode);
        mediaTaDataManagementTaskDetail.setPopulaceCount(populaceCount);
        mediaTaDataManagementTaskDetail.setRemark(remark);
        mediaTaDataManagementTaskDetail.setExtraAttrs(extraAttrs);

        mediaTaDataManagementTaskDetail.setCreatedByUser(userId);
        mediaTaDataManagementTaskDetail.setUpdatedByUser(userId);
        return mediaTaDataManagementTaskDetail;
    }
}
