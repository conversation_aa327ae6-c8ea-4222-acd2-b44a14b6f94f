package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.ApiMapLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleApiMapLog implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "74")
    private Long id;

    @ApiModelProperty(value = "请求参数", required = false, example = "请求参数")
    private String reqParam;

    @ApiModelProperty(value = "请求对象体", required = false, example = "请求对象体")
    private String reqBody;

    @ApiModelProperty(value = "是否成功", required = false, example = "83")
    private Integer isSuc;

    @ApiModelProperty(value = "错误码", required = false, example = "错误码")
    @Size(max = 64, message = "错误码长度不能超出64位")
    private String errCode;

    @ApiModelProperty(value = "错误信息", required = false, example = "错误信息")
    @Size(max = 512, message = "错误信息长度不能超出512位")
    private String errMessage;

    @ApiModelProperty(value = "响应体", required = false, example = "响应体")
    private String respData;


    public TChangebleApiMapLog() {
    }

    public ApiMapLog adapToPO() {
        ApiMapLog apiMapLog = new ApiMapLog();
        apiMapLog.setId(id);
        apiMapLog.setReqParam(reqParam);
        apiMapLog.setReqBody(reqBody);
        apiMapLog.setIsSuc(isSuc);
        apiMapLog.setErrCode(errCode);
        apiMapLog.setErrMessage(errMessage);
        apiMapLog.setRespData(respData);

        return apiMapLog;
    }
}
