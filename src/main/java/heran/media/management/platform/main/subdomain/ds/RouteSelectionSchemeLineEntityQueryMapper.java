package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.main.RouteSelectionSchemeLine;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface RouteSelectionSchemeLineEntityQueryMapper {
    @SelectProvider(type = RouteSelectionSchemeLineEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "schemeId", column = "scheme_id"), @Result(property = "regionCode", column = "region_code"), @Result(property = "lineName", column = "line_name"), @Result(property = "level", column = "level"), @Result(property = "launchCount", column = "launch_count"), @Result(property = "launchPeriod", column = "launch_period"), @Result(property = "productionCost", column = "production_cost"), @Result(property = "issueCost", column = "issue_cost"), @Result(property = "costAggregate", column = "cost_aggregate"), @Result(property = "poi", column = "poi"), @Result(property = "aoi", column = "aoi"), @Result(property = "cbd", column = "cbd"), @Result(property = "mainLine", column = "main_line"), @Result(property = "heatingPower", column = "heating_power"), @Result(property = "taHeatingPower", column = "ta_heating_power"), @Result(property = "pitchOn", column = "pitch_on"), @Result(property = "remark", column = "remark"), @Result(property = "extraAttrs", column = "extra_attrs"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<RouteSelectionSchemeLine> list(SearchCriteria criteria);
}
