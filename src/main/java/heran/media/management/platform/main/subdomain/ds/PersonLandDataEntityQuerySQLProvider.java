package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.MainDictRegionData;
import org.apache.ibatis.jdbc.SQL;

public class PersonLandDataEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        String withPart =
                "WITH ta_task_pack AS ( \n" +
                        "    SELECT \n" +
                        "        t2.pack_id , \n" +
                        "        t2.stat_month, \n" +
                        "        t2.region_code, \n" +
                        "        t3.pack_name \n" +
                        "    FROM media_ta_data_management_task t1 \n" +
                        "    JOIN media_ta_data_management_task_detail t2 ON t1.id = t2.management_task_id \n" +
                        "    JOIN media_ta_crowd_pack t3 ON t3.id = t2.pack_id " +
                        "    WHERE t1.ta_population = 'TA_RESIDENT_POPULATION'\n" +
                        "    GROUP BY t2.stat_month, t2.region_code,t2.id" +
                        "), \n" +
                        "grid_types AS ( \n" +
                        "    SELECT \n" +
                        "        stat_month, \n" +
                        "        type_info, \n" +
                        "        GROUP_CONCAT(DISTINCT statistical_type SEPARATOR ',') AS types \n" +
                        "    FROM grid_hot_data \n" +
                        "    WHERE data_type = 'person_land_data' AND hot_value = '100.0' \n" +
                        "    GROUP BY stat_month, type_info \n" +
                        "), \n" +

                        "cell_hot_value_wkt AS ( \n" +
                        "    SELECT \n" +
                        "        stat_month, \n" +
                        "        region_code, \n" +
                        "        GROUP_CONCAT(DISTINCT statistical_type SEPARATOR ',') AS wktTypes \n" +
                        "    FROM cell_hot_value_wkt_config WHERE statistical_type IN('RESIDENT_POPULATION','DWELL_POPULATION','WORK_POPULATION','PASSENGER_POPULATION','PASSENGER_HOUR_POPULATION') \n" +
                        "    GROUP BY stat_month, region_code \n" +
                        "), \n" +

                        "dimension_keys AS ( \n" +
                        "    SELECT DISTINCT \n" +
                        "        stat_month, \n" +
                        "        region_code \n" +
                        "    FROM statistics_dimension \n" +
                        "    WHERE data_type = 'person_land_data' \n" +
                        ")";
        return withPart + new SQL() {{
            SELECT("pl.id,pl.stat_month,pl.dwell_population,pl.work_population,pl.region_code,pl.passenge_population,pl.resident_population,pl.passenge_hour_population,pl.update_time,IF(dk.region_code IS NOT NULL, 1, 0) AS dimensions,gt.types,GROUP_CONCAT(DISTINCT tp.pack_name SEPARATOR ',') AS taResidentData,ch.wktTypes");
            FROM("person_land_data as pl");
            LEFT_OUTER_JOIN("dimension_keys dk ON pl.stat_month = dk.stat_month AND pl.region_code = dk.region_code");
            LEFT_OUTER_JOIN("grid_types gt ON pl.stat_month = gt.stat_month AND pl.region_code = gt.type_info");
            LEFT_OUTER_JOIN("ta_task_pack tp ON pl.stat_month = tp.stat_month AND pl.region_code = tp.region_code");
            LEFT_OUTER_JOIN("cell_hot_value_wkt ch ON ch.stat_month = pl.stat_month AND ch.region_code = pl.region_code");
            String orderSql = null;
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("pl.id = " + cri.getValue());
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("pl.region_code IN (" + cri.getValue() + ")");
                    } else if ("statMonth".equals(cri.getKey())) {
                        WHERE("pl.stat_month IN( " + cri.getValue() + ")");
                    } else if ("crowdPack".equals(cri.getKey()) && cri.getValue() != null) {
                        // WHERE("pl.crowd_pack like '%"+ cri.getValue()+"%'");
                        WHERE("tp.pack_id = " + cri.getValue());
                    } else if ("orderStatMonth".equalsIgnoreCase(cri.getKey()) && ("desc".equals(cri.getValue()) || "asc".equals(cri.getValue()))) {
                        if (orderSql == null) {
                            orderSql = "pl.stat_month " + cri.getValue();
                        } else {
                            orderSql += ",pl.stat_month " + cri.getValue();
                        }
                    } else if ("orderUpdateTime".equalsIgnoreCase(cri.getKey()) && ("desc".equals(cri.getValue()) || "asc".equals(cri.getValue()))) {
                        if (orderSql == null) {
                            orderSql = "pl.update_time " + cri.getValue();
                        } else {
                            orderSql += ",pl.update_time " + cri.getValue();
                        }
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }

            GROUP_BY("pl.id");
            if (orderSql == null) {
                ORDER_BY("pl.stat_month asc");
            } else {
                ORDER_BY(orderSql);
            }

        }};
    }


    public String selectList(SearchCriteria criteria) {
        return new SQL() {{
            String orderSql = null;
            SELECT("t1.stat_month,t1.region_code,t2.region_name lv3Name,t3.region_name lv2Name,t4.region_name lv1Name,t1.resident_population,t1.work_population,t1.dwell_population,t1.passenge_population,t1.passenge_hour_population");
            FROM("`person_land_data` t1 ");
            LEFT_OUTER_JOIN("main_dict_region t3 ON t3.`code` = t2.`parent_code`");
            LEFT_OUTER_JOIN("main_dict_region t4 ON t4.`code` = t3.`parent_code`");
            JOIN("main_dict_region t2 ON t1.region_code = t2.`code`");
            if (criteria.getCriterias() != null && !criteria.getCriterias().isEmpty()) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t2.code IN (" + cri.getValue() + ")");
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.stat_month IN (" + cri.getValue() + ")");
                    } else if ("orderStatMonth".equalsIgnoreCase(cri.getKey()) && ("desc".equals(cri.getValue()) || "asc".equals(cri.getValue()))) {
                        if (orderSql == null) {
                            orderSql = "t1.stat_month " + cri.getValue();
                        } else {
                            orderSql += ",t1.stat_month " + cri.getValue();
                        }
                    } else if ("orderUpdateTime".equalsIgnoreCase(cri.getKey()) && ("desc".equals(cri.getValue()) || "asc".equals(cri.getValue()))) {
                        if (orderSql == null) {
                            orderSql = "t1.update_time " + cri.getValue();
                        } else {
                            orderSql += ",t1.update_time " + cri.getValue();
                        }
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            if (orderSql == null) {
                ORDER_BY("t1.stat_month asc");
            } else {
                ORDER_BY(orderSql);
            }
        }}.toString();
    }


    private String buildHotDataSql(String statMonth, String regionCode) {
        return " WITH hot_data AS (" +
                "SELECT " +
                "longitude,latitude," +
                "MAX( CASE WHEN statistical_type = 'RESIDENT_POPULATION' THEN hot_value END ) AS resident_hot_value," +
                "MAX( CASE WHEN statistical_type = 'DWELL_POPULATION' THEN hot_value END ) AS dwell_hot_value," +
                "MAX( CASE WHEN statistical_type = 'WORK_POPULATION' THEN hot_value END ) AS work_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_POPULATION' THEN hot_value END ) AS passenger_hot_value ," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 0 THEN hot_value END) AS passenger_hour_0_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 1 THEN hot_value END) AS passenger_hour_1_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 2 THEN hot_value END) AS passenger_hour_2_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 3 THEN hot_value END) AS passenger_hour_3_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 4 THEN hot_value END) AS passenger_hour_4_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 5 THEN hot_value END) AS passenger_hour_5_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 6 THEN hot_value END) AS passenger_hour_6_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 7 THEN hot_value END) AS passenger_hour_7_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 8 THEN hot_value END) AS passenger_hour_8_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 9 THEN hot_value END) AS passenger_hour_9_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 10 THEN hot_value END) AS passenger_hour_10_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 11 THEN hot_value END) AS passenger_hour_11_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 12 THEN hot_value END) AS passenger_hour_12_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 13 THEN hot_value END) AS passenger_hour_13_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 14 THEN hot_value END) AS passenger_hour_14_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 15 THEN hot_value END) AS passenger_hour_15_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 16 THEN hot_value END) AS passenger_hour_16_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 17 THEN hot_value END) AS passenger_hour_17_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 18 THEN hot_value END) AS passenger_hour_18_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 19 THEN hot_value END) AS passenger_hour_19_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 20 THEN hot_value END) AS passenger_hour_20_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 21 THEN hot_value END) AS passenger_hour_21_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 22 THEN hot_value END) AS passenger_hour_22_hot_value," +
                "MAX( CASE WHEN statistical_type = 'PASSENGER_HOUR_POPULATION' AND time_index = 23 THEN hot_value END) AS passenger_hour_23_hot_value " +
                "FROM grid_hot_data " +
                "WHERE stat_month = '" + statMonth + "' AND type = 3 AND type_info = '" + regionCode + "' " +
                "GROUP BY extra_attrs)";
    }


    public String getExportCityCellHotDataList(String statMonth, String regionCode, MainDictRegionData dictRegionData) {
        String hotData = buildHotDataSql(statMonth, regionCode);
        return hotData + new SQL() {{
            SELECT("t1.city_code," +
                    "t3.cell_id," +
                    "CONCAT( t1.center_lng, ',', t1.center_lat ) AS center," +
                    "CONCAT( t1.up_left_lng, ',', t1.up_left_lat ) AS upLeft," +
                    "CONCAT( t1.lower_left_lng, ',', t1.lower_left_lat ) AS lowerLeft," +
                    "CONCAT( t1.lower_right_lng, ',', t1.lower_right_lat ) AS lowerRight," +
                    "CONCAT( t1.up_right_lng, ',', t1.up_right_lat ) AS upRight," +
                    "t1.people_count," +
                    "COUNT( t3.id ) AS cell_count," +
                    "t2.resident_hot_value," +
                    "t2.dwell_hot_value," +
                    "t2.work_hot_value," +
                    "t2.passenger_hot_value," +
                    "t2.passenger_hour_0_hot_value," +
                    "t2.passenger_hour_1_hot_value," +
                    "t2.passenger_hour_2_hot_value," +
                    "t2.passenger_hour_3_hot_value," +
                    "t2.passenger_hour_4_hot_value," +
                    "t2.passenger_hour_5_hot_value," +
                    "t2.passenger_hour_6_hot_value," +
                    "t2.passenger_hour_7_hot_value," +
                    "t2.passenger_hour_8_hot_value," +
                    "t2.passenger_hour_9_hot_value," +
                    "t2.passenger_hour_10_hot_value," +
                    "t2.passenger_hour_11_hot_value," +
                    "t2.passenger_hour_12_hot_value," +
                    "t2.passenger_hour_13_hot_value," +
                    "t2.passenger_hour_14_hot_value," +
                    "t2.passenger_hour_15_hot_value," +
                    "t2.passenger_hour_16_hot_value," +
                    "t2.passenger_hour_17_hot_value," +
                    "t2.passenger_hour_18_hot_value," +
                    "t2.passenger_hour_19_hot_value," +
                    "t2.passenger_hour_20_hot_value," +
                    "t2.passenger_hour_21_hot_value," +
                    "t2.passenger_hour_22_hot_value," +
                    "t2.passenger_hour_23_hot_value");
            FROM("merged_resident_map_cell_resource t1");
            LEFT_OUTER_JOIN("hot_data t2 ON t1.center_lng = t2.longitude AND t1.center_lat = t2.latitude");
            LEFT_OUTER_JOIN("resident_map_cell_resource t3 ON t1.id = t3.merge_id");
            //用OR不走索引。。
            if (dictRegionData.getLevel() == 3) {
                WHERE("t1.area_code = '" + regionCode + "'");
            } else {
                WHERE("t1.city_code = '" + regionCode + "'");
            }
            GROUP_BY("t1.id");
        }}.toString();
    }


    public String getExportCityIncludeDataList(String regionCode, MainDictRegionData dictRegionData) {
        return new SQL() {{
            SELECT("t1.area_id,t1.area_code,t5.region_name lv1Name,t4.region_name lv2Name,t3.region_name lv3Name,t1.cell_id," +
                    "CONCAT( t2.center_lng, ',', t2.center_lat ) AS center1000,lng_lat AS center100," +
                    "t1.wkt_up_left,t1.wkt_lower_left,t1.wkt_lower_right,t1.wkt_up_right,t1.people_count peopleCount100,t2.people_count peopleCount1000");
            FROM("resident_map_cell_resource t1");
            LEFT_OUTER_JOIN("merged_resident_map_cell_resource t2 ON t1.merge_id = t2.id");
            LEFT_OUTER_JOIN("main_dict_region t3 ON t3.`code` = t1.area_code");
            LEFT_OUTER_JOIN("main_dict_region t4 ON t4.`code` = t3.parent_code");
            LEFT_OUTER_JOIN("main_dict_region t5 ON t5.`code` = t4.parent_code");
            if (dictRegionData.getLevel() == 3) {
                WHERE("t1.area_code = '" + regionCode + "'");
            } else {
                WHERE("t1.city_code = '" + regionCode + "'");
            }
        }}.toString();
    }

    public String getExportCityIncludeDataListWithCursor(String regionCode, MainDictRegionData dictRegionData, Long lastId, Integer limit) {
        return new SQL() {{
            SELECT("t1.id,t1.area_id,t1.area_code,t5.region_name lv1Name,t4.region_name lv2Name,t3.region_name lv3Name,t1.cell_id," +
                    "CONCAT( t2.center_lng, ',', t2.center_lat ) AS center1000,lng_lat AS center100," +
                    "t1.wkt_up_left,t1.wkt_lower_left,t1.wkt_lower_right,t1.wkt_up_right,t1.people_count peopleCount100,t2.people_count peopleCount1000");
            FROM("resident_map_cell_resource t1");
            JOIN("merged_resident_map_cell_resource t2 ON t1.merge_id = t2.id");
            JOIN("main_dict_region t3 ON t3.`code` = t1.area_code");
            LEFT_OUTER_JOIN("main_dict_region t4 ON t4.`code` = t3.parent_code");
            LEFT_OUTER_JOIN("main_dict_region t5 ON t5.`code` = t4.parent_code");

            // 基础条件
            if (dictRegionData.getLevel() == 3) {
                WHERE("t1.area_code = '" + regionCode + "'");
            } else {
                WHERE("t1.city_code = '" + regionCode + "'");
            }

            // 游标分页条件
            if (lastId != null) {
                WHERE("t1.id > " + lastId);
            }

            // 排序和限制
            ORDER_BY("t1.id ASC");
            LIMIT(limit);
        }}.toString();
    }

}
