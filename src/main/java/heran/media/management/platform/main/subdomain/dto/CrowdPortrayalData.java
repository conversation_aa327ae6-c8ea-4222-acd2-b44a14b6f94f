package heran.media.management.platform.main.subdomain.dto;

import com.google.gson.Gson;
import heran.media.sharelib.domain.bo.CrowdPortrayalAttribute;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class CrowdPortrayalData {
    /**
     * 性别
     */
    private String sex;
    /**
     * 年龄
     */
    private String age;
    /**
     * 消费水平
     */
    private String consumption;
    /**
     * 有无子女
     */
    private String haveChildren;
    /**
     * 职业
     */
    private String occupation;


    public CrowdPortrayalData(Map<String, Map<String, String>> dimensions, Map<String, String> map) {
        this.sex = getValue(dimensions, map, CrowdPortrayalAttribute.SEX.getValue());
        this.age = getValue(dimensions, map, CrowdPortrayalAttribute.AGE.getValue());
        this.consumption = getValue(dimensions, map, CrowdPortrayalAttribute.CONSUMPTION.getValue());
        this.haveChildren = getValue(dimensions, map, CrowdPortrayalAttribute.HAVE_CHILDREN.getValue());
        this.occupation = getValue(dimensions, map, CrowdPortrayalAttribute.OCCUPATION.getValue());
    }

    private String getValue(Map<String, Map<String, String>> dimensions, Map<String, String> map, String key) {
        Map<String, String> dimValue = dimensions.get(key);
        return dimValue != null ? new Gson().toJson(dimValue) : map.get(key);
    }
}
