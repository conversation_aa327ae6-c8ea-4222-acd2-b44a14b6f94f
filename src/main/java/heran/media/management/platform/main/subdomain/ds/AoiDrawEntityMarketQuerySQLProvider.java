package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.request.AoiDrawRoleDownRequest;
import org.apache.ibatis.jdbc.SQL;

import java.util.stream.Collectors;

public class AoiDrawEntityMarketQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("ad.*,mr.code as region_code,r1.region_name as one_name,r2.region_name as two_name,mr.region_name as three_name,ad.extra_attrs as market_counts, max_mc.max_update_time,mc.hot_population ");
            FROM("aoi_draw as ad");
            LEFT_OUTER_JOIN("main_dict_region mr ON ad.region_id = mr.id");
            LEFT_OUTER_JOIN("main_dict_region r2 on mr.parent_code = r2.code");
            LEFT_OUTER_JOIN("main_dict_region r1 on r2.parent_code = r1.code");
            LEFT_OUTER_JOIN("marketing_scene_aoi_data mc on ad.id = mc.biz_id");
            LEFT_OUTER_JOIN("(SELECT biz_id, MAX(update_time) AS max_update_time FROM marketing_scene_aoi_data GROUP BY biz_id) max_mc ON ad.id = max_mc.biz_id");
            LEFT_OUTER_JOIN("aoi_draw_tag_rel ar ON ar.aoi_draw_id = ad.id");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if (cri.getKey().equals("id")) {
                        WHERE("ad.id = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("regionCode")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("mr.code between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("mr.code >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("mr.code <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("mr.code = " + cri.getValue());
                    } else if (cri.getKey().equals("aoiName") && cri.getValue() != null) {
                        WHERE("ad.aoi_name like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("quoteCbd")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("ad.quote_cbd between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("ad.quote_cbd >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("ad.quote_cbd <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("ad.quote_cbd = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("cbdId")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("ad.cbd_id between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("ad.cbd_id >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("ad.cbd_id <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("ad.cbd_id = " + cri.getValue());
                    } else if (cri.getKey().equals("aoiArea") && cri.getValue() != null) {
                        WHERE("ad.aoi_area like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("aoiType") && cri.getValue() != null) {
                        WHERE("ad.aoi_type like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("polyline") && cri.getValue() != null) {
                        WHERE("ad.polyline like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("remark") && cri.getValue() != null) {
                        WHERE("ad.remark like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("extraAttrs") && cri.getValue() != null) {
                        WHERE("ad.extra_attrs like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("createdByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("ad.created_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("ad.created_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("ad.created_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("ad.created_by_user = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("updatedByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("ad.updated_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("ad.updated_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("ad.updated_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("ad.updated_by_user = " + cri.getValue());
                    } else if ("tagId".equalsIgnoreCase(cri.getKey())) {
                        WHERE("ar.tag_id IN(" + cri.getValue() + ")");
                    } else if ("maxUpdateTime".equals(cri.getKey())) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("max_mc.max_update_time between '" + cri.getMinValue() + "' AND '" + cri.getMaxValue() + "'");
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("max_mc.max_update_time >= '" + cri.getMinValue() + "'");
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("max_mc.max_update_time <= '" + cri.getMaxValue() + "'");
                        } else if (cri.getValue() != null) {
                            WHERE("max_mc.max_update_time = '" + cri.getValue() + "'");
                        }
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }

                }
            }
            GROUP_BY("ad.id");
            ORDER_BY("ad.update_time desc");
        }}.toString();
    }


    public String getAoiDrawRoleDownData(AoiDrawRoleDownRequest request) {
        return new SQL() {{
            SELECT("t1.id id,t1.aoi_name aoiName,t2.tag_id tagId");
            FROM("aoi_draw t1");
            JOIN("aoi_draw_tag_rel t2 ON t1.id = t2.aoi_draw_id");
            JOIN("main_dict_region t3 ON t1.region_id = t3.id");
            if (CollectionUtils.isNotEmpty(request.getRegionCode()) && !request.getRegionCode().contains("ALL")) {
                String codes = request.getRegionCode().stream()
                        .map(code -> "'" + code + "'")
                        .collect(Collectors.joining(","));
                WHERE("t3.code IN(" + codes + ")");
            }

            if (request.getQuoteCbd() != null) {
                WHERE("t1.quote_cbd = " + request.getQuoteCbd());
            }

        }}.toString();
    }
}
