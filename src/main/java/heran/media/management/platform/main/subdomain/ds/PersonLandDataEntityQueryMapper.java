package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.bo.XlsExportTaCityCellHot;
import heran.media.management.platform.main.subdomain.dto.*;
import heran.media.sharelib.domain.db.model.MainDictRegionData;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PersonLandDataEntityQueryMapper {
    @SelectProvider(type = PersonLandDataEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "regionCode", column = "region_code"),
            @Result(property = "dwellPopulation", column = "dwell_population"),
            @Result(property = "workPopulation", column = "work_population"),
            @Result(property = "residentPopulation", column = "resident_population"),
            @Result(property = "passengePopulation", column = "passenge_population"),
            @Result(property = "passengeHourPopulation", column = "passenge_hour_population"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<PersonLandDataList> list(SearchCriteria criteria);

    @SelectProvider(type = PersonLandDataEntityQuerySQLProvider.class, method = "selectList")
    @Results(value = {
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "regionCode", column = "region_code"),
            @Result(property = "regionName", column = "region_name"),
            @Result(property = "dwellPopulation", column = "dwell_population"),
            @Result(property = "workPopulation", column = "work_population"),
            @Result(property = "residentPopulation", column = "resident_population"),
            @Result(property = "passengerPopulation", column = "passenge_population"),
            @Result(property = "passengerHourPopulation", column = "passenge_hour_population")
    }
    )
    List<ExportPersonLandDataList> selectList(SearchCriteria criteria);

    @Select("SELECT statistical_type FROM grid_hot_data  WHERE stat_month = #{statMonth} AND type_info = #{regionCode} AND data_type = 'person_land_data' GROUP BY statistical_type")
    List<String> getHotStatisticalType(@Param("statMonth") String statMonth, @Param("regionCode") String regionCode);

    @SelectProvider(type = PersonLandDataEntityExportSQLProvider.class, method = "select")
    List<TPersonLandExportData> exportList(SearchCriteria criteria);

    @SelectProvider(type = PersonLandHotDataQuerySQLProvider.class, method = "select")
    List<TPersonLandExportData> exportCityHotDate(SearchCriteria criteria);

    @Select("SELECT latitude,longitude,hot_value as `value` FROM grid_hot_data WHERE type_info = #{typeInfo} AND stat_month = #{statMonth} AND data_type = 'person_land_data' GROUP BY latitude,longitude ORDER BY hot_value DESC ")
    List<HotDataLandTypeData> getEliminationHotData(@Param("typeInfo") String typeInfo, @Param("statMonth") String statMonth);

    @Select("SELECT latitude,longitude,hot_value as `value` FROM grid_hot_data WHERE type_info = #{typeInfo} AND stat_month = #{statMonth} AND data_type = 'person_land_data' AND statistical_type = #{statisticalType} GROUP BY latitude,longitude")
    List<HotDataLandTypeData> getEliminationHotDataByStatisticalType(@Param("typeInfo") String typeInfo, @Param("statMonth") String statMonth, String statisticalType);

    @Select("SELECT center_lng longitude,center_lat latitude,people_count `value`,lower_left_lng,lower_left_lat,lower_right_lng,lower_right_lat,up_left_lng,up_left_lat,up_right_lng,up_right_lat FROM merged_resident_map_cell_resource WHERE city_code = #{cityCode}")
    List<HotDataLandData> getResidentMapResourceData(@Param("cityCode") String cityCode);


    @Select({
            "<script>",
            "SELECT pl.*,group_concat(distinct gh.statistical_type separator ',') as types from person_land_data as pl LEFT JOIN grid_hot_data as gh on pl.stat_month  = gh.stat_month and pl.region_code = gh.type_info and  gh.data_type = 'person_land_data' ",
            "WHERE pl.stat_month = #{statMonth} ",
            "AND pl.region_code IN",
            "<foreach item='regionCode' collection='regionCodeList' open='(' separator=',' close=')'>",
            "#{regionCode}",
            "</foreach>",
            "GROUP BY pl.id",
            "</script>"
    })
    List<TPersonLandExportData> getByStatMonthAndCodeList(@Param("statMonth") String statMonth, @Param("regionCodeList") List<String> regionCodeList);

    @Select({
            "<script>",
            "SELECT COUNT(*) FROM poi_city_offline_scene_detail ",
            "WHERE stat_month = #{statMonth} ",
            "AND region_code IN",
            "<foreach item='regionCode' collection='regionCodeList' open='(' separator=',' close=')'>",
            "#{regionCode}",
            "</foreach>",
            "</script>"
    })
    Integer getByCitOfflineScene(@Param("statMonth") String statMonth, @Param("regionCodeList") List<String> regionCodeList);


    @Select("SELECT\n" +
            "\tt1.crowd_pack_id,\n" +
            "\tt3.pack_name,\n" +
            "\tt4.category_name categoryLv1Name,\n" +
            "\tt5.category_name categoryLv2Name,\n" +
            "\tt2.populace_count\n" +
            "FROM\n" +
            "\tmedia_ta_data_management_task t1\n" +
            "\tJOIN media_ta_data_management_task_detail t2 ON t1.id = t2.management_task_id\n" +
            "\tJOIN media_ta_crowd_pack t3 ON t3.id = t1.crowd_pack_id\n" +
            "\tJOIN main_resource_category t4 ON t4.id = t3.category_id_lv1\n" +
            "\tJOIN main_resource_category t5 ON t5.id = t3.category_id_lv2\n" +
            "WHERE\n" +
            "\tt1.stat_month = #{statMonth} \n" +
            "\tAND t2.region_code = #{regionCode} AND t1.ta_population = 'TA_RESIDENT_POPULATION'")
    List<TaResidentData> getTaResidentData(@Param("statMonth") String statMonth, @Param("regionCode") String regionCode);


    /**
     * 城市网格热力导出
     *
     * @param statMonth  查询月份
     * @param regionCode 城市
     * @return List<ExportCityCellHotData>
     */
    @SelectProvider(type = PersonLandDataEntityQuerySQLProvider.class, method = "getExportCityCellHotDataList")
    List<ExportCityCellHotData> getExportCityCellHotDataList(String statMonth, String regionCode, MainDictRegionData dictRegionData);

    /**
     * 网格关系数据导出
     *
     * @param regionCode 城市
     * @return List<ExportCityIncludeData>
     */
    @SelectProvider(type = PersonLandDataEntityQuerySQLProvider.class, method = "getExportCityIncludeDataList")
    List<ExportCityIncludeData> getExportCityIncludeDataList(String regionCode, MainDictRegionData dictRegionData);

    /**
     * 网格关系数据导出 游标查询实现
     *
     * @param regionCode 城市
     * @return List<ExportCityIncludeData>
     */
    @SelectProvider(type = PersonLandDataEntityQuerySQLProvider.class, method = "getExportCityIncludeDataListWithCursor")
    List<ExportCityIncludeData> getExportCityIncludeDataListWithCursor(String regionCode, MainDictRegionData dictRegionData, Long lastId, Integer limit);

}
