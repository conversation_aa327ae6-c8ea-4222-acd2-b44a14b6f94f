package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.AoiDraw;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleAoiDraw implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "188")
    private Integer id;

    @ApiModelProperty(value = "城市id(如果能划分三级的话则增加省市区的id字段便于搜索)", required = false, example = "65")
//    @NotNull(message = "城市id(如果能划分三级的话则增加省市区的id字段便于搜索)不能为空")
    private Integer regionId;

    @ApiModelProperty(value = "城市id(如果能划分三级的话则增加省市区的id字段便于搜索)", required = true, example = "440100")
    @NotNull(message = "城市id(如果能划分三级的话则增加省市区的id字段便于搜索)不能为空")
    private String regionCode;

    @ApiModelProperty(value = "AOI名称", required = true, example = "AOI名称")
    @Size(max = 64, message = "AOI名称长度不能超出64位")
    @NotBlank(message = "AOI名称不能为空")
    private String aoiName;

    @ApiModelProperty(value = "引用媒介商圈概括", required = true, example = "1为引用，0为否")
    @NotNull(message = "引用媒介商圈概括不能为空")
    private Integer quoteCbd;

    @ApiModelProperty(value = "商圈id", required = false, example = "132")
    private Integer cbdId;

    @ApiModelProperty(value = "面积", required = false, example = "面积")
    @Size(max = 64, message = "面积长度不能超出64位")
    private String aoiArea;

    @ApiModelProperty(value = "坐标串", required = true, example = "坐标串")
    @NotBlank(message = "坐标串不能为空")
    private String polyline;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "详细地址", required = false, example = "详细地址")
    private String address;

    @ApiModelProperty(value = "图形类型", required = false, example = "图形类型")
    private String graphType;

    @ApiModelProperty(value = "半径", required = false, example = "半径")
    private Double radius;


    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "标签", required = true, example = "[1,2,3]")
    @NotNull(message = "标签不能为空")
    private List<Integer> tagIds;


    public TChangebleAoiDraw() {
    }

    public AoiDraw adapToPO() {
        AoiDraw aoiDraw = new AoiDraw();
        aoiDraw.setId(id);
        aoiDraw.setRegionId(regionId);
        aoiDraw.setAoiName(aoiName);
        aoiDraw.setQuoteCbd(quoteCbd);
        aoiDraw.setCbdId(cbdId);
        aoiDraw.setAoiArea(aoiArea);
        aoiDraw.setPolyline(polyline);
        aoiDraw.setRemark(remark);
        aoiDraw.setAddress(address);
        aoiDraw.setExtraAttrs("0");
        aoiDraw.setGraphType(graphType);
        aoiDraw.setRadius(radius);
        return aoiDraw;
    }
}
