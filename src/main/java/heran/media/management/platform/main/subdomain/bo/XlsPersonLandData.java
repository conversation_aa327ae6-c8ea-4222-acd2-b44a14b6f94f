package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.main.subdomain.dto.PersonLandDataList;
import heran.media.sharelib.domain.db.model.main.PersonLandData;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsPersonLandData {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "查询时间", columnIndex = 1)
    private String statMonth;

    @XlsField(title = "省份编号", columnIndex = 2)
    private String regionCode;

    @XlsField(title = "居住人口统计/天级", columnIndex = 8)
    private Integer dwellPopulation;

    @XlsField(title = "工作人口统计/天级", columnIndex = 9)
    private Integer workPopulation;

    @XlsField(title = "常驻人口统计/天级", columnIndex = 10)
    private Integer residentPopulation;

    @XlsField(title = "客流人口统计/天级", columnIndex = 11)
    private Integer passengePopulation;

    @XlsField(title = "客流人口统计/小时级", columnIndex = 12)
    private Integer passengeHourPopulation;

    @XlsField(title = "人群包名称", columnIndex = 13)
    private String crowdPack;

    @XlsField(title = "TA常驻人口统计", columnIndex = 14)
    private String taResidentPopulation;

    @XlsField(title = "TA客流人口统计", columnIndex = 15)
    private String taPassengePopulation;

    @XlsField(title = "行记录创建用户", columnIndex = 16)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 17)
    private Integer updatedByUser;


    public XlsPersonLandData() {
    }

    public XlsPersonLandData(PersonLandData personLandData) {
        this.id = personLandData.getId();
        this.statMonth = personLandData.getStatMonth();
        this.regionCode = personLandData.getRegionCode();
        this.dwellPopulation = personLandData.getDwellPopulation();
        this.workPopulation = personLandData.getWorkPopulation();
        this.residentPopulation = personLandData.getResidentPopulation();
        this.passengePopulation = personLandData.getPassengePopulation();
        this.passengeHourPopulation = personLandData.getPassengeHourPopulation();
        this.crowdPack = personLandData.getCrowdPack();
        this.taResidentPopulation = personLandData.getTaResidentPopulation();
        this.taPassengePopulation = personLandData.getTaPassengePopulation();
        this.createdByUser = personLandData.getCreatedByUser();
        this.updatedByUser = personLandData.getUpdatedByUser();

    }

    public XlsPersonLandData(PersonLandDataList personLandData) {
        this.id = personLandData.getId();
        this.statMonth = personLandData.getStatMonth();
        this.regionCode = personLandData.getRegionCode();
        this.dwellPopulation = personLandData.getDwellPopulation();
        this.workPopulation = personLandData.getWorkPopulation();
        this.residentPopulation = personLandData.getResidentPopulation();
        this.passengePopulation = personLandData.getPassengePopulation();
        this.passengeHourPopulation = personLandData.getPassengeHourPopulation();

    }

    public PersonLandData adapToPO(Integer userId) {
        PersonLandData personLandData = new PersonLandData();
        personLandData.setId(id);
        personLandData.setStatMonth(statMonth);
        personLandData.setRegionCode(regionCode);
        personLandData.setDwellPopulation(dwellPopulation);
        personLandData.setWorkPopulation(workPopulation);
        personLandData.setResidentPopulation(residentPopulation);
        personLandData.setPassengePopulation(passengePopulation);
        personLandData.setPassengeHourPopulation(passengeHourPopulation);
        personLandData.setCrowdPack(crowdPack);
        personLandData.setTaResidentPopulation(taResidentPopulation);
        personLandData.setTaPassengePopulation(taPassengePopulation);

        personLandData.setCreatedByUser(userId);
        personLandData.setUpdatedByUser(userId);
        return personLandData;
    }
}
