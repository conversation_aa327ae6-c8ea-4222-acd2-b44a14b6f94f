package heran.media.management.platform.main.subdomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TaskPersonLandRequest {

    @ApiModelProperty("月份")
    @NotNull(message = "查询月份不能为空")
    private String statMonth;

    @ApiModelProperty("城市" )
    @NotNull(message = "城市不能为空 ")
    private List<String> regionCodes;

    @ApiModelProperty("人口口径 DWELL_POPULATION（居住人口）RESIDENT_POPULATION（常驻人口）WORK_POPULATION（工作人口）PASSENGER_POPULATION（客流人口）WEEKDAY（工作日客流）ALL_DAY（日常客流）HOLIDAY（节假日客流）")
    @NotNull(message = "人口口径不能为空 ")
    private List<String> poiPopulation;

    @ApiModelProperty("洞察纬度")
    @NotNull(message = "洞察纬度不能为空")
    private String inSightDimension;

    @ApiModelProperty("类型")
    @NotNull(message = "类型不能为空")
    private String type;


}
