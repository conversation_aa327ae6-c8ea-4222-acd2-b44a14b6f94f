package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MainMediaOrderManLandPlacement;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleMainMediaOrderManLandPlacement implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "36")
    private Integer id;

    @ApiModelProperty(value = "类型MEDIUM_FORMAT(按媒介)CITY(按城市)", required = true, example = "")
    @Size(max = 64, message = "类型MEDIUM_FORMAT(按媒介)CITY(按城市)长度不能超出64位")
    @NotBlank(message = "类型MEDIUM_FORMAT(按媒介)CITY(按城市)不能为空")
    private String placementType;

    @ApiModelProperty(value = "订单id", required = true, example = "143")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "点位编号", required = false, example = "点位编号")
    @Size(max = 64, message = "点位编号长度不能超出64位")
    private String placementCode;

    @ApiModelProperty(value = "媒体形式", required = false, example = "媒体形式")
    @Size(max = 64, message = "媒体形式长度不能超出64位")
    private String mediumFormat;

    @ApiModelProperty(value = "省份编号", required = false, example = "省份编号")
    @Size(max = 64, message = "省份编号长度不能超出64位")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称", required = false, example = "省份名称")
    @Size(max = 64, message = "省份名称长度不能超出64位")
    private String province;

    @ApiModelProperty(value = "城市编号", required = false, example = "城市编号")
    @Size(max = 64, message = "城市编号长度不能超出64位")
    private String cityCode;

    @ApiModelProperty(value = "城市名称", required = false, example = "城市名称")
    @Size(max = 64, message = "城市名称长度不能超出64位")
    private String city;

    @ApiModelProperty(value = "区域/县级市编号", required = false, example = "区域/县级市编号")
    @Size(max = 64, message = "区域/县级市编号长度不能超出64位")
    private String districtCode;

    @ApiModelProperty(value = "区域/县级市名称", required = false, example = "区域/县级市名称")
    @Size(max = 64, message = "区域/县级市名称长度不能超出64位")
    private String district;

    @ApiModelProperty(value = "媒体名称", required = false, example = "媒体名称")
    @Size(max = 64, message = "媒体名称长度不能超出64位")
    private String mediumName;

    @ApiModelProperty(value = "线路级别", required = false, example = "线路级别")
    @Size(max = 64, message = "线路级别长度不能超出64位")
    private String lineLevel;

    @ApiModelProperty(value = "投放周期", required = false, example = "投放周期")
    @Size(max = 64, message = "投放周期长度不能超出64位")
    private String launchPeriod;

    @ApiModelProperty(value = "投放周期/天", required = false, example = "97")
    private Integer launchPeriodDay;

    @ApiModelProperty(value = "已选空档", required = false, example = "114")
    private Integer neutralPosition;

    @ApiModelProperty(value = "发布费/辆/元", required = false, example = "20")
    private BigDecimal publishCost;

    @ApiModelProperty(value = "制作费/辆/元", required = false, example = "228")
    private BigDecimal makeCost;

    @ApiModelProperty(value = "缓冲区距离", required = false, example = "139")
    private Integer bufferDistance;

    @ApiModelProperty(value = "缓冲区WKT坐标", required = false, example = "缓冲区WKT坐标")
    @Size(max = 255, message = "缓冲区WKT坐标长度不能超出255位")
    private String bufferWktCoordinate;

    @ApiModelProperty(value = "POI", required = false, example = "124")
    private Integer poi;

    @ApiModelProperty(value = "AOI", required = false, example = "49")
    private Integer aoi;

    @ApiModelProperty(value = "商圈", required = false, example = "252")
    private Integer cbdName;

    @ApiModelProperty(value = "主干道", required = false, example = "64")
    private Integer arterialTraffic;

    @ApiModelProperty(value = "热力指数", required = false, example = "热力指数")
    @Size(max = 64, message = "热力指数长度不能超出64位")
    private String heatingIndex;

    @ApiModelProperty(value = "热力周期", required = false, example = "热力周期")
    @Size(max = 64, message = "热力周期长度不能超出64位")
    private String heatingPeriod;

    @ApiModelProperty(value = "媒体位置", required = false, example = "媒体位置")
    @Size(max = 64, message = "媒体位置长度不能超出64位")
    private String address;


    public TChangebleMainMediaOrderManLandPlacement() {
    }

    public MainMediaOrderManLandPlacement adapToPO() {
        MainMediaOrderManLandPlacement mainMediaOrderManLandPlacement = new MainMediaOrderManLandPlacement();
        mainMediaOrderManLandPlacement.setId(id);
        mainMediaOrderManLandPlacement.setPlacementType(placementType);
        mainMediaOrderManLandPlacement.setOrderId(orderId);
        mainMediaOrderManLandPlacement.setPlacementCode(placementCode);
        mainMediaOrderManLandPlacement.setMediumFormat(mediumFormat);
        mainMediaOrderManLandPlacement.setProvinceCode(provinceCode);
        mainMediaOrderManLandPlacement.setProvince(province);
        mainMediaOrderManLandPlacement.setCityCode(cityCode);
        mainMediaOrderManLandPlacement.setCity(city);
        mainMediaOrderManLandPlacement.setDistrictCode(districtCode);
        mainMediaOrderManLandPlacement.setDistrict(district);
        mainMediaOrderManLandPlacement.setMediumName(mediumName);
        mainMediaOrderManLandPlacement.setLineLevel(lineLevel);
        mainMediaOrderManLandPlacement.setLaunchPeriod(launchPeriod);
        mainMediaOrderManLandPlacement.setLaunchPeriodDay(launchPeriodDay);
        mainMediaOrderManLandPlacement.setNeutralPosition(neutralPosition);
        mainMediaOrderManLandPlacement.setPublishCost(publishCost);
        mainMediaOrderManLandPlacement.setMakeCost(makeCost);
        mainMediaOrderManLandPlacement.setBufferDistance(bufferDistance);
        mainMediaOrderManLandPlacement.setBufferWktCoordinate(bufferWktCoordinate);
        mainMediaOrderManLandPlacement.setPoi(poi);
        mainMediaOrderManLandPlacement.setAoi(aoi);
        mainMediaOrderManLandPlacement.setCbdName(cbdName);
        mainMediaOrderManLandPlacement.setArterialTraffic(arterialTraffic);
        mainMediaOrderManLandPlacement.setHeatingIndex(heatingIndex);
        mainMediaOrderManLandPlacement.setHeatingPeriod(heatingPeriod);
        mainMediaOrderManLandPlacement.setAddress(address);

        return mainMediaOrderManLandPlacement;
    }
}
