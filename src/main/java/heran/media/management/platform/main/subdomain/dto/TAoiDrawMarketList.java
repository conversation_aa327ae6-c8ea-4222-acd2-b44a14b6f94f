package heran.media.management.platform.main.subdomain.dto;

import com.alibaba.nacos.common.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TAoiDrawMarketList implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "15")
    private Integer id;

    @ApiModelProperty(value = "城市id(如果能划分三级的话则增加省市区的id字段便于搜索)", required = true, example = "200")
    private Integer regionId;

    @ApiModelProperty(value = "城市code")
    private String regionCode;

    @ApiModelProperty(value = "AOI名称", required = true, example = "AOI名称")
    @NotBlank(message = "AOI名称不能为空")
    private String aoiName;

    @ApiModelProperty(value = "面积", required = true, example = "")
    private String aoiArea;

    @ApiModelProperty("是否关联商圈")
    private Integer quoteCbd;

    @ApiModelProperty(value = "坐标串", required = true, example = "坐标串")
    private String polyline;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;

    @ApiModelProperty(value = "数值")
    private Integer marketCounts;

    @ApiModelProperty(value = "详细地址", required = false, example = "详细地址")
    private String address;

    @ApiModelProperty(value = "最后请求时间")
    private Date maxUpdateTime;

    @ApiModelProperty(value = "标签id的信息", required = false, example = "")
    List<TagIdInfoData> tagIdInfo;

    @ApiModelProperty("热力值人口口径")
    private List<String> hotPopulationList;


    public TAoiDrawMarketList() {
    }

    public TAoiDrawMarketList(TAoiDrawMarket aoiDraw) {
        this.id = aoiDraw.getId();
        this.regionId = aoiDraw.getRegionId();
        this.aoiName = aoiDraw.getAoiName();
        this.regionCode = aoiDraw.getRegionCode();
        this.polyline = aoiDraw.getPolyline();
        this.createTime = aoiDraw.getCreateTime();
        this.updateTime = aoiDraw.getUpdateTime();
        this.marketCounts = aoiDraw.getMarketCounts();
        this.aoiArea = aoiDraw.getAoiArea();
        this.maxUpdateTime = aoiDraw.getMaxUpdateTime();
        this.quoteCbd = aoiDraw.getQuoteCbd();
        this.address = aoiDraw.getAddress();
        this.hotPopulationList = StringUtils.isEmpty(aoiDraw.getHotPopulation()) ? null : Arrays.asList(aoiDraw.getHotPopulation().split(","));
    }
}
