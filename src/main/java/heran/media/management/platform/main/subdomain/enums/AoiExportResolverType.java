package heran.media.management.platform.main.subdomain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AoiExportResolverType {
    /**
     * aoi导出适配枚举值
     */
    UV("UV"),
    PORTRAIT_CROWD("PORTRAIT_CROWD"),
    OCCUPATION("OCCUPATION"),
    SOURCE_CROWD("SOURCE_CROWD"),
    SOURCE_PASSENGER("SOURCE_PASSENGER"),
    TRIP_WAY("TRIP_WAY");

    private String code;
}
