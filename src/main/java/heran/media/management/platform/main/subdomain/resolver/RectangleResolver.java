package heran.media.management.platform.main.subdomain.resolver;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.sharelib.domain.db.model.main.AoiDraw;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class RectangleResolver implements AoiWktResolver {

    private static final Integer VALUE = 2;

    @Override
    public void getAoiWkt(Integer userKey, AoiDraw aoiDraw) {
        String polyline = aoiDraw.getPolyline();
        if (StringUtils.isNotEmpty(polyline)) {
            String[] points = polyline.split("\\|");
            if (points.length >= VALUE) {
                String[] nePoint = points[0].split(",");
                String[] swPoint = points[1].split(",");
                if (nePoint.length == VALUE && swPoint.length == VALUE) {
                    String wkt = generateWktCoordinatesFromNeSw(
                            nePoint[0], nePoint[1],
                            swPoint[0], swPoint[1]
                    );
                    aoiDraw.setAoiWkt(wkt);
                }
            }
        }
    }

    @Override
    public String type() {
        return "RECTANGLE";
    }

    public static String generateWktCoordinatesFromNeSw(String lngNe, String latNe, String lngSw, String latSw) {
        return String.join(", ",
                lngSw + " " + latNe,
                lngNe + " " + latNe,
                lngNe + " " + latSw,
                lngSw + " " + latSw,
                lngSw + " " + latNe
        );
    }


}
