package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.bo.XlsExportTaCityCellHot;
import heran.media.management.platform.main.subdomain.dto.*;
import heran.media.management.platform.main.subdomain.request.ExportTaOfflineDataRequest;
import heran.media.sharelib.domain.db.model.MainDictRegionData;
import heran.media.sharelib.domain.db.model.main.MediaTaDataManagementTask;
import heran.media.sharelib.domain.db.model.main.MergedResidentMapCellResource;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface MediaTaDataManagementTaskEntityQueryMapper {
    @SelectProvider(type = MediaTaDataManagementTaskEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "regionCode", column = "region_code"),
            @Result(property = "taskCode", column = "task_code"),
            @Result(property = "taskName", column = "task_name"),
            @Result(property = "crowdPackId", column = "crowd_pack_id"),
            @Result(property = "taskStatus", column = "task_status"),
            @Result(property = "mapTaskId", column = "map_task_id"),
            @Result(property = "taPopulation", column = "ta_population"),
            @Result(property = "taInsight", column = "ta_Insight"),
            @Result(property = "taEnergetics", column = "ta_energetics"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
            @Result(property = "createdByUser", column = "created_by_user"),
            @Result(property = "updatedByUser", column = "updated_by_user"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<MediaTaDataManagementTask> list(SearchCriteria criteria);


    @SelectProvider(type = MediaTaDataManagementTaskEntityQuerySQLProvider.class, method = "listTaskHeatData")
    @Results(value = {
            @Result(property = "taskName", column = "task_name"),
            @Result(property = "mapTaskId", column = "map_task_id"),
            @Result(property = "packName", column = "pack_name"),
            @Result(property = "regionName", column = "region_name"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "cellId", column = "cell_id"),
            @Result(property = "cellSize", column = "cell_size"),
            @Result(property = "centerLng", column = "center_lng"),
            @Result(property = "centerLat", column = "center_lat"),
            @Result(property = "heat", column = "heat"),
            @Result(property = "taPopulation", column = "ta_population")
    }
    )
    List<TaHeatListData> listTaskHeatData(SearchCriteria criteria);


    /**
     * 列表数据
     *
     * @param criteria 查询条件
     * @return List<MediaTaDataManagementTaskData>
     */
    @SelectProvider(type = MediaTaDataManagementTaskEntityQuerySQLProvider.class, method = "selectList")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "taskCode", column = "task_code"),
            @Result(property = "taskName", column = "task_name"),
            @Result(property = "crowdPackId", column = "crowd_pack_id"),
            @Result(property = "taskStatus", column = "task_status"),
            @Result(property = "mapTaskId", column = "map_task_id"),
            @Result(property = "crowdPackName", column = "pack_name"),
            @Result(property = "regionCode", column = "region_code"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "taEnergetics", column = "ta_energetics"),
            @Result(property = "taPopulation", column = "ta_population"),
            @Result(property = "taInsight", column = "ta_insight"),
            @Result(property = "taType", column = "ta_type"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<MediaTaDataManagementTaskData> listSelect(SearchCriteria criteria);


    /**
     * TA数据管理列表查询
     *
     * @param criteria SearchCriteria
     * @return List<TaDataSelectListData>
     */
    @SelectProvider(type = MediaTaDataManagementTaskEntityQuerySQLProvider.class, method = "taDataSelectList")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "crowdPackId", column = "crowd_pack_id"),
            @Result(property = "taskId", column = "taskId"),
            @Result(property = "packName", column = "pack_name"),
            @Result(property = "regionCode", column = "region_code"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "mapTaskId", column = "map_task_id"),
            @Result(property = "populaceCount", column = "populace_count"),
            @Result(property = "resident", column = "resident"),
            @Result(property = "passenger", column = "passenger"),
            @Result(property = "taPopulation", column = "ta_population"),
            @Result(property = "taOffline", column = "taOffline"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<TaDataSelectListData> taDataSelectList(SearchCriteria criteria);

    /**
     * TA线下场景明细
     *
     * @param criteria SearchCriteria
     * @return List<TaOfflineSceneDataList>
     */
    @SelectProvider(type = MediaTaDataManagementTaskEntityQuerySQLProvider.class, method = "getTaOfflineSceneDataList")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "packId", column = "packId"),
            @Result(property = "packName", column = "packName"),
            @Result(property = "regionCode", column = "region_code"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "tagName", column = "tag_name"),
            @Result(property = "tagValue", column = "tag_value"),
            @Result(property = "isUpdate", column = "is_update"),
            @Result(property = "updateTagValue", column = "update_tag_value"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<TaOfflineSceneDataList> getTaOfflineSceneDataList(SearchCriteria criteria);

    /**
     * 恢复初始化数据
     *
     * @param criteria SearchCriteria
     */
    @SelectProvider(type = MediaTaDataManagementTaskEntityQuerySQLProvider.class, method = "recoverTaData")
    void recoverTaData(SearchCriteria criteria);

    /**
     * 导出TA线下场景偏好
     *
     * @param request ExportTaOfflineDataRequest
     * @return List<TaOfflineData>
     */
    @SelectProvider(type = MediaTaDataManagementTaskEntityQuerySQLProvider.class, method = "selectTaOfflineExport")
    @Results(value = {
            @Result(property = "headingName", column = "headingName"),
            @Result(property = "headingRatio", column = "headingRatio"),
            @Result(property = "mediumName", column = "mediumName"),
            @Result(property = "mediumRatio", column = "mediumRatio"),
            @Result(property = "headingIsUpdate", column = "headingIsUpdate"),
            @Result(property = "mediumIsUpdate", column = "mediumIsUpdate"),
            @Result(property = "headingUpdateValue", column = "headingUpdateValue"),
            @Result(property = "mediumUpdateValue", column = "mediumUpdateValue"),
    }
    )
    List<TaOfflineData> selectTaOfflineExport(ExportTaOfflineDataRequest request);

    /**
     * 获取TA线下场景偏好
     *
     * @param statMonth  时间
     * @param taskId     任务id
     * @param regionCode 城市code
     * @param level      级别
     * @param parentId   父级id
     * @return List<CityOfflineSceneData>
     */
    @Select({
            "<script>",
            "SELECT id,tag_name,",
            "CASE WHEN is_update = TRUE THEN update_tag_value ELSE tag_value END AS tagValue",
            "FROM poi_task_offline_scene_detail",
            "WHERE stat_month = #{statMonth}",
            "AND task_id = #{taskId}",
            "AND region_code = #{regionCode}",
            "AND `level` = #{level}",
            "AND is_deleted = false",
            "<if test='parentId != null'>",
            "AND parent_id = #{parentId}",
            "</if>",
            "ORDER BY tagValue DESC",
            "</script>"
    })
    @Results(value = {
            @Result(property = "tagName", column = "tag_name"),
            @Result(property = "tagValue", column = "tagValue")
    }
    )
    List<CityOfflineSceneData> getTaOfflineSceneData(@Param("statMonth") String statMonth,
                                                     @Param("taskId") Integer taskId,
                                                     @Param("regionCode") String regionCode,
                                                     @Param("level") Integer level,
                                                     @Param("parentId") Integer parentId);

    /**
     * 原始数据查看
     *
     * @param statMonth  查询时间
     * @param taskId     任务id
     * @param regionCode 城市编号
     * @param level      级别
     * @param parentId   父id
     * @return List<CityOfflineSceneData>
     */
    @Select({
            "<script>",
            "SELECT id,tag_name, ",
            "tag_value AS tagValue",
            "FROM poi_task_offline_scene_detail",
            "WHERE stat_month = #{statMonth}",
            "AND task_id = #{taskId}",
            "AND region_code = #{regionCode}",
            "AND `level` = #{level}",
            "<if test='parentId != null'>",
            "AND parent_id = #{parentId}",
            "</if>",
            "ORDER BY tagValue DESC",
            "</script>"
    })
    @Results(value = {
            @Result(property = "tagName", column = "tag_name"),
            @Result(property = "tagValue", column = "tagValue")
    }
    )
    List<CityOfflineSceneData> getTaOfflineSceneDataIsNotDelete(@Param("statMonth") String statMonth,
                                                                @Param("taskId") Integer taskId,
                                                                @Param("regionCode") String regionCode,
                                                                @Param("level") Integer level,
                                                                @Param("parentId") Integer parentId);


    /**
     * 根据经纬度获取到网格聚合关系表数据
     *
     * @param centerLng 经度
     * @param centerLat 纬度
     * @return MergedResidentMapCellResource
     */
    @Select("SELECT * FROM merged_resident_map_cell_resource WHERE center_lng = #{centerLng} AND center_lat = #{centerLat}")
    MergedResidentMapCellResource getMergedResidentMapCellResource(@Param("centerLng") String centerLng, @Param("centerLat") String centerLat);

    /**
     * 获取TA数据
     *
     * @param taskIdResident  常驻任务id
     * @param taskIdPassenger 客流任务id
     * @param statMonth       月份
     * @param regionCode      城市
     * @param dictRegionData  城市对象
     * @return List<XlsExportTaCityCellHot>
     */
    @SelectProvider(type = MediaTaDataManagementTaskEntityQuerySQLProvider.class, method = "getExportCityIncludeTaDataList")
    List<ExportTaCityCellHot> getExportCityIncludeTaDataList(Integer taskIdResident, Integer taskIdPassenger, String statMonth, String regionCode, MainDictRegionData dictRegionData);


}
