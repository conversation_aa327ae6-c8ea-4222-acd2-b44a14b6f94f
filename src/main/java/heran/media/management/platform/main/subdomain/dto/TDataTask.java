package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.DataTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TDataTask implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "134")
    private Integer id;

    @ApiModelProperty(value = "任务名称", required = true, example = "任务名称")
    @Size(max = 64, message = "任务名称长度不能超出64位")
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    @ApiModelProperty(value = "任务状态 PENDING_REQUEST（待处理）ON_REQUEST（处理中）FINISHED（已完成）", required = true, example = "任务状态 PENDING_REQUEST（待处理）ON_REQUEST（处理中）FINISHED（已完成）")
    @Size(max = 64, message = "任务状态 PENDING_REQUEST（待处理）ON_REQUEST（处理中）FINISHED（已完成）长度不能超出64位")
    @NotBlank(message = "任务状态 PENDING_REQUEST（待处理）ON_REQUEST（处理中）FINISHED（已完成）不能为空")
    private String taskStatus;

    @ApiModelProperty(value = "批量选择城市 多个逗号分隔", required = false, example = "批量选择城市 多个逗号分隔")
    @Size(max = 255, message = "批量选择城市 多个逗号分隔长度不能超出255位")
    private String regionCode;

    @ApiModelProperty(value = "批量查询时间", required = false, example = "批量查询时间")
    @Size(max = 64, message = "批量查询时间长度不能超出64位")
    private String statMonth;

    @ApiModelProperty(value = "处理时间", required = true, example = "1741321657646")
    @NotNull(message = "处理时间不能为空")
    private Date disposeTime;

    @ApiModelProperty(value = "数智城市是否选中", required = true, example = "57")
    @NotNull(message = "数智城市是否选中不能为空")
    private Boolean numberSmartCity;

    @ApiModelProperty(value = "数智城市请求参数json", required = false, example = "数智城市请求参数json")
    private String numberSmartCityRequest;

    @ApiModelProperty(value = "数智TA是否选中", required = true, example = "77")
    @NotNull(message = "数智TA是否选中不能为空")
    private Boolean numberSmartTa;

    @ApiModelProperty(value = "数智TA请求参数json", required = false, example = "数智TA请求参数json")
    private String numberSmartTaRequest;

    @ApiModelProperty(value = "商圈洞察是否选中", required = true, example = "130")
    @NotNull(message = "商圈洞察是否选中不能为空")
    private Boolean cbdInsight;

    @ApiModelProperty(value = "商圈洞察请求参数json", required = false, example = "商圈洞察请求参数json")
    private String cbdInsightRequest;

    @ApiModelProperty(value = "特定场景洞察是否选中", required = true, example = "140")
    @NotNull(message = "特定场景洞察是否选中不能为空")
    private Boolean specificSceneInsight;

    @ApiModelProperty(value = "特定场景洞察请求参数json", required = false, example = "特定场景洞察请求参数json")
    private String specificSceneInsightRequest;

    @ApiModelProperty(value = "数智城市透视是否选中", required = true, example = "86")
    @NotNull(message = "数智城市透视是否选中不能为空")
    private Boolean numberSmartCityPerspective;

    @ApiModelProperty(value = "数智城市透视请求参数json", required = false, example = "数智城市透视请求参数json")
    private String numberSmartCityPerspectiveRequest;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TDataTask() {
    }

    public TDataTask(DataTask dataTask) {
        this.id = dataTask.getId();
        this.taskName = dataTask.getTaskName();
        this.taskStatus = dataTask.getTaskStatus();
        this.regionCode = dataTask.getRegionCode();
        this.statMonth = dataTask.getStatMonth();
        this.disposeTime = dataTask.getDisposeTime();
        this.numberSmartCity = dataTask.getNumberSmartCity();
        this.numberSmartCityRequest = dataTask.getNumberSmartCityRequest();
        this.numberSmartTa = dataTask.getNumberSmartTa();
        this.numberSmartTaRequest = dataTask.getNumberSmartTaRequest();
        this.cbdInsight = dataTask.getCbdInsight();
        this.cbdInsightRequest = dataTask.getCbdInsightRequest();
        this.specificSceneInsight = dataTask.getSpecificSceneInsight();
        this.specificSceneInsightRequest = dataTask.getSpecificSceneInsightRequest();
        this.numberSmartCityPerspective = dataTask.getNumberSmartCityPerspective();
        this.numberSmartCityPerspectiveRequest = dataTask.getNumberSmartCityPerspectiveRequest();
        this.remark = dataTask.getRemark();
        this.extraAttrs = dataTask.getExtraAttrs();
        this.createdByUser = dataTask.getCreatedByUser();
        this.updatedByUser = dataTask.getUpdatedByUser();
        this.creator = dataTask.getCreator();
        this.updater = dataTask.getUpdater();
        this.createTime = dataTask.getCreateTime();
        this.updateTime = dataTask.getUpdateTime();

    }
}
