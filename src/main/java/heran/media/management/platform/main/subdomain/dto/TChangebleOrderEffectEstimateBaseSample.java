package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseSample;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleOrderEffectEstimateBaseSample implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "22")
    private Integer id;

    @ApiModelProperty(value = "订单id", required = true, example = "84")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "样本类型EXPOSURE_INSIGHT(曝光人群洞察)CUSTOM(自定义模块)", required = true, example = "")
    @Size(max = 64, message = "样本类型EXPOSURE_INSIGHT(曝光人群洞察)CUSTOM(自定义模块)长度不能超出64位")
    @NotBlank(message = "样本类型EXPOSURE_INSIGHT(曝光人群洞察)CUSTOM(自定义模块)不能为空")
    private String sampleType;

    @ApiModelProperty(value = "全量里面的点位id（还不知道全量数据来源 暂时先这样定义）", required = true, example = "77")
    @NotNull(message = "全量里面的点位id（还不知道全量数据来源 暂时先这样定义）不能为空")
    private Long placementId;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangebleOrderEffectEstimateBaseSample() {
    }

    public OrderEffectEstimateBaseSample adapToPO() {
        OrderEffectEstimateBaseSample orderEffectEstimateBaseSample = new OrderEffectEstimateBaseSample();
        orderEffectEstimateBaseSample.setId(id);
        orderEffectEstimateBaseSample.setOrderId(orderId);
        orderEffectEstimateBaseSample.setSampleType(sampleType);
        orderEffectEstimateBaseSample.setPlacementId(placementId);
        orderEffectEstimateBaseSample.setRemark(remark);
        orderEffectEstimateBaseSample.setExtraAttrs(extraAttrs);

        return orderEffectEstimateBaseSample;
    }
}
