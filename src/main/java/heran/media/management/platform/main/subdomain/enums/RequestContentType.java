package heran.media.management.platform.main.subdomain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum RequestContentType {
    /**
     * 营销场景AOI数据库 请求数据 洞察维度
     */
    DEMOGRAPHIC_STATISTICS("人口统计", Collections.singletonList("uv")),
    PORTRAIT_CROWD("人群画像", Arrays.asList("sex", "consumption_level", "age", "have_children", "occupation")),
    SOURCE_POPULATION("人群来源", Arrays.asList("home_aoi", "company_aoi")),
    FLOW_STATISTICS("流量统计",Collections.singletonList("uv"));
    private String remark;
    private List<String> value;
}
