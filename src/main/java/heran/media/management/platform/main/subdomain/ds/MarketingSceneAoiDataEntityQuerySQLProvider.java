package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class MarketingSceneAoiDataEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("marketing_scene_aoi_data");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                     }
                    if(cri.getKey().equals("id")){
                        WHERE("id = "+ cri.getValue());
                    }
                    else if(cri.getKey().equals("statMonth") && cri.getValue()!=null){
                        WHERE("stat_month like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("provinceCode") && cri.getValue()!=null){
                        WHERE("province_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("province") && cri.getValue()!=null){
                        WHERE("province like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("cityCode") && cri.getValue()!=null){
                        WHERE("city_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("city") && cri.getValue()!=null){
                        WHERE("city like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("districtCode") && cri.getValue()!=null){
                        WHERE("district_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("district") && cri.getValue()!=null){
                        WHERE("district like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("aoiName") && cri.getValue()!=null){
                        WHERE("aoi_name like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("tagLv1") && cri.getValue()!=null){
                        WHERE("tag_lv1 like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("tagLv2") && cri.getValue()!=null){
                        WHERE("tag_lv2 like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("tagLv3") && cri.getValue()!=null){
                        WHERE("tag_lv3 like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("polyline") && cri.getValue()!=null){
                        WHERE("polyline like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("aoiNum")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("aoi_num between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("aoi_num >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("aoi_num <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("aoi_num = "+ cri.getValue());
                    }else if(cri.getKey().equals("poiPopulation") && cri.getValue()!=null){
                        WHERE("poi_population like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("requestContent") && cri.getValue()!=null){
                        WHERE("request_content like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("remark") && cri.getValue()!=null){
                        WHERE("remark like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("extraAttrs") && cri.getValue()!=null){
                        WHERE("extra_attrs like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("createdByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("created_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("created_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("created_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("created_by_user = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("updatedByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("updated_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("updated_by_user = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }

                }
            };
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
