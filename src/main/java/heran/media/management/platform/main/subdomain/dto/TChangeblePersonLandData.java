package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.PersonLandData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangeblePersonLandData implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "207")
    private Integer id;

    @ApiModelProperty(value = "查询月份", required = true, example = "查询月份")
    @Size(max = 64, message = "查询月份长度不能超出64位")
    @NotBlank(message = "查询月份不能为空")
    private String statMonth;

    @ApiModelProperty(value = "查询城市", required = false, example = "查询城市")
    @Size(max = 64, message = "查询城市长度不能超出64位")
    private String regionCode;

    @ApiModelProperty(value = "居住人口统计/天级", required = false, example = "243")
    private Integer dwellPopulation;

    @ApiModelProperty(value = "工作人口统计/天级", required = false, example = "137")
    private Integer workPopulation;

    @ApiModelProperty(value = "常驻人口统计/天级", required = false, example = "24")
    private Integer residentPopulation;

    @ApiModelProperty(value = "客流人口统计/天级", required = false, example = "166")
    private Integer passengePopulation;

    @ApiModelProperty(value = "客流人口统计/小时级", required = false, example = "166")
    private Integer passengeHourPopulation;

    @ApiModelProperty(value = "人群包名称", required = false, example = "人群包名称")
    @Size(max = 64, message = "人群包名称长度不能超出64位")
    private String crowdPack;

    @ApiModelProperty(value = "TA常驻人口统计", required = false, example = "TA常驻人口统计")
    @Size(max = 64, message = "TA常驻人口统计长度不能超出64位")
    private String taResidentPopulation;

    @ApiModelProperty(value = "TA客流人口统计", required = false, example = "TA客流人口统计")
    @Size(max = 64, message = "TA客流人口统计长度不能超出64位")
    private String taPassengePopulation;


    @ApiModelProperty(value = "客流量小时级数据", required = true, example = "[{\"dataHour\":\"0\",\"passengerFlowCount\":10},{\"dataHour\":\"5\",\"passengerFlowCount\":10}]")
    private List<TChangeblePassengerFlowHour> tpassengerFlowHours;


    public TChangeblePersonLandData() {
    }

    public PersonLandData adapToPO() {
        PersonLandData personLandData = new PersonLandData();
        personLandData.setId(id);
        personLandData.setStatMonth(statMonth);
        personLandData.setRegionCode(regionCode);
        personLandData.setDwellPopulation(dwellPopulation);
        personLandData.setWorkPopulation(workPopulation);
        personLandData.setResidentPopulation(residentPopulation);
        personLandData.setPassengePopulation(passengePopulation);
        personLandData.setPassengeHourPopulation(passengeHourPopulation);
        personLandData.setCrowdPack(crowdPack);
        personLandData.setTaResidentPopulation(taResidentPopulation);
        personLandData.setTaPassengePopulation(taPassengePopulation);

        return personLandData;
    }
}
