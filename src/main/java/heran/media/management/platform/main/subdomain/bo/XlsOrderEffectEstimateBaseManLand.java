package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseManLand;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsOrderEffectEstimateBaseManLand {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "订单id", columnIndex = 1)
    private Integer orderId;

    @XlsField(title = "曝光类型 MEDIUM_FORMAT(媒介曝光) CITY_FORMAT(城市曝光)", columnIndex = 2)
    private String exposureType;

    @XlsField(title = "媒介id", columnIndex = 3)
    private String mediumId;

    @XlsField(title = "城市行政编码", columnIndex = 4)
    private String regionCode;

    @XlsField(title = "投放时间", columnIndex = 5)
    private String marketTime;

    @XlsField(title = "投放点位数量", columnIndex = 6)
    private String marketPlacementCount;

    @XlsField(title = "曝光人数PV(人次)", columnIndex = 7)
    private String pv;

    @XlsField(title = "曝光人数UV(人)", columnIndex = 8)
    private String uv;

    @XlsField(title = "广告平均触发频次", columnIndex = 9)
    private String af;

    @XlsField(title = "曝光潜客人数(人)", columnIndex = 10)
    private String impTaUv;

    @XlsField(title = "城市渗透率", columnIndex = 11)
    private String reach;

    @XlsField(title = "曝光潜客浓度(%)", columnIndex = 12)
    private String ta;

    @XlsField(title = "千人成本", columnIndex = 13)
    private String cpm;

    @XlsField(title = "单人成本", columnIndex = 14)
    private String cpuv;

    @XlsField(title = "单TA成本", columnIndex = 15)
    private String cpta;

    @XlsField(title = "说明", columnIndex = 16)
    private String remark;

    @XlsField(title = "扩展字段", columnIndex = 17)
    private String extraAttrs;

    @XlsField(title = "行记录创建用户", columnIndex = 18)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 19)
    private Integer updatedByUser;


    public XlsOrderEffectEstimateBaseManLand() {
    }

    public XlsOrderEffectEstimateBaseManLand(OrderEffectEstimateBaseManLand orderEffectEstimateBaseManLand) {
        this.id = orderEffectEstimateBaseManLand.getId();
        this.orderId = orderEffectEstimateBaseManLand.getOrderId();
        this.exposureType = orderEffectEstimateBaseManLand.getExposureType();
        this.mediumId = orderEffectEstimateBaseManLand.getMediumId();
        this.regionCode = orderEffectEstimateBaseManLand.getRegionCode();
        this.marketTime = orderEffectEstimateBaseManLand.getMarketTime();
        this.marketPlacementCount = orderEffectEstimateBaseManLand.getMarketPlacementCount();
        this.pv = orderEffectEstimateBaseManLand.getPv();
        this.uv = orderEffectEstimateBaseManLand.getUv();
        this.af = orderEffectEstimateBaseManLand.getAf();
        this.impTaUv = orderEffectEstimateBaseManLand.getImpTaUv();
        this.reach = orderEffectEstimateBaseManLand.getReach();
        this.ta = orderEffectEstimateBaseManLand.getTa();
        this.cpm = orderEffectEstimateBaseManLand.getCpm();
        this.cpuv = orderEffectEstimateBaseManLand.getCpuv();
        this.cpta = orderEffectEstimateBaseManLand.getCpta();
        this.remark = orderEffectEstimateBaseManLand.getRemark();
        this.extraAttrs = orderEffectEstimateBaseManLand.getExtraAttrs();
        this.createdByUser = orderEffectEstimateBaseManLand.getCreatedByUser();
        this.updatedByUser = orderEffectEstimateBaseManLand.getUpdatedByUser();

    }

    public OrderEffectEstimateBaseManLand adapToPO(Integer userId) {
        OrderEffectEstimateBaseManLand orderEffectEstimateBaseManLand = new OrderEffectEstimateBaseManLand();
        orderEffectEstimateBaseManLand.setId(id);
        orderEffectEstimateBaseManLand.setOrderId(orderId);
        orderEffectEstimateBaseManLand.setExposureType(exposureType);
        orderEffectEstimateBaseManLand.setMediumId(mediumId);
        orderEffectEstimateBaseManLand.setRegionCode(regionCode);
        orderEffectEstimateBaseManLand.setMarketTime(marketTime);
        orderEffectEstimateBaseManLand.setMarketPlacementCount(marketPlacementCount);
        orderEffectEstimateBaseManLand.setPv(pv);
        orderEffectEstimateBaseManLand.setUv(uv);
        orderEffectEstimateBaseManLand.setAf(af);
        orderEffectEstimateBaseManLand.setImpTaUv(impTaUv);
        orderEffectEstimateBaseManLand.setReach(reach);
        orderEffectEstimateBaseManLand.setTa(ta);
        orderEffectEstimateBaseManLand.setCpm(cpm);
        orderEffectEstimateBaseManLand.setCpuv(cpuv);
        orderEffectEstimateBaseManLand.setCpta(cpta);
        orderEffectEstimateBaseManLand.setRemark(remark);
        orderEffectEstimateBaseManLand.setExtraAttrs(extraAttrs);

        orderEffectEstimateBaseManLand.setCreatedByUser(userId);
        orderEffectEstimateBaseManLand.setUpdatedByUser(userId);
        return orderEffectEstimateBaseManLand;
    }
}
