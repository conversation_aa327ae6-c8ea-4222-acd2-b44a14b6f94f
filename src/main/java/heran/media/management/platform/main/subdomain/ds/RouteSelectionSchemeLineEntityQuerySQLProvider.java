package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class RouteSelectionSchemeLineEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("route_selection_scheme_line");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                     }
                    if(cri.getKey().equals("id")){
                        WHERE("id = "+ cri.getValue());
                    }
                    else if(cri.getKey().equalsIgnoreCase("schemeId")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("scheme_id between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("scheme_id >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("scheme_id <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("scheme_id = "+ cri.getValue());
                    }else if(cri.getKey().equals("regionCode") && cri.getValue()!=null){
                        WHERE("region_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("lineName") && cri.getValue()!=null){
                        WHERE("line_name like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("level") && cri.getValue()!=null){
                        WHERE("level like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("launchCount")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("launch_count between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("launch_count >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("launch_count <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("launch_count = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("launchPeriod")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("launch_period between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("launch_period >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("launch_period <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("launch_period = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("productionCost")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("production_cost between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("production_cost >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("production_cost <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("production_cost = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("issueCost")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("issue_cost between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("issue_cost >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("issue_cost <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("issue_cost = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("costAggregate")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("cost_aggregate between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("cost_aggregate >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("cost_aggregate <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("cost_aggregate = "+ cri.getValue());
                    }else if(cri.getKey().equals("poi") && cri.getValue()!=null){
                        WHERE("poi like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("aoi") && cri.getValue()!=null){
                        WHERE("aoi like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("cbd") && cri.getValue()!=null){
                        WHERE("cbd like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("mainLine") && cri.getValue()!=null){
                        WHERE("main_line like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("heatingPower") && cri.getValue()!=null){
                        WHERE("heating_power like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("taHeatingPower") && cri.getValue()!=null){
                        WHERE("ta_heating_power like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("pitchOn")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("pitch_on between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("pitch_on >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("pitch_on <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("pitch_on = "+ cri.getValue());
                    }else if(cri.getKey().equals("remark") && cri.getValue()!=null){
                        WHERE("remark like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("extraAttrs") && cri.getValue()!=null){
                        WHERE("extra_attrs like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("createdByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("created_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("created_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("created_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("created_by_user = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("updatedByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("updated_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("updated_by_user = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }

                }
            };
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
