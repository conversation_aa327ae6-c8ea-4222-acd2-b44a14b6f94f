package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.AoiDetailsData;
import heran.media.management.platform.main.subdomain.dto.GetMarketingSceneAoiData;
import heran.media.management.platform.main.subdomain.dto.SceneAoiDataListData;
import heran.media.management.platform.main.subdomain.dto.SceneAoiListData;
import heran.media.sharelib.domain.db.model.main.MarketingSceneAoiData;
import heran.media.sharelib.domain.db.model.main.MarketingSceneAoiDataDetail;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface MarketingSceneAoiDataEntityQueryMapper {
    @SelectProvider(type = MarketingSceneAoiDataEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "statMonth", column = "stat_month"), @Result(property = "provinceCode", column = "province_code"), @Result(property = "province", column = "province"), @Result(property = "cityCode", column = "city_code"), @Result(property = "city", column = "city"), @Result(property = "districtCode", column = "district_code"), @Result(property = "district", column = "district"), @Result(property = "aoiName", column = "aoi_name"), @Result(property = "tagLv1", column = "tag_lv1"), @Result(property = "tagLv2", column = "tag_lv2"), @Result(property = "tagLv3", column = "tag_lv3"), @Result(property = "polyline", column = "polyline"), @Result(property = "aoiNum", column = "aoi_num"), @Result(property = "poiPopulation", column = "poi_population"), @Result(property = "requestContent", column = "request_content"), @Result(property = "remark", column = "remark"), @Result(property = "extraAttrs", column = "extra_attrs"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<MarketingSceneAoiData> list(SearchCriteria criteria);


    /**
     * 查询aoi下的洞察数据
     *
     * @param aoiId              aoiid
     * @param poiPopulation      维度
     * @param throughClearlyType 类型
     * @param statMonth          时间
     * @return
     */
    @Select("SELECT " +
            "t2.*,t1.region_code " +
            "FROM " +
            "`marketing_scene_aoi_data` t1 " +
            "JOIN marketing_scene_aoi_data_detail t2 ON t1.id = t2.marketing_scene_aoi_data_id " +
            "WHERE t1.stat_month = #{statMonth} AND t1.through_clearly_type = #{throughClearlyType} AND t1.biz_id = #{aoiId} AND t2.data_type = #{poiPopulation}")
    GetMarketingSceneAoiData getMarketingSceneAoiDataByAoiId(@Param("aoiId") Integer aoiId,
                                                             @Param("poiPopulation") String poiPopulation,
                                                             @Param("throughClearlyType") String throughClearlyType,
                                                             @Param("statMonth") String statMonth);

    /**
     * 获取aoi洞察时间
     *
     * @param aoiId         aoiId
     * @param poiPopulation 人口口径
     * @return List<String>
     */
    @Select("SELECT t1.stat_month FROM `marketing_scene_aoi_data` t1 JOIN marketing_scene_aoi_data_detail t2 ON t1.id = t2.marketing_scene_aoi_data_id WHERE t1.biz_id = #{aoiId} AND t2.data_type = #{poiPopulation}")
    public List<String> getStatMonthData(@Param("aoiId") Integer aoiId, @Param("poiPopulation") String poiPopulation);

    /**
     * 修改AOI的请求条数
     *
     * @param id 主键id
     */
    @Select("UPDATE aoi_draw SET extra_attrs = IFNULL( extra_attrs, 0 ) + 1,update_time = update_time WHERE id = #{id}")
    void updateAoiDrawRequestCount(Integer id);

    /**
     * 获取 AOI 请求数据
     *
     * @param statMonth 月份
     * @param bizIds    bizId
     * @param type      类型
     * @return List<MarketingSceneAoiData>
     */
    @Select({
            "<script>",
            "SELECT t2.* FROM marketing_scene_aoi_data t1 ",
            "JOIN marketing_scene_aoi_data_detail t2 ON t1.id = t2.marketing_scene_aoi_data_id",
            "WHERE t1.stat_month = #{statMonth} AND t1.through_clearly_type = #{type}",
            "AND t1.biz_id IN",
            "<foreach item='bizId' collection='bizIds' open='(' separator=',' close=')'>",
            "#{bizId}",
            "</foreach>",
            "</script>"
    })
    List<MarketingSceneAoiDataDetail> selectByStatMonthAndBizIds(@Param("statMonth") String statMonth, @Param("bizIds") List<Integer> bizIds, @Param("type") String type);


    @Select({
            "<script>",
            "SELECT t1.hot_population FROM marketing_scene_aoi_data t1 ",
            "WHERE t1.stat_month = #{statMonth} AND t1.through_clearly_type = #{type}",
            "AND t1.biz_id IN",
            "<foreach item='bizId' collection='bizIds' open='(' separator=',' close=')'>",
            "#{bizId}",
            "</foreach>",
            "</script>"
    })
    List<String> selectByHotPopulation(@Param("statMonth") String statMonth, @Param("bizIds") List<Integer> bizIds, @Param("type") String type);


    @Select("SELECT t1.stat_month,t2.data_type,t2.passenger_portrayal portrayal,t2.passenger_source source,t2.demographic_statistics statistics,t2.trip_way trip_way FROM `marketing_scene_aoi_data` t1 JOIN marketing_scene_aoi_data_detail t2 ON t1.id = t2.marketing_scene_aoi_data_id WHERE biz_id = #{aoiId}")
    List<SceneAoiListData> getSceneCbdAoiListData(@Param("aoiId") Integer aoiId);

    @Select("SELECT t1.stat_month,t2.data_type,t2.crowd_portrayal portrayal,t2.crowd_source source,t2.demographic_statistics statistics,t2.api_trip_way trip_way FROM `marketing_scene_aoi_data` t1 JOIN marketing_scene_aoi_data_detail t2 ON t1.id = t2.marketing_scene_aoi_data_id WHERE biz_id = #{aoiId}")
    List<SceneAoiListData> getSceneAoiListData(@Param("aoiId") Integer aoiId);

    @Select("SELECT\n" +
            "\t t1.id,t1.aoi_name,t2.region_name,t1.address,t6.label labelLv1,t5.label labelLv2,t4.label labelLv3,t1.aoi_area,t1.polyline,t1.quote_cbd\n" +
            "FROM\n" +
            "\taoi_draw t1\n" +
            "\tJOIN main_dict_region t2 ON t1.region_id = t2.id\n" +
            "\tJOIN aoi_draw_tag_rel t3 ON t3.aoi_draw_id = t1.id\n" +
            "\tLEFT JOIN main_media_tag t4 ON t4.id = t3.tag_id\n" +
            "\tLEFT JOIN main_media_tag t5 ON t5.id = t4.parent_id\n" +
            "\tLEFT JOIN main_media_tag t6 ON t6.id = t5.parent_id " +
            "WHERE t1.id = #{aoiId} LIMIT 1")
    AoiDetailsData getAoiDetailsData(@Param("aoiId") Integer aoiId);

    @Select("SELECT t1.stat_month,t2.tag_name tagNameLv1,t1.tag_name tagNameLv2,t1.tag_value,t1.is_update,t1.update_tag_value FROM `poi_aoi_offline_scene_detail` t1 JOIN poi_aoi_offline_scene_detail t2 ON t1.parent_id = t2.id WHERE t1.aoi_id = #{aoiId} AND t1.is_deleted = false AND t2.is_deleted = false AND t1.`level` = 2 ORDER BY t1.stat_month,t2.tag_name ")
    List<SceneAoiDataListData> getSceneAoiDataListData(@Param("aoiId") Integer aoiId);

    @Select("<script>" +
            "SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   statistics_dimension " +
            "WHERE " +
            "   region_code IN " +
            "   <foreach item='regionCode' collection='regionCodeList' open='(' separator=',' close=')'>" +
            "       #{regionCode}" +
            "   </foreach> " +
            "   AND statistical_type = #{statisticalType} " +
            "   AND stat_month = #{statMonth} " +
            "   AND data_type = #{dataType} " +
            "   AND dimension_name IN " +
            "   <foreach item='dimension' collection='list' open='(' separator=',' close=')'>" +
            "       #{dimension}" +
            "   </foreach>" +
            "</script>")
    Integer getAoiCityShoppingRecreation(@Param("regionCodeList") List<String> regionCodeList,
                                                              @Param("statMonth") String statMonth,
                                                              @Param("statisticalType") String statisticalType,
                                                              @Param("dataType") String dataType,
                                                              @Param("list") List<String> list);

    @Select({
            "<script>",
            "SELECT COUNT(*) FROM poi_aoi_offline_scene_detail t1 ",
            "WHERE t1.stat_month = #{statMonth} AND is_deleted = false ",
            "AND t1.aoi_id IN",
            "<foreach item='aoiId' collection='aoiId' open='(' separator=',' close=')'>",
            "#{aoiId}",
            "</foreach>",
            "</script>"
    })
    Integer getAoiOfflineSceneCount(String statMonth,List<Integer> aoiId);


}
