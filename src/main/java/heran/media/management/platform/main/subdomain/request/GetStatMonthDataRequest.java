package heran.media.management.platform.main.subdomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GetStatMonthDataRequest {
    @ApiModelProperty("aoiId")
    @NotNull(message = "aoiId不能为空")
    private Integer aoiId;
    @ApiModelProperty("人口口径 DWELL_POPULATION（居住人口）RESIDENT_POPULATION（常驻人口）WORK_POPULATION（工作人口）PASSENGER_POPULATION（客流人口）WEEKDAY（工作日客流）ALL_DAY（日常客流）HOLIDAY（节假日客流）")
    @NotNull(message = "人口口径不能为空 ")
    private String poiPopulation;
}
