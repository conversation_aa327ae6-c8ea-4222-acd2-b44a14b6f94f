package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.StatisticsDimension;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TStatisticsDimension implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "165")
    private Integer id;

    @ApiModelProperty(value = "查询月份", required = true, example = "查询月份")
    @Size(max = 64, message = "查询月份长度不能超出64位")
    @NotBlank(message = "查询月份不能为空")
    private String statMonth;

    @ApiModelProperty(value = "查询城市", required = true, example = "查询城市")
    @Size(max = 32, message = "查询城市长度不能超出32位")
    @NotBlank(message = "查询城市不能为空")
    private String regionCode;

    @ApiModelProperty(value = "统计类型DWELL_POPULATION（居住人口）WORK_POPULATION(常驻人口)WORK_POPULATION(工作人口)PASSENGE_POPULATION(客流人口)", required = false, example = "")
    @Size(max = 32, message = "统计类型DWELL_POPULATION（居住人口）WORK_POPULATION(常驻人口)WORK_POPULATION(工作人口)PASSENGE_POPULATION(客流人口)长度不能超出32位")
    private String statisticalType;

    @ApiModelProperty(value = "统计维度名称", required = true, example = "统计维度名称")
    @Size(max = 64, message = "统计维度名称长度不能超出64位")
    @NotBlank(message = "统计维度名称不能为空")
    private String dimensionName;

    @ApiModelProperty(value = "统计维度值 JOIN", required = true, example = "统计维度值 JOIN")
    @NotBlank(message = "统计维度值 JOIN不能为空")
    private String dimensionValue;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date updateTime;


    public TStatisticsDimension() {
    }

    public TStatisticsDimension(StatisticsDimension statisticsDimension) {
        this.id = statisticsDimension.getId();
        this.statMonth = statisticsDimension.getStatMonth();
        this.regionCode = statisticsDimension.getRegionCode();
        this.statisticalType = statisticsDimension.getStatisticalType();
        this.dimensionName = statisticsDimension.getDimensionName();
        this.dimensionValue = statisticsDimension.getDimensionValue();
        this.createdByUser = statisticsDimension.getCreatedByUser();
        this.updatedByUser = statisticsDimension.getUpdatedByUser();
        this.creator = statisticsDimension.getCreator();
        this.updater = statisticsDimension.getUpdater();
        this.createTime = statisticsDimension.getCreateTime();
        this.updateTime = statisticsDimension.getUpdateTime();

    }
}
