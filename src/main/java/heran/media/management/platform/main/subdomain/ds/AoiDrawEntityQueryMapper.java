package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.MainMediaCbdList;
import heran.media.management.platform.main.subdomain.dto.TAoiDrawMarket;
import heran.media.management.platform.main.subdomain.dto.TagIdInfoData;
import heran.media.management.platform.main.subdomain.request.AoiDrawRoleDownRequest;
import heran.media.management.platform.main.subdomain.response.AoiDrawResponse;
import heran.media.management.platform.main.subdomain.response.AoiDrawRoleDownResponse;
import heran.media.management.platform.main.subdomain.response.AoiDrawTagRoleDownResponse;
import heran.media.management.platform.main.subdomain.response.TagCountInfoDataResponse;
import heran.media.sharelib.domain.db.model.main.AoiDraw;
import heran.media.sharelib.domain.db.model.main.AoiDrawTagRel;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface AoiDrawEntityQueryMapper {
    @SelectProvider(type = AoiDrawEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "regionId", column = "region_id"),
            @Result(property = "aoiName", column = "aoi_name"),
            @Result(property = "quoteCbd", column = "quote_cbd"),
            @Result(property = "cbdId", column = "cbd_id"),
            @Result(property = "aoiArea", column = "aoi_area"),
            @Result(property = "aoiType", column = "aoi_type"),
            @Result(property = "polyline", column = "polyline"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "address", column = "address"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
            @Result(property = "createdByUser", column = "created_by_user"),
            @Result(property = "updatedByUser", column = "updated_by_user"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    }
    )
    List<AoiDrawResponse> list(SearchCriteria criteria);

    @SelectProvider(type = AoiDrawEntityMarketQuerySQLProvider.class, method = "select")
    List<TAoiDrawMarket> marketList(SearchCriteria criteria);

    @Select({
            "<script>",
            "SELECT * from aoi_draw as ad left join main_dict_region mr ON ad.region_id = mr.id left join aoi_draw_tag_rel adt ON adt.aoi_draw_id = ad.id  where 1=1",
            "<if test='codeList != null and codeList.size() > 0'>",
            "And mr.code IN ",
            "<foreach collection='codeList' item='code' open='(' separator=',' close=')'>",
            "#{code}",
            "</foreach>",
            "</if>",
            "<if test='tagIdList != null and tagIdList.size() > 0'>",
            "And adt.tag_id IN ",
            "<foreach collection='tagIdList' item='tag' open='(' separator=',' close=')'>",
            "#{tag}",
            "</foreach>",
            "</if>",
            "<if test='quoteCbd != null'>",
            "And ad.quote_cbd =  #{quoteCbd} ",
            "</if>",
            "group by ad.id",
            "</script>"
    })
    List<AoiDraw> selectCodeList(List<String> codeList, Integer quoteCbd, List<Integer> tagIdList);

    @Insert({"<script>",
            "INSERT INTO aoi_draw_tag_rel(id,tag_id,aoi_draw_id, remark, extra_attrs, created_by_user, " +
                    "updated_by_user, creator, updater,create_time)",
            "VALUES ",
            "<foreach item='item' collection='batchInsertList' separator=','>",
            "(#{item.id},#{item.tagId},#{item.aoiDrawId}, #{item.remark}, #{item.extraAttrs},  #{item.createdByUser}, #{item.updatedByUser}, #{item.creator}, #{item.updater},#{item.createTime})",
            "</foreach>",

            "</script>"
    })
    void batchInsertEntities(List<AoiDrawTagRel> batchInsertList);

    @Select("SELECT id,cbd_name from main_media_cbd where region_id = #{id} ")
    @Results(id = "getCbdList-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "cbdName", column = "cbd_name"),

    })
    List<MainMediaCbdList> getCbdList(@Param("id") Integer id);

    @Delete("DELETE FROM aoi_draw_tag_rel WHERE aoi_draw_id = #{aoiDrawId}")
    void deleteByAoiDrawId(@Param("aoiDrawId") int aoiDrawId);


    @Select("SELECT ad.tag_id, m1.label as label_lv1, m2.label as label_lv2, m3.label as label_lv3 FROM `aoi_draw_tag_rel` AS ad LEFT JOIN main_media_tag m3 on ad.tag_id = m3.id LEFT JOIN main_media_tag m2 on m3.parent_id = m2.id LEFT JOIN main_media_tag m1 on m2.parent_id = m1.id WHERE ad.aoi_draw_id = #{aoiDrawId} ")
    @Results(id = "getTagIdInfo-mapping", value = {
            @Result(property = "tagId", column = "tag_id"),
            @Result(property = "labelLv1", column = "label_lv1"),
            @Result(property = "labelLv2", column = "label_lv2"),
            @Result(property = "labelLv3", column = "label_lv3"),
    })
    List<TagIdInfoData> getTagIdInfo(@Param("aoiDrawId") int aoiDrawId);

    @Select({
            "<script>",
            "SELECT ad.tag_id, m1.label as label_lv1, m2.label as label_lv2, m3.label as label_lv3 ,ad.aoi_draw_id",
            "FROM `aoi_draw_tag_rel` AS ad ",
            "JOIN main_media_tag m3 ON ad.tag_id = m3.id ",
            "JOIN main_media_tag m2 ON m3.parent_id = m2.id ",
            "JOIN main_media_tag m1 ON m2.parent_id = m1.id ",
            "WHERE ad.aoi_draw_id IN ",
            "<foreach item='item' collection='ids' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    @Results(id = "getTagIdsInfo-mapping", value = {
            @Result(property = "id", column = "aoi_draw_id"),
            @Result(property = "tagId", column = "tag_id"),
            @Result(property = "labelLv1", column = "label_lv1"),
            @Result(property = "labelLv2", column = "label_lv2"),
            @Result(property = "labelLv3", column = "label_lv3"),
    })
    List<TagIdInfoData> getTagIdsInfo(@Param("ids") List<Integer> ids);

    /**
     * 获取AOI总量
     *
     * @return
     */
    @Select("SELECT COUNT(*) FROM aoi_draw ")
    Integer selectAllAoiCount();

    /**
     * 获取AOI城市总量
     *
     * @return
     */
    @Select("SELECT COUNT(DISTINCT region_id) as city_count FROM aoi_draw")
    Integer selectCityAllAoiCount();

    @Select("SELECT COUNT(*) FROM aoi_draw t1 JOIN main_dict_region t2 ON t1.region_id = t2.id WHERE t2.`code` = #{code}")
    Integer selectCityAoiCount(@Param("code") String code);

    @Select({
            "<script>",
            "SELECT t3.id as tagId,t3.label tagName,COUNT(DISTINCT t1.id) as aoiCount ",
            "FROM aoi_draw t1 ",
            "LEFT JOIN aoi_draw_tag_rel t2 ON t1.id = t2.aoi_draw_id ",
            "LEFT JOIN main_media_tag t3 ON t3.id = t2.tag_id ",
            "WHERE t3.tag_group = 'AOI' AND t3.id IN ",
            "<foreach item='item' collection='ids' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "GROUP BY t3.id",
            "</script>"
    })
    List<TagCountInfoDataResponse> selectTagCount(@Param("ids") List<Integer> ids);

    /**
     * 通过城市code 标签id 获取AOI数据
     *
     * @param request 请求参数
     * @return List<AoiDrawRoleDownResponse>
     */
    @SelectProvider(type = AoiDrawEntityMarketQuerySQLProvider.class, method = "getAoiDrawRoleDownData")
    List<AoiDrawRoleDownResponse> getAoiDrawRoleDownData(AoiDrawRoleDownRequest request);
}
