package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 */
public class PersonLandHotDataQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("pl.*,r1.region_name as one_name,r2.region_name as two_name,r3.region_name as three_name");
            FROM("person_land_data as pl");
            LEFT_OUTER_JOIN("main_dict_region r3 on pl.region_code = r3.code");
            LEFT_OUTER_JOIN("main_dict_region r2 on r3.parent_code = r2.code");
            LEFT_OUTER_JOIN("main_dict_region r1 on r2.parent_code = r1.code");
            if (criteria.getCriterias() != null && !criteria.getCriterias().isEmpty()) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("pl.id = " + cri.getValue());
                    } else if ("statMonth".equalsIgnoreCase(cri.getKey())) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("pl.stat_month between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null) {
                            WHERE("pl.stat_month >= " + cri.getMinValue());
                        } else if (cri.getMaxValue() != null) {
                            WHERE("pl.stat_month <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("pl.stat_month = " + cri.getValue());
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("pl.region_code IN(" + cri.getValue() + ")");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            GROUP_BY("pl.id");
        }}.toString();
    }
}
