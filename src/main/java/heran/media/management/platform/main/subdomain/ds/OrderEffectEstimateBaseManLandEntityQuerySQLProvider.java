package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class OrderEffectEstimateBaseManLandEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("order_effect_estimate_base_man_land");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                     }
                    if(cri.getKey().equals("id")){
                        WHERE("id = "+ cri.getValue());
                    }
                    else if(cri.getKey().equalsIgnoreCase("orderId")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("order_id between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("order_id >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("order_id <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("order_id = "+ cri.getValue());
                    }else if(cri.getKey().equals("exposureType") && cri.getValue()!=null){
                        WHERE("exposure_type like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("mediumId")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("medium_id between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("medium_id >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("medium_id <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("medium_id = "+ cri.getValue());
                    }else if(cri.getKey().equals("regionCode") && cri.getValue()!=null){
                        WHERE("region_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("marketTime") && cri.getValue()!=null){
                        WHERE("market_time like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("marketPlacementCount") && cri.getValue()!=null){
                        WHERE("market_placement_count like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("pv") && cri.getValue()!=null){
                        WHERE("pv like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("uv") && cri.getValue()!=null){
                        WHERE("uv like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("af") && cri.getValue()!=null){
                        WHERE("af like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("impTaUv") && cri.getValue()!=null){
                        WHERE("imp_ta_uv like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("reach") && cri.getValue()!=null){
                        WHERE("reach like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("ta") && cri.getValue()!=null){
                        WHERE("ta like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("cpm") && cri.getValue()!=null){
                        WHERE("cpm like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("cpuv") && cri.getValue()!=null){
                        WHERE("cpuv like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("cpta") && cri.getValue()!=null){
                        WHERE("cpta like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("remark") && cri.getValue()!=null){
                        WHERE("remark like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("extraAttrs") && cri.getValue()!=null){
                        WHERE("extra_attrs like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("createdByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("created_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("created_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("created_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("created_by_user = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("updatedByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("updated_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("updated_by_user = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }

                }
            };
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
