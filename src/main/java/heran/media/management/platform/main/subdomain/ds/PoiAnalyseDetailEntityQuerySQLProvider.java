package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class PoiAnalyseDetailEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("poi_analyse_detail");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if (cri.getKey().equals("id")) {
                        WHERE("id = " + cri.getValue());
                    } else if (cri.getKey().equals("statMonth") && cri.getValue() != null) {
                        WHERE("stat_month like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("analyseName") && cri.getValue() != null) {
                        WHERE("analyse_name like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("analyseType") && cri.getValue() != null) {
                        WHERE("analyse_type like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("poiType") && cri.getValue() != null) {
                        WHERE("poi_type like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("provinceCode") && cri.getValue() != null) {
                        WHERE("province_code like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("province") && cri.getValue() != null) {
                        WHERE("province like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("cityCode") && cri.getValue() != null) {
                        WHERE("city_code like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("city") && cri.getValue() != null) {
                        WHERE("city like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("districtCode") && cri.getValue() != null) {
                        WHERE("district_code like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("district") && cri.getValue() != null) {
                        WHERE("district like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("address") && cri.getValue() != null) {
                        WHERE("address like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("longitude") && cri.getValue() != null) {
                        WHERE("longitude like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("latitude") && cri.getValue() != null) {
                        WHERE("latitude like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("targetAddress") && cri.getValue() != null) {
                        WHERE("target_address like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("geofencing") && cri.getValue() != null) {
                        WHERE("geofencing like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("remark") && cri.getValue() != null) {
                        WHERE("remark like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("extraAttrs") && cri.getValue() != null) {
                        WHERE("extra_attrs like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("createdByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("created_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("created_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("created_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("created_by_user = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("updatedByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("updated_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("updated_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("updated_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("updated_by_user = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }

                }
            }
            ORDER_BY("update_time desc");
        }}.toString();
    }


    public String selectList(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t1.*,t2.region_name as provinceName,t3.region_name as cityName,t4.region_name as districtName");
            FROM("poi_analyse_detail t1");
            LEFT_OUTER_JOIN("main_dict_region t2 ON t2.code = t1.province_code");
            LEFT_OUTER_JOIN("main_dict_region t3 ON t3.code = t1.city_code");
            LEFT_OUTER_JOIN("main_dict_region t4 ON t4.code = t1.district_code");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("t1.id = " + cri.getValue());
                    } else if ("poiType".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.poi_type like '%" + cri.getValue() + "%'");
                    } else if ("code".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.code = '" + cri.getValue() + "'");
                    } else if ("poiId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.poi_id like '%" + cri.getValue() + "%'");
                    } else if ("analyseName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.analyse_name like '%" + cri.getValue() + "%'");
                    } else if ("analyseType".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.analyse_type = '" + cri.getValue() + "'");
                    } else if ("updateTime".equals(cri.getKey())) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("t1.update_time between '" + cri.getMinValue() + "' AND '" + cri.getMaxValue() + "'");
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("t1.update_time >= '" + cri.getMinValue() + "'");
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("t1.update_time <= '" + cri.getMaxValue() + "'");
                        } else if (cri.getValue() != null) {
                            WHERE("t1.update_time = '" + cri.getValue() + "'");
                        }
                    } else if ("area".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("(t1.province_code IN(" + cri.getValue() + ") OR t1.city_code IN(" + cri.getValue() + ") OR t1.district_code IN(" + cri.getValue() + "))");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            ORDER_BY("t1.update_time desc");
        }}.toString();
    }
}
