package heran.media.management.platform.main.subdomain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExportCityCellHotData {
    private String statMonth;
    private String code;
    private String cellId;
    private String center;
    private String upLeft;
    private String lowerLeft;
    private String lowerRight;
    private String upRight;
    private String peopleCount;
    private String cellCount;
    private String residentHotValue;
    private String dwellHotValue;
    private String workHotValue;
    private String passengerHotValue;

    private String passengerHour0HotValue;
    private String passengerHour1HotValue;
    private String passengerHour2HotValue;
    private String passengerHour3HotValue;
    private String passengerHour4HotValue;
    private String passengerHour5HotValue;
    private String passengerHour6HotValue;
    private String passengerHour7HotValue;
    private String passengerHour8HotValue;
    private String passengerHour9HotValue;
    private String passengerHour10HotValue;
    private String passengerHour11HotValue;
    private String passengerHour12HotValue;
    private String passengerHour13HotValue;
    private String passengerHour14HotValue;
    private String passengerHour15HotValue;
    private String passengerHour16HotValue;
    private String passengerHour17HotValue;
    private String passengerHour18HotValue;
    private String passengerHour19HotValue;
    private String passengerHour20HotValue;
    private String passengerHour21HotValue;
    private String passengerHour22HotValue;
    private String passengerHour23HotValue;
}
