package heran.media.management.platform.main.subdomain.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.Date;

@Data

/**
 * <AUTHOR>
 */
public class MediaTaCrowdPackData {
    private Integer id;

    private String packName;

    private String packCode;

    private Integer categoryIdLv1;

    private Integer categoryIdLv2;

    private Integer isEnabled;

    private String remark;

    private String extraAttrs;

    private String categoryNameLv1;

    private String categoryNameLv2;

    private Integer createdByUser;

    private String createdByUserName;

    private Integer updatedByUser;

    private String creator;

    private String updater;

    private Date createTime;

    private Date updateTime;

    private String tagNames;

    public MediaTaCrowdPackData() {
    }
}

