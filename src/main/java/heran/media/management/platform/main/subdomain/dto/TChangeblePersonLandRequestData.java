package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.PersonLandData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangeblePersonLandRequestData implements Serializable {


    @ApiModelProperty(value = "月份", required = true, example = "月份")
    @Size(max = 64, message = "月份长度不能超出64位")
    @NotBlank(message = "月份不能为空")
    private String statMonth;

    @ApiModelProperty(value = "城市", required = true, example = "440100,440100")
    @Size(max = 200, message = "城市长度不能超出64位")
    @NotBlank(message = "城市不能为空")
    private String regionCode;

    @ApiModelProperty(value = "城市人口统计", required = true, example = "DWELL_POPULATION,RESIDENT_POPULATION,WORK_POPULATION,PASSENGER_POPULATION")
    @Size(max = 200, message = "统计类型长度不能超出200位")
    private String statisticType;

    @ApiModelProperty(value = "网络热力指数", required = true, example = "DWELL_POPULATION,RESIDENT_POPULATION")
    @Size(max = 200, message = "热力指数类型长度不能超出200位")
    private String indexType;

    @ApiModelProperty(value = "洞察类型", required = true, example = "DWELL_POPULATION,RESIDENT_POPULATION,WORK_POPULATION")
    @Size(max = 200, message = "洞察类型长度不能超出200位")
    private String inSightType;

    @ApiModelProperty(value = "洞察纬度", required = true, example = "age,sex,education")
    private String inSightDimension;


}
