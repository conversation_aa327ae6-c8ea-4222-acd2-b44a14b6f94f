package heran.media.management.platform.main.subdomain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ExportPersonLandDataList {
    private String regionCode;
    private String lv1Name;
    private String lv2Name;
    private String lv3Name;
    private String statMonth;

    private Integer dwellPopulation;
    private Integer workPopulation;
    private Integer residentPopulation;
    private Integer passengerPopulation;
    private Integer passengerHourPopulation;


    public String getRegionName() {
        String cityName = "";
        if (lv1Name != null) {
            cityName += lv1Name;
        }
        if (lv2Name != null) {
            cityName += lv2Name;
        }
        if (lv3Name != null) {
            cityName += lv3Name;
        }
        return cityName;
    }

}
