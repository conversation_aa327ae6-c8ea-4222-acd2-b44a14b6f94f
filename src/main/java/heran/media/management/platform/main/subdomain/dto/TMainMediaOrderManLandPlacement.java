package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MainMediaOrderManLandPlacement;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TMainMediaOrderManLandPlacement implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "47")
    private Integer id;

    @ApiModelProperty(value = "类型MEDIUM_FORMAT(按媒介)CITY(按城市)", required = true, example = "")
    @Size(max = 64, message = "类型MEDIUM_FORMAT(按媒介)CITY(按城市)长度不能超出64位")
    @NotBlank(message = "类型MEDIUM_FORMAT(按媒介)CITY(按城市)不能为空")
    private String placementType;

    @ApiModelProperty(value = "订单id", required = true, example = "42")
    @NotNull(message = "订单id不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "点位编号", required = false, example = "点位编号")
    @Size(max = 64, message = "点位编号长度不能超出64位")
    private String placementCode;

    @ApiModelProperty(value = "媒体形式", required = false, example = "媒体形式")
    @Size(max = 64, message = "媒体形式长度不能超出64位")
    private String mediumFormat;

    @ApiModelProperty(value = "省份编号", required = false, example = "省份编号")
    @Size(max = 64, message = "省份编号长度不能超出64位")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称", required = false, example = "省份名称")
    @Size(max = 64, message = "省份名称长度不能超出64位")
    private String province;

    @ApiModelProperty(value = "城市编号", required = false, example = "城市编号")
    @Size(max = 64, message = "城市编号长度不能超出64位")
    private String cityCode;

    @ApiModelProperty(value = "城市名称", required = false, example = "城市名称")
    @Size(max = 64, message = "城市名称长度不能超出64位")
    private String city;

    @ApiModelProperty(value = "区域/县级市编号", required = false, example = "区域/县级市编号")
    @Size(max = 64, message = "区域/县级市编号长度不能超出64位")
    private String districtCode;

    @ApiModelProperty(value = "区域/县级市名称", required = false, example = "区域/县级市名称")
    @Size(max = 64, message = "区域/县级市名称长度不能超出64位")
    private String district;

    @ApiModelProperty(value = "媒体名称", required = false, example = "媒体名称")
    @Size(max = 64, message = "媒体名称长度不能超出64位")
    private String mediumName;

    @ApiModelProperty(value = "线路级别", required = false, example = "线路级别")
    @Size(max = 64, message = "线路级别长度不能超出64位")
    private String lineLevel;

    @ApiModelProperty(value = "投放周期", required = false, example = "投放周期")
    @Size(max = 64, message = "投放周期长度不能超出64位")
    private String launchPeriod;

    @ApiModelProperty(value = "投放周期/天", required = false, example = "200")
    private Integer launchPeriodDay;

    @ApiModelProperty(value = "已选空档", required = false, example = "128")
    private Integer neutralPosition;

    @ApiModelProperty(value = "发布费/辆/元", required = false, example = "225")
    private BigDecimal publishCost;

    @ApiModelProperty(value = "制作费/辆/元", required = false, example = "10")
    private BigDecimal makeCost;

    @ApiModelProperty(value = "缓冲区距离", required = false, example = "213")
    private Integer bufferDistance;

    @ApiModelProperty(value = "缓冲区WKT坐标", required = false, example = "缓冲区WKT坐标")
    @Size(max = 255, message = "缓冲区WKT坐标长度不能超出255位")
    private String bufferWktCoordinate;

    @ApiModelProperty(value = "POI", required = false, example = "229")
    private Integer poi;

    @ApiModelProperty(value = "AOI", required = false, example = "116")
    private Integer aoi;

    @ApiModelProperty(value = "商圈", required = false, example = "138")
    private Integer cbdName;

    @ApiModelProperty(value = "主干道", required = false, example = "43")
    private Integer arterialTraffic;

    @ApiModelProperty(value = "热力指数", required = false, example = "热力指数")
    @Size(max = 64, message = "热力指数长度不能超出64位")
    private String heatingIndex;

    @ApiModelProperty(value = "热力周期", required = false, example = "热力周期")
    @Size(max = 64, message = "热力周期长度不能超出64位")
    private String heatingPeriod;

    @ApiModelProperty(value = "媒体位置", required = false, example = "媒体位置")
    @Size(max = 64, message = "媒体位置长度不能超出64位")
    private String address;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TMainMediaOrderManLandPlacement() {
    }

    public TMainMediaOrderManLandPlacement(MainMediaOrderManLandPlacement mainMediaOrderManLandPlacement) {
        this.id = mainMediaOrderManLandPlacement.getId();
        this.placementType = mainMediaOrderManLandPlacement.getPlacementType();
        this.orderId = mainMediaOrderManLandPlacement.getOrderId();
        this.placementCode = mainMediaOrderManLandPlacement.getPlacementCode();
        this.mediumFormat = mainMediaOrderManLandPlacement.getMediumFormat();
        this.provinceCode = mainMediaOrderManLandPlacement.getProvinceCode();
        this.province = mainMediaOrderManLandPlacement.getProvince();
        this.cityCode = mainMediaOrderManLandPlacement.getCityCode();
        this.city = mainMediaOrderManLandPlacement.getCity();
        this.districtCode = mainMediaOrderManLandPlacement.getDistrictCode();
        this.district = mainMediaOrderManLandPlacement.getDistrict();
        this.mediumName = mainMediaOrderManLandPlacement.getMediumName();
        this.lineLevel = mainMediaOrderManLandPlacement.getLineLevel();
        this.launchPeriod = mainMediaOrderManLandPlacement.getLaunchPeriod();
        this.launchPeriodDay = mainMediaOrderManLandPlacement.getLaunchPeriodDay();
        this.neutralPosition = mainMediaOrderManLandPlacement.getNeutralPosition();
        this.publishCost = mainMediaOrderManLandPlacement.getPublishCost();
        this.makeCost = mainMediaOrderManLandPlacement.getMakeCost();
        this.bufferDistance = mainMediaOrderManLandPlacement.getBufferDistance();
        this.bufferWktCoordinate = mainMediaOrderManLandPlacement.getBufferWktCoordinate();
        this.poi = mainMediaOrderManLandPlacement.getPoi();
        this.aoi = mainMediaOrderManLandPlacement.getAoi();
        this.cbdName = mainMediaOrderManLandPlacement.getCbdName();
        this.arterialTraffic = mainMediaOrderManLandPlacement.getArterialTraffic();
        this.heatingIndex = mainMediaOrderManLandPlacement.getHeatingIndex();
        this.heatingPeriod = mainMediaOrderManLandPlacement.getHeatingPeriod();
        this.address = mainMediaOrderManLandPlacement.getAddress();
        this.createdByUser = mainMediaOrderManLandPlacement.getCreatedByUser();
        this.updatedByUser = mainMediaOrderManLandPlacement.getUpdatedByUser();
        this.creator = mainMediaOrderManLandPlacement.getCreator();
        this.updater = mainMediaOrderManLandPlacement.getUpdater();
        this.createTime = mainMediaOrderManLandPlacement.getCreateTime();
        this.updateTime = mainMediaOrderManLandPlacement.getUpdateTime();

    }
}
