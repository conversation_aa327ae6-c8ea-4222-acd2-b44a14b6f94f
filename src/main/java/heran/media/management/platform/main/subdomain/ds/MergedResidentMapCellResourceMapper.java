package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.main.subdomain.dto.ResidentCellData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MergedResidentMapCellResourceMapper {

    @Select({
            "<script>",
            "SELECT merge_id,center_lng,center_lat,people_count FROM resident_map_cell_resource ",
            "WHERE merge_id IN",
            "<foreach collection='mergeIdList' item='id' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    List<ResidentCellData> getResidentCellDataList(@Param("mergeIdList") List<Long> mergeIdList);

}
