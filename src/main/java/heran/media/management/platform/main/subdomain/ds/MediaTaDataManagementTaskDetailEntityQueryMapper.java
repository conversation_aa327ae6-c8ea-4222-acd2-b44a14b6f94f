package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.main.MediaTaDataManagementTaskDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface MediaTaDataManagementTaskDetailEntityQueryMapper {
    @SelectProvider(type = MediaTaDataManagementTaskDetailEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "managementTaskId", column = "management_task_id"), @Result(property = "placementCode", column = "placement_code"), @Result(property = "provinceCode", column = "province_code"), @Result(property = "province", column = "province"), @Result(property = "cityCode", column = "city_code"), @Result(property = "city", column = "city"), @Result(property = "districtCode", column = "district_code"), @Result(property = "district", column = "district"), @Result(property = "mediumFormat", column = "medium_format"), @Result(property = "mediumFiltrate", column = "medium_filtrate"), @Result(property = "mediumName", column = "medium_name"), @Result(property = "mediumAddress", column = "medium_address"), @Result(property = "placementCount", column = "placement_count"), @Result(property = "planLaunchStartTime", column = "plan_launch_start_time"), @Result(property = "planLaunchEndTime", column = "plan_launch_end_time"), @Result(property = "remark", column = "remark"), @Result(property = "extraAttrs", column = "extra_attrs"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<MediaTaDataManagementTaskDetail> list(SearchCriteria criteria);
}
