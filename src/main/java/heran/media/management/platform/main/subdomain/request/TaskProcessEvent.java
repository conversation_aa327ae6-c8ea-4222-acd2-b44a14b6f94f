package heran.media.management.platform.main.subdomain.request;

import heran.media.management.platform.main.service.DataTaskService;
import heran.media.sharelib.domain.db.model.main.DataTask;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TaskProcessEvent {
    private Integer userKey;
    private DataTask dataTask;
}
