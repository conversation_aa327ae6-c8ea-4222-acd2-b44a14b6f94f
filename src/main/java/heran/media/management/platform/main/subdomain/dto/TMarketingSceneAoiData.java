package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MarketingSceneAoiData;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.ToString;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TMarketingSceneAoiData implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "40")
    private Integer id;

	@ApiModelProperty(value = "查询月份", required = true, example = "查询月份")
    @Size(max=64, message = "查询月份长度不能超出64位")
    @NotBlank(message = "查询月份不能为空")
    private String statMonth;

	@ApiModelProperty(value = "区域编号", required = false, example = "省份编号")
    @Size(max=64, message = "区域编号长度不能超出64位")
    private String regionCode;

	@ApiModelProperty(value = "业务id", required = false, example = "AOI名称")
    @Size(max=64, message = "业务id")
    private Integer bizId;

	@ApiModelProperty(value = "数值", required = false, example = "53")
    private Integer aoiNum;

	@ApiModelProperty(value = "人口口径（到时候看可以不可以定义成字典数据）", required = false, example = "人口口径（到时候看可以不可以定义成字典数据）")
    @Size(max=64, message = "人口口径（到时候看可以不可以定义成字典数据）长度不能超出64位")
    private String poiPopulation;

	@ApiModelProperty(value = "请求内容", required = false, example = "请求内容")
    @Size(max=64, message = "请求内容长度不能超出64位")
    private String requestContent;

	@ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max=255, message = "说明长度不能超出255位")
    private String remark;

	@ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max=255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

	@ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

	@ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

	@ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max=64, message = "行记录创建标记长度不能超出64位")
    private String creator;

	@ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max=64, message = "行记录更新标记长度不能超出64位")
    private String updater;

	@ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

	@ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;

	

    public TMarketingSceneAoiData() {}

    public TMarketingSceneAoiData(MarketingSceneAoiData marketingSceneAoiData) {
        this.id=marketingSceneAoiData.getId();
	this.statMonth=marketingSceneAoiData.getStatMonth();
	this.regionCode=marketingSceneAoiData.getRegionCode();
	this.bizId=marketingSceneAoiData.getBizId();
	this.aoiNum=marketingSceneAoiData.getAoiNum();
	this.poiPopulation=marketingSceneAoiData.getPoiPopulation();
	this.requestContent=marketingSceneAoiData.getRequestContent();
	this.remark=marketingSceneAoiData.getRemark();
	this.extraAttrs=marketingSceneAoiData.getExtraAttrs();
	this.createdByUser=marketingSceneAoiData.getCreatedByUser();
	this.updatedByUser=marketingSceneAoiData.getUpdatedByUser();
	this.creator=marketingSceneAoiData.getCreator();
	this.updater=marketingSceneAoiData.getUpdater();
	this.createTime=marketingSceneAoiData.getCreateTime();
	this.updateTime=marketingSceneAoiData.getUpdateTime();
	
    }
}
