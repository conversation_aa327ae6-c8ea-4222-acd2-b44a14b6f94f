package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MediaTaDataManagementTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TMediaTaDataManagementTask implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "6")
    private Integer id;

    @ApiModelProperty(value = "查询月份", required = true, example = "查询月份")
    @Size(max = 64, message = "查询月份长度不能超出64位")
    @NotBlank(message = "查询月份不能为空")
    private String statMonth;

    @ApiModelProperty(value = "查询城市", required = true, example = "查询城市")
    @Size(max = 32, message = "查询城市长度不能超出32位")
    @NotBlank(message = "查询城市不能为空")
    private String regionCode;

    @ApiModelProperty(value = "任务ID（不知是否有有特殊ID 预留字段）", required = true, example = "任务ID（不知是否有有特殊ID 预留字段）")
    @Size(max = 64, message = "任务ID（不知是否有有特殊ID 预留字段）长度不能超出64位")
    @NotBlank(message = "任务ID（不知是否有有特殊ID 预留字段）不能为空")
    private String taskCode;

    @ApiModelProperty(value = "任务名称", required = true, example = "任务名称")
    @Size(max = 64, message = "任务名称长度不能超出64位")
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    @ApiModelProperty(value = "人群包id", required = true, example = "220")
    @NotNull(message = "人群包id不能为空")
    private Integer crowdPackId;

    @ApiModelProperty(value = "任务状态 PENDING_REQUEST（待请求）ON_REQUEST（请求中）FINISHED（已完成）", required = true, example = "任务状态 PENDING_REQUEST（待请求）ON_REQUEST（请求中）FINISHED（已完成）")
    @Size(max = 64, message = "任务状态 PENDING_REQUEST（待请求）ON_REQUEST（请求中）FINISHED（已完成）长度不能超出64位")
    @NotBlank(message = "任务状态 PENDING_REQUEST（待请求）ON_REQUEST（请求中）FINISHED（已完成）不能为空")
    private String taskStatus;

    @ApiModelProperty(value = "高德任务id", required = false, example = "高德任务id")
    @Size(max = 64, message = "高德任务id长度不能超出64位")
    private String mapTaskId;

    @ApiModelProperty(value = "TA人口统计", required = false, example = "TA人口统计")
    @Size(max = 64, message = "TA人口统计长度不能超出64位")
    private String taPopulation;

    @ApiModelProperty(value = "TA洞察维度", required = false, example = "TA洞察维度")
    @Size(max = 64, message = "TA洞察维度长度不能超出64位")
    private String taInsight;

    @ApiModelProperty(value = "TA网格热力指数", required = false, example = "TA网格热力指数")
    @Size(max = 64, message = "TA网格热力指数长度不能超出64位")
    private String taEnergetics;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TMediaTaDataManagementTask() {
    }

    public TMediaTaDataManagementTask(MediaTaDataManagementTask mediaTaDataManagementTask) {
        this.id = mediaTaDataManagementTask.getId();
        this.statMonth = mediaTaDataManagementTask.getStatMonth();
        this.regionCode = mediaTaDataManagementTask.getRegionCode();
        this.taskCode = mediaTaDataManagementTask.getTaskCode();
        this.taskName = mediaTaDataManagementTask.getTaskName();
        this.crowdPackId = mediaTaDataManagementTask.getCrowdPackId();
        this.taskStatus = mediaTaDataManagementTask.getTaskStatus();
        this.mapTaskId = mediaTaDataManagementTask.getMapTaskId();
        this.taPopulation = mediaTaDataManagementTask.getTaPopulation();
        this.taInsight = mediaTaDataManagementTask.getTaInsight();
        this.taEnergetics = mediaTaDataManagementTask.getTaEnergetics();
        this.remark = mediaTaDataManagementTask.getRemark();
        this.extraAttrs = mediaTaDataManagementTask.getExtraAttrs();
        this.createdByUser = mediaTaDataManagementTask.getCreatedByUser();
        this.updatedByUser = mediaTaDataManagementTask.getUpdatedByUser();
        this.creator = mediaTaDataManagementTask.getCreator();
        this.updater = mediaTaDataManagementTask.getUpdater();
        this.createTime = mediaTaDataManagementTask.getCreateTime();
        this.updateTime = mediaTaDataManagementTask.getUpdateTime();

    }
}
