package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.main.RouteSelectionScheme;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface RouteSelectionSchemeEntityQueryMapper {
    @SelectProvider(type = RouteSelectionSchemeEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "selectionPlanId", column = "selection_plan_id"), @Result(property = "regionCode", column = "region_code"), @Result(property = "filterCondition", column = "filter_condition"), @Result(property = "status", column = "status"), @Result(property = "remark", column = "remark"), @Result(property = "extraAttrs", column = "extra_attrs"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<RouteSelectionScheme> list(SearchCriteria criteria);
}
