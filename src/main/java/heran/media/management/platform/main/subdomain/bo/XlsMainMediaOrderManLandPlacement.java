package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.MainMediaOrderManLandPlacement;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@ToString
@Data
public class XlsMainMediaOrderManLandPlacement {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "类型MEDIUM_FORMAT(按媒介)CITY(按城市)", columnIndex = 1)
    private String placementType;

    @XlsField(title = "订单id", columnIndex = 2)
    private Integer orderId;

    @XlsField(title = "点位编号", columnIndex = 3)
    private String placementCode;

    @XlsField(title = "媒体形式", columnIndex = 4)
    private String mediumFormat;

    @XlsField(title = "省份编号", columnIndex = 5)
    private String provinceCode;

    @XlsField(title = "省份名称", columnIndex = 6)
    private String province;

    @XlsField(title = "城市编号", columnIndex = 7)
    private String cityCode;

    @XlsField(title = "城市名称", columnIndex = 8)
    private String city;

    @XlsField(title = "区域/县级市编号", columnIndex = 9)
    private String districtCode;

    @XlsField(title = "区域/县级市名称", columnIndex = 10)
    private String district;

    @XlsField(title = "媒体名称", columnIndex = 11)
    private String mediumName;

    @XlsField(title = "线路级别", columnIndex = 12)
    private String lineLevel;

    @XlsField(title = "投放周期", columnIndex = 13)
    private String launchPeriod;

    @XlsField(title = "投放周期/天", columnIndex = 14)
    private Integer launchPeriodDay;

    @XlsField(title = "已选空档", columnIndex = 15)
    private Integer neutralPosition;

    @XlsField(title = "发布费/辆/元", columnIndex = 16)
    private BigDecimal publishCost;

    @XlsField(title = "制作费/辆/元", columnIndex = 17)
    private BigDecimal makeCost;

    @XlsField(title = "缓冲区距离", columnIndex = 18)
    private Integer bufferDistance;

    @XlsField(title = "缓冲区WKT坐标", columnIndex = 19)
    private String bufferWktCoordinate;

    @XlsField(title = "POI", columnIndex = 20)
    private Integer poi;

    @XlsField(title = "AOI", columnIndex = 21)
    private Integer aoi;

    @XlsField(title = "商圈", columnIndex = 22)
    private Integer cbdName;

    @XlsField(title = "主干道", columnIndex = 23)
    private Integer arterialTraffic;

    @XlsField(title = "热力指数", columnIndex = 24)
    private String heatingIndex;

    @XlsField(title = "热力周期", columnIndex = 25)
    private String heatingPeriod;

    @XlsField(title = "媒体位置", columnIndex = 26)
    private String address;

    @XlsField(title = "行记录创建用户", columnIndex = 27)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 28)
    private Integer updatedByUser;


    public XlsMainMediaOrderManLandPlacement() {
    }

    public XlsMainMediaOrderManLandPlacement(MainMediaOrderManLandPlacement mainMediaOrderManLandPlacement) {
        this.id = mainMediaOrderManLandPlacement.getId();
        this.placementType = mainMediaOrderManLandPlacement.getPlacementType();
        this.orderId = mainMediaOrderManLandPlacement.getOrderId();
        this.placementCode = mainMediaOrderManLandPlacement.getPlacementCode();
        this.mediumFormat = mainMediaOrderManLandPlacement.getMediumFormat();
        this.provinceCode = mainMediaOrderManLandPlacement.getProvinceCode();
        this.province = mainMediaOrderManLandPlacement.getProvince();
        this.cityCode = mainMediaOrderManLandPlacement.getCityCode();
        this.city = mainMediaOrderManLandPlacement.getCity();
        this.districtCode = mainMediaOrderManLandPlacement.getDistrictCode();
        this.district = mainMediaOrderManLandPlacement.getDistrict();
        this.mediumName = mainMediaOrderManLandPlacement.getMediumName();
        this.lineLevel = mainMediaOrderManLandPlacement.getLineLevel();
        this.launchPeriod = mainMediaOrderManLandPlacement.getLaunchPeriod();
        this.launchPeriodDay = mainMediaOrderManLandPlacement.getLaunchPeriodDay();
        this.neutralPosition = mainMediaOrderManLandPlacement.getNeutralPosition();
        this.publishCost = mainMediaOrderManLandPlacement.getPublishCost();
        this.makeCost = mainMediaOrderManLandPlacement.getMakeCost();
        this.bufferDistance = mainMediaOrderManLandPlacement.getBufferDistance();
        this.bufferWktCoordinate = mainMediaOrderManLandPlacement.getBufferWktCoordinate();
        this.poi = mainMediaOrderManLandPlacement.getPoi();
        this.aoi = mainMediaOrderManLandPlacement.getAoi();
        this.cbdName = mainMediaOrderManLandPlacement.getCbdName();
        this.arterialTraffic = mainMediaOrderManLandPlacement.getArterialTraffic();
        this.heatingIndex = mainMediaOrderManLandPlacement.getHeatingIndex();
        this.heatingPeriod = mainMediaOrderManLandPlacement.getHeatingPeriod();
        this.address = mainMediaOrderManLandPlacement.getAddress();
        this.createdByUser = mainMediaOrderManLandPlacement.getCreatedByUser();
        this.updatedByUser = mainMediaOrderManLandPlacement.getUpdatedByUser();

    }

    public MainMediaOrderManLandPlacement adapToPO(Integer userId) {
        MainMediaOrderManLandPlacement mainMediaOrderManLandPlacement = new MainMediaOrderManLandPlacement();
        mainMediaOrderManLandPlacement.setId(id);
        mainMediaOrderManLandPlacement.setPlacementType(placementType);
        mainMediaOrderManLandPlacement.setOrderId(orderId);
        mainMediaOrderManLandPlacement.setPlacementCode(placementCode);
        mainMediaOrderManLandPlacement.setMediumFormat(mediumFormat);
        mainMediaOrderManLandPlacement.setProvinceCode(provinceCode);
        mainMediaOrderManLandPlacement.setProvince(province);
        mainMediaOrderManLandPlacement.setCityCode(cityCode);
        mainMediaOrderManLandPlacement.setCity(city);
        mainMediaOrderManLandPlacement.setDistrictCode(districtCode);
        mainMediaOrderManLandPlacement.setDistrict(district);
        mainMediaOrderManLandPlacement.setMediumName(mediumName);
        mainMediaOrderManLandPlacement.setLineLevel(lineLevel);
        mainMediaOrderManLandPlacement.setLaunchPeriod(launchPeriod);
        mainMediaOrderManLandPlacement.setLaunchPeriodDay(launchPeriodDay);
        mainMediaOrderManLandPlacement.setNeutralPosition(neutralPosition);
        mainMediaOrderManLandPlacement.setPublishCost(publishCost);
        mainMediaOrderManLandPlacement.setMakeCost(makeCost);
        mainMediaOrderManLandPlacement.setBufferDistance(bufferDistance);
        mainMediaOrderManLandPlacement.setBufferWktCoordinate(bufferWktCoordinate);
        mainMediaOrderManLandPlacement.setPoi(poi);
        mainMediaOrderManLandPlacement.setAoi(aoi);
        mainMediaOrderManLandPlacement.setCbdName(cbdName);
        mainMediaOrderManLandPlacement.setArterialTraffic(arterialTraffic);
        mainMediaOrderManLandPlacement.setHeatingIndex(heatingIndex);
        mainMediaOrderManLandPlacement.setHeatingPeriod(heatingPeriod);
        mainMediaOrderManLandPlacement.setAddress(address);

        mainMediaOrderManLandPlacement.setCreatedByUser(userId);
        mainMediaOrderManLandPlacement.setUpdatedByUser(userId);
        return mainMediaOrderManLandPlacement;
    }
}
