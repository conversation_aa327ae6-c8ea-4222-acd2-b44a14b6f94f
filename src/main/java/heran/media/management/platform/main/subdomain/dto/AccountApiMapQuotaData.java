package heran.media.management.platform.main.subdomain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class AccountApiMapQuotaData {

    @ApiModelProperty(value = "配额", required = true, example = "10")
    private Integer quota;
    @ApiModelProperty(value = "业务类型", required = true, example = "POI_BRAND_STATISTICS")
    private String bizType;
    @ApiModelProperty(value = "业务名称", required = false, example = "品牌 POI 配額")
    private String bizName;
    @ApiModelProperty(value = "今日使用次数", required = false, example = "品牌 POI 配額")
    private Integer todayUsed;
    @ApiModelProperty(value = "备注", required = false, example = "")
    private String remark;


}
