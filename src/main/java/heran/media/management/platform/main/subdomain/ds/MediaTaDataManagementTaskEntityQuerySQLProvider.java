package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.google.gson.Gson;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.request.ExportTaOfflineDataRequest;
import heran.media.sharelib.domain.db.model.MainDictRegionData;
import org.apache.ibatis.jdbc.SQL;

import java.util.Arrays;
import java.util.List;

public class MediaTaDataManagementTaskEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("media_ta_data_management_task");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if (cri.getKey().equals("id")) {
                        WHERE("id = " + cri.getValue());
                    } else if (cri.getKey().equals("statMonth") && cri.getValue() != null) {
                        WHERE("stat_month like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("regionCode") && cri.getValue() != null) {
                        WHERE("region_code like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("taskCode") && cri.getValue() != null) {
                        WHERE("task_code like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("taskName") && cri.getValue() != null) {
                        WHERE("task_name like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("crowdPackId")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("crowd_pack_id between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("crowd_pack_id >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("crowd_pack_id <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("crowd_pack_id = " + cri.getValue());
                    } else if (cri.getKey().equals("taskStatus") && cri.getValue() != null) {
                        WHERE("task_status like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("mapTaskId") && cri.getValue() != null) {
                        WHERE("map_task_id like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("taPopulation") && cri.getValue() != null) {
                        WHERE("ta_population like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("taInsight") && cri.getValue() != null) {
                        WHERE("ta_Insight like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("taEnergetics") && cri.getValue() != null) {
                        WHERE("ta_energetics like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("remark") && cri.getValue() != null) {
                        WHERE("remark like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("extraAttrs") && cri.getValue() != null) {
                        WHERE("extra_attrs like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("createdByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("created_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("created_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("created_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("created_by_user = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("updatedByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("updated_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("updated_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("updated_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("updated_by_user = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }

                }
            }
            ORDER_BY("update_time desc");
        }}.toString();
    }

    public String selectList(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t1.id,t1.task_code,t1.task_name,t1.crowd_pack_id,t2.pack_name,t1.region_code,t1.stat_month,t1.ta_energetics,t1.ta_population,t1.ta_Insight,t1.task_status,t1.ta_type,t1.update_time,t1.map_task_id");
            FROM("media_ta_data_management_task t1");
            LEFT_OUTER_JOIN("media_ta_crowd_pack t2 ON t1.crowd_pack_id = t2.id");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("t1.id = " + cri.getValue());
                    } else if ("taskCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.task_code like '%" + cri.getValue() + "%'");
                    } else if ("taskName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.task_name like '%" + cri.getValue() + "%'");
                    } else if ("crowdPackName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t2.pack_name like '%" + cri.getValue() + "%'");
                    } else if ("crowdPackId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t2.id IN( " + cri.getValue() + ")");
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("t1.stat_month >= '" + cri.getMinValue() + "' AND t1.stat_month <= '" + cri.getMaxValue() + "'");
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("t1.stat_month >= '" + cri.getMinValue() + "'");
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("t1.stat_month <= '" + cri.getMaxValue() + "'");
                        } else if (cri.getValue() != null) {
                            WHERE("t1.stat_month = '" + cri.getValue() + "'");
                        }
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        List<String> regionCodeList = Arrays.asList(cri.getValue().split(","));
                        if (CollectionUtils.isNotEmpty(regionCodeList)) {
                            Gson gson = new Gson();
                            String region = gson.toJson(regionCodeList);
                            WHERE("JSON_OVERLAPS(t1.region_code, '" + region + "')");
                        }
                    } else if ("mapTaskId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.map_task_id like '%" + cri.getValue() + "%'");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            ORDER_BY("update_time desc");
        }}.toString();
    }


    public String taDataSelectList(SearchCriteria criteria) {
        String withPart = "WITH " +
                "cell_hot_value_wkt AS ( " +
                "SELECT " +
                "data_id,stat_month,region_code,GROUP_CONCAT(DISTINCT statistical_type SEPARATOR ',') AS wktTypes " +
                "FROM cell_hot_value_wkt_config WHERE statistical_type IN('TA_RESIDENT_POPULATION','TA_PASSENGER_POPULATION') " +
                "GROUP BY stat_month, region_code, data_id ), " +
                "heat_data AS ( " +
                "SELECT task_id, stat_month, region_code, GROUP_CONCAT( DISTINCT ta_population SEPARATOR ',' ) AS statisticalTypes " +
                "FROM ta_grid_task_heat_data WHERE heat = 100 " +
                "GROUP BY task_id, stat_month, region_code ) ";
        return withPart + new SQL() {{
            SELECT("t1.id,t2.id taskId,t2.crowd_pack_id, t3.pack_name,t1.region_code,t2.stat_month,t2.map_task_id,t1.populace_count,t1.update_time,t2.ta_population,ch.wktTypes,hd.statisticalTypes");
            SELECT("(SELECT 1 FROM poi_task_offline_scene_detail t6 WHERE t6.task_id = t2.id AND t6.region_code = t1.region_code LIMIT 1 ) as taOffline");
            FROM("media_ta_data_management_task_detail t1");
            JOIN("media_ta_data_management_task t2 ON t1.management_task_id = t2.id ");
            LEFT_OUTER_JOIN("cell_hot_value_wkt ch ON ch.stat_month = t1.stat_month AND ch.region_code = t1.region_code AND ch.data_id = t1.management_task_id");
            LEFT_OUTER_JOIN("media_ta_crowd_pack t3 ON t3.id = t2.crowd_pack_id ");
            LEFT_OUTER_JOIN("heat_data hd ON hd.task_id = t1.management_task_id AND hd.stat_month = t1.stat_month AND hd.region_code = t1.region_code");

            if (criteria.getCriterias() != null && !criteria.getCriterias().isEmpty()) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("t1.id = " + cri.getValue());
                    } else if ("taskName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t2.task_name like '%" + cri.getValue() + "%'");
                    } else if ("crowdPackId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t3.id IN( " + cri.getValue() + ")");
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t2.stat_month = " + cri.getValue());
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.region_code IN(" + cri.getValue() + ")");
                    } else if ("mapTaskId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t2.map_task_id like '%" + cri.getValue() + "%'");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            ORDER_BY("t1.update_time desc");
        }}.toString();
    }


    public String listTaskHeatData(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t2.task_name,t2.map_task_id,t4.pack_name,t3.region_name,t1.stat_month,t1.cell_id,t1.cell_size,t1.center_lng,t1.center_lat,t1.heat,t2.ta_population");
            FROM("ta_grid_task_heat_data t1");
            JOIN("media_ta_data_management_task t2 ON t1.task_id = t2.id");
            LEFT_OUTER_JOIN("main_dict_region t3 ON t3.`code` = t1.region_code");
            LEFT_OUTER_JOIN("media_ta_crowd_pack t4 ON t4.id = t2.crowd_pack_id");
            if (criteria.getCriterias() != null && !criteria.getCriterias().isEmpty()) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("t1.id = " + cri.getValue());
                    } else if ("taskName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t2.task_name like '%" + cri.getValue() + "%'");
                    } else if ("crowdPackId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t4.id IN( " + cri.getValue() + ")");
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.stat_month = " + cri.getValue());
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.region_code IN(" + cri.getValue() + ")");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
        }}.toString();
    }


    public String getTaOfflineSceneDataList(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t1.id,t3.id packId,t3.pack_name packName,t1.region_code,t1.stat_month,t1.tag_name,t1.tag_value,t1.is_update,t1.update_tag_value,t1.update_time");
            FROM("poi_task_offline_scene_detail t1");
            JOIN("media_ta_data_management_task t2 ON t1.task_id = t2.id");
            LEFT_OUTER_JOIN("media_ta_crowd_pack t3 ON t3.id = t2.crowd_pack_id");
            if (criteria.getCriterias() != null && !criteria.getCriterias().isEmpty()) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("t1.id = " + cri.getValue());
                    } else if ("taskId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t2.id = " + cri.getValue());
                    } else if ("isDeleted".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.is_deleted = " + cri.getValue());
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.stat_month = " + cri.getValue());
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.region_code IN(" + cri.getValue() + ")");
                    } else if ("level".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.level = " + cri.getValue());
                    } else if ("parentId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.parent_id = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
        }}.toString();
    }


    public String recoverTaData(SearchCriteria criteria) {
        return new SQL() {{
            UPDATE("poi_task_offline_scene_detail");
            SET("update_tag_value = NULL");
            SET("is_update = 0");
            SET("is_deleted = 0");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("region_code IN(" + cri.getValue() + ")");
                    } else if ("statMonth".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("stat_month = '" + cri.getValue() + "'");
                    } else if ("taskId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t2.id = " + cri.getValue());
                    } else if ("ids".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("id iN(" + cri.getValue() + ")");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
        }}.toString();
    }


    public String selectTaOfflineExport(ExportTaOfflineDataRequest request) {
        return new SQL() {{
            SELECT("t1.tag_name headingName ,t1.tag_value headingRatio,t2.tag_name mediumName,t2.tag_value mediumRatio,t1.is_update headingIsUpdate,t1.update_tag_value headingUpdateValue,t2.is_update mediumIsUpdate,t2.update_tag_value mediumUpdateValue");
            FROM("poi_task_offline_scene_detail t1");
            JOIN("poi_task_offline_scene_detail t2 ON t1.id = t2.parent_id");
            if (request.getTaskId() != null) {
                WHERE("t2.task_id = " + request.getTaskId());
            }
            if (StringUtils.isNotEmpty(request.getRegionCode())) {
                WHERE("t1.region_code IN(" + request.getRegionCode() + ")");
            }
            if (StringUtils.isNotEmpty(request.getStatMonth())) {
                WHERE("t1.stat_month = '" + request.getStatMonth() + "'");
            }
            WHERE("t2.`level` = 2");
            if (!request.getIsBackups()) {
                WHERE("t1.is_deleted = false AND t2.is_deleted = false");
            }
        }}.toString();
    }


    public String getExportCityIncludeTaDataList(Integer taskIdResident, Integer taskIdPassenger, String statMonth, String regionCode, MainDictRegionData dictRegionData) {
        return new SQL() {{
            SELECT("t1.city_code,t4.cell_id,CONCAT( t1.center_lng, ',', t1.center_lat ) AS center," +
                    "CONCAT( t1.up_left_lng, ',', t1.up_left_lat ) AS upLeft," +
                    "CONCAT( t1.lower_left_lng, ',', t1.lower_left_lat ) AS lowerLeft," +
                    "CONCAT( t1.lower_right_lng, ',', t1.lower_right_lat ) AS lowerRight," +
                    "CONCAT( t1.up_right_lng, ',', t1.up_right_lat ) AS upRight," +
                    "t1.people_count," +
                    "COUNT( t4.id ) AS cell_count");
            FROM("merged_resident_map_cell_resource t1");
            if (taskIdResident != null) {
                SELECT("t2.heat residentHeat");
                LEFT_OUTER_JOIN("ta_grid_task_heat_data t2 ON t1.center_lng = t2.center_lng AND t1.center_lat = t2.center_lat AND t2.ta_population = 'TA_RESIDENT_POPULATION' AND t2.stat_month = '" + statMonth + "' AND t2.region_code = '" + regionCode + "' AND t2.task_id = " + taskIdResident);
            }
            if (taskIdPassenger != null) {
                SELECT("t3.heat passengerHeat");
                LEFT_OUTER_JOIN("ta_grid_task_heat_data t3 ON t1.center_lng = t3.center_lng AND t1.center_lat = t3.center_lat AND t3.ta_population = 'TA_PASSENGER_POPULATION' AND t3.stat_month = '" + statMonth + "' AND t3.region_code = '" + regionCode + "' AND t3.task_id = " + taskIdPassenger);
            }
            LEFT_OUTER_JOIN("resident_map_cell_resource t4 ON t1.id = t4.merge_id");
            if (dictRegionData.getLevel() == 3) {
                WHERE("t1.area_code = '" + regionCode + "'");
            } else {
                WHERE("t1.city_code = '" + regionCode + "'");
            }
            GROUP_BY("t1.id");
        }}.toString();
    }

}
