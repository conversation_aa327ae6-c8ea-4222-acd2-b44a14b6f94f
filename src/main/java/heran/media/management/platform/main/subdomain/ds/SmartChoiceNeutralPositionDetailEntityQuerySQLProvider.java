package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class SmartChoiceNeutralPositionDetailEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("smart_choice_neutral_position_detail");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                     }
                    if(cri.getKey().equals("id")){
                        WHERE("id = "+ cri.getValue());
                    }
                    else if(cri.getKey().equalsIgnoreCase("selectionPlanId")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("selection_plan_id between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("selection_plan_id >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("selection_plan_id <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("selection_plan_id = "+ cri.getValue());
                    }else if(cri.getKey().equals("placementCode") && cri.getValue()!=null){
                        WHERE("placement_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("provinceCode") && cri.getValue()!=null){
                        WHERE("province_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("province") && cri.getValue()!=null){
                        WHERE("province like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("cityCode") && cri.getValue()!=null){
                        WHERE("city_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("city") && cri.getValue()!=null){
                        WHERE("city like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("districtCode") && cri.getValue()!=null){
                        WHERE("district_code like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("district") && cri.getValue()!=null){
                        WHERE("district like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("mediumFormat") && cri.getValue()!=null){
                        WHERE("medium_format like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("mediumFiltra") && cri.getValue()!=null){
                        WHERE("medium_filtra like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("mediumName") && cri.getValue()!=null){
                        WHERE("medium_name like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("mediumAddress") && cri.getValue()!=null){
                        WHERE("medium_address like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("placementCount")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("placement_count between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("placement_count >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("placement_count <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("placement_count = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("planLaunchStartTime")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("plan_launch_start_time between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("plan_launch_start_time >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("plan_launch_start_time <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("plan_launch_start_time = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("planLaunchEndTime")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("plan_launch_end_time between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("plan_launch_end_time >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("plan_launch_end_time <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("plan_launch_end_time = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("createdByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("created_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("created_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("created_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("created_by_user = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("updatedByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("updated_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("updated_by_user = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }

                }
            };
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
