package heran.media.management.platform.main.subdomain.response;

import heran.media.sharelib.domain.db.model.main.MainResourceCategory;
import heran.media.sharelib.domain.db.model.main.MediaTaCrowdPack;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MainResourceCategoryResponse {
    @ApiModelProperty("主键")
    private Integer id;
    @ApiModelProperty("分类名称")
    private String categoryName;
    @ApiModelProperty("父id")
    private Integer parentId;
    @ApiModelProperty("级别")
    private Integer level;
    @ApiModelProperty("人群包数据")
    private List<MediaTaCrowdPack> crowdPacks;
    @ApiModelProperty("子集")
    private List<MainResourceCategoryResponse> resourceCategoryResponses;

    public MainResourceCategoryResponse(MainResourceCategory category) {
        this.id = category.getId();
        this.categoryName = category.getCategoryName();
        this.parentId = category.getParentId();
        this.level = category.getLevel();
    }
}
