package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.google.gson.Gson;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

import java.util.Arrays;
import java.util.List;

public class DataTaskEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("data_task");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if (cri.getKey().equals("id")) {
                        WHERE("id = " + cri.getValue());
                    } else if (cri.getKey().equals("taskName") && cri.getValue() != null) {
                        WHERE("task_name like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("taskStatus") && cri.getValue() != null) {
                        WHERE("task_status like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("regionCode") && cri.getValue() != null) {
                        WHERE("region_code like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("statMonth") && cri.getValue() != null) {
                        WHERE("stat_month like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("disposeTime")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("dispose_time between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("dispose_time >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("dispose_time <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("dispose_time = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("numberSmartCity")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("number_smart_city between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("number_smart_city >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("number_smart_city <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("number_smart_city = " + cri.getValue());
                    } else if (cri.getKey().equals("numberSmartCityRequest") && cri.getValue() != null) {
                        WHERE("number_smart_city_request like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("numberSmartTa")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("number_smart_ta between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("number_smart_ta >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("number_smart_ta <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("number_smart_ta = " + cri.getValue());
                    } else if (cri.getKey().equals("numberSmartTaRequest") && cri.getValue() != null) {
                        WHERE("number_smart_ta_request like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("cbdInsight")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("cbd_Insight between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("cbd_Insight >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("cbd_Insight <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("cbd_Insight = " + cri.getValue());
                    } else if (cri.getKey().equals("cbdInsightRequest") && cri.getValue() != null) {
                        WHERE("cbd_Insight_request like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("specificSceneInsight")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("specific_scene_Insight between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("specific_scene_Insight >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("specific_scene_Insight <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("specific_scene_Insight = " + cri.getValue());
                    } else if (cri.getKey().equals("specificSceneInsightRequest") && cri.getValue() != null) {
                        WHERE("specific_scene_Insight_request like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("numberSmartCityPerspective")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("number_smart_city_perspective between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("number_smart_city_perspective >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("number_smart_city_perspective <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("number_smart_city_perspective = " + cri.getValue());
                    } else if (cri.getKey().equals("numberSmartCityPerspectiveRequest") && cri.getValue() != null) {
                        WHERE("number_smart_city_perspective_request like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("remark") && cri.getValue() != null) {
                        WHERE("remark like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equals("extraAttrs") && cri.getValue() != null) {
                        WHERE("extra_attrs like '%" + cri.getValue() + "%'");
                    } else if (cri.getKey().equalsIgnoreCase("createdByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("created_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("created_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("created_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("created_by_user = " + cri.getValue());
                    } else if (cri.getKey().equalsIgnoreCase("updatedByUser")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("updated_by_user between " + cri.getMinValue() + " and " + cri.getMaxValue());
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("updated_by_user >= " + cri.getMinValue());
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("updated_by_user <= " + cri.getMaxValue());
                        } else if (cri.getValue() != null)
                            WHERE("updated_by_user = " + cri.getValue());
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }

                }
            }
            ;
            ORDER_BY("update_time desc");
        }}.toString();
    }

    public String selectList(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t1.id,t1.task_name,t1.region_code_all,t1.stat_month_all,t1.task_status,t1.update_time");
            FROM("data_task t1");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("t1.id = " + cri.getValue());
                    } else if ("taskName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.task_name like '%" + cri.getValue() + "%'");
                    } else if ("regionCode".equals(cri.getKey()) && cri.getValue() != null) {
                        List<String> regionCodeList = Arrays.asList(cri.getValue().split(","));
                        if (CollectionUtils.isNotEmpty(regionCodeList)) {
                            Gson gson = new Gson();
                            String region = gson.toJson(regionCodeList);
                            WHERE("JSON_OVERLAPS(t1.region_code_all, '" + region + "')");
                        }
                    } else if ("statMonth".equals(cri.getKey())) {
                        List<String> statMonthList = Arrays.asList(cri.getValue().split(","));
                        if (CollectionUtils.isNotEmpty(statMonthList)) {
                            Gson gson = new Gson();
                            String region = gson.toJson(statMonthList);
                            WHERE("JSON_OVERLAPS(t1.stat_month_all, '" + region + "')");
                        }
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            ORDER_BY("t1.update_time desc");
        }}.toString();
    }
}
