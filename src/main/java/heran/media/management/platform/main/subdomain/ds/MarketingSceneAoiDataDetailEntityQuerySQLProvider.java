package heran.media.management.platform.main.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class MarketingSceneAoiDataDetailEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("*");
            FROM("marketing_scene_aoi_data_detail");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                     }
                    if(cri.getKey().equals("id")){
                        WHERE("id = "+ cri.getValue());
                    }
                    else if(cri.getKey().equalsIgnoreCase("marketingSceneAoiDataId")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("marketing_scene_aoi_data_id between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("marketing_scene_aoi_data_id >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("marketing_scene_aoi_data_id <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("marketing_scene_aoi_data_id = "+ cri.getValue());
                    }else if(cri.getKey().equals("trafficStatistics") && cri.getValue()!=null){
                        WHERE("traffic_statistics like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("passengerPortrayal") && cri.getValue()!=null){
                        WHERE("passenger_portrayal like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("tripWay") && cri.getValue()!=null){
                        WHERE("trip_way like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("passengerSource") && cri.getValue()!=null){
                        WHERE("passenger_source like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("demographicStatistics") && cri.getValue()!=null){
                        WHERE("demographic_statistics like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("crowdPortrayal") && cri.getValue()!=null){
                        WHERE("crowd_portrayal like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("apiTripWay") && cri.getValue()!=null){
                        WHERE("api_trip_way like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("crowdSource") && cri.getValue()!=null){
                        WHERE("crowd_source like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("offlineScene") && cri.getValue()!=null){
                        WHERE("offline_scene like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("remark") && cri.getValue()!=null){
                        WHERE("remark like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equals("extraAttrs") && cri.getValue()!=null){
                        WHERE("extra_attrs like '%"+ cri.getValue()+"%'");
                    }else if(cri.getKey().equalsIgnoreCase("createdByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("created_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("created_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("created_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("created_by_user = "+ cri.getValue());
                    }else if(cri.getKey().equalsIgnoreCase("updatedByUser")){
                        if(cri.getMinValue()!=null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user between "+ cri.getMinValue()+" and "+ cri.getMaxValue());
                        }
                        else if(cri.getMinValue()!=null && cri.getMaxValue()==null){
                            WHERE("updated_by_user >= "+ cri.getMinValue());
                        }
                        else if(cri.getMinValue()==null && cri.getMaxValue()!=null){
                            WHERE("updated_by_user <= "+ cri.getMaxValue());
                        }else if(cri.getValue()!=null)
                            WHERE("updated_by_user = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }

                }
            };
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
