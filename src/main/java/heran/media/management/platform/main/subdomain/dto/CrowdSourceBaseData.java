package heran.media.management.platform.main.subdomain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrowdSourceBaseData {
    /**
     * 名称
     */
    private String name;
    /**
     * poiId
     */
    private String poiId;
    /**
     * 占比
     */
    private String proportion;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 维度
     */
    private String latitude;

    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 区域名称
     */
    private String districtName;
    /**
     * 城市编号
     */
    private String cityCode;
    /**
     * 区域编号
     */
    private String districtCode;
}
