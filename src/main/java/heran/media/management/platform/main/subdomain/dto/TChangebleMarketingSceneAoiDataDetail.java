package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.MarketingSceneAoiDataDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleMarketingSceneAoiDataDetail implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "182")
    private Integer id;

    @ApiModelProperty(value = "营销场景AOI数据id", required = true, example = "246")
    @NotNull(message = "营销场景AOI数据id不能为空")
    private Integer marketingSceneAoiDataId;

    @ApiModelProperty(value = "流量统计", required = false, example = "流量统计")
    private String trafficStatistics;

    @ApiModelProperty(value = "客流画像", required = false, example = "客流画像")
    private String passengerPortrayal;

    @ApiModelProperty(value = "出行方式", required = false, example = "出行方式")
    private String tripWay;

    @ApiModelProperty(value = "客流来源", required = false, example = "客流来源")
    private String passengerSource;

    @ApiModelProperty(value = "人口统计", required = false, example = "人口统计")
    private String demographicStatistics;

    @ApiModelProperty(value = "人群画像", required = false, example = "人群画像")
    private String crowdPortrayal;

    @ApiModelProperty(value = "AOI洞察出行方式", required = false, example = "AOI洞察出行方式")
    private String apiTripWay;

    @ApiModelProperty(value = "人群来源", required = false, example = "人群来源")
    private String crowdSource;

    @ApiModelProperty(value = "线下场景偏好", required = false, example = "线下场景偏好")
    private String offlineScene;

    @ApiModelProperty(value = "说明", required = false, example = "说明")
    @Size(max = 255, message = "说明长度不能超出255位")
    private String remark;

    @ApiModelProperty(value = "扩展字段", required = false, example = "扩展字段")
    @Size(max = 255, message = "扩展字段长度不能超出255位")
    private String extraAttrs;


    public TChangebleMarketingSceneAoiDataDetail() {
    }

    public MarketingSceneAoiDataDetail adapToPO() {
        MarketingSceneAoiDataDetail marketingSceneAoiDataDetail = new MarketingSceneAoiDataDetail();
        marketingSceneAoiDataDetail.setId(id);
        marketingSceneAoiDataDetail.setMarketingSceneAoiDataId(marketingSceneAoiDataId);
        marketingSceneAoiDataDetail.setTrafficStatistics(trafficStatistics);
        marketingSceneAoiDataDetail.setPassengerPortrayal(passengerPortrayal);
        marketingSceneAoiDataDetail.setTripWay(tripWay);
        marketingSceneAoiDataDetail.setPassengerSource(passengerSource);
        marketingSceneAoiDataDetail.setDemographicStatistics(demographicStatistics);
        marketingSceneAoiDataDetail.setCrowdPortrayal(crowdPortrayal);
        marketingSceneAoiDataDetail.setApiTripWay(apiTripWay);
        marketingSceneAoiDataDetail.setCrowdSource(crowdSource);
        marketingSceneAoiDataDetail.setOfflineScene(offlineScene);
        marketingSceneAoiDataDetail.setRemark(remark);
        marketingSceneAoiDataDetail.setExtraAttrs(extraAttrs);

        return marketingSceneAoiDataDetail;
    }
}
