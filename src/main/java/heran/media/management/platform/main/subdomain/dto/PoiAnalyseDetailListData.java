package heran.media.management.platform.main.subdomain.dto;

import heran.media.sharelib.domain.db.model.main.PoiAnalyseDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PoiAnalyseDetailListData extends PoiAnalyseDetail {
    @ApiModelProperty("省份")
    private String provinceName;
    @ApiModelProperty("城市")
    private String cityName;
    @ApiModelProperty("区域")
    private String districtName;
}
