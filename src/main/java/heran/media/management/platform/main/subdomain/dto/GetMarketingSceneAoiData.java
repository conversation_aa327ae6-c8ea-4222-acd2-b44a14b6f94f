package heran.media.management.platform.main.subdomain.dto;

import lombok.Data;
import org.codehaus.jettison.json.JSONObject;

/**
 * <AUTHOR>
 */
@Data
public class GetMarketingSceneAoiData {
    /**
     * 行政编码
     */
    private String regionCode;
    /**
     * 流量统计
     */
    private String trafficStatistics;
    /**
     * 客流画像
     */
    private String passengerPortrayal;
    /**
     * 出行方式
     */
    private String tripWay;
    /**
     * 客流来源
     */
    private String passengerSource;
    /**
     * 人口统计
     */
    private String demographicStatistics;
    /**
     * 人群画像
     */
    private String crowdPortrayal;
    /**
     * AOI洞察出行方式
     */
    private String apiTripWay;
    /**
     * 人群来源
     */
    private String crowdSource;
    /**
     * 线下场景偏好
     */
    private String offlineScene;
}
