package heran.media.management.platform.main.subdomain.bo;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.main.subdomain.dto.TaOfflineData;
import heran.media.management.platform.main.subdomain.request.ExportTaOfflineDataRequest;
import heran.media.sharelib.domain.db.model.main.MediaTaCrowdPack;
import heran.media.sharelib.domain.dto.hot.MainDictRegionNameData;
import lombok.Data;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
@Data
public class XlsTaOfflineDataList {
    @XlsField(title = "人群包id", columnIndex = 0)
    private Integer packId;
    @XlsField(title = "人群包名称", columnIndex = 1)
    private String packName;
    @XlsField(title = "城市", columnIndex = 2)
    private String region;
    @XlsField(title = "查询时间", columnIndex = 3)
    private String statMonth;
    @XlsField(title = "大类", columnIndex = 4)
    private String headingName;
    @XlsField(title = "大类占比", columnIndex = 5)
    private String headingRatio;
    @XlsField(title = "中类", columnIndex = 6)
    private String mediumName;
    @XlsField(title = "中类占比", columnIndex = 7)
    private String mediumRatio;

    public XlsTaOfflineDataList(TaOfflineData data, MainDictRegionNameData regionNameData, MediaTaCrowdPack mediaTaCrowdPack, ExportTaOfflineDataRequest request) {
        this.packId = mediaTaCrowdPack.getId();
        this.packName = mediaTaCrowdPack.getPackName();

        StringBuilder cityNameBuilder = new StringBuilder();
        if (StringUtils.isNotEmpty(regionNameData.getLv1Name())) {
            cityNameBuilder.append(regionNameData.getLv1Name()).append("/");
        }
        if (StringUtils.isNotEmpty(regionNameData.getLv2Name())) {
            cityNameBuilder.append(regionNameData.getLv2Name()).append("/");
        }
        if (StringUtils.isNotEmpty(regionNameData.getLv3Name())) {
            cityNameBuilder.append(regionNameData.getLv3Name());
        }
        this.region = cityNameBuilder.toString();

        String hadingValue = data.getHeadingIsUpdate() ? data.getHeadingUpdateValue() : data.getHeadingRatio();
        String mediumValue = data.getMediumIsUpdate() ? data.getMediumUpdateValue() : data.getMediumRatio();

        if (request.getIsBackups()){
            hadingValue = data.getHeadingRatio();
            mediumValue = data.getMediumRatio();
        }

        this.statMonth = convertWithDate(request.getStatMonth());

        this.headingName = data.getHeadingName();
        this.mediumName = data.getMediumName();
        double headingRatioValue = new BigDecimal(hadingValue).multiply(new BigDecimal(100)).doubleValue();
        this.headingRatio = headingRatioValue + "%";
        double mediumRatioValue = new BigDecimal(mediumValue).multiply(new BigDecimal(100)).doubleValue();
        this.mediumRatio = mediumRatioValue + "%";
    }

    public String convertWithDate(String input) {
        try {
            DateTimeFormatter parser = DateTimeFormatter.ofPattern("yyyyMM");
            YearMonth ym = YearMonth.parse(input, parser);
            return ym.getYear() + "年" + ym.getMonthValue() + "月";
        } catch (Exception e) {
            return "非法格式";
        }
    }
}
