package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.StatisticsDimension;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsStatisticsDimension {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "查询月份", columnIndex = 1)
    private String statMonth;

    @XlsField(title = "查询城市", columnIndex = 2)
    private String regionCode;

    @XlsField(title = "统计类型DWELL_POPULATION（居住人口）WORK_POPULATION(常驻人口)WORK_POPULATION(工作人口)PASSENGE_POPULATION(客流人口)", columnIndex = 3)
    private String statisticalType;

    @XlsField(title = "统计维度名称", columnIndex = 4)
    private String dimensionName;

    @XlsField(title = "统计维度值 JOIN", columnIndex = 5)
    private String dimensionValue;

    @XlsField(title = "行记录创建用户", columnIndex = 6)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 7)
    private Integer updatedByUser;


    public XlsStatisticsDimension() {
    }

    public XlsStatisticsDimension(StatisticsDimension statisticsDimension) {
        this.id = statisticsDimension.getId();
        this.statMonth = statisticsDimension.getStatMonth();
        this.regionCode = statisticsDimension.getRegionCode();
        this.statisticalType = statisticsDimension.getStatisticalType();
        this.dimensionName = statisticsDimension.getDimensionName();
        this.dimensionValue = statisticsDimension.getDimensionValue();
        this.createdByUser = statisticsDimension.getCreatedByUser();
        this.updatedByUser = statisticsDimension.getUpdatedByUser();

    }

    public StatisticsDimension adapToPO(Integer userId) {
        StatisticsDimension statisticsDimension = new StatisticsDimension();
        statisticsDimension.setId(id);
        statisticsDimension.setStatMonth(statMonth);
        statisticsDimension.setRegionCode(regionCode);
        statisticsDimension.setStatisticalType(statisticalType);
        statisticsDimension.setDimensionName(dimensionName);
        statisticsDimension.setDimensionValue(dimensionValue);

        statisticsDimension.setCreatedByUser(userId);
        statisticsDimension.setUpdatedByUser(userId);
        return statisticsDimension;
    }
}
