package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.AoiDraw;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class XlsAoiDraw {
    @XlsField(title = "主键", columnIndex = 0)
    private Integer id;

    @XlsField(title = "城市id(如果能划分三级的话则增加省市区的id字段便于搜索)", columnIndex = 1)
    private Integer regionId;

    @XlsField(title = "AOI名称", columnIndex = 2)
    private String aoiName;

    @XlsField(title = "引用媒介商圈概括", columnIndex = 3)
    private Integer quoteCbd;

    @XlsField(title = "商圈id", columnIndex = 4)
    private Integer cbdId;

    @XlsField(title = "面积", columnIndex = 5)
    private String aoiArea;

    @XlsField(title = "aoi类型", columnIndex = 6)
    private String aoiType;

    @XlsField(title = "坐标串", columnIndex = 7)
    private String polyline;

    @XlsField(title = "说明", columnIndex = 8)
    private String remark;

    @XlsField(title = "扩展字段", columnIndex = 9)
    private String extraAttrs;

    @XlsField(title = "行记录创建用户", columnIndex = 10)
    private Integer createdByUser;

    @XlsField(title = "行记录更新用户", columnIndex = 11)
    private Integer updatedByUser;


    public XlsAoiDraw() {
    }

    public XlsAoiDraw(AoiDraw aoiDraw) {
        this.id = aoiDraw.getId();
        this.regionId = aoiDraw.getRegionId();
        this.aoiName = aoiDraw.getAoiName();
        this.quoteCbd = aoiDraw.getQuoteCbd();
        this.cbdId = aoiDraw.getCbdId();
        this.aoiArea = aoiDraw.getAoiArea();
        this.aoiType = aoiDraw.getAoiType();
        this.polyline = aoiDraw.getPolyline();
        this.remark = aoiDraw.getRemark();
        this.extraAttrs = aoiDraw.getExtraAttrs();
        this.createdByUser = aoiDraw.getCreatedByUser();
        this.updatedByUser = aoiDraw.getUpdatedByUser();

    }

    public AoiDraw adapToPO(Integer userId) {
        AoiDraw aoiDraw = new AoiDraw();
        aoiDraw.setId(id);
        aoiDraw.setRegionId(regionId);
        aoiDraw.setAoiName(aoiName);
        aoiDraw.setQuoteCbd(quoteCbd);
        aoiDraw.setCbdId(cbdId);
        aoiDraw.setAoiArea(aoiArea);
        aoiDraw.setAoiType(aoiType);
        aoiDraw.setPolyline(polyline);
        aoiDraw.setRemark(remark);
        aoiDraw.setExtraAttrs(extraAttrs);

        aoiDraw.setCreatedByUser(userId);
        aoiDraw.setUpdatedByUser(userId);
        return aoiDraw;
    }
}
