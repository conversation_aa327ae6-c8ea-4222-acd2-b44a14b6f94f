package heran.media.management.platform.main.subdomain.bo;

import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.sharelib.domain.db.model.main.PassengerFlowHour;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XlsPassengerHourData {
    @XlsField(title = "小时数", columnIndex = 0)
    private Integer dataHour;
    @XlsField(title = "人口数", columnIndex = 1)
    private Integer passengerFlowCount;

    public XlsPassengerHourData(PassengerFlowHour data){
        this.dataHour = data.getDataHour();
        this.passengerFlowCount = data.getPassengerFlowCount();
    }
}
