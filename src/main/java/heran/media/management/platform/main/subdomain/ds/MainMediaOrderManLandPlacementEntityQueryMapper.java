package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.main.MainMediaOrderManLandPlacement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface MainMediaOrderManLandPlacementEntityQueryMapper {
    @SelectProvider(type = MainMediaOrderManLandPlacementEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "placementType", column = "placement_type"), @Result(property = "orderId", column = "order_id"), @Result(property = "placementCode", column = "placement_code"), @Result(property = "mediumFormat", column = "medium_format"), @Result(property = "provinceCode", column = "province_code"), @Result(property = "province", column = "province"), @Result(property = "cityCode", column = "city_code"), @Result(property = "city", column = "city"), @Result(property = "districtCode", column = "district_code"), @Result(property = "district", column = "district"), @Result(property = "mediumName", column = "medium_name"), @Result(property = "lineLevel", column = "line_level"), @Result(property = "launchPeriod", column = "launch_period"), @Result(property = "launchPeriodDay", column = "launch_period_day"), @Result(property = "neutralPosition", column = "neutral_position"), @Result(property = "publishCost", column = "publish_cost"), @Result(property = "makeCost", column = "make_cost"), @Result(property = "bufferDistance", column = "buffer_distance"), @Result(property = "bufferWktCoordinate", column = "buffer_wkt_coordinate"), @Result(property = "poi", column = "poi"), @Result(property = "aoi", column = "aoi"), @Result(property = "cbdName", column = "cbd_name"), @Result(property = "arterialTraffic", column = "arterial_traffic"), @Result(property = "heatingIndex", column = "heating_index"), @Result(property = "heatingPeriod", column = "heating_period"), @Result(property = "address", column = "address"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<MainMediaOrderManLandPlacement> list(SearchCriteria criteria);
}
