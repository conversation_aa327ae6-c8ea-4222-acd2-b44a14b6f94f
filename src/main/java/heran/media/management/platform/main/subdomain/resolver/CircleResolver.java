package heran.media.management.platform.main.subdomain.resolver;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import heran.media.management.platform.main.error.MediaPlacementImportValidateException;
import heran.media.sharelib.client.AMapClient;
import heran.media.sharelib.client.handle.AMapResponseHandle;
import heran.media.sharelib.domain.db.model.main.AoiDraw;
import heran.media.sharelib.domain.dto.amap.AMapBufferInfo;
import heran.media.sharelib.domain.dto.amap.AMapBufferResponse;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class CircleResolver implements AoiWktResolver {

    @Resource
    private AMapClient aMapClient;

    @Override
    public void getAoiWkt(Integer userKey, AoiDraw aoiDraw) {
        String polyline = aoiDraw.getPolyline();
        Double radius = aoiDraw.getRadius();
        AMapBufferInfo aMapBufferInfo = new AMapBufferInfo();
        aMapBufferInfo.setWkt(convertToWktPoint(polyline));
        aMapBufferInfo.setRadius(radius);
        aMapBufferInfo.setUseEqualCs(true);
        AMapBufferResponse buffer = aMapClient.createBuffer(aMapBufferInfo, userKey);
        AMapResponseHandle.handle(buffer);
        if (buffer.getAMapBufferDataInfo() != null) {
            String wkt = buffer.getAMapBufferDataInfo().getWkt();
            aoiDraw.setAoiWkt(extractPolygonCoordinates(wkt));
        }
    }


    public static String convertToWktPoint(String lngLat) {
        if (StringUtils.isBlank(lngLat) || !lngLat.contains(",")) {
            throw new MediaPlacementImportValidateException("Invalid coordinate string:" + lngLat);
        }
        String[] parts = lngLat.split(",");
        if (parts.length != 2) {
            throw new MediaPlacementImportValidateException("Expected format: lng,lat" + lngLat);
        }
        return String.format("POINT (%s %s)", parts[0].trim(), parts[1].trim());
    }

    public static String extractPolygonCoordinates(String wkt) {
        if (StringUtils.isBlank(wkt)) {
            return "";
        }

        String prefix = "POLYGON ((";
        String suffix = "))";

        if (wkt.startsWith(prefix) && wkt.endsWith(suffix)) {
            return wkt.substring(prefix.length(), wkt.length() - suffix.length()).trim();
        } else {
            throw new MediaPlacementImportValidateException("Invalid WKT format: must start with 'POLYGON ((' and end with '))'");
        }
    }


    @Override
    public String type() {
        return "CIRCLE";
    }
}
