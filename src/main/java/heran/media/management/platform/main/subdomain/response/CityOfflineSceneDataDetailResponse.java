package heran.media.management.platform.main.subdomain.response;

import heran.media.management.platform.main.subdomain.dto.CityOfflineSceneData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CityOfflineSceneDataDetailResponse {
    @ApiModelProperty("主键")
    private Integer id;
    @ApiModelProperty("标签名称")
    private String tagName;
    @ApiModelProperty("占比")
    private String tagValue;

    public CityOfflineSceneDataDetailResponse(CityOfflineSceneData data) {
        this.id = data.getId();
        this.tagName = data.getTagName();
        this.tagValue = new BigDecimal(data.getTagValue()).multiply(new BigDecimal(100)).toString();
    }
}
