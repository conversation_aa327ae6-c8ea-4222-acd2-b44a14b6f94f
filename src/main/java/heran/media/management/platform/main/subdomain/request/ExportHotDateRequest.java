package heran.media.management.platform.main.subdomain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ExportHotDateRequest {
    @ApiModelProperty("城市行政编号数组")
    @NotNull(message = "城市行政编号不能为空")
    private List<String> regionCodes;
    @ApiModelProperty("月份数组")
    @NotNull(message = "月份不能为空")
    private List<String> statMonths;
}
