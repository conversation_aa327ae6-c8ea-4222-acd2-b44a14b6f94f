package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBaseCustom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface OrderEffectEstimateBaseCustomEntityQueryMapper {
    @SelectProvider(type = OrderEffectEstimateBaseCustomEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "orderId", column = "order_id"), @Result(property = "busCover", column = "bus_cover"), @Result(property = "residenceCover", column = "residence_cover"), @Result(property = "officeBuildingCover", column = "office_building_cover"), @Result(property = "cbdCover", column = "cbd_cover"), @Result(property = "supermarketCover", column = "supermarket_cover"), @Result(property = "universityCover", column = "university_cover"), @Result(property = "administrativeRegionCover", column = "administrative_region_cover"), @Result(property = "undergroundCover", column = "underground_cover"), @Result(property = "hotelCover", column = "hotel_cover"), @Result(property = "brandCover", column = "brand_cover"), @Result(property = "isShow", column = "is_show"), @Result(property = "remark", column = "remark"), @Result(property = "extraAttrs", column = "extra_attrs"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<OrderEffectEstimateBaseCustom> list(SearchCriteria criteria);
}
