package heran.media.management.platform.main.subdomain.dto;

import heran.media.sharelib.domain.db.mapper.main.MainMediaTagMapper;
import heran.media.sharelib.domain.db.model.main.MainMediaTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MainMediaTagResponse {
    @ApiModelProperty("主键id")
    private Integer id;
    @ApiModelProperty("标签名称")
    private String label;
    @ApiModelProperty("说明")
    private String remark;
    @ApiModelProperty("父级标签")
    private Integer parentId;
    @ApiModelProperty("次级标记标配")
    private List<MainMediaTagResponse> secondaryMediaTagList;


    public MainMediaTagResponse(MainMediaTag mainMediaTag){
        this.id = mainMediaTag.getId();
        this.label = mainMediaTag.getLabel();
        this.remark = mainMediaTag.getRemark();
        this.parentId = mainMediaTag.getParentId();
    }
}
