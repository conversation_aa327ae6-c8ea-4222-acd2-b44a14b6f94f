package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.PassengerFlowHour;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangeblePassengerFlowHour implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "182")
    private Integer id;

    @ApiModelProperty(value = "人地数据id", required = true, example = "115")
    @NotNull(message = "人地数据id不能为空")
    private Integer landDataId;

    @ApiModelProperty(value = "小时", required = true, example = "小时")
    @Size(max = 64, message = "小时长度不能超出64位")
    @NotBlank(message = "小时不能为空")
    private Integer dataHour;

    @ApiModelProperty(value = "客流人数", required = true, example = "245")
    @NotNull(message = "客流人数不能为空")
    private Integer passengerFlowCount;


    public TChangeblePassengerFlowHour() {
    }

    public PassengerFlowHour adapToPO() {
        PassengerFlowHour passengerFlowHour = new PassengerFlowHour();
        passengerFlowHour.setId(id);
        passengerFlowHour.setLandDataId(landDataId);
        passengerFlowHour.setDataHour(dataHour);
        passengerFlowHour.setPassengerFlowCount(passengerFlowCount);

        return passengerFlowHour;
    }
}
