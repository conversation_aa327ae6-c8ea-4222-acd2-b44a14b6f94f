package heran.media.management.platform.main.subdomain.response;

import heran.media.management.platform.main.subdomain.dto.TaDataSelectListData;
import heran.media.sharelib.domain.bo.TaStatisticalType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TaDataSelectListDataResponse {
    @ApiModelProperty("主键ID")
    public Integer id;
    @ApiModelProperty("任务id")
    public Integer taskId;
    @ApiModelProperty("人群包id")
    private Integer crowdPackId;
    @ApiModelProperty("人群包名称")
    private String packName;
    @ApiModelProperty("区域code")
    private String regionCode;
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("高德任务id")
    private String mapTaskId;
    @ApiModelProperty("城市人口数")
    private Integer populaceCount;
    @ApiModelProperty("TA线下场景偏好")
    private Boolean taOffline;
    @ApiModelProperty("TA常驻网格热力")
    private Boolean resident;
    @ApiModelProperty("TA客流网格热力")
    private Boolean passenger;
    @ApiModelProperty("TA城市网格人口")
    private String wktTypes;
    @ApiModelProperty("TA网格热力")
    private String statisticalTypes;
    @ApiModelProperty("更新时间")
    private Date updateTime;

    public TaDataSelectListDataResponse(TaDataSelectListData data) {
        this.id = data.getId();
        this.taskId = data.getTaskId();
        this.crowdPackId = data.getCrowdPackId();
        this.packName = data.getPackName();
        this.regionCode = data.getRegionCode();
        this.statMonth = data.getStatMonth();
        this.mapTaskId = data.getMapTaskId();
        this.populaceCount = data.getPopulaceCount();
        if (TaStatisticalType.TA_PASSENGER_POPULATION.name().equals(data.getTaPopulation())) {
            this.populaceCount = null;
        }
        this.taOffline = data.getTaOffline() != null && data.getTaOffline() > 0;
        this.resident = data.getResident() != null && data.getResident() > 0;
        this.passenger = data.getPassenger() != null && data.getPassenger() > 0;
        this.updateTime = data.getUpdateTime();
        this.wktTypes = data.getWktTypes();
        this.statisticalTypes = data.getStatisticalTypes();
    }
}
