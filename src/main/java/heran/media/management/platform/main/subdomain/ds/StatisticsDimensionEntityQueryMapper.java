package heran.media.management.platform.main.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.main.subdomain.dto.DimensionListData;
import heran.media.management.platform.main.subdomain.dto.TPersonLandExportData;
import heran.media.sharelib.domain.db.model.main.PassengerFlowHour;
import heran.media.sharelib.domain.db.model.main.StatisticsDimension;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface StatisticsDimensionEntityQueryMapper {
    @SelectProvider(type = StatisticsDimensionEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"), @Result(property = "statMonth", column = "stat_month"), @Result(property = "regionCode", column = "region_code"), @Result(property = "statisticalType", column = "statistical_type"), @Result(property = "dimensionName", column = "dimension_name"), @Result(property = "dimensionValue", column = "dimension_value"), @Result(property = "createdByUser", column = "created_by_user"), @Result(property = "updatedByUser", column = "updated_by_user"), @Result(property = "creator", column = "creator"), @Result(property = "updater", column = "updater"), @Result(property = "createTime", column = "create_time"), @Result(property = "updateTime", column = "update_time")
    }
    )
    List<StatisticsDimension> list(SearchCriteria criteria);


    @SelectProvider(type = StatisticsDimensionEntityQuerySQLProvider.class, method = "selectList")
    @Results(value = {
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "regionName", column = "region_name"),
            @Result(property = "statisticalType", column = "statistical_type"),
            @Result(property = "dimensionName", column = "dimension_name"),
            @Result(property = "dimensionValue", column = "dimension_value"),
            @Result(property = "dictName", column = "dict_name"),
            @Result(property = "header", column = "header"),
    }
    )
    List<DimensionListData> selectList(String regionCode, String statMonth);


    @Insert({"<script>",
            "INSERT INTO statistics_dimension(id, stat_month, region_code, statistical_type, dimension_name, dimension_value, created_by_user, updated_by_user, creator, updater,create_time, data_type, data_id)",
            "VALUES ",
            "<foreach item='item' collection='batchInsertList' separator=','>",
            "(#{item.id},#{item.statMonth},#{item.regionCode}, #{item.statisticalType}, #{item.dimensionName}, #{item.dimensionValue}, #{item.createdByUser}, #{item.updatedByUser}, #{item.creator}, #{item.updater},#{item.createTime},#{item.dataType},#{item.dataId})",
            "</foreach>",

            "</script>"
    })
    void batchInsertEntities(List<StatisticsDimension> batchInsertList);

    @Select("SELECT dimension_name FROM statistics_dimension WHERE stat_month = #{statMonth} AND region_code = #{regionCode} AND statistical_type = #{statisticalType}")
    List<String> getDimensionNames(String statMonth, String regionCode, String statisticalType);


    @Select({
            "<script>",
            "SELECT statistical_type,group_concat(distinct dimension_name separator ',') as dimension_name FROM statistics_dimension  ",
            "WHERE stat_month = #{statMonth} AND data_type = 'person_land_data' ",
            "AND region_code IN",
            "<foreach item='regionCode' collection='regionCodeList' open='(' separator=',' close=')'>",
            "#{regionCode}",
            "</foreach>",
            "GROUP BY statistical_type",
            "</script>"
    })
    List<StatisticsDimension> getByStatMonthAndCodeList(@Param("statMonth") String statMonth, @Param("regionCodeList") List<String> regionCodeList);





}
