package heran.media.management.platform.main.subdomain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DimensionListData {
    private String statMonth;
    private String lv1Name;
    private String lv2Name;
    private String lv3Name;
    private String statisticalType;
    private String dimensionName;
    private String dimensionValue;
    private String dictName;
    private String header;

    public String getRegionName() {
        String region = "";
        if (lv1Name != null) {
            region += lv1Name;
        }
        if (lv2Name != null) {
            region += lv2Name;
        }
        if (lv3Name != null) {
            region += lv3Name;
        }
        return region;
    }
}
