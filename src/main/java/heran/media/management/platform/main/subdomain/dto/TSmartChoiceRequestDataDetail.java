package heran.media.management.platform.main.subdomain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.SmartChoiceRequestDataDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TSmartChoiceRequestDataDetail implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "157")
    private Integer id;

    @ApiModelProperty(value = "智选方案id", required = false, example = "87")
    private Integer selectionPlanId;

    @ApiModelProperty(value = "点位编号", required = false, example = "点位编号")
    @Size(max = 64, message = "点位编号长度不能超出64位")
    private String placementCode;

    @ApiModelProperty(value = "省份编号", required = false, example = "省份编号")
    @Size(max = 64, message = "省份编号长度不能超出64位")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称", required = false, example = "省份名称")
    @Size(max = 64, message = "省份名称长度不能超出64位")
    private String province;

    @ApiModelProperty(value = "城市编号", required = false, example = "城市编号")
    @Size(max = 64, message = "城市编号长度不能超出64位")
    private String cityCode;

    @ApiModelProperty(value = "城市名称", required = false, example = "城市名称")
    @Size(max = 64, message = "城市名称长度不能超出64位")
    private String city;

    @ApiModelProperty(value = "区域/县级市编号", required = false, example = "区域/县级市编号")
    @Size(max = 64, message = "区域/县级市编号长度不能超出64位")
    private String districtCode;

    @ApiModelProperty(value = "区域/县级市名称", required = false, example = "区域/县级市名称")
    @Size(max = 64, message = "区域/县级市名称长度不能超出64位")
    private String district;

    @ApiModelProperty(value = "媒体形式", required = false, example = "媒体形式")
    @Size(max = 64, message = "媒体形式长度不能超出64位")
    private String mediumFormat;

    @ApiModelProperty(value = "媒体筛选条件", required = false, example = "媒体筛选条件")
    @Size(max = 64, message = "媒体筛选条件长度不能超出64位")
    private String mediumFiltra;

    @ApiModelProperty(value = "媒体名称", required = false, example = "媒体名称")
    @Size(max = 64, message = "媒体名称长度不能超出64位")
    private String mediumName;

    @ApiModelProperty(value = "媒体位置", required = false, example = "媒体位置")
    @Size(max = 64, message = "媒体位置长度不能超出64位")
    private String mediumAddress;

    @ApiModelProperty(value = "点位数据 ？？？？？这都具体到点位了为啥还有点位数量", required = false, example = "98")
    private Integer placementCount;

    @ApiModelProperty(value = "缓冲区距离", required = false, example = "142")
    private Integer bufferDistance;

    @ApiModelProperty(value = "TA浓度", required = false, example = "172")
    private Integer taConcentration;

    @ApiModelProperty(value = "浓度周期", required = false, example = "浓度周期")
    @Size(max = 64, message = "浓度周期长度不能超出64位")
    private String concentrationPeriod;

    @ApiModelProperty(value = "计划投放开始时间", required = false, example = "1741321657553")
    private Date planLaunchStartTime;

    @ApiModelProperty(value = "计划投放结束时间", required = false, example = "1741321657553")
    private Date planLaunchEndTime;

    @ApiModelProperty(value = "行记录创建用户", required = false, example = "")
    private Integer createdByUser;

    @ApiModelProperty(value = "行记录更新用户", required = false, example = "")
    private Integer updatedByUser;

    @ApiModelProperty(value = "行记录创建标记", required = false, example = "")
    @Size(max = 64, message = "行记录创建标记长度不能超出64位")
    private String creator;

    @ApiModelProperty(value = "行记录更新标记", required = false, example = "")
    @Size(max = 64, message = "行记录更新标记长度不能超出64位")
    private String updater;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;


    public TSmartChoiceRequestDataDetail() {
    }

    public TSmartChoiceRequestDataDetail(SmartChoiceRequestDataDetail smartChoiceRequestDataDetail) {
        this.id = smartChoiceRequestDataDetail.getId();
        this.selectionPlanId = smartChoiceRequestDataDetail.getSelectionPlanId();
        this.placementCode = smartChoiceRequestDataDetail.getPlacementCode();
        this.provinceCode = smartChoiceRequestDataDetail.getProvinceCode();
        this.province = smartChoiceRequestDataDetail.getProvince();
        this.cityCode = smartChoiceRequestDataDetail.getCityCode();
        this.city = smartChoiceRequestDataDetail.getCity();
        this.districtCode = smartChoiceRequestDataDetail.getDistrictCode();
        this.district = smartChoiceRequestDataDetail.getDistrict();
        this.mediumFormat = smartChoiceRequestDataDetail.getMediumFormat();
        this.mediumFiltra = smartChoiceRequestDataDetail.getMediumFiltra();
        this.mediumName = smartChoiceRequestDataDetail.getMediumName();
        this.mediumAddress = smartChoiceRequestDataDetail.getMediumAddress();
        this.placementCount = smartChoiceRequestDataDetail.getPlacementCount();
        this.bufferDistance = smartChoiceRequestDataDetail.getBufferDistance();
        this.taConcentration = smartChoiceRequestDataDetail.getTaConcentration();
        this.concentrationPeriod = smartChoiceRequestDataDetail.getConcentrationPeriod();
        this.planLaunchStartTime = smartChoiceRequestDataDetail.getPlanLaunchStartTime();
        this.planLaunchEndTime = smartChoiceRequestDataDetail.getPlanLaunchEndTime();
        this.createdByUser = smartChoiceRequestDataDetail.getCreatedByUser();
        this.updatedByUser = smartChoiceRequestDataDetail.getUpdatedByUser();
        this.creator = smartChoiceRequestDataDetail.getCreator();
        this.updater = smartChoiceRequestDataDetail.getUpdater();
        this.createTime = smartChoiceRequestDataDetail.getCreateTime();
        this.updateTime = smartChoiceRequestDataDetail.getUpdateTime();

    }
}
