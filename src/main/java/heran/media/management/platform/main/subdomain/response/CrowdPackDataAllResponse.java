package heran.media.management.platform.main.subdomain.response;

import heran.media.sharelib.domain.db.model.main.MediaTaCrowdPack;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CrowdPackDataAllResponse {
    @ApiModelProperty("主键id")
    private Integer id;
    @ApiModelProperty("人群包名称")
    private String packName;
    @ApiModelProperty("人群包编码")
    private String packCode;

    public CrowdPackDataAllResponse(MediaTaCrowdPack mediaTaCrowdPack){
        this.id = mediaTaCrowdPack.getId();
        this.packName = mediaTaCrowdPack.getPackName();
        this.packCode = mediaTaCrowdPack.getPackCode();
    }
}
