package heran.media.management.platform.aioptionmode.mapper;

import heran.media.management.platform.aioptionmode.dto.PlacementSearchInfo;
import heran.media.management.platform.aioptionmode.dto.PlannedSpeedResponse;
import heran.media.management.platform.aioptionmode.dto.ProvinceCityInfo;
import heran.media.management.platform.aioptionmode.po.*;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.sharelib.domain.db.model.main.MainCalendarPlacement;
import heran.media.management.platform.mediaorder.sudomain.dto.GridHotDataOrder;
import heran.media.sharelib.domain.db.model.main.*;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 智选模式mapper
 * @Author: yzj
 * @Date: 2023/11/17 16:07
 * @Version: 1.0
 */
@Mapper
public interface AiOptionModeMapper {

    @SelectProvider(type = AiOptionModeQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "planSn", column = "plan_sn"),
            @Result(property = "planName", column = "plan_name"),
            @Result(property = "groupName", column = "group_name"),
            @Result(property = "brandName", column = "brand_name"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "selectionType", column = "selection_type"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
            @Result(property = "planStatus", column = "plan_status"),
            @Result(property = "mediumCategoryIds", column = "medium_category_ids"),
            @Result(property = "mediumAttrExtras", column = "medium_attr_extras"),
            @Result(property = "geoRange", column = "geo_range"),
            @Result(property = "isUnrepeated", column = "is_unrepeated"),
            @Result(property = "salesCityRegions", column = "sales_city_regions"),
            @Result(property = "stepType", column = "step_type"),
            @Result(property = "creatorName", column = "creatorName"),
            @Result(property = "packId", column = "pack_id"),
            @Result(property = "packName", column = "pack_name"),
    })
    List<AiOptionPlanListData> list(String selectionType, SearchCriteria criteria);

    // @Select("<script>" +
    // "SELECT " +
    // "t1.region_name province, " +
    // "t1.`code` provinceCode, " +
    // "t2.region_name city, " +
    // "t2.`code` cityCode, " +
    // "t3.region_name area, " +
    // "t3.`code` areaCode  " +
    // "FROM " +
    // "main_dict_region t1 " +
    // "INNER JOIN main_dict_region t2 ON t1.`code` = t2.parent_code " +
    // "INNER JOIN main_dict_region t3 ON t2.`code` = t3.parent_code  " +
    // "WHERE " +
    // "t3.`code` IN " +
    // "<foreach item = 'areaCode' collection='areaCodeList' open='(' separator=','
    // close=')' >" +
    // "#{areaCode}" +
    // "</foreach>  " +
    // "</script>")
    // List<RegionData> getRegionDataListByAreaCodeList(@Param("areaCodeList")
    // Set<String> areaCodeList);

    @Select("<script>" +
            "SELECT " +
            "t1.id lv1Id, " +
            "t1.category_name lv1Name, " +
            "t2.id lv2Id, " +
            "t2.category_name lv2Name, " +
            "t3.id lv3Id, " +
            "t3.category_name lv3Name  " +
            "FROM " +
            "main_resource_category t1 " +
            "INNER JOIN main_resource_category t2 ON t1.id = t2.parent_id " +
            "INNER JOIN main_resource_category t3 ON t2.id = t3.parent_id  " +
            "WHERE " +
            "t3.id IN " +
            "<foreach item = 'lv3Id' collection='lv3IdList' open='(' separator=',' close=')' >" +
            "#{lv3Id}" +
            "</foreach>  " +
            "</script>")
    List<MediumFormatData> getMediumFormatDataByLv3IdList(@Param("lv3IdList") Set<Integer> lv3IdList);

    @Select("SELECT " +
            "t2.id,t2.poi_id,t2.longitude,t2.latitude,t2.address_excel_detail,t3.plan_status,t3.geo_range,t3.is_unrepeated "
            +
            "FROM " +
            "media_geo_plan_match_record t1 " +
            "INNER JOIN media_plan_geolocation_match t2 ON t1.id = t2.record_id " +
            "INNER JOIN media_placement_selection_plan t3 ON t1.selection_plan_id = t3.id " +
            "WHERE t1.selection_plan_id = #{planId} " +
            "ORDER BY t2.id")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "poiId", column = "poi_id"),
            @Result(property = "longitude", column = "longitude"),
            @Result(property = "latitude", column = "latitude"),
            @Result(property = "addressExcelDetail", column = "address_excel_detail"),
            @Result(property = "planStatus", column = "plan_status"),
            @Result(property = "geoRange", column = "geo_range"),
            @Result(property = "isDistinct", column = "is_unrepeated")
    })
    List<PlanRequirementData> getGeoLocationRequirementData(@Param("planId") Integer planId);

    @Select("SELECT sales_city_regions FROM media_placement_selection_plan WHERE id = #{planId}")
    String getCrowdTagRequirementData(@Param("planId") Integer planId);

    @Select("SELECT " +
            "t2.id geolocationId, " +
            "t2.poi_id, " +
            "t2.address_excel_detail, " +
            "t3.placement_longitude, " +
            "t3.placement_latitude, " +
            "t3.extras, " +
            "t3.distance  " +
            "FROM " +
            "media_geo_plan_match_record t1 " +
            "INNER JOIN media_plan_geolocation_match t2 ON t1.id = t2.record_id " +
            "INNER JOIN placement_geolocation_match_result t3 ON t2.id = t3.geolocation_id  " +
            "WHERE " +
            "t1.selection_plan_id = #{planId}")
    @Results(value = {
            @Result(property = "poiId", column = "poi_id"),
            @Result(property = "geolocationId", column = "geolocationId"),
            @Result(property = "addressExcelDetail", column = "address_excel_detail"),
            @Result(property = "extras", column = "extras"),
            @Result(property = "longitude", column = "placement_longitude"),
            @Result(property = "latitude", column = "placement_latitude"),
            @Result(property = "distance", column = "distance")
    })
    List<GeoLocationMatchResultData> getGeoLocationMatchResultDataList(@Param("planId") Integer planId);

    @Select("SELECT " +
            "t1.placement_count, " +
            "t1.concentration_score, " +
            "t1.city_code cityCode, " +
            "t1.district_code areaCode, " +
            "t3.region_name province, " +
            "t2.region_name city, " +
            "t4.region_name area  " +
            "FROM " +
            "media_placement_region_match_result t1 " +
            "INNER JOIN main_dict_region t2 ON t1.city_code = t2.`code` " +
            "INNER JOIN main_dict_region t3 ON t2.parent_code = t3.`code` " +
            "LEFT JOIN main_dict_region t4 ON t1.district_code = t4.`code`  " +
            "WHERE " +
            "t1.plan_id = #{planId}")
    @Results(value = {
            @Result(property = "province", column = "province"),
            @Result(property = "city", column = "city"),
            @Result(property = "area", column = "area"),
            @Result(property = "cityCode", column = "cityCode"),
            @Result(property = "areaCode", column = "areaCode"),
            @Result(property = "placementCount", column = "placement_count"),
            @Result(property = "concentrationScore", column = "concentration_score")
    })
    List<CrowdTagMatchResultData> getCrowdTagMatchResultList(@Param("planId") Integer planId);

    @Delete("<script>" +
            "DELETE FROM placement_geolocation_match_result WHERE geolocation_id IN " +
            "<foreach collection='geolocationIds' item='geolocationId' open='(' separator=',' close=')'>" +
            "#{geolocationId}" +
            "</foreach>" +
            "</script>")
    int deleteMatchResults(@Param("geolocationIds") List<Integer> geolocationIds);

    @Select("SELECT " +
            "t2.id  " +
            "FROM " +
            "media_geo_plan_match_record t1 " +
            "INNER JOIN media_plan_geolocation_match t2 ON t1.id = t2.record_id  " +
            "WHERE " +
            "t1.selection_plan_id = #{planId} ")
    List<Integer> getGeolocationIdList(@Param("planId") Integer planId);

    @Select("SELECT " +
            "t1.id, " +
            "t2.plan_sn, " +
            "t1.plan_name, " +
            "t2.group_name, " +
            "t2.brand_name, " +
            "t1.selection_type, " +
            "t2.delivery_start_time, " +
            "t2.delivery_end_time, " +
            "t1.plan_status, " +
            "t2.belong_industry_category_id, " +
            "t2.product_promotion, " +
            "t2.competitor, " +
            "t2.brand_situation, " +
            "t2.present_stage_requirement, " +
            "t2.disseminate_purpose, " +
            "t2.core_sales_regions, " +
            "t2.product_prospect, " +
            "t2.distribution_channel, " +
            "t2.product_pricing, " +
            "t1.sales_city_regions, " +
            "t2.budget, " +
            "t1.update_time, " +
            "t3.stat_month " +
            "FROM " +
            " media_placement_selection_plan t1 " +
            " INNER JOIN media_placement_base_sales_plan t2 ON t2.id = t1.base_plan_id " +
            " left join selection_plan_hottype_detail t3 on t3.plan_id= t1.id " +
            " WHERE t1.id = #{planId}")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "planSn", column = "plan_sn"),
            @Result(property = "planName", column = "plan_name"),
            @Result(property = "groupName", column = "group_name"),
            @Result(property = "brandName", column = "brand_name"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "selectionType", column = "selection_type"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
            @Result(property = "planStatus", column = "plan_status"),
            @Result(property = "belongIndustryCategoryId", column = "belong_industry_category_id"),
            @Result(property = "productPromotion", column = "product_promotion"),
            @Result(property = "competitor", column = "competitor"),
            @Result(property = "brandSituation", column = "brand_situation"),
            @Result(property = "presentStageRequirement", column = "present_stage_requirement"),
            @Result(property = "disseminatePurpose", column = "disseminate_purpose"),
            @Result(property = "coreSalesRegions", column = "core_sales_regions"),
            @Result(property = "budget", column = "budget"),
            @Result(property = "productProspect", column = "product_prospect"),
            @Result(property = "distributionChannel", column = "distribution_channel"),
            @Result(property = "productPricing", column = "product_pricing"),
            @Result(property = "salesCityRegions", column = "sales_city_regions"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "statMonth", column = "stat_month"),
    })
    AiOptionBasePlanDetailData getBasePlanDetailData(@Param("planId") Integer planId);

    @Select({
            "<script>",
            "SELECT r1.region_name AS one_name, r2.region_name AS two_name, mr.region_name AS three_name ",
            "FROM main_dict_region mr ",
            "LEFT JOIN main_dict_region r2 ON mr.parent_code = r2.code ",
            "LEFT JOIN main_dict_region r1 ON r2.parent_code = r1.code ",
            "<where>",
            "<if test='regionCodeList != null and regionCodeList.size > 0'>",
            "mr.code IN ",
            "<foreach item='regionCode' collection='regionCodeList' open='(' separator=',' close=')'>",
            "#{regionCode}",
            "</foreach>",
            "</if>",
            "</where>",
            "</script>"
    })
    List<ThreeLevelCityData> getByRegionName(@Param("regionCodeList") List<String> regionCodeList);

    @SelectProvider(type = AiOptionModeQuerySQLProvider.class, method = "calendarPlacementSelect")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "planName", column = "plan_name"),
            @Result(property = "planSn", column = "plan_sn"),
            @Result(property = "groupName", column = "group_name"),
            @Result(property = "brandName", column = "brand_name"),
            @Result(property = "selectionType", column = "selection_type"),
            @Result(property = "planStatus", column = "plan_status"),
            @Result(property = "geoRange", column = "geo_range"),
            @Result(property = "isUnrepeated", column = "is_unrepeated"),
            @Result(property = "calendarPlacementId", column = "calendar_placement_id"),
            @Result(property = "planId", column = "plan_id"),
            @Result(property = "placementSn", column = "placement_sn"),
            @Result(property = "mediaName", column = "media_name"),
            @Result(property = "resourceProvider", column = "resource_provider"),
            @Result(property = "resourceProviderType", column = "resource_provider_type"),
            @Result(property = "provinceId", column = "province_id"),
            @Result(property = "cityId", column = "city_id"),
            @Result(property = "districtId", column = "district_id"),
            @Result(property = "addressId", column = "address_id"),
            @Result(property = "mediumLv1CategoryId", column = "medium_lv1_category_id"),
            @Result(property = "mediumLv2CategoryId", column = "medium_lv2_category_id"),
            @Result(property = "mediumLv3CategoryId", column = "medium_lv3_category_id"),
            @Result(property = "packId", column = "pack_id"),
            @Result(property = "packName", column = "pack_name"),
            @Result(property = "aoiCoordinates", column = "aoi_coordinates"),
            @Result(property = "vacancyPosition", column = "vacancy_position"),
            @Result(property = "bufferArea", column = "buffer_area"),
            @Result(property = "bufferType", column = "buffer_type"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
            @Result(property = "mediumAttrExtras", column = "medium_attr_extras"),
            @Result(property = "stepType", column = "step_type"),
            @Result(property = "aoiCaliber", column = "aoi_caliber"),
            @Result(property = "aoiHotValue", column = "aoi_hot_value"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "launchStartDate", column = "launch_start_date"),
    })
    List<PlacementSelectionPlanData> calendarPlacementSelect(SearchCriteria criteria);

    @Select("SELECT pack.id,pack.pack_name,pack.pack_code " +
            " FROM main_crowd_plan_pack plan_pack " +
            " inner join media_ta_crowd_pack pack on  plan_pack.pack_id=pack.id " +
            " WHERE plan_pack.plan_id=#{planId} ")
    @Results(id = "mediaTaCrowdPack-plan", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "packName", column = "pack_name"),
            @Result(property = "packCode", column = "pack_code")
    })
    List<MediaTaCrowdPack> getByPlanId(@Param("planId") Integer planId);

    @Select("select count(1) cityCount, sum(vacancyPosition) vacancyPosition from (" +
            " SELECT medium_lv3_category_id, count(1) cityCount, sum(vacancy_position) vacancyPosition from main_calendar_placement where plan_id=#{planId} and medium_lv3_category_id=#{categoryId} GROUP BY  city_id"
            +
            " ) as temp  ")
    CityPositionData getCitySumByplanAndCategoryId(@Param("planId") Integer planId,
            @Param("categoryId") Integer categoryId);

    @Select("SELECT mcp.id," +
            "mcp.placement_sn,mcp.plan_id, mcp.media_name, mcp.resource_provider,mcp.resource_provider_type, " +
            "mcp.province_id, mcp.city_id, mcp.district_id, address_id," +
            "mcp.medium_lv1_category_id, mcp.medium_lv2_category_id, mcp.medium_lv3_category_id, " +
            "mcp.pack_id, mcp.pack_name, mcp.remark,mcp.extra_attrs," +
            "mcp.delivery_start_time,mcp.delivery_end_time," +
            "mcp.longitude,mcp.latitude, " +
            " FROM main_calendar_placement mcp " +
            " left join main_calendar_placement_ext mcpext on mcp.id=mcpext.calendar_placement_id " +
            " where " +
            " mcp.plan_id=#{planId} and mcp.medium_lv3_category_id=#{categoryId}  ")
    @Results(id = "mainCalendarPlacement-ext-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "placementSn", column = "placement_sn"),
            @Result(property = "planId", column = "plan_id"),
            @Result(property = "mediaName", column = "media_name"),
            @Result(property = "resourceProviderType", column = "resource_provider_type"),
            @Result(property = "resourceProvider", column = "resource_provider"),
            @Result(property = "provinceId", column = "province_id"),
            @Result(property = "cityId", column = "city_id"),
            @Result(property = "districtId", column = "district_id"),
            @Result(property = "addressId", column = "address_id"),
            @Result(property = "mediumLv1CategoryId", column = "medium_lv1_category_id"),
            @Result(property = "mediumLv2CategoryId", column = "medium_lv2_category_id"),
            @Result(property = "mediumLv3CategoryId", column = "medium_lv3_category_id"),
            @Result(property = "resourceProviderType", column = "resource_provider_type"),
            @Result(property = "packId", column = "pack_id"),
            @Result(property = "packName", column = "pack_name"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
            @Result(property = "createdByUser", column = "created_by_user"),
            @Result(property = "updatedByUser", column = "updated_by_user"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    MainCalendarPlacement getByIdEX(@Param("planId") Integer planId, @Param("categoryId") Integer categoryId);

    @Select("SELECT mcp.id," +
            "mcp.placement_sn,mcp.plan_id, mcp.media_name, mcp.resource_provider,mcp.resource_provider_type, " +
            "mcp.province_id, mcp.city_id, mcp.district_id, mcp.address_id," +
            "mcp.medium_lv1_category_id, mcp.medium_lv2_category_id, mcp.medium_lv3_category_id, " +
            "mcp.pack_id, mcp.pack_name, mcp.remark,mcp.extra_attrs," +
            "mcp.delivery_start_time,mcp.delivery_end_time," +
            "mcp.longitude,mcp.latitude,sum(vacancy_position) vacancy_position, mcp.update_time, " +
            "r1.code district_code,  r2.code city_code,t4.stat_month, t6.launch_start_date," +
            "buffer_area,buffer_type," +
            " aoi_area,extend_aoi_coordinates,aoi_center,execute_quotation,sum(mcp.ta_hot_value) ta_hot_value,aoi_caliber, sum(mcp.aoi_hot_value) aoi_hot_value " +
            " FROM " +
            " main_calendar_placement mcp " +
            " left join main_dict_region r1 on  mcp.district_id = r1.id " +
            " left join main_dict_region r2 on  mcp.city_id = r2.id " +
            " left join selection_plan_hottype_detail t4 on t4.plan_id= mcp.plan_id " +
            " left join route_selection_scheme t5 on mcp.id= t5.selection_plan_id " +
            " left join route_selection_scheme_line t6 on t5.id= t6.scheme_id "+
            " WHERE " +
            " medium_lv3_category_id = #{categoryId} AND mcp.plan_id=#{planId} " +
            " group by city_id ")
    @Results(id = "MainCalendarPlacementData-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "placementSn", column = "placement_sn"),
            @Result(property = "planId", column = "plan_id"),
            @Result(property = "mediaName", column = "media_name"),
            @Result(property = "resourceProviderType", column = "resource_provider_type"),
            @Result(property = "resourceProvider", column = "resource_provider"),
            @Result(property = "provinceId", column = "province_id"),
            @Result(property = "cityId", column = "city_id"),
            @Result(property = "districtId", column = "district_id"),
            @Result(property = "addressId", column = "address_id"),
            @Result(property = "mediumLv1CategoryId", column = "medium_lv1_category_id"),
            @Result(property = "mediumLv2CategoryId", column = "medium_lv2_category_id"),
            @Result(property = "mediumLv3CategoryId", column = "medium_lv3_category_id"),
            @Result(property = "resourceProviderType", column = "resource_provider_type"),
            @Result(property = "vacancyPosition", column = "vacancy_position"),
            @Result(property = "packId", column = "pack_id"),
            @Result(property = "packName", column = "pack_name"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
            @Result(property = "districtCode", column = "district_code"),
            @Result(property = "cityCode", column = "city_code"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "aoiArea", column = "aoi_area"),
            @Result(property = "extendAoiCoordinates", column = "extend_aoi_coordinates"),
            @Result(property = "aoiCenter", column = "aoi_center"),
            @Result(property = "executeQuotation", column = "execute_quotation"),
            @Result(property = "taHotValue", column = "ta_hot_value"),
            @Result(property = "aoiCaliber", column = "aoi_caliber"),
            @Result(property = "aoiHotValue", column = "aoi_hot_value"),
            @Result(property = "bufferArea", column = "buffer_area"),
            @Result(property = "bufferType", column = "buffer_type"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "launchStartDate", column = "launch_start_date"),
    })
    List<MainCalendarPlacementData> getMainCalendarPlacementByPlanId(@Param("categoryId") Integer categoryId,
            @Param("planId") Integer planId);

    @SelectProvider(type = AiOptionModeQuerySQLProvider.class, method = "selectPlacementSnList")
    @Results(id = "calendarPlacementData-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "placementSn", column = "placement_sn"),
            @Result(property = "planId", column = "plan_id"),
            @Result(property = "mediaName", column = "media_name"),
            @Result(property = "resourceProviderType", column = "resource_provider_type"),
            @Result(property = "resourceProvider", column = "resource_provider"),
            @Result(property = "provinceId", column = "province_id"),
            @Result(property = "cityId", column = "city_id"),
            @Result(property = "districtId", column = "district_id"),
            @Result(property = "addressId", column = "address_id"),
            @Result(property = "cityName", column = "city_name"),
            @Result(property = "mediumLv1CategoryId", column = "medium_lv1_category_id"),
            @Result(property = "mediumLv2CategoryId", column = "medium_lv2_category_id"),
            @Result(property = "mediumLv3CategoryId", column = "medium_lv3_category_id"),
            @Result(property = "resourceProviderType", column = "resource_provider_type"),
            @Result(property = "vacancyPosition", column = "vacancy_position"),
            @Result(property = "packId", column = "pack_id"),
            @Result(property = "packName", column = "pack_name"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
            @Result(property = "districtCode", column = "district_code"),
            @Result(property = "cityCode", column = "city_code"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "brighten", column = "brighten"),
            @Result(property = "mileage", column = "mileage"),
            @Result(property = "busName", column = "bus_name"),
            @Result(property = "launch", column = "launch"),
            @Result(property = "issueAmount", column = "issue_amount"),
            @Result(property = "productionAmount", column = "production_amount"),
            @Result(property = "placementExtraAttrs", column = "placementExtraAttrs"),
            @Result(property = "deliveryTime", column = "deliveryTime"),
            @Result(property = "longitude", column = "longitude"),
            @Result(property = "bufferArea", column = "buffer_area"),
            @Result(property = "bufferType", column = "buffer_type"),
            @Result(property = "aoiArea", column = "aoi_area"),
            @Result(property = "aoiHotValue", column = "aoi_hot_value"),
            @Result(property = "taHotValue", column = "ta_hot_value"),
            @Result(property = "extendAoiCoordinates", column = "extend_aoi_coordinates"),
            @Result(property = "aoiCenter", column = "aoi_center"),
            @Result(property = "aoiCaliber", column = "aoi_caliber"),
            @Result(property = "mediaPosition", column = "media_position"),
            @Result(property = "aoiCoordinates", column = "aoi_coordinates"),
            @Result(property = "placementCost", column = "placement_cost"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "statisticalType", column = "statistical_type"),
            @Result(property = "longitudeAndLatitude", column = "longitudeAndLatitude"),
    })
    List<CalendarPlacementData> getMainCalendarPlacementByCity(SearchCriteria criteria);

    @Select({
            "<script>",
            "SELECT mcp.id, mcp.placement_sn placementSn, mcp.media_name mediaName, mcp.media_position mediaPosition, mcp.longitude, mcp.latitude ",
            "FROM main_calendar_placement mcp ",
            "<where>",
            "<if test='criterias != null'>",
            "<foreach collection='criterias' item='criteria'>",
            "<if test='criteria.key == \"keyword\" and criteria.value != null and criteria.value != \"\" '>",
            "AND (  " +
                    " mcp.placement_sn  LIKE CONCAT('%', #{criteria.value}, '%') ",
                    " or mcp.media_name LIKE CONCAT('%', #{criteria.value}, '%') ",
                    " or mcp.media_position LIKE CONCAT('%', #{criteria.value}, '%') " +
                    " ) ",
            "</if>",
            "<if test='criteria.key == \"cityIds\" and criteria.value != null and criteria.value != \"\" '>",
            "AND ( mcp.city_id in ( ${criteria.value} ) or mcp.district_id in ( ${criteria.value} ) ) ",
            "</if>",
            "<if test='criteria.key == \"planId\" and criteria.value != null and criteria.value != \"\" '>",
            "AND mcp.plan_id = #{criteria.value} ",
            "</if>",
            "</foreach>",
            "</if>",
            "</where>",
            "</script>"
    })
    List<MainCalendarPlacement> searchPlacementsByCondition(SearchCriteria criteria);

    @Update({
            "<script>",
            "UPDATE main_calendar_placement",
            "SET media_position = #{placementInfo.mediaPosition},",
            "longitude = #{placementInfo.longitude},",
            "latitude = #{placementInfo.latitude}," +
                    "updated_by_user = #{userKey} ",
            "WHERE id = #{placementInfo.id} and placement_sn=#{placementInfo.placementSn}",
            "</script>"
    })
    int updatePlacement(PlacementSearchInfo placementInfo, Integer userKey);

    @Update({
            "<script>",
            " UPDATE main_calendar_placement ",
            " SET " +
                    " buffer_area = #{mainCalendarPlacement.bufferArea},",
            " aoi_coordinates = #{mainCalendarPlacement.aoiCoordinates}," +
                    " aoi_area = #{mainCalendarPlacement.aoiArea}," +
                    " aoi_center = #{mainCalendarPlacement.aoiCenter}, " +
                    " longitude = #{mainCalendarPlacement.longitude}, " +
                    " latitude = #{mainCalendarPlacement.latitude}, " +
                    " buffer_type = #{mainCalendarPlacement.bufferType}, " +
                    " updated_by_user = #{userKey} ",
            " WHERE id = #{mainCalendarPlacement.id} and placement_sn=#{mainCalendarPlacement.placementSn}",
            "</script>"
    })
    int updatePlacementBuffer(MainCalendarPlacement mainCalendarPlacement, Integer userKey);

    @Update({
            "<script>",
            " UPDATE media_placement_selection_plan ",
            " SET step_type = #{stepType},",
            " updated_by_user = #{userKey}," +
            " update_time = now() ",
            " WHERE id = #{planId}  ",
            "</script>"
    })
    int updateMediaPlacementSelectionPlanStepType(Long planId, String stepType, Integer userKey);

    @Update({
            "<script>",
            " UPDATE media_placement_selection_plan ",
            " SET  update_time = now() ",
            " WHERE id = #{planId}  ",
            "</script>"
    })
    int updateMediaPlacementSelection(Long planId, Integer userKey);

    @Select("SELECT id,placement_sn,media_name,province_id,city_id,district_id,address_id,address,stat_region_id,medium_lv1_category_id,medium_lv2_category_id,medium_lv3_category_id,resource_provider_type,resource_provider,resource_count,extra_attrs,longitude,latitude,concentration_value,cbd_name,is_deleted,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM main_media_placement WHERE placement_sn=#{placementSn} and medium_lv3_category_id=#{mediumLv3CategoryId} limit 1")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "placementSn", column = "placement_sn"),
            @Result(property = "mediaName", column = "media_name"),
            @Result(property = "provinceId", column = "province_id"),
            @Result(property = "cityId", column = "city_id"),
            @Result(property = "districtId", column = "district_id"),
            @Result(property = "address", column = "address"),
            @Result(property = "addressId", column = "address_id"),
            @Result(property = "statRegionId", column = "stat_region_id"),
            @Result(property = "mediumLv1CategoryId", column = "medium_lv1_category_id"),
            @Result(property = "mediumLv2CategoryId", column = "medium_lv2_category_id"),
            @Result(property = "mediumLv3CategoryId", column = "medium_lv3_category_id"),
            @Result(property = "resourceProviderType", column = "resource_provider_type"),
            @Result(property = "resourceProvider", column = "resource_provider"),
            @Result(property = "resourceCount", column = "resource_count"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
            @Result(property = "longitude", column = "longitude"),
            @Result(property = "latitude", column = "latitude"),
            @Result(property = "concentrationValue", column = "concentration_value"),
            @Result(property = "cbdName", column = "cbd_name"),
            @Result(property = "isDeleted", column = "is_deleted"),
            @Result(property = "createdByUser", column = "created_by_user"),
            @Result(property = "updatedByUser", column = "updated_by_user"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    MainMediaPlacement getByplacementSnAndMediumLv3CategoryId(@Param("placementSn") String placementSn,
            @Param("mediumLv3CategoryId") Integer mediumLv3CategoryId);

    @Select("SELECT " +
            "    grid_hot_data.type_info code ,GROUP_CONCAT( DISTINCT grid_hot_data.statistical_type ) AS statisticalTypes  "
            +
            "    FROM " +
            "    grid_hot_data  " +
            "    inner join main_dict_region on grid_hot_data.type_info = main_dict_region.code and  grid_hot_data.data_type = 'person_land_data' AND grid_hot_data.type = 3  "
            +
            "    inner join main_calendar_placement mcp on main_dict_region.id=mcp.city_id  " +
            "    WHERE " +
            "    grid_hot_data.stat_month = #{statMonth}  " +
            "    and mcp.plan_id=#{planId} " +
            "    GROUP BY grid_hot_data.stat_month,grid_hot_data.type_info " +
            "")
    List<GridHotDataOrder> getGridHotDataAndPlanId(@Param("statMonth") String statMonth, @Param("planId") Long planId);

    @Select("SELECT " +
            " mcp.id, mcp.placement_sn,mcp.plan_id, mcp.media_name, mcp.resource_provider,mcp.resource_provider_type, "
            +
            " mcp.province_id, mcp.city_id, mcp.district_id, mcp.address_id, " +
            " mcp.medium_lv1_category_id, mcp.medium_lv2_category_id, mcp.medium_lv3_category_id,  " +
            " mcp.pack_id, mcp.pack_name, mcp.remark,mcp.extra_attrs, " +
            " mcp.delivery_start_time,mcp.delivery_end_time, " +
            " mcp.longitude,mcp.latitude, vacancy_position, mcp.update_time, mcp.buffer_area,mcp.buffer_type, " +
            " mcp.aoi_area, mcp.aoi_coordinates,mcp.extend_aoi_coordinates,mcp.aoi_center, mcp.aoi_caliber, mcp.aoi_hot_value," +
            " r1.code district_code,  r2.code city_code,  " +
            " ext.brighten, ext.extra_attrs placementExtraAttrs, " +
            " CONCAT(DATE_FORMAT(delivery_start_time,'%Y/%m/%d'),'-',DATE_FORMAT(delivery_end_time,'%Y/%m/%d')) as deliveryTime "
            +
            " from main_calendar_placement mcp " +
            " left join main_dict_region r1 on  mcp.district_id = r1.id  " +
            " left join main_dict_region r2 on  mcp.city_id = r2.id  " +
            " left join main_calendar_placement_ext ext on mcp.id = ext.calendar_placement_id " +
            " where 1=1" +
            " and mcp.plan_id =#{planId} ")
    @ResultMap("calendarPlacementData-mapping")
    List<CalendarPlacementData> selectPlacementSnListByPlanId(@Param("planId") Long planId);

    @Select({
            "<script>",
            "SELECT * from  grid_hot_data   ",
            " WHERE  stat_month = #{statMonth}  data_type = 'person_land_data' and type=#{type} ",
            "AND type_info  IN ",
            "<foreach item='regionCode' collection='regionCodeList' open='(' separator=',' close=')'>",
            " #{regionCode}",
            "</foreach>",
            "</script>"
    })
    @Results(id = "GridHotData_Mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "type", column = "type"),
            @Result(property = "typeInfo", column = "type_info"),
            @Result(property = "statisticalType", column = "statistical_type"),
            @Result(property = "gridCode", column = "grid_code"),
            @Result(property = "longitude", column = "longitude"),
            @Result(property = "latitude", column = "latitude"),
            @Result(property = "hotValue", column = "hot_value"),
            @Result(property = "dataSize", column = "data_size"),
            @Result(property = "timeType", column = "time_type"),
            @Result(property = "timeIndex", column = "time_index"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "extraAttrs", column = "extra_attrs")
    })
    List<GridHotData> getByStatMonthAndCodeList(@Param("statMonth") String statMonth, @Param("type") String type,
            @Param("regionCodeList") List<String> regionCodeList);

    @Select({ " SELECT * from  grid_hot_data   ",
            " WHERE  stat_month = #{statMonth} and data_type = 'person_land_data' and type=#{type} and statistical_type=#{statisticalType}",
            " AND type_info = #{cityCode} "
    })
    @ResultMap("GridHotData_Mapping")
    List<GridHotData> getByStatMonthAndCode(@Param("statMonth") String statMonth, @Param("type") String type,
            @Param("statisticalType") String statisticalType,
            @Param("cityCode") String cityCode);

    @Update({
            "<script>",
            " UPDATE main_calendar_placement",
            " SET aoi_hot_value = #{aoiHotValue}," +
            " aoi_caliber=#{aoiCaliber},",
            " ta_hot_value=#{taHotValue},",
            " updated_by_user = #{userKey} ",
            " WHERE plan_id = #{planId} and placement_sn=#{placementSn}  ",
            "</script>"
    })
    int updateAoiHotValue(Long planId, String placementSn, Double aoiHotValue, Double taHotValue,String aoiCaliber, Integer userKey);

    @Select({
            "<script>",
            "SELECT ",
            "provinceRegion.region_name provinceRegion_name, provinceRegion.id provinceRegion_id, provinceRegion.code provinceRegion_code,",
            "cityRegion.region_name city_name, cityRegion.id city_id, cityRegion.code city_code ",
            "FROM main_bus_line mbl ",
            "INNER JOIN main_dict_region cityRegion ON mbl.city_id = cityRegion.id ",
            "INNER JOIN main_dict_region provinceRegion ON provinceRegion.code = cityRegion.parent_code ",
            "<where>",
            "<if test='criterias != null'>",
            "<foreach collection='criterias' item='criteria'>",
            "<if test='criteria.key == \"cityId\" and criteria.value != null'>",
            "AND cityRegion.id = #{criteria.value} ",
            "</if>",
            "<if test='criteria.key == \"cityName\" and criteria.value != null'>",
            "AND cityRegion.region_name like '%${criteria.value}%' ",
            "</if>",
            "</foreach>",
            "</if>",
            "</where>",
            "GROUP BY mbl.city_id ",
            "ORDER BY provinceRegion.id",
            "</script>"
    })
    @Results(id = "provinceCityInfo_mapping", value = {
            @Result(property = "provinceRegionName", column = "provinceRegion_name"),
            @Result(property = "provinceRegionId", column = "provinceRegion_id"),
            @Result(property = "provinceRegionCode", column = "provinceRegion_code"),
            @Result(property = "cityName", column = "city_name"),
            @Result(property = "cityId", column = "city_id"),
            @Result(property = "cityCode", column = "city_code")
    })
    List<ProvinceCityInfo> getProvinceCityList(SearchCriteria criteria);

    @Select({
            "<script>",
            "SELECT ",
            "provinceRegion.region_name provinceRegion_name, provinceRegion.id provinceRegion_id, provinceRegion.code provinceRegion_code,",
            "cityRegion.region_name city_name, cityRegion.id city_id, cityRegion.code city_code ",
            "FROM main_bus_line mbl ",
            "INNER JOIN main_dict_region cityRegion ON mbl.city_id = cityRegion.id ",
            "INNER JOIN main_dict_region provinceRegion ON provinceRegion.code = cityRegion.parent_code ",
            "<where>",
            "<if test='cityCodes != null and cityCodes.size > 0'>",
            "AND cityRegion.code IN ",
            "<foreach collection='cityCodes' item='cityCode' open='(' separator=',' close=')'>",
            "#{cityCode}",
            "</foreach>",
            "</if>",
            "<if test='criteria != null and criteria.criterias != null'>",
            "<foreach collection='criteria.criterias' item='criteria'>",
            "<if test='criteria.key == \"cityId\" and criteria.value != null'>",
            "AND cityRegion.id = #{criteria.value} ",
            "</if>",
            "<if test='criteria.key == \"cityName\" and criteria.value != null'>",
            "AND cityRegion.region_name like '%${criteria.value}%' ",
            "</if>",
            "</foreach>",
            "</if>",
            "</where>",
            "GROUP BY mbl.city_id ",
            "ORDER BY provinceRegion.id",
            "</script>"
    })
    @ResultMap("provinceCityInfo_mapping")
    List<ProvinceCityInfo> getProvinceCityListByCityCodes(@Param("cityCodes") List<String> cityCodes,
            @Param("criteria") SearchCriteria criteria);

    @Select({
            "<script>",
            "SELECT mp.*, mbl.line_name, mbl.polyline, mbl.bus_stops, mbl.distance ",
            "FROM " +
             " ( " +
                 "SELECT  " +
                 "    *, " +
                 "    JSON_UNQUOTE(JSON_EXTRACT( " +
                 "        extra_attrs, " +
                 "        REPLACE( " +
                 "            JSON_UNQUOTE(JSON_SEARCH(extra_attrs, 'one', '线路', NULL, '$[*].attrKey')), " +
                 "            '.attrKey', " +
                 "            '.attrValue' " +
                 "        ) " +
                 "    )) AS placement_line_name " +
                 "FROM  " +
                 "    main_media_placement " +
                 "WHERE city_id in   " +
                 "<foreach collection='cityIds' item='cityId' open='(' separator=',' close=')'>",
                    "#{cityId}",
                "</foreach>",
                 " and " +
                 "    JSON_SEARCH(extra_attrs, 'one', '线路', NULL, '$[*].attrKey') IS NOT NULL " +
                 " ) as mp " +
            " INNER JOIN  main_bus_line mbl ON   mbl.city_id= mp.city_id and mp.placement_line_name = mbl.line_name  ",
            " WHERE  ",
            "  mp.is_deleted = 0 ",
            " AND mbl.is_deleted = 0 GROUP BY mbl.line_name ",
            "</script>"
    })
    @Results(id = "BusLinePlacementData-mapping", value = {
            @Result(property = "placementSn", column = "placement_sn"),
            @Result(property = "mediaName", column = "media_name"),
            @Result(property = "resourceProvider", column = "resource_provider"),
            @Result(property = "resourceProviderType", column = "resource_provider_type"),
            @Result(property = "provinceId", column = "province_id"),
            @Result(property = "cityId", column = "city_id"),
            @Result(property = "districtId", column = "district_id"),
            @Result(property = "addressId", column = "address_id"),
            @Result(property = "mediumLv1CategoryId", column = "medium_lv1_category_id"),
            @Result(property = "mediumLv2CategoryId", column = "medium_lv2_category_id"),
            @Result(property = "mediumLv3CategoryId", column = "medium_lv3_category_id"),
            @Result(property = "resourceCount", column = "resource_count"),
            @Result(property = "longitude", column = "longitude"),
            @Result(property = "latitude", column = "latitude"),
            @Result(property = "distance", column = "distance"),
            @Result(property = "lineName", column = "line_name"),
            @Result(property = "extraAttrs", column = "extra_attrs"),
    })
    List<BusLinePlacementData> getBusLinePlacementsByCityIds(@Param("cityIds") List<Integer> cityIds);

    @Select({
            "<script>",
            "SELECT mbl.* ",
            "FROM main_media_placement mp ",
            "INNER JOIN main_bus_line mbl ON mp.id = mbl.placement_id ",
            "WHERE mp.placement_sn=#{placementSn} and mp.resource_provider=#{resourceProvider} ",
            "AND mp.is_deleted = 0 ",
            "AND mbl.is_deleted = 0 " +
            " limit 1 ",
            "</script>"
    })
    @Results(id = "mainBusLineMapping", value = {
            @Result(property = "id", column = "id", id = true),
            @Result(property = "placementId", column = "placement_id"),
            @Result(property = "provinceId", column = "province_id"),
            @Result(property = "cityId", column = "city_id"),
            @Result(property = "districtId", column = "district_id"),
            @Result(property = "addressId", column = "address_id"),
            @Result(property = "lineName", column = "line_name"),
            @Result(property = "polyline", column = "polyline"),
            @Result(property = "busStops", column = "bus_stops"),
            @Result(property = "isDeleted", column = "is_deleted"),
            @Result(property = "extra", column = "extra"),
            @Result(property = "createdByUser", column = "created_by_user"),
            @Result(property = "updatedByUser", column = "updated_by_user"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "uiColor", column = "uicolor"),
            @Result(property = "timeDesc", column = "timedesc"),
            @Result(property = "distance", column = "distance"),
            @Result(property = "loop", column = "loop"),
            @Result(property = "status", column = "status"),
            @Result(property = "direc", column = "direc"),
            @Result(property = "company", column = "company"),
            @Result(property = "basicPrice", column = "basic_price"),
            @Result(property = "totalPrice", column = "total_price"),
            @Result(property = "bounds", column = "bounds"),
            @Result(property = "lineId", column = "line_id")
    })
    MainBusLine getBusLinePlacementsn(@Param("placementSn") String placementSn,
            @Param("resourceProvider") String resourceProvider);



    @Select({
            "<script>",
            " SELECT bl.line_id as lineId, bl.line_name as lineName, bl.polyline, bl.bus_stops as busStops,cp.resource_provider extra ",
            " FROM main_bus_line bl ",
            " inner JOIN main_calendar_placement cp ON cp.city_id = bl.city_id ",
            " inner join main_calendar_placement_ext ex on ex.calendar_placement_id = cp.id and  bl.line_name = ex.bus_name",
            "<where>",
            "   <if test='searchCriteria != null and searchCriteria.criterias != null'>",
            "       <foreach collection='searchCriteria.criterias' item='criteria'>",
            "           <if test='criteria.key == \"planId\" and criteria.value != null and criteria.value != \"\"'>",
            "               AND cp.plan_id = #{criteria.value}",
            "           </if>",
            "           <if test='criteria.key == \"lineName\" and criteria.value != null and criteria.value != \"\"'>",
            "               AND bl.line_name LIKE CONCAT('%', #{criteria.value}, '%')",
            "           </if>",
            "           <if test='criteria.key == \"resourceProvider\" and criteria.value != null and criteria.value != \"\"'>",
            "               AND cp.resource_provider LIKE CONCAT('%', #{criteria.value}, '%')",
            "           </if>",
            "       </foreach>",
            "   </if>",
            "</where>",
            " group by cp.id ",
            "</script>"
    })
    List<MainBusLine> getBusLinesByPlacementSearch(@Param("searchCriteria") SearchCriteria searchCriteria);

    @Select({
            "<script>",
            " SELECT bl.line_id as lineId, bl.line_name as lineName, bl.polyline ",
            " FROM main_bus_line bl ",
            " inner JOIN main_calendar_placement cp ON cp.city_id = bl.city_id ",
            " inner join main_calendar_placement_ext ex on ex.calendar_placement_id = cp.id and  bl.line_name = ex.bus_name",
            " where cp.id = #{id} limit 1",
            "</script>"
    })
    MainBusLine getBusLinesByPlacement(@Param("id") Long id);

    @Select({
            "<script>",
            " SELECT cp.id calendarPlacementDataId,bl.id busId, bl.polyline ",
            " FROM main_bus_line bl ",
            " inner JOIN main_calendar_placement cp ON cp.city_id = bl.city_id ",
            " inner join main_calendar_placement_ext ex on ex.calendar_placement_id = cp.id and  bl.line_name = ex.bus_name",
            " where cp.id  in ",
            "<foreach item = 'id' collection='ids' open='(' separator=',' close=')' >" +
                    "#{id}" +
             "</foreach>  " +
             " group by cp.id "+
            "</script>"
    })
    List<MainBusLineInfo> getBusLinesByPlacements(@Param("ids") List<Long> ids);



    @Select({" select grid.* from media_ta_data_management_task task " +
            " inner join ta_grid_task_heat_data grid on task.id=grid.task_id " +
            " where task.task_status='FINISHED' and task.ta_energetics= CONCAT('TA_', #{statisticalType})  and crowd_pack_id=#{crowdPackId} " +
            " and grid.region_code in (${cityCode}) and grid.stat_month=#{statMonth} "
    })
    @Results(id = "Grid_TaHotData_Mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "regionCode", column = "region_code"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "taPopulation", column = "ta_population"),
            @Result(property = "centerLng", column = "center_lng"),
            @Result(property = "centerLat", column = "center_lat"),
            @Result(property = "heat", column = "heat")
    })
    List<TaGridTaskHeatData> getTaValueByStatMonthAndCode(@Param("statMonth") String statMonth, @Param("statisticalType") String statisticalType,
                                            @Param("cityCode") String cityCode, @Param("crowdPackId") Integer crowdPackId);


    @Select("select concat(hotValueCount, '/',totalCount) from (  " +
            "SELECT   " +
            "    SUM(CASE WHEN ta_hot_value IS NOT NULL AND aoi_hot_value IS NOT NULL THEN 1 ELSE 0 END) AS hotValueCount,  " +
            "    COUNT(1) AS totalCount  " +
            "FROM main_calendar_placement  " +
            "WHERE plan_id = #{planId}  " +
            ") as temp")
    String getGenerateCount(@Param("planId") Long planId);


    @Select("select medium_lv3_category_id " +
            "FROM main_calendar_placement  " +
            "WHERE plan_id = #{planId} group by medium_lv3_category_id ")
    List<Integer> getCtegoryIdsByPlanId(@Param("planId") Long planId);


    @Select({"SELECT plan_status FROM media_placement_selection_plan WHERE id=#{id} "})
    String getMediaPlacementSelectionPlanStatusByIdEX(@Param("id") Integer id);

    @Select("select medium_category_ids " +
            "FROM media_placement_selection_plan  " +
            "WHERE id = #{planId}  ")
    String getSelectionPlanCtegoryIdsByPlanId(@Param("planId") Long planId);


    @Select({
            "<script>",
            " SELECT  * ",
            " FROM  main_bus_line   ",
            " where city_id = #{cityId} and line_name = #{LineName}   ",
            " limit 1 ",
            "</script>"
    })
    @ResultMap("mainBusLineMapping")
    MainBusLine getBusLineByCityAndName(@Param("cityId") Integer cityId,
                                      @Param("LineName") String LineName);



    @Update({
            "<script>",
            "UPDATE main_calendar_placement",
            "SET aoi_hot_value = CASE id",
            "  <foreach collection='list' item='item'>",
            "    WHEN #{item.id} THEN #{item.aoiHotValue}",
            "  </foreach>",
            "END,",
            "aoi_caliber = CASE id",
            "  <foreach collection='list' item='item'>",
            "    WHEN #{item.id} THEN #{item.aoiCaliber}",
            "  </foreach>",
            "END,",
            "ta_hot_value = CASE id",
            "  <foreach collection='list' item='item'>",
            "    WHEN #{item.id} THEN #{item.taHotValue}",
            "  </foreach>",
            "END,",
            "updated_by_user = CASE id",
            "  <foreach collection='list' item='item'>",
            "    WHEN #{item.id} THEN #{item.userKey}",
            "  </foreach>",
            "END",
            "WHERE id IN (",
            "  <foreach collection='list' item='item' separator=','>",
            "    #{item.id}",
            "  </foreach>",
            ")",
            "</script>"
    })
    int batchUpdateAoiHotValue(@Param("list") List<CalendarPlacementData> updateList);


    @Select({"SELECT count(1) FROM main_calendar_placement WHERE plan_id=#{planId} and aoi_coordinates is null  "})
    Integer getAoiCoordinates(@Param("planId") Integer planId);

    @Select(" SELECT    " +
            "        SUM(CASE WHEN aoi_coordinates IS NOT NULL  THEN 1 ELSE 0 END) AS runingCount,   " +
            "        COUNT(1) AS totalCount   " +
            "    FROM main_calendar_placement   " +
            "    WHERE plan_id = #{planId} ")
    PlannedSpeedResponse getAoiCoordinatesCount(@Param("planId") Long planId);

    @Select({"SELECT count(1) FROM main_calendar_placement WHERE plan_id=#{planId} and ( longitude is null or latitude is null) "})
    Integer checkLatitudeAndLongitude(@Param("planId") Integer planId);

    @Select(" SELECT    " +
            "        SUM(CASE WHEN longitude IS NOT NULL  THEN 1 ELSE 0 END) AS runingCount,   " +
            "        COUNT(1) AS totalCount   " +
            "    FROM main_calendar_placement   " +
            "    WHERE plan_id = #{planId} ")
    PlannedSpeedResponse selectAddressCoordinate(@Param("planId") Long planId);
}
