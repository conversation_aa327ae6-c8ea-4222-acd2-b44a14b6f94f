package heran.media.management.platform.aioptionmode.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 智选方案列表数据po
 * @Author: yzj
 * @Date: 2023/11/20 16:36
 * @Version: 1.0
 */
@Data
public class AiOptionPlanListData {


    /**
     * 方案id
     */
    private Integer id;

    /**
     * 方案编号
     */
    private String planSn;

    /**
     * 集团名称
     */
    private String groupName;
    
    /**
     * 方案名称
     */
    private String planName;

    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Date updateTime;
    
    /**
     * 智选模式
     */
    private String selectionType;
    
    /**
     * 投放开始时间
     */
    private Date deliveryStartTime;
    
    /**
     * 投放结束时间
     */
    private Date deliveryEndTime;
    
    /**
     * 智选状态
     */
    private String planType;
    /**
     * 媒体形式id集合
     */
    private String mediumCategoryIds;
    /**
     * 媒体属性json集合
     */
    private String mediumAttrExtras;
    /**
     * 地理围栏范围
     */
    private Integer geoRange;
    /**
     * 是否去重
     */
    private Boolean isUnrepeated;
    /**
     * 销售城市编码数组
     */
    private String salesCityRegions;
    /**
     * 方案状态
     */
    private String planStatus;

    /**
     * 步骤
     */
    private String stepType;

    private Double taHotValue;

    private Double aoiHotValue;

    private String aoiCaliber;

    private String creatorName;

    private String bufferArea;


    private Long packId;

    private String packName;
}
