package heran.media.management.platform.aioptionmode.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import heran.media.management.platform.aioptionmode.dto.*;
import heran.media.management.platform.aioptionmode.mapper.AiOptionModeMapper;
import heran.media.management.platform.aioptionmode.mapper.MainCalendarPlacementModelMapper;
import heran.media.management.platform.aioptionmode.po.*;
import heran.media.management.platform.aioptionmode.xls.model.*;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.error.MissRequirementDataException;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.main.service.MainMediaPlacementService;
import heran.media.management.platform.main.subdomain.ds.MainMediaPlacementEntityQueryMapper;
import heran.media.management.platform.main.subdomain.dto.TCategoryIds;
import heran.media.management.platform.mediaresource.nationwidemedia.mapper.BusinessAreaMediaMapper;
import heran.media.management.platform.mediaresource.nationwidemedia.po.RegionData;
import heran.media.sharelib.client.AMapClient;
import heran.media.sharelib.client.handle.AMapResponseHandle;
import heran.media.sharelib.domain.bo.PlanStatus;
import heran.media.sharelib.domain.db.mapper.main.*;
import heran.media.sharelib.domain.db.model.main.MainCalendarPlacement;
import heran.media.sharelib.domain.db.model.main.MediaPlacementRegionMatchResult;
import heran.media.sharelib.domain.db.model.main.MediaPlacementSelectionPlan;
import heran.media.sharelib.domain.db.model.main.PlacementGeolocationMatchResult;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.domain.dto.amap.AMapGeoInfo;
import heran.media.sharelib.domain.dto.amap.AMapGeoSearchResponse;
import heran.media.sharelib.errors.InternalException;
import heran.media.sharelib.utils.PositionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;


/**
 * @Description: 智选模式service
 * @Author: yzj
 * @Date: 2023/11/17 10:26
 * @Version: 1.0
 */
@Slf4j
@Service
public class AiOptionModeService extends BaseCallServiceImpl {

    @Resource
    private AiOptionModeMapper aiOptionModeMapper;
    @Resource
    private MediaPlacementSelectionPlanMapper selectionPlanMapper;
    @Resource
    private BusinessAreaMediaMapper businessAreaMediaMapper;
    @Resource
    private AMapClient aMapClient;
    @Resource
    private PlacementGeolocationMatchResultMapper geolocationMatchResultMapper;
    @Resource
    private MediaPlacementRegionMatchResultMapper regionMatchResultMapper;
    @Resource
    private MediaOrderMapper mediaOrderMapper;
    @Resource
    private MainMediaTagMapper mainMediaTagMapper;
    @Resource
    MainCalendarPlacementModelMapper mainCalendarPlacementModelMapper;
    @Resource
    MainMediaPlacementEntityQueryMapper mainMediaPlacementEntityQueryMapper;
    @Resource
    MainMediaPlacementService mainMediaPlacementService;
    @Resource
    private MediaOrderReferSelectionPlanMapper mediaOrderReferSelectionPlanMapper;

    public PageResponse<AiOptionPlanListResponse> list(String selectionType, SearchCriteria criteria) {
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        PageResponse<AiOptionPlanListResponse> pageResponse = new PageResponse<>();
        List<AiOptionPlanListData> list = aiOptionModeMapper.list(selectionType, criteria);
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        if (CollectionUtils.isEmpty(list)) {
            return pageResponse;
        }
        Set<String> regionCodeList = new HashSet<>();
        Set<Integer> lv3List = new HashSet<>();
//        List<MediumAttrExtras> mediumAttrDataList = new ArrayList<>();
        list.forEach(s -> {
//            if (StringUtils.isNotBlank(s.getMediumAttrExtras())) {
//                mediumAttrDataList.addAll(JSONArray.parseArray(s.getMediumAttrExtras(), MediumAttrExtras.class));
//            }
            if (StringUtils.isNotBlank(s.getSalesCityRegions())) {
                regionCodeList.addAll(JSONArray.parseArray(s.getSalesCityRegions(), String.class));
            }
            if (StringUtils.isNotBlank(s.getMediumCategoryIds())) {
                lv3List.addAll(JSONArray.parseArray(s.getMediumCategoryIds(), Integer.class));
            }
        });
        Map<String, RegionData> groupByCode = null;
        Map<Integer, MediumFormatData> groupLv3Id = null;
        if (regionCodeList.size() > 0) {
            List<RegionData> regionDataList = businessAreaMediaMapper.getRegionDataByAreaOrCityList(new ArrayList<>(regionCodeList));
            groupByCode = regionDataList.stream().collect(Collectors.toMap(RegionData::getCode, Function.identity()));
        }
        if (CollectionUtils.isNotEmpty(lv3List)) {
            List<MediumFormatData> mediumFormatDataList = aiOptionModeMapper.getMediumFormatDataByLv3IdList(lv3List);
            groupLv3Id = mediumFormatDataList.stream().collect(Collectors.toMap(MediumFormatData::getLv3Id, Function.identity()));
        }
        Map<String, RegionData> finalGroupByCode = groupByCode;
        Map<Integer, MediumFormatData> finalGroupLv3Id = groupLv3Id;
        pageResponse.setResults(list.stream().map(s -> new AiOptionPlanListResponse(s, finalGroupByCode, finalGroupLv3Id)).collect(Collectors.toList()));
        return pageResponse;
    }


    public GeoLocationRequirementDetailResponse getGeoLocationRequirementDetail(Integer planId, Integer page, Integer limit) {
        Page pageInfo = PageHelper.startPage(page, limit, true);
        List<PlanRequirementData> requirementData = aiOptionModeMapper.getGeoLocationRequirementData(planId);
        PageResponse<GeoLocationRequirementDetail> pageResponse = new PageResponse<>();
        pageResponse.setCount(pageInfo.getTotal());
        pageResponse.setCurrentPageNumber(page);
        GeoLocationRequirementDetailResponse response = new GeoLocationRequirementDetailResponse();
        response.setDetailDataList(pageResponse);
        if (CollectionUtils.isEmpty(requirementData)) {
            return response;
        }
        response.setPlanStatus(requirementData.get(0).getPlanStatus());
        List<GeoLocationRequirementDetail> resultList = requirementData.stream().map(s -> {
            try {
                return new GeoLocationRequirementDetail(s, new ObjectMapper());
            } catch (JsonProcessingException e) {
                log.error("build RequirementDetailResponse fail with error={} and data={}", e, s);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        pageResponse.setResults(resultList);
        return response;
    }

    public CrowdTagRequirementDetailResponse getCrowdTagRequirementDetail(Integer planId) {
        MediaPlacementSelectionPlan plan = selectionPlanMapper.getByIdEX(planId);
        if (plan == null) {
            return null;
        }
        List<String> regionCodeList = JSONArray.parseArray(plan.getSalesCityRegions(), String.class);
        List<RegionData> regionDataList = businessAreaMediaMapper.getRegionDataByAreaOrCityList(regionCodeList);
        CrowdTagRequirementDetailResponse response = new CrowdTagRequirementDetailResponse();
        response.setPlanStatus(plan.getPlanStatus());
        if (CollectionUtils.isNotEmpty(regionDataList)) {
            response.setDetailDataList(regionDataList.stream().map(CrowdTagRequirementDetail::new).collect(Collectors.toList()));
        }
        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<GeoLocationMatchResultResponse> geoLocationScheduleUpload(Integer planId, MultipartFile file, Integer identifier) {
        List<PlanRequirementData> requirementDataList = aiOptionModeMapper.getGeoLocationRequirementData(planId);
        if (CollectionUtils.isEmpty(requirementDataList)) {
            throw new MissRequirementDataException();
        }
        selectionPlanMapper.updatePlanStatus(planId, PlanStatus.ONGOING.name(), appName, identifier);
        List<GeoLocationMatchResultResponse> resultList = new ArrayList<>();
        List<GeoLocationMatchResultResponse> matchResultList = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            EasyExcel.read(file.getInputStream(), GeoLocationScheduleXlsModel.class, new PageReadListener<GeoLocationScheduleXlsModel>(
                    dataList -> {
                        for (GeoLocationScheduleXlsModel scheduleData : dataList) {
                            try {
                                String keyword = scheduleData.getProvince() + scheduleData.getCity() + scheduleData.getArea() + scheduleData.getDetailAddress();
                                AMapGeoSearchResponse response = aMapClient.searchGeo(scheduleData.getArea(), keyword, identifier);
                                AMapResponseHandle.handle(response);
                                for (PlanRequirementData requirementData : requirementDataList) {
                                    double requirementDataLongitude = Double.parseDouble(requirementData.getLongitude());
                                    double requirementDataLatitude = Double.parseDouble(requirementData.getLatitude());
                                    GeoLocationScheduleParseData parseData = parseScheduleData(response, requirementDataLongitude, requirementDataLatitude, scheduleData);
                                    if (parseData != null && parseData.getDistance() != null && parseData.getDistance() <= requirementData.getGeoRange()) {
                                        matchResultList.add(new GeoLocationMatchResultResponse(requirementData, parseData, objectMapper));
                                    }
                                }
                            } catch (InternalException ie) {
                                log.error("parse scheduleData fail with scheduleData = {},error ={}", new Gson().toJson(scheduleData), ie.getParams() == null
                                        ? InternalResponse.fail(ie.getErrorCode()).getBody() : ie.getParams());
                            } catch (Exception e) {
                                log.error("parse scheduleData fail with scheduleData = {},error =", new Gson().toJson(scheduleData), e);
                            }
                        }
                    }
            )).sheet().doRead();
            resultList = matchResultList;
            if (requirementDataList.get(0).getIsDistinct()) {
                resultList = new ArrayList<>(matchResultList.stream()
                        .collect(Collectors.toMap(
                                GeoLocationMatchResultResponse::getCoverPointAddress,
                                Function.identity(),
                                //出现重复键时比较distance,替换成最小的
                                (existing, replacement) ->
                                        existing.getDistance() < replacement.getDistance() ? existing : replacement
                        ))
                        .values());
            }
        } catch (Exception e) {
            log.error("geoLocationScheduleUpload fail with error=", e);
        } finally {
            selectionPlanMapper.updatePlanStatus(planId, PlanStatus.WAIT_SUBMIT_RESULT.name(), appName, identifier);
        }
        List<GeoLocationMatchResultRequest> resultRequestList = resultList.stream().map(GeoLocationMatchResultResponse::adaptToGeoLocationMatchResultRequest).collect(Collectors.toList());
        submitGeoLocationMatchResult(planId, resultRequestList, identifier, false);
        return resultList;
    }

    private GeoLocationScheduleParseData parseScheduleData(AMapGeoSearchResponse response, double requirementDataLongitude, double requirementDataLatitude, GeoLocationScheduleXlsModel scheduleData) {
        try {

            List<AMapGeoInfo> geoInfoList = response.getGeocodes();
            if (CollectionUtils.isEmpty(geoInfoList)) {
                return null;
            }
            String location = geoInfoList.get(0).getLocation();
            String[] split = location.split(",");
            double longitude = Double.parseDouble(split[0]);
            double latitude = Double.parseDouble(split[1]);
            Double distance = PositionUtil.getDistance(longitude, latitude, requirementDataLongitude, requirementDataLatitude);
            return new GeoLocationScheduleParseData(scheduleData, distance, split[0], split[1]);
        } catch (Exception e) {
            log.error("compareDistance fail with scheduleData = {},error=", new Gson().toJson(scheduleData), e);
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void submitGeoLocationMatchResult(Integer planId, List<GeoLocationMatchResultRequest> matchResultList, Integer identifier, boolean isSubmit) {
        List<Integer> geolocationIdList = aiOptionModeMapper.getGeolocationIdList(planId);
        if (CollectionUtils.isNotEmpty(geolocationIdList)) {
            aiOptionModeMapper.deleteMatchResults(geolocationIdList);
        }
        List<PlacementGeolocationMatchResult> dataList = matchResultList.stream()
                .map(s -> {
                    PlacementGeolocationMatchResult matchResult = s.adaptToGeolocationMatchResult();
                    setCreatorInfo(identifier, matchResult);
                    setUpdaterInfo(identifier, matchResult);
                    return matchResult;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        geolocationMatchResultMapper.batchInsertUpdateEntity(dataList);
        if (isSubmit) {
            selectionPlanMapper.updatePlanStatus(planId, PlanStatus.COMPLETED.name(), appName, identifier);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public List<CrowdTagMatchResultResponse> crowdTagScheduleUpload(Integer planId, MultipartFile file, Integer identifier) {
        String crowdTagRequirementData = aiOptionModeMapper.getCrowdTagRequirementData(planId);
        if (StringUtils.isEmpty(crowdTagRequirementData)) {
            throw new MissRequirementDataException();
        }
        selectionPlanMapper.updatePlanStatus(planId, PlanStatus.ONGOING.name(), appName, identifier);
        List<String> requirementRegionCodeList = JSONArray.parseArray(crowdTagRequirementData, String.class);
        List<RegionData> regionDataList = businessAreaMediaMapper.getRegionDataByAreaOrCityList(requirementRegionCodeList);
        Map<String, CrowdTagMatchResultResponse> resultMap = regionDataList.stream().collect(Collectors.toMap(RegionData::getCode, CrowdTagMatchResultResponse::new));
        try {
            EasyExcel.read(file.getInputStream(), CrowdTagScheduleXlsModel.class, new PageReadListener<CrowdTagScheduleXlsModel>(
                    dataList -> {
                        for (CrowdTagScheduleXlsModel scheduleData : dataList) {
                            try {
                                for (RegionData requirementData : regionDataList) {
                                    boolean isMatch = (requirementData.getArea() != null && requirementData.getCity().equals(scheduleData.getCity()) && requirementData.getName().equals(scheduleData.getArea()))
                                            || (requirementData.getArea() == null && requirementData.getName().equals(scheduleData.getCity()));
                                    if (isMatch) {
                                        CrowdTagMatchResultResponse matchResultResponse = resultMap.get(requirementData.getCode());
                                        matchResultResponse.setCoverPointCount(matchResultResponse.getCoverPointCount() + 1);
                                        matchResultResponse.setConcentrationScore(matchResultResponse.getConcentrationScore().add(new BigDecimal(scheduleData.getConcentrationScore())));
                                    }
                                }
                            } catch (Exception e) {
                                log.error("parse scheduleData fail with scheduleData = {},error = {}", new Gson().toJson(scheduleData), e);
                            }
                        }
                    }
            )).sheet().doRead();
        } catch (IOException e) {
            log.error("crowdTagScheduleUpload fail with error=", e);
        } finally {
            selectionPlanMapper.updatePlanStatus(planId, PlanStatus.WAIT_SUBMIT_RESULT.name(), appName, identifier);
        }
        List<CrowdTagMatchResultResponse> resultList = new ArrayList<>(resultMap.values());
        List<CrowdTagMatchResultRequest> resultRequestList = resultList.stream().map(CrowdTagMatchResultRequest::new).collect(Collectors.toList());
        submitCrowdTagMatchResult(planId, resultRequestList, identifier, false);
        return resultList.stream().map(CrowdTagMatchResultResponse::setAvgConcentrationScore)
                .sorted(Comparator.comparing(CrowdTagMatchResultResponse::getConcentrationScore)
                        .reversed()).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void submitCrowdTagMatchResult(Integer planId, List<CrowdTagMatchResultRequest> request, Integer identifier, boolean isSubmit) {
        regionMatchResultMapper.deleteByPlanId(planId);
        List<MediaPlacementRegionMatchResult> regionMatchResultList = request.stream().map(s -> {
            MediaPlacementRegionMatchResult regionMatchResult = s.adaptToRegionMatchResult();
            regionMatchResult.setPlanId(planId);
            setCreatorInfo(identifier, regionMatchResult);
            setUpdaterInfo(identifier, regionMatchResult);
            return regionMatchResult;
        }).collect(Collectors.toList());
        regionMatchResultMapper.batchInsertUpdateEntity(regionMatchResultList);
        if (isSubmit) {
            selectionPlanMapper.updatePlanStatus(planId, PlanStatus.COMPLETED.name(), appName, identifier);
        }
    }

    public void logicDeletePlanData(Integer planId, Integer identifier) {
        selectionPlanMapper.deleteByIdLogically(appName, identifier, planId);
        //删除引用改方案的智选订单
        mediaOrderMapper.updateReferSelectionPlanId(planId);
        //mediaOrderReferSelectionPlanMapper.deleteSelectionPlan(planId);
    }

    public void geoLocationScheduleTemplateDownload(HttpServletResponse response) {
        try {
            setFileResponse(response, "地理围栏档期模板");
            EasyExcel.write(response.getOutputStream(), GeoLocationScheduleXlsModel.class).sheet().doWrite(Collections.emptyList());
        } catch (Exception e) {
            log.error("geoLocationScheduleTemplateDownload fail with error=", e);
        }
    }

    public void crowdTagScheduleTemplateDownload(HttpServletResponse response) {
        try {
            setFileResponse(response, "TA浓度档期模板");
            EasyExcel.write(response.getOutputStream(), CrowdTagScheduleXlsModel.class).sheet().doWrite(Collections.emptyList());
        } catch (Exception e) {
            log.error("crowdTagScheduleTemplateDownload fail with error=", e);
        }
    }

    public List<GeoLocationMatchResultResponse> getGeoLocationMatchResult(Integer planId) {
//        Page pageInfo = PageHelper.startPage(page, limit, true);
        List<GeoLocationMatchResultData> matchResultDataList = aiOptionModeMapper.getGeoLocationMatchResultDataList(planId);
//        PageResponse<GeoLocationMatchResultResponse> pageResponse = new PageResponse<>();
//        pageResponse.setCount(pageInfo.getTotal());
//        pageResponse.setCurrentPageNumber(page);
        if (CollectionUtils.isEmpty(matchResultDataList)) {
//            return pageResponse;
            return Collections.emptyList();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        List<GeoLocationMatchResultResponse> resultList = new ArrayList<>();
        for (GeoLocationMatchResultData matchResultData : matchResultDataList) {
            try {
                resultList.add(new GeoLocationMatchResultResponse(matchResultData, objectMapper));
            } catch (Exception e) {
                log.error("getGeoLocationMatchResult read geoLocation match result fail with error=", e);
            }
        }
//        pageResponse.setResults(resultList);
//        return pageResponse;
        return resultList;
    }

    public List<CrowdTagMatchResultResponse> getCrowdTagMatchResult(Integer planId) {
//        Page pageInfo = PageHelper.startPage(page, limit, true);
        List<CrowdTagMatchResultData> crowdTagMatchResultList = aiOptionModeMapper.getCrowdTagMatchResultList(planId);
//        PageResponse<CrowdTagMatchResultResponse> pageResponse = new PageResponse<>();
//        pageResponse.setCount(pageInfo.getTotal());
//        pageResponse.setCurrentPageNumber(page);
        if (CollectionUtils.isEmpty(crowdTagMatchResultList)) {
//            return pageResponse;
            return Collections.emptyList();
        }
//        pageResponse.setResults(crowdTagMatchResultList.stream().map(CrowdTagMatchResultResponse::new).collect(Collectors.toList()));
        return crowdTagMatchResultList.stream().map(CrowdTagMatchResultResponse::new)
                .sorted(Comparator.comparing(CrowdTagMatchResultResponse::getConcentrationScore)
                        .reversed()).collect(Collectors.toList());
    }

    public String getPlanStatus(Integer planId) {
        MediaPlacementSelectionPlan plan = selectionPlanMapper.getByIdEX(planId);
        return plan.getPlanStatus();
    }

    public void geoLocationMatchResultExport(HttpServletResponse response, Integer planId) {
        try {
            List<GeoLocationMatchResultData> matchResultDataList = aiOptionModeMapper.getGeoLocationMatchResultDataList(planId);
            if (CollectionUtils.isEmpty(matchResultDataList)) {
                return;
            }
            ObjectMapper objectMapper = new ObjectMapper();
            List<GeoLocationMatchResultXlsModel> resultList = new ArrayList<>();
            for (GeoLocationMatchResultData matchResultData : matchResultDataList) {
                try {
                    resultList.add(new GeoLocationMatchResultXlsModel(matchResultData, objectMapper));
                } catch (Exception e) {
                    log.error("geoLocationMatchResultExport read geoLocation match result fail with error=", e);
                }
            }
            MediaPlacementSelectionPlan plan = selectionPlanMapper.getByIdEX(planId);
            setFileResponse(response, plan.getPlanName() + "地理围栏方案匹配结果");
            EasyExcel.write(response.getOutputStream(), GeoLocationMatchResultXlsModel.class).sheet().doWrite(resultList);
        } catch (Exception e) {
            log.error("geoLocationMatchResultExport fail with error=", e);
        }
    }

    public void crowdTagMatchResultExport(HttpServletResponse response, Integer planId) {
        try {
            List<CrowdTagMatchResultData> crowdTagMatchResultList = aiOptionModeMapper.getCrowdTagMatchResultList(planId);
            if (CollectionUtils.isEmpty(crowdTagMatchResultList)) {
                return;
            }
            List<CrowdTagMatchResultXlsModel> resultList = crowdTagMatchResultList.stream().map(CrowdTagMatchResultXlsModel::new).collect(Collectors.toList());
            MediaPlacementSelectionPlan plan = selectionPlanMapper.getByIdEX(planId);
            setFileResponse(response, plan.getPlanName() + "TA浓度选点方案匹配结果");
            EasyExcel.write(response.getOutputStream(), CrowdTagMatchResultXlsModel.class).sheet().doWrite(resultList);
        } catch (Exception e) {
            log.error("crowdTagMatchResultExport fail with error=", e);
        }
    }


    public void geoLocationRequirementDetailExport(HttpServletResponse response, Integer planId) {
        try {
            List<PlanRequirementData> requirementDataList = aiOptionModeMapper.getGeoLocationRequirementData(planId);
            if (CollectionUtils.isEmpty(requirementDataList)) {
                return;
            }
            List<GeoLocationRequirementXlsModel> resultList = new ArrayList<>();
            ObjectMapper objectMapper = new ObjectMapper();
            for (PlanRequirementData planRequirementData : requirementDataList) {
                try {
                    resultList.add(new GeoLocationRequirementXlsModel(planRequirementData, objectMapper));
                } catch (Exception e) {
                    log.error("geoLocationMatchResultExport read geoLocation requirement detail data fail with error=", e);
                }
            }
            MediaPlacementSelectionPlan plan = selectionPlanMapper.getByIdEX(planId);
            setFileResponse(response, plan.getPlanName() + "地理围栏方案需求详情");
            EasyExcel.write(response.getOutputStream(), GeoLocationRequirementXlsModel.class).sheet().doWrite(resultList);
        } catch (Exception e) {
            log.error("geoLocationRequirementDetailExport fail with error=", e);
        }
    }


    public void taDensityRequirementExport(HttpServletResponse response, Integer planId) {
        try {

            // 获取基础数据
            MediaPlacementSelectionPlan plan = selectionPlanMapper.getByIdEX(planId);
            // 处理基础方案数据
            AiOptionBasePlanDetailData baseData = aiOptionModeMapper.getBasePlanDetailData(planId);
            // 设置行业标签
            Optional.ofNullable(mainMediaTagMapper.getByIdEX(baseData.getBelongIndustryCategoryId()))
                    .ifPresent(tag -> baseData.setIndustryTag(tag.getLabel()));
            // 处理地区信息
            String coreRegionsJson = baseData.getCoreSalesRegions();
            List<String> regions = JSONArray.parseArray(coreRegionsJson, String.class);
            String area = CollectionUtils.isNotEmpty(regions) ?
                    aiOptionModeMapper.getByRegionName(regions).stream()
                            .map(city -> Stream.of(city.getOneName(), city.getTwoName(), city.getThreeName())
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.joining("/")))
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(", ")) : "";
            baseData.setArea(area);

            // 构建导出数据
            setFileResponse(response, plan.getPlanName() + "TA浓度选点方案匹配结果");
            EasyExcel.write(response.getOutputStream(), TaDensityRequirementXlsModel.class)
                    .sheet()
                    .doWrite(Collections.singletonList(new TaDensityRequirementXlsModel(baseData)));

        } catch (Exception e) {
            log.error("TA浓度导出失败 - {}", e.getMessage(), e);
        }
    }

    public void taDensityScheduleTemplateDownload(HttpServletResponse response) {
        try {
            setFileResponse(response, "TA浓度档期模板");
            EasyExcel.write(response.getOutputStream(), TaDensityScheduleXlsModel.class).sheet().doWrite(Collections.emptyList());
        } catch (Exception e) {
            log.error("crowdTagScheduleTemplateDownload fail with error=", e);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public List<String> taDensityScheduleUpload(Integer planId, MultipartFile file, Integer identifier) {
        MediaPlacementSelectionPlan byIdEX = selectionPlanMapper.getByIdEX(planId);
        if (byIdEX == null) {
            throw new MissRequirementDataException();
        }
        List<String> errMsg = new ArrayList<>();
        mainCalendarPlacementModelMapper.deleteByplanId(planId);
        List<MainCalendarPlacement> mainCalendarPlacementList = new ArrayList<>();

        List<Integer> categoryIdList = JSONArray.parseArray(byIdEX.getMediumCategoryIds(), Integer.class);
        List<String> regionsCodeList = JSONArray.parseArray(byIdEX.getSalesCityRegions(), String.class);
        List<TCategoryIds> byCategoryIds = mainMediaPlacementEntityQueryMapper.getByCategoryIds(categoryIdList);
        List<Integer> regionsIdList = mainMediaPlacementEntityQueryMapper.getIdsByRegionCodes(regionsCodeList);
        HashMap<String, String> publicRedisTMediaDictRegionMap = mainMediaPlacementService.getPublicRedisTMediaDictRegionMap();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            EasyExcel.read(file.getInputStream(), TaDensityScheduleXlsModel.class, new PageReadListener<TaDensityScheduleXlsModel>(
                    dataList -> {
                        for (TaDensityScheduleXlsModel scheduleData : dataList) {
                            try {
                                MainCalendarPlacement currMainCalendarPlacement = new MainCalendarPlacement();
                                currMainCalendarPlacement.setPlanId(planId);
                                currMainCalendarPlacement.setPlacementSn(scheduleData.getPlacementSn());
                                currMainCalendarPlacement.setMediaName(scheduleData.getMediaName());
                                currMainCalendarPlacement.setResourceProvider(scheduleData.getResourceProvider());
                                currMainCalendarPlacement.setResourceProviderType(scheduleData.getResourceProviderType());
                                currMainCalendarPlacement.setPackId(scheduleData.getPackId());
                                currMainCalendarPlacement.setPackName(scheduleData.getPackName());
                                currMainCalendarPlacement.setResourceProviderType(scheduleData.getResourceProviderType());
                                currMainCalendarPlacement.setAoiCoordinates(scheduleData.getAoiCoordinates());
                                currMainCalendarPlacement.setVacancyPosition(scheduleData.getVacancyPosition());
                                currMainCalendarPlacement.setBufferArea(scheduleData.getBufferArea());
                                currMainCalendarPlacement.setBufferType(scheduleData.getBufferType());
                                currMainCalendarPlacement.setDeliveryStartTime(dateFormat.parse(scheduleData.getDeliveryStartStr()));
                                currMainCalendarPlacement.setDeliveryEndTime(dateFormat.parse(scheduleData.getDeliveryEndStr()));

                                String mediaCategoryExcel = scheduleData.getMediumLv1Category() + scheduleData.getMediumLv2Category() + scheduleData.getMediumLv3Category();

                                Optional<TCategoryIds> matchedCategory = byCategoryIds.stream()
                                        .filter(c -> {
                                            String categoryName = Stream.of(c.getCategoryIdLv1Name(),
                                                    c.getCategoryIdLv2Name(),
                                                    c.getCategoryIdLv3Name())
                                                    .collect(Collectors.joining());
                                            return categoryName.equals(mediaCategoryExcel);
                                        })
                                        .findFirst();

                                if (matchedCategory.isPresent()) {
                                    TCategoryIds category = matchedCategory.get();
                                    currMainCalendarPlacement.setMediumLv1CategoryId(category.getCategoryIdLv1());
                                    currMainCalendarPlacement.setMediumLv2CategoryId(category.getCategoryIdLv2());
                                    currMainCalendarPlacement.setMediumLv3CategoryId(category.getCategoryIdLv3());
                                } else {
                                    errMsg.add(scheduleData.getPlacementSn() + "媒体分类不存在：" + mediaCategoryExcel);
                                }


                                String province = scheduleData.getProvince() != null ? scheduleData.getProvince() : "";
                                String city = scheduleData.getCity() != null ? scheduleData.getCity() : "";
                                String district = scheduleData.getDistrict() != null ? scheduleData.getDistrict() : "";
                                String address = scheduleData.getAddress() != null ? scheduleData.getAddress() : "";

                                String cityName = province + city + district + address;
                                String cityIdStr = publicRedisTMediaDictRegionMap.get(cityName);
                                if (StringUtils.isBlank(cityIdStr)) {
                                    errMsg.add(scheduleData.getPlacementSn() + "地址不存在：" + cityName);
                                    continue;
                                }
                                List<Integer> cityIdList = Arrays.stream(cityIdStr.split(","))
                                        .map(Integer::parseInt)
                                        .collect(Collectors.toList());
                                int cityIdListSize = cityIdList.size();
                                if (regionsIdList.contains(cityIdList.get(cityIdListSize - 1))) {
                                    IntStream.range(0, cityIdList.size()).forEach(i -> {
                                        switch (i) {
                                            case 0:
                                                currMainCalendarPlacement.setProvinceId(cityIdList.get(0));
                                                break;
                                            case 1:
                                                currMainCalendarPlacement.setCityId(cityIdList.get(1));
                                                break;
                                            case 2:
                                                currMainCalendarPlacement.setDistrictId(cityIdList.get(2));
                                                break;
                                            case 3:
                                                currMainCalendarPlacement.setAddressId(cityIdList.get(3));
                                                break;
                                        }
                                    });
                                } else {
                                    errMsg.add(scheduleData.getPlacementSn() + "地址不属于销售城市：" + cityName);
                                    continue;
                                }

                                currMainCalendarPlacement.setCreator(appName);
                                currMainCalendarPlacement.setUpdater(appName);
                                currMainCalendarPlacement.setCreateTime(new Date());
                                mainCalendarPlacementList.add(currMainCalendarPlacement);
                            } catch (Exception e) {
                                log.error("parse scheduleData fail with scheduleData = {},error = {}", new Gson().toJson(scheduleData), e);
                            }
                        }
                    }
            )).sheet().doRead();
        } catch (IOException e) {
            log.error("crowdTagScheduleUpload fail with error=", e);
        }
        if (mainCalendarPlacementList.size() > 0) {
            mainCalendarPlacementModelMapper.batchInsertEntities(mainCalendarPlacementList);
        }
        return errMsg;
    }


    public PageResponse<TaDensityScheduleResponse> taDensityScheduleList(TaDensityScheduleRequest request) {

        Page page = PageHelper.startPage(request.getPage(), request.getLimit(), true);
        List<TaDensityScheduleData> searchResult = mainCalendarPlacementModelMapper.selectByplanId(request.getPlanId(), request.getRegionCodes(), request.getCategoryIds(), request.getKeyword());
        PageResponse<TaDensityScheduleResponse> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(request.getPage());
        pageResponse.setResults(searchResult.stream().map(TaDensityScheduleResponse::new).collect(Collectors.toList()));
        return pageResponse;
    }


    public void taDensityScheduleExport(HttpServletResponse response, Integer planId) {
        try {
            List<TaDensityScheduleData> taDensityScheduleData = mainCalendarPlacementModelMapper.selectByplanId(planId, null, null, null);
            if (CollectionUtils.isEmpty(taDensityScheduleData)) {
                return;
            }
            List<TaDensityScheduleExportXlsModel> collect = taDensityScheduleData.stream().map(TaDensityScheduleExportXlsModel::new).collect(Collectors.toList());
            MediaPlacementSelectionPlan plan = selectionPlanMapper.getByIdEX(planId);
            setFileResponse(response, plan.getPlanName() + "档期明细");
            EasyExcel.write(response.getOutputStream(), TaDensityScheduleExportXlsModel.class).sheet().doWrite(collect);
        } catch (Exception e) {
            log.error("geoLocationRequirementDetailExport fail with error=", e);
        }
    }
}
