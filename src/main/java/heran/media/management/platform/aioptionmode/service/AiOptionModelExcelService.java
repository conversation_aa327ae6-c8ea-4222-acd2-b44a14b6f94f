package heran.media.management.platform.aioptionmode.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import heran.media.management.platform.aioptionmode.dto.BufferAreaInput;
import heran.media.management.platform.aioptionmode.dto.BufferAreaOutput;
import heran.media.management.platform.aioptionmode.dto.MediumFormatInfo;
import heran.media.management.platform.aioptionmode.dto.ValidationInput;
import heran.media.management.platform.aioptionmode.mapper.AiOptionModeMapper;
import heran.media.management.platform.aioptionmode.mapper.MainCalendarPlacementModelMapper;
import heran.media.management.platform.aioptionmode.po.*;
import heran.media.management.platform.aioptionmode.validators.ExcelValidatorManager;
import heran.media.management.platform.aioptionmode.xls.listener.MapStringKeyListener;
import heran.media.management.platform.aioptionmode.xls.model.DynamicPlacementImportXlsModel;
import heran.media.management.platform.common.build.PageResponseBuilder;
import heran.media.management.platform.common.db.po.RegionDetail;
import heran.media.management.platform.common.utils.DateUtils;
import heran.media.sharelib.client.AMapClient;
import heran.media.sharelib.client.handle.AMapResponseHandle;
import heran.media.sharelib.domain.bo.PlanStep;
import heran.media.management.platform.common.domain.dto.MetaFieldInfo;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.management.platform.mediaorder.sudomain.dto.ResultMatchDetailErrorData;
import heran.media.sharelib.domain.bo.PlanType;
import heran.media.sharelib.domain.db.mapper.main.MainCalendarPlacementExtMapper;
import heran.media.sharelib.domain.db.mapper.main.MainCalendarPlacementMapper;
import heran.media.sharelib.domain.db.mapper.main.MainDictRegionMapper;
import heran.media.sharelib.domain.db.mapper.main.MediaSelectionPlanMenuFieldAttrMapper;
import heran.media.sharelib.domain.db.model.main.*;
import heran.media.sharelib.domain.dto.amap.AMapBusDataResponse;
import heran.media.sharelib.domain.dto.amap.AMapGeoInfo;
import heran.media.sharelib.domain.dto.amap.AMapGeoSearchResponse;
import heran.media.sharelib.domain.dto.amap.BusLine;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.function.Function;

import static heran.media.management.platform.common.constants.AppConstants.MEDIA_SELECTION_PLAN_MENU_FIELD_ATTR_COMMON;
import static heran.media.management.platform.common.constants.AppConstants.MEDIA_SELECTION_PLAN_MENU_FIELD_ATTR_EXPAND;
import static heran.media.management.platform.common.utils.MetaFieldUtils.convertToMetaFieldInfoList;

/**
 * AI选项模式Excel导出服务
 */
@Slf4j
@Service
public class AiOptionModelExcelService extends BaseCallServiceImpl {

    @Resource
    private MainCalendarPlacementExtMapper mainCalendarPlacementExtMapper;

    @Resource
    private MainCalendarPlacementMapper mainCalendarPlacementMapper;

    @Resource
    private AiOptionModeMapper aiOptionModeMapper;

    @Resource
    private ExcelValidatorManager excelValidatorManager;

    @Resource
    private MediaSelectionPlanMenuFieldAttrMapper mediaSelectionPlanMenuFieldAttrMapper;

    @Autowired
    private CalendarPlacementService calendarPlacementService;

    @Autowired
    private CrowdTagPlanService crowdTagPlanService;

    @Autowired
    private BusSmartPlanService busSmartPlanService;
    @Resource
    private MainDictRegionMapper mainDictRegionMapper;
    @Resource
    private AMapClient aMapClient;
    @Resource
    private MainCalendarPlacementModelMapper mainCalendarPlacementModelMapper;

    private void insertOrUpdateData(List<MainCalendarPlacement> mainCalendarPlacementList,
            Map<String, MainCalendarPlacementExt> resultMap, Integer planId,
            String selectionType, Integer identifier,String uploadType) {
        List<BusLinePlacementData> busLinePlacementDatas = new ArrayList<>();
        deleteOldData(planId,uploadType);

        List<List<MainCalendarPlacement>> partition = Lists.partition(mainCalendarPlacementList, 50);
        for (List<MainCalendarPlacement> list : partition) {
            mainCalendarPlacementMapper.batchInsertOrUpdate(list);

            List<String> placementSnList = list.stream().map(MainCalendarPlacement::getPlacementSn)
                    .collect(Collectors.toList());
            List<MainCalendarPlacement> newDataList = mainCalendarPlacementMapper.getByPlanIdAndSns(planId.longValue(),
                    placementSnList);
            Map<String, MainCalendarPlacement> collect = newDataList.stream()
                    .collect(Collectors.toMap(MainCalendarPlacement::getPlacementSn,  Function.identity()));

            List<Long> calendarPlacementIds = newDataList.stream().map(MainCalendarPlacement::getId).collect(Collectors.toList());

            List<MainCalendarPlacementExt> byCalendarPlacementIds = mainCalendarPlacementExtMapper.getByCalendarPlacementIds(calendarPlacementIds.stream().map(Long::intValue).collect(Collectors.toList()));
            Map<Integer, MainCalendarPlacementExt> placementExtMap = byCalendarPlacementIds.stream().collect(Collectors.toMap(MainCalendarPlacementExt::getCalendarPlacementId, Function.identity()));

            List<MainCalendarPlacementExt> mainCalendarPlacementExts = new ArrayList<>();
            resultMap.keySet().forEach(key -> {
                MainCalendarPlacementExt mainCalendarPlacementExt = resultMap.get(key);
                MainCalendarPlacement placement = collect.get(key);
                if (placement != null) {
                    mainCalendarPlacementExt.setCalendarPlacementId(placement.getId().intValue());
                    getBusLinePlacementDataBySelectionType(placement, mainCalendarPlacementExt, selectionType, busLinePlacementDatas, identifier);
                    addOldPlacementExtData(mainCalendarPlacementExt, placementExtMap);
                    mainCalendarPlacementExts.add(mainCalendarPlacementExt);
                }
            });
            if(!mainCalendarPlacementExts.isEmpty()){
                mainCalendarPlacementExtMapper.batchInsertOrUpdate(mainCalendarPlacementExts);
            }
        }
    }

    private void deleteOldData(Integer planId,String uploadType) {
        if(uploadType.equals(PlanStep.IN_DEMAND_UPLOAD.name())){
            mainCalendarPlacementModelMapper.deletePlacementExtByIdEX(planId.longValue());
            mainCalendarPlacementModelMapper.deletePlacementByPlanId(planId.longValue());
        }
    }

    private void addOldPlacementExtData(MainCalendarPlacementExt mainCalendarPlacementExt, Map<Integer, MainCalendarPlacementExt> placementExtMap){
        ObjectMapper objectMapper = new ObjectMapper();
        MainCalendarPlacementExt oldData = placementExtMap.get(mainCalendarPlacementExt.getCalendarPlacementId());
        if(oldData!=null && StringUtils.isNotBlank(oldData.getExtraAttrs()) && StringUtils.isNotBlank(mainCalendarPlacementExt.getExtraAttrs())){
            try {
                List<MediaSelectionPlanMenuFieldAttrData> oldDataAttrList = objectMapper.readValue(oldData.getExtraAttrs(), new TypeReference<List<MediaSelectionPlanMenuFieldAttrData>>() {
                });
                List<MediaSelectionPlanMenuFieldAttrData> newDataAttrList = objectMapper.readValue(mainCalendarPlacementExt.getExtraAttrs(), new TypeReference<List<MediaSelectionPlanMenuFieldAttrData>>() {
                });

                // 比对 oldDataAttrList 和 newDataAttrList，将 oldData 中有但 newData 中没有的数据添加到 newData 中
                mergeMissingAttrData(oldDataAttrList, newDataAttrList);

                // 将合并后的数据重新序列化并更新
                String mergedExtraAttrs = objectMapper.writeValueAsString(newDataAttrList);
                mainCalendarPlacementExt.setExtraAttrs(mergedExtraAttrs);

            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }

    }

    /**
     * 合并缺失的属性数据
     * 将 oldDataAttrList 中存在但 newDataAttrList 中不存在的数据添加到 newDataAttrList 中
     *
     * @param oldDataAttrList 旧数据属性列表
     * @param newDataAttrList 新数据属性列表（会被修改）
     */
    private void mergeMissingAttrData(List<MediaSelectionPlanMenuFieldAttrData> oldDataAttrList,
                                     List<MediaSelectionPlanMenuFieldAttrData> newDataAttrList) {
        if (oldDataAttrList == null || oldDataAttrList.isEmpty()) {
            return;
        }

        if (newDataAttrList == null) {
            return;
        }

        // 将 newDataAttrList 中的 attrKey 收集到 Set 中，用于快速查找
        Set<String> newDataAttrKeys = newDataAttrList.stream()
                .map(MediaSelectionPlanMenuFieldAttrData::getAttrKey)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 遍历 oldDataAttrList，找出 newDataAttrList 中不存在的数据并添加
        for (MediaSelectionPlanMenuFieldAttrData oldAttrData : oldDataAttrList) {
            if (oldAttrData != null && oldAttrData.getAttrKey() != null
                && !newDataAttrKeys.contains(oldAttrData.getAttrKey())) {
                // 创建新的对象避免引用问题
                MediaSelectionPlanMenuFieldAttrData missingAttrData = new MediaSelectionPlanMenuFieldAttrData();
                missingAttrData.setId(oldAttrData.getId());
                missingAttrData.setAttrKey(oldAttrData.getAttrKey());
                missingAttrData.setAttrValue(oldAttrData.getAttrValue());

                newDataAttrList.add(missingAttrData);
                newDataAttrKeys.add(oldAttrData.getAttrKey()); // 更新 Set 避免重复添加
            }
        }
    }

    protected void getAddressCoordinate(MainCalendarPlacement mainCalendarPlacement, RegionDetail regionDetail, Integer userKey, AiOptionPlanListData aiOptionPlanListData){
        //处理经纬度问题
        try {
            if(!PlanType.BUS_SMART.name().equals(aiOptionPlanListData.getSelectionType())){
                AMapGeoSearchResponse geoSearchResponse = aMapClient.searchGeo(regionDetail.getCity(), mainCalendarPlacement.getMediaPosition(), userKey);
                AMapResponseHandle.handle(geoSearchResponse);
                if (CollectionUtils.isNotEmpty(geoSearchResponse.getGeocodes())) {
                    AMapGeoInfo geoInfo = geoSearchResponse.getGeocodes().get(0);
                    String location = geoInfo.getLocation();
                    String[] split = location.split(",");
                    mainCalendarPlacement.setLongitude(split[0]);
                    mainCalendarPlacement.setLatitude(split[1]);
                }
            }
        } catch (Exception e) {
            log.error("excel 获取经纬度 失败error:{}，{} ",e, new Gson().toJson(mainCalendarPlacement));
        }
    }

    private SearchCriteria getSearchCriteria(Integer planId) {
        SearchCriteria searchCriteria = new SearchCriteria();
        SearchCriteria.Criteria criteria = new SearchCriteria.Criteria();
        criteria.setKey("id");
        criteria.setValue(planId.toString());
        searchCriteria.setCriterias(Arrays.asList(criteria));
        return searchCriteria;
    }

    public void exportPlacementSnForUploadQuote(HttpServletResponse response, Integer planId, String stepType){
        SearchCriteria searchCriteria = new SearchCriteria();
        SearchCriteria.Criteria criteria = new SearchCriteria.Criteria();
        criteria.setValue(planId.toString());
        criteria.setKey("planId");
        ArrayList<SearchCriteria.Criteria> criterias = new ArrayList<>();
        criterias.add(criteria);
        searchCriteria.setCriterias(criterias);
        exportPlacementSnListV2(response, searchCriteria, stepType, "报价模板导出");
    }


    /**
     * 分页查询， 查询全部
     *
     * @param response
     * @param searchCriteria
     */
    public void exportPlacementSnListV2(HttpServletResponse response, SearchCriteria searchCriteria, String stepType, String fileName) {
        // 获取表头信息
        List<Integer> categoryIds = new ArrayList<>();
        if (searchCriteria.getCriterias() != null) {
            Optional<SearchCriteria.Criteria> categoryCriteria = searchCriteria.getCriterias().stream()
                    .filter(c -> "categoryId".equals(c.getKey()))
                    .findFirst();
            Integer categoryId = categoryCriteria.map(criteria -> Integer.valueOf(criteria.getValue())).orElse(null);
            if (categoryId == null) {
                SearchCriteria.Criteria criteria = searchCriteria.getCriterias().stream()
                        .filter(c -> "planId".equals(c.getKey()))
                        .findFirst().get();
                String planIdStr = criteria.getValue();
                if (StringUtils.isNotBlank(planIdStr)) {
                    categoryIds = aiOptionModeMapper.getCtegoryIdsByPlanId(Long.valueOf(planIdStr));
                }
            } else {
                categoryIds.add(categoryId);
            }
        }
        PageResponseBuilder.exportExcelWithMetaHeaderScrollBySheet(
                response,
                searchCriteria,
                categoryIds,
                a -> crowdTagPlanService.getPlacementSnLabel(a, stepType),
                (criteria, page) -> {
                    criteria = searchCriteria;
                    return crowdTagPlanService.getPlacementSnList(criteria, page, stepType);
                },
                fileName,
                true);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<String> dynamicPlacementScheduleImport(Integer planId, MultipartFile file, Integer identifier,
            String uploadType,
            String selectionType, HttpServletResponse response) {
        List<String> errorMessages = new ArrayList<>();
        AiOptionPlanListData aiOptionPlanListData = getAiOptionPlanListData(planId,uploadType,selectionType);
        ValidationInput inDemand = new ValidationInput(
                calendarPlacementService.getMediumFormatInfosByExcel(aiOptionPlanListData, uploadType),
                calendarPlacementService.getMediumCategoryIdsByExcel(aiOptionPlanListData, uploadType),
                calendarPlacementService.getRegionData(planId, aiOptionPlanListData.getSalesCityRegions(), uploadType));
        List<MediumFormatInfo> mediumFormatInfos = inDemand.getMediumFormatInfos();
        List<String> mdiumFormatList = mediumFormatInfos.stream().map(mediumFormatInfo -> String.format("%s/%s", mediumFormatInfo.getLv2Name(), mediumFormatInfo.getLv3Name())).collect(Collectors.toList());
        List<MediaSelectionPlanMenuFieldAttr> fieldAttrList = mediaSelectionPlanMenuFieldAttrMapper
                .getBySequenceCodeAndCategoryNamesList(mdiumFormatList, uploadType);
        List<MetaFieldInfo> metaFieldInfoList = convertToMetaFieldInfoList(
                fieldAttrList.stream().filter(
                        mediaSelectionPlanMenuFieldAttr -> mediaSelectionPlanMenuFieldAttr.getGroupTag()
                                .equals(MEDIA_SELECTION_PLAN_MENU_FIELD_ATTR_COMMON))
                        .collect(Collectors.toList()));
        Map<String, List<MediaSelectionPlanMenuFieldAttr>> fieldAttrMap = fieldAttrList.stream()
                .filter(mediaSelectionPlanMenuFieldAttr -> mediaSelectionPlanMenuFieldAttr.getGroupTag()
                        .equals(MEDIA_SELECTION_PLAN_MENU_FIELD_ATTR_EXPAND))
                .collect(Collectors.groupingBy(MediaSelectionPlanMenuFieldAttr::getMediaCategoryName));
        // 错误信息
        List<ResultMatchDetailErrorData> errorResultDataList = new ArrayList<>();
        Map<String, MainCalendarPlacementExt> resultMap = new HashMap<>();
        List<MainCalendarPlacement> mainCalendarPlacementList = new ArrayList<>();
        try {
            easyExcelRead(file, planId, identifier, metaFieldInfoList, fieldAttrMap, inDemand, errorResultDataList,
                    errorMessages, resultMap, mainCalendarPlacementList, aiOptionPlanListData);

            if (!errorResultDataList.isEmpty()) {
                XLSWriter.export(errorResultDataList, ResultMatchDetailErrorData.class, "校验不通过列表", response);
            } else {
                insertOrUpdateData(mainCalendarPlacementList, resultMap, planId, selectionType, identifier, uploadType);
            }
        } catch (Exception e) {
            log.error("导入Excel失败: {}", e.getMessage(), e);
            errorMessages.add("导入Excel失败: " + e.getMessage());
        }

        return errorMessages;
    }

    private AiOptionPlanListData getAiOptionPlanListData(Integer planId, String uploadType, String selectionType) {
        AiOptionPlanListData aiOptionPlanListData = new AiOptionPlanListData();
        if(uploadType.equals(PlanStep.IN_DEMAND_UPLOAD.name())){
            List<AiOptionPlanListData> list = aiOptionModeMapper.list(selectionType, getSearchCriteria(planId));
            if (list.isEmpty()) {
                throw new RuntimeException("没找到对应的方案数据");
            }
            aiOptionPlanListData = list.get(0);
        }else {
            List<PlacementSelectionPlanData> placementSelectionPlanData = aiOptionModeMapper.calendarPlacementSelect(getSearchCriteria(planId));
            if (placementSelectionPlanData.isEmpty()) {
                throw new RuntimeException("没找到对应的方案数据");
            }
            aiOptionPlanListData = placementSelectionPlanData.get(0);
        }
        return aiOptionPlanListData;
    }

    private void easyExcelRead(MultipartFile file, Integer planId, Integer identifier,
                               List<MetaFieldInfo> metaFieldInfoList,
                               Map<String, List<MediaSelectionPlanMenuFieldAttr>> fieldAttrMap, ValidationInput inDemand,
                               List<ResultMatchDetailErrorData> errorResultDataList,
                               List<String> errorMessages,
                               Map<String, MainCalendarPlacementExt> resultMap,
                               List<MainCalendarPlacement> mainCalendarPlacementList, AiOptionPlanListData aiOptionPlanListData) throws Exception {
        AtomicInteger rowIndex = new AtomicInteger(2); // 默认从第 2 行开始（假设表头在第 1 行）
        final String[] currentSheetName = {null}; // 记录当前 sheet 名称
        EasyExcel.read(file.getInputStream(), null,
                        new MapStringKeyListener<Map<String, Object>>(new Consumer<List<Map<String, Object>>>() {
                            @Override
                            public void accept(List<Map<String, Object>> maps) {

                            }
                        }) {
                            @Override
                            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                                // 每次进入新 sheet 时，重置行号
                                this.headerList = headMap.values().stream()
                                        .map(Object::toString)
                                        .collect(Collectors.toList());
                                String newSheetName = context.readSheetHolder().getSheetName();
                                if (!newSheetName.equals(currentSheetName[0])) {
                                    currentSheetName[0] = newSheetName;
                                    rowIndex.set(2); // 新 sheet 从第 2 行开始
                                }
                            }

                            @Override
                            public void invoke(Map<Integer, Object> data, AnalysisContext context) {
                                try {
                                    super.invoke(data, context);
                                    Map<String, Object> result = new LinkedHashMap<>();
                                    data.forEach((k, v) -> result.put(headerList.get(k), v));
                                    // 校验数据
                                    DynamicPlacementImportXlsModel dynamicPlacementImportXlsModel = new DynamicPlacementImportXlsModel(result);
                                    inDemand.setImportData(dynamicPlacementImportXlsModel);
                                    excelValidatorManager.validate(inDemand);

                                    // 处理主表数据
                                    DynamicPlacementImportXlsModel importData = (DynamicPlacementImportXlsModel) inDemand.getImportData();
                                    MainCalendarPlacement placement = importData.adaptToMainCalendarPlacement(
                                            planId, appName, identifier, metaFieldInfoList, aiOptionPlanListData);
                                    //getAddressCoordinate(placement, importData.getRegionDetail(), identifier, aiOptionPlanListData);
                                    mainCalendarPlacementList.add(placement);

                                    // 处理附表 attr 数据
                                    MainCalendarPlacementExt placementExt = adaptToMediaSelectionPlanMenuFieldAttr(
                                            importData, fieldAttrMap, placement, identifier, aiOptionPlanListData);
                                    resultMap.put(placement.getPlacementSn(), placementExt);

                                } catch (Exception e) {
                                    List<String> errorMsgList = new ArrayList<>();
                                    errorMsgList.add("sheet【"+currentSheetName[0] +"】 : "+ e.getMessage());
                                    ResultMatchDetailErrorData errorData = new ResultMatchDetailErrorData(
                                            rowIndex.get(), errorMsgList);
                                    log.error("ImportData and validate data error={}", e);
                                    errorResultDataList.add(errorData);
                                } finally {
                                    rowIndex.incrementAndGet(); // 无论成功失败，都递增行号
                                }
                            }

                            @Override
                            public void doAfterAllAnalysed(AnalysisContext context) {
                                // 所有数据解析完成
                            }
                        })
                .doReadAll();
    }

    private MainCalendarPlacementExt adaptToMediaSelectionPlanMenuFieldAttr(DynamicPlacementImportXlsModel importData,
            Map<String, List<MediaSelectionPlanMenuFieldAttr>> fieldAttrMap, MainCalendarPlacement placement,
            Integer identifier, AiOptionPlanListData aiOptionPlanListData) {
        MainCalendarPlacementExt placementExt = new MainCalendarPlacementExt();
        placementExt.setBrighten((String) importData.getDynamicFields().get("开屏时间"));
        placementExt.setIssueAmount(importData.getDynamicFields().get("发布费（元/月/辆）") != null
                ? Double.valueOf(importData.getDynamicFields().get("发布费（元/月/辆）").toString())
                : null);
        placementExt.setProductionAmount(importData.getDynamicFields().get("制作费（元/辆）") != null
                ? Double.valueOf(importData.getDynamicFields().get("制作费（元/辆）").toString())
                : null);
        placementExt.setLaunchTime(importData.getDynamicFields().get("投放时长（秒）") != null
                ? Integer.valueOf(importData.getDynamicFields().get("投放时长（秒）").toString())
                : null);
        placementExt.setLaunchRate(importData.getDynamicFields().get("投放频次（次）") != null
                ? Integer.valueOf(importData.getDynamicFields().get("投放频次（次）").toString())
                : null);

        placementExt.setLaunchCount(importData.getDynamicFields().get("投放点位（个）") != null
                ? Integer.valueOf(importData.getDynamicFields().get("投放点位（个）").toString())
                : null);

        placementExt.setBusName(importData.getDynamicFields().get("线路")!=null ? importData.getDynamicFields().get("线路").toString() : null);
        List<MediaSelectionPlanMenuFieldAttrData> extraAttrs = new ArrayList<>();
        List<MediaSelectionPlanMenuFieldAttr> mediaSelectionPlanMenuFieldAttrs1 = fieldAttrMap
                .get(String.format("%s/%s",importData.getMediumFormatInfo().getLv2Name(), importData.getMediumFormatInfo().getLv3Name()));
        String format = String.format("%s/%s", importData.getMediumFormatInfo().getLv2Name(), importData.getMediumFormatInfo().getLv3Name());
        log.info("------{}========", format);
        for (MediaSelectionPlanMenuFieldAttr attr : mediaSelectionPlanMenuFieldAttrs1) {
            MediaSelectionPlanMenuFieldAttrData mediaSelectionPlanMenuFieldAttrData = new MediaSelectionPlanMenuFieldAttrData();
            mediaSelectionPlanMenuFieldAttrData.setId(attr.getId());
            mediaSelectionPlanMenuFieldAttrData.setAttrKey(attr.getAttrKey());
            if (importData.getDynamicFields().containsKey(attr.getAttrName())) {
                Object value = importData.getDynamicFields().get(attr.getAttrName());
                mediaSelectionPlanMenuFieldAttrData.setAttrValue(value.toString());
                extraAttrs.add(mediaSelectionPlanMenuFieldAttrData);
            }
        }
        if(PlanType.CROWD_TAG.name().equals(aiOptionPlanListData.getSelectionType())){
            MediaSelectionPlanMenuFieldAttrData mediaSelectionPlanMenuFieldAttrData = new MediaSelectionPlanMenuFieldAttrData();
            mediaSelectionPlanMenuFieldAttrData.setId(null);
            mediaSelectionPlanMenuFieldAttrData.setAttrKey("launchPeriod");
            mediaSelectionPlanMenuFieldAttrData.setAttrValue(DateUtils.getDaysBetween(placement.getDeliveryStartTime(), placement.getDeliveryEndTime()));
            extraAttrs.add(mediaSelectionPlanMenuFieldAttrData);
        }
        placementExt.setExtraAttrs(JSON.toJSONString(extraAttrs));

        placementExt.setCreator(appName);
        placementExt.setUpdater(appName);
        placementExt.setCreatedByUser(identifier);
        placementExt.setUpdatedByUser(identifier);
        placementExt.setCreateTime(new Date());
        placementExt.setUpdateTime(new Date());

        return placementExt;
    }

    /**
     * 获取点位标签信息
     *
     * @param categoryId   分类ID
     * @param sequenceCode 序列代码
     * @return MetaFieldInfo列表
     */
    public List<MetaFieldInfo> getPlacementSnLabel(Integer categoryId, String sequenceCode) {
        HashSet<Integer> categoryIdSet = new HashSet<>();
        categoryIdSet.add(categoryId);
        Map<Integer, MediumFormatData> mediumFormatDataMap = getMediumFormatDataMap(categoryIdSet);
        MediumFormatData mediumFormatData = mediumFormatDataMap.get(categoryId);
        List<MediaSelectionPlanMenuFieldAttr> fieldAttrList = mediaSelectionPlanMenuFieldAttrMapper
                .getBySequenceCodeAndCategoryNameList(
                        String.format("%s/%s", mediumFormatData.getLv2Name(), mediumFormatData.getLv3Name()),
                        sequenceCode);
        if(fieldAttrList.isEmpty()){
            fieldAttrList = mediaSelectionPlanMenuFieldAttrMapper
                    .getBySequenceCodeAndCategoryNameList(
                            "商圈/LED大屏", sequenceCode);
        }
        List<MetaFieldInfo> metaFieldInfoList = convertToMetaFieldInfoList(fieldAttrList);
        return metaFieldInfoList;
    }

    /**
     * 导出点位标签模板
     * 根据getPlacementSnLabel方法获取的MetaFieldInfo动态生成Excel模板
     *
     * @param response     HTTP响应对象
     * @param categoryId   分类ID
     * @param sequenceCode 序列代码
     */
    public void placementSnLabelTemplateDownload(HttpServletResponse response, Integer categoryId,
                                                 String sequenceCode) {
        try {
            // 获取MetaFieldInfo列表作为表头
            List<MetaFieldInfo> metaFieldInfoList = getPlacementSnLabel(categoryId, sequenceCode);

            // 过滤掉隐藏的字段
            List<MetaFieldInfo> visibleFields = metaFieldInfoList.stream()
                    .filter(field -> !Boolean.TRUE.equals(field.getHide()))
                    .sorted(Comparator.comparing(MetaFieldInfo::getColumnIndex))
                    .collect(Collectors.toList());

            // 构建表头数据
            List<List<String>> headList = visibleFields.stream()
                    .map(field -> Collections.singletonList(field.getLabelCn()))
                    .collect(Collectors.toList());

            setFileResponse(response, "点位标签模板");
            EasyExcel.write(response.getOutputStream())
                    .head(headList)
                    .sheet()
                    .doWrite(Collections.emptyList());
        } catch (Exception e) {
            log.error("placementSnLabelTemplateDownload fail with error=", e);
        }
    }

    /**
     * 获取媒体格式数据映射
     *
     * @param mediumCategoryIdList 媒体分类ID列表
     * @return 媒体格式数据映射
     */
    private Map<Integer, MediumFormatData> getMediumFormatDataMap(Set<Integer> mediumCategoryIdList) {
        if (mediumCategoryIdList == null || mediumCategoryIdList.isEmpty()) {
            return new HashMap<>();
        }
        List<MediumFormatData> mediumFormatDataList = aiOptionModeMapper
                .getMediumFormatDataByLv3IdList(mediumCategoryIdList);
        return mediumFormatDataList.stream()
                .collect(Collectors.toMap(MediumFormatData::getLv3Id, Function.identity()));
    }


    public void placementSnLabelTemplateDownloadByPlanId(HttpServletResponse response, @RequestParam("planId") Integer planId,
                                                 String sequenceCode) {
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcel.write(response.getOutputStream()).build();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        try {
            List<Integer> ctegoryIdsByPlanId = aiOptionModeMapper.getCtegoryIdsByPlanId(planId.longValue());
            if(ctegoryIdsByPlanId.isEmpty()){
                String ctegoryIds = aiOptionModeMapper.getSelectionPlanCtegoryIdsByPlanId(planId.longValue());
                if(StringUtils.isNotBlank(ctegoryIds)){
                    ctegoryIdsByPlanId = Arrays.stream(ctegoryIds.replace("[", "").replace("]", "").split(","))
                            .map(String::trim)
                            .map(Integer::parseInt)
                            .collect(Collectors.toList());
                }
            }
            PlanStep.valueOf(sequenceCode);
            setFileResponse(response, PlanStep.valueOf(sequenceCode).getDesc()+"模板");

            for(Integer categoryId: ctegoryIdsByPlanId){
                // 获取MetaFieldInfo列表作为表头
                List<MetaFieldInfo> metaFieldInfoList = getPlacementSnLabel(categoryId, sequenceCode);

                // 过滤掉隐藏的字段
                List<MetaFieldInfo> visibleFields = metaFieldInfoList.stream()
                        .filter(field -> !Boolean.TRUE.equals(field.getHide()))
                        .sorted(Comparator.comparing(MetaFieldInfo::getColumnIndex))
                        .collect(Collectors.toList());

                // 构建表头数据
                List<List<String>> headList = visibleFields.stream()
                        .map(field -> Collections.singletonList(field.getLabelCn()))
                        .collect(Collectors.toList());
                if(headList.isEmpty()){
                    continue;
                }

                HashSet<Integer> categoryIdSet = new HashSet<>();
                categoryIdSet.add(categoryId);
                Map<Integer, MediumFormatData> mediumFormatDataMap = getMediumFormatDataMap(categoryIdSet);
                MediumFormatData mediumFormatData = mediumFormatDataMap
                        .get(categoryId);
                String sheetName = String.format("%s-%s-%s", mediumFormatData.getLv1Name(), mediumFormatData.getLv2Name(), mediumFormatData.getLv3Name());
                String replaceSheetName = sheetName.replace("/", "&");
                WriteSheet sheet = EasyExcel.writerSheet(replaceSheetName).head(headList).build();
                excelWriter.write(Collections.emptyList(), sheet);
            }
        } catch (Exception e) {
            log.error("placementSnLabelTemplateDownload fail with error=", e);
        }finally {
            excelWriter.finish();
        }
    }

    private void getBusLinePlacementDataBySelectionType(MainCalendarPlacement placement, MainCalendarPlacementExt mainCalendarPlacementExt,
                                                        String selectionType, List<BusLinePlacementData> busLinePlacementDatas, Integer identifier){
        if (selectionType.equals("BUS_SMART")) {
            BusLinePlacementData busLinePlacementData = new BusLinePlacementData();
            busLinePlacementData.setPlacementSn(placement.getPlacementSn());
            busLinePlacementData.setMediumLv3CategoryId(placement.getMediumLv3CategoryId());
            busLinePlacementData.setMediumLv2CategoryId(placement.getMediumLv2CategoryId());
            busLinePlacementData.setMediumLv1CategoryId(placement.getMediumLv1CategoryId());
            busLinePlacementData.setLineName(placement.getMediaName());
            busLinePlacementData.setProvinceId(placement.getProvinceId());
            busLinePlacementData.setCityId(placement.getCityId());
            busLinePlacementData.setDistrictId(placement.getDistrictId());
            busLinePlacementData.setAddressId(placement.getAddressId());
            busLinePlacementData.setResourceProvider(placement.getResourceProvider());
            busLinePlacementData.setResourceProviderType(placement.getResourceProviderType());
            busLinePlacementData.setExtraAttrs(placement.getExtraAttrs());
           MainDictRegion mainDictRegion = mainDictRegionMapper.getByIdEX(busLinePlacementData.getCityId().longValue());
            /*  AMapBusDataResponse busDataResponse = busSmartPlanService.getBusLineResponseByLineName( mainDictRegion.getCode(), mainCalendarPlacementExt.getBusName(), identifier);
            if(busDataResponse!=null && busDataResponse.getBusLines()!=null && !busDataResponse.getBusLines().isEmpty()){
                List<BusLine> busLines = busDataResponse.getBusLines();
                mainCalendarPlacementExt.setMileage(busLines.get(0).getDistance().toString());
            }
            busLinePlacementData.setAMapBusDataResponse(busDataResponse);*/

            MainBusLine busLineByLineName = busSmartPlanService.getBusLineByLineName(placement, mainDictRegion.getCode(), mainCalendarPlacementExt.getBusName(), identifier);
            if(busLineByLineName!=null && busLineByLineName.getDistance()!=null){
                mainCalendarPlacementExt.setMileage(busLineByLineName.getDistance().toString());
            }
            busLinePlacementDatas.add(busLinePlacementData);
        }
    }
}

