package heran.media.management.platform.aioptionmode.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import heran.media.management.platform.aioptionmode.po.AiOptionPlanListData;
import heran.media.management.platform.aioptionmode.po.MediumAttrExtras;
import heran.media.management.platform.aioptionmode.po.MediumFormatData;
import heran.media.management.platform.main.subdomain.dto.TMainDictRegions;
import heran.media.management.platform.mediaresource.nationwidemedia.po.RegionData;
import heran.media.sharelib.domain.db.model.MainDictRegionData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 智选方案列表response
 * @Author: yzj
 * @Date: 2023/11/20 15:30
 * @Version: 1.0
 */
@Data
public class AiOptionPlanListResponse {

    @ApiModelProperty("方案id")
    private Integer id;
    @ApiModelProperty("方案编号")
    private String planName;
    @ApiModelProperty("方案名称")
    private String planSn;
    @ApiModelProperty("集团名称")
    private String groupName;
    @ApiModelProperty("品牌名称")
    private String brandName;
    @ApiModelProperty("智选模式")
    private String selectionType;
    @ApiModelProperty("媒体属性列表")
    private List<MediumFormatAttrResponse> mediumFormatAttrList;
    @ApiModelProperty("地理围栏范围")
    private Integer geoRange;
    @ApiModelProperty("智选状态")
    private String planStatus;
    @ApiModelProperty("是否去重")
    private Boolean isDistinct;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("投放开始时间")
    private Date deliveryStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("投放结束时间")
    private Date deliveryEndTime;
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    @ApiModelProperty("投放城市列表")
    private List<TMainDictRegions> salesCityRegions;
    @ApiModelProperty("步骤类型")
    private String stepType;
    @ApiModelProperty("创建者名称")
    private String creatorName;

    public AiOptionPlanListResponse(AiOptionPlanListData data, Map<String, RegionData> groupByCode, Map<Integer, MediumFormatData> groupLv3Id) {
        this.id = data.getId();
        this.planSn = data.getPlanSn();
        this.planName = data.getPlanName();
        this.groupName = data.getGroupName();
        this.brandName = data.getBrandName();
        this.createTime = data.getCreateTime();
        this.selectionType = data.getSelectionType();
        this.deliveryStartTime = data.getDeliveryStartTime();
        this.deliveryEndTime = data.getDeliveryEndTime();
        this.planStatus = data.getPlanStatus();
        this.updateTime = data.getUpdateTime();
        this.isDistinct = data.getIsUnrepeated();
        this.geoRange = data.getGeoRange();
        this.stepType = data.getStepType();
        this.creatorName = data.getCreatorName();
        if (StringUtils.isNotBlank(data.getSalesCityRegions())){
            List<String> codeList = JSONArray.parseArray(data.getSalesCityRegions(), String.class);
            List<MainDictRegionData> regionsList = new ArrayList<>();
            for (String code : codeList) {
                RegionData regionData = groupByCode.get(code);
                if(regionData!=null){
                    List<MainDictRegionData> regions = regionData.adaptToHierarchyRegionList();
                    regionsList.addAll(regions);
                }
            }
            this.salesCityRegions = assembleProvinceCategory(regionsList).stream().map(TMainDictRegions::new).collect(Collectors.toList());
        }
        //已有的媒体属性对应三级分类id列表
        List<MediumFormatAttrResponse> mediumFormatAttrList = new ArrayList<>();
        final List<Integer> existMediumAttrCategoryIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(data.getMediumAttrExtras())){
            List<MediumAttrExtras> mediumAttrExtras = JSONArray.parseArray(data.getMediumAttrExtras(), MediumAttrExtras.class);
            for (MediumAttrExtras attrData : mediumAttrExtras) {
                existMediumAttrCategoryIdList.add(attrData.getMediumCategoryId());
                MediumFormatData formatData = groupLv3Id.get(attrData.getMediumCategoryId());
                List<MediumFormatAttrResponse> responseList = formatData.adaptToMediumFormatList(attrData);
                mediumFormatAttrList.addAll(responseList);
            }
        }
        if (StringUtils.isNotBlank(data.getMediumCategoryIds())){
            List<Integer> lv3IdList = JSONArray.parseArray(data.getMediumCategoryIds(), Integer.class);
            lv3IdList.stream().filter(s->!existMediumAttrCategoryIdList.contains(s))
                    .forEach(s->{
                        MediumFormatData formatData = groupLv3Id.get(s);
                        if (formatData !=null){
                            List<MediumFormatAttrResponse> responseList = formatData.adaptToMediumFormatList(null);
                            mediumFormatAttrList.addAll(responseList);
                        }
                    });
        }
        if (CollectionUtils.isNotEmpty(mediumFormatAttrList)){
            this.mediumFormatAttrList = assembleMediumFormatCategory(mediumFormatAttrList);
        }
    }

    public List<MainDictRegionData> assembleProvinceCategory(List<MainDictRegionData> regionsList) {
        List<MainDictRegionData> distinctList = new ArrayList<>(regionsList
                .stream()
                .collect(Collectors.toMap(
                        MainDictRegionData::getCode,
                        Function.identity(),
                        (existing, replacement) -> existing
                ))
                .values());
        Map<String, List<MainDictRegionData>> regionMap = new HashMap<>();
        for (MainDictRegionData regionData : distinctList) {
            regionMap.computeIfAbsent(regionData.getParentCode(), k -> new ArrayList<>()).add(regionData);
        }
        Function<MainDictRegionData, MainDictRegionData> buildHierarchy = new Function<MainDictRegionData, MainDictRegionData>() {
            @Override
            public MainDictRegionData apply(MainDictRegionData dictRegionData) {
                List<MainDictRegionData> childrenList = regionMap.get(dictRegionData.getCode());
                if (childrenList != null) {
                    dictRegionData.setChildrenList(childrenList.
                            stream().
                            sorted(Comparator.comparing(MainDictRegionData::getCode))
                            .map(this).collect(Collectors.toList()));
                }
                return dictRegionData;
            }
        };
        //获取省份列表
        List<MainDictRegionData> provinceList = regionMap.get(null);
        if (provinceList != null) {
            return provinceList.stream()
                    .sorted(Comparator.comparing(MainDictRegionData::getCode))
                    .map(buildHierarchy)
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    public List<MediumFormatAttrResponse> assembleMediumFormatCategory(List<MediumFormatAttrResponse> mediumFromList) {
        List<MediumFormatAttrResponse> distinctList = new ArrayList<>(mediumFromList
                .stream()
                .collect(Collectors.toMap(
                        MediumFormatAttrResponse::getCategoryId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ))
                .values());
        Map<Integer, List<MediumFormatAttrResponse>> mediumFormatAttrMap = new HashMap<>();
        for (MediumFormatAttrResponse mediumFormatAttrResponse : distinctList) {
            mediumFormatAttrMap.computeIfAbsent(mediumFormatAttrResponse.getParentId(), k -> new ArrayList<>()).add(mediumFormatAttrResponse);
        }
        Function<MediumFormatAttrResponse, MediumFormatAttrResponse> buildHierarchy = new Function<MediumFormatAttrResponse, MediumFormatAttrResponse>() {
            @Override
            public MediumFormatAttrResponse apply(MediumFormatAttrResponse mediumFormat) {
                List<MediumFormatAttrResponse> childrenList = mediumFormatAttrMap.get(mediumFormat.getCategoryId());
                if (childrenList != null) {
                    mediumFormat.setSecondaryList(childrenList.
                            stream().
                            sorted(Comparator.comparing(MediumFormatAttrResponse::getCategoryId))
                            .map(this).collect(Collectors.toList()));
                }
                return mediumFormat;
            }
        };
        List<MediumFormatAttrResponse> lv1MediumFormatList = mediumFormatAttrMap.get(null);
        if (lv1MediumFormatList != null) {
            return lv1MediumFormatList.stream()
                    .sorted(Comparator.comparing(MediumFormatAttrResponse::getCategoryId))
                    .map(buildHierarchy)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
