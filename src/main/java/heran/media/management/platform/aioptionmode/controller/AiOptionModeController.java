package heran.media.management.platform.aioptionmode.controller;

import heran.media.management.platform.aioptionmode.dto.*;
import heran.media.management.platform.aioptionmode.service.AiOptionModeService;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;

import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 智选模式controller
 * @Author: yzj
 * @Date: 2023/11/17 10:25
 * @Version: 1.0
 */
@ApiOperation(value = "智选模式相关接口")
@Slf4j
@RestController
@RequestMapping("ai-option-mode")
public class AiOptionModeController {

    @Resource
    private AiOptionModeService aiOptionModeService;

    @ApiLog
    @ApiOperation(value = "智选方案列表",notes = "{keyword(模糊搜索方案ID/方案名称/品牌名);planStatus(根据方案状态搜索【】)}")
    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<AiOptionPlanListResponse>> list(@RequestParam("selectionType")String selectionType,
                                                                         @RequestBody @Valid SearchCriteria criteria) {
        PageResponse<AiOptionPlanListResponse> planListResponse = aiOptionModeService.list(selectionType,criteria);
        return InternalResponse.<PageResponse<AiOptionPlanListResponse>>success().withBody(planListResponse);
    }

    @ApiLog
    @ApiOperation(value = "获取地理围栏需求详情")
    @RequestMapping(value = "/getGeoLocationRequirementDetail", method = RequestMethod.GET, produces = "application/json")
    @PreAuthorize("hasPermission(null,'GEOLOCATIONLIST_LOOK')")
    @ResponseBody
    public InternalResponse<GeoLocationRequirementDetailResponse> getGeoLocationRequirementDetail(@RequestParam("planId")Integer planId,
                                                                                                     @RequestParam("page")Integer page,
                                                                                                     @RequestParam("limit")Integer limit) {
        GeoLocationRequirementDetailResponse planDetailList = aiOptionModeService.getGeoLocationRequirementDetail(planId, page, limit);
        return InternalResponse.<GeoLocationRequirementDetailResponse>success().withBody(planDetailList);
    }

    @ApiLog
    @ApiOperation(value = "获取ta浓度选点需求详情")
    @RequestMapping(value = "/getCrowdTagRequirementDetail", method = RequestMethod.GET, produces = "application/json")
    @PreAuthorize("hasPermission(null,'CROWDTAGLIST_LOOK')")
    @ResponseBody
    public InternalResponse<CrowdTagRequirementDetailResponse> getCrowdTagRequirementDetail(@RequestParam("planId")Integer planId) {
        CrowdTagRequirementDetailResponse planDetailList = aiOptionModeService.getCrowdTagRequirementDetail(planId);
        return InternalResponse.<CrowdTagRequirementDetailResponse>success().withBody(planDetailList);
    }

    @ApiLog
    @ApiOperation(value = "地理围栏需求档期上传")
    @RequestMapping(value = "/geoLocationScheduleUpload", method = RequestMethod.POST)
    @PreAuthorize("hasPermission(null,'GEOLOCATIONSCHEDULE_IMPORT')")
    public InternalResponse<List<GeoLocationMatchResultResponse>> geoLocationScheduleUpload(@RequestBody MultipartFile file, @RequestParam("planId")Integer planId) {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        List<GeoLocationMatchResultResponse> responses = aiOptionModeService.geoLocationScheduleUpload(planId, file, currentUser.getIdentifier());
        return InternalResponse.<List<GeoLocationMatchResultResponse>>success().withBody(responses);
    }

    @ApiLog
    @ApiOperation(value = "提交地理围栏需求匹配结果")
    @RequestMapping(value = "/submitGeoLocationMatchResult", method = RequestMethod.POST)
    @PreAuthorize("hasPermission(null,'GEOLOCATIONMATCHRESULT_SUBMIT')")
    public InternalResponse<Void> submitGeoLocationMatchResult(@RequestParam("planId")Integer planId,@RequestBody List<GeoLocationMatchResultRequest> request) {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        aiOptionModeService.submitGeoLocationMatchResult(planId,request, currentUser.getIdentifier(), true);
        return InternalResponse.success();
    }

    @ApiLog
    @ApiOperation(value = "ta浓度需求档期上传")
    @RequestMapping(value = "/crowdTagScheduleUpload", method = RequestMethod.POST)
    @PreAuthorize("hasPermission(null,'CROWDTAGSCHEDULE_IMPORT')")
    public InternalResponse<List<CrowdTagMatchResultResponse>> crowdTagScheduleUpload(@RequestBody MultipartFile file, @RequestParam("planId")Integer planId) {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        List<CrowdTagMatchResultResponse> responses = aiOptionModeService.crowdTagScheduleUpload(planId, file, currentUser.getIdentifier());
        return InternalResponse.<List<CrowdTagMatchResultResponse>>success().withBody(responses);
    }

    @ApiLog
    @ApiOperation(value = "提交ta浓度需求匹配结果")
    @RequestMapping(value = "/submitCrowdTagMatchResult", method = RequestMethod.POST)
    @PreAuthorize("hasPermission(null,'CROWDTAGMATCHRESULT_SUBMIT')")
    public InternalResponse<Void> submitCrowdTagMatchResult(@RequestParam("planId")Integer planId,@RequestBody List<CrowdTagMatchResultRequest> request) {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        aiOptionModeService.submitCrowdTagMatchResult(planId,request, currentUser.getIdentifier(), true);
        return InternalResponse.success();
    }

    @ApiLog
    @ApiOperation(value = "地理围栏查看匹配结果")
    @RequestMapping(value = "/getGeoLocationMatchResult", method = RequestMethod.POST)
    public InternalResponse<List<GeoLocationMatchResultResponse>> getGeoLocationMatchResult(@RequestParam("planId")Integer planId) {
        List<GeoLocationMatchResultResponse> responses = aiOptionModeService.getGeoLocationMatchResult(planId);
        return InternalResponse.<List<GeoLocationMatchResultResponse>>success().withBody(responses);
    }

    @ApiLog
    @ApiOperation(value = "TA浓度查看匹配结果")
    @RequestMapping(value = "/getCrowdTagMatchResult", method = RequestMethod.POST)
    public InternalResponse<List<CrowdTagMatchResultResponse>> getCrowdTagMatchResult(@RequestParam("planId")Integer planId) {
        List<CrowdTagMatchResultResponse> responses = aiOptionModeService.getCrowdTagMatchResult(planId);
        return InternalResponse.<List<CrowdTagMatchResultResponse>>success().withBody(responses);
    }

    @ApiLog
    @ApiOperation(value = "获取方案状态")
    @RequestMapping(value = "/getPlanStatus", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<String> getPlanStatus(@RequestParam("planId")Integer planId) {
        String planStatus = aiOptionModeService.getPlanStatus(planId);
        return InternalResponse.<String>success().withBody(planStatus);
    }

    @ApiLog
    @ApiOperation(value = "逻辑删除方案数据")
    @RequestMapping(value = "/logicDeletePlanData", method = RequestMethod.DELETE)
    @PreAuthorize("hasPermission(null,'AIOPTIONMODELIST_DELETE')")
    public InternalResponse<Void> logicDeletePlanData(@RequestParam("planId")Integer planId) {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        aiOptionModeService.logicDeletePlanData(planId, currentUser.getIdentifier());
        return InternalResponse.success();
    }

    @ApiLog
    @ApiOperation(value = "地理围栏档期模板下载")
    @RequestMapping(value = "/geoLocationScheduleTemplateDownload", method = RequestMethod.GET)
    public void geoLocationScheduleTemplateDownload(HttpServletResponse response) {
        aiOptionModeService.geoLocationScheduleTemplateDownload(response);
    }

    @ApiLog
    @ApiOperation(value = "TA浓度档期模板下载")
    @RequestMapping(value = "/crowdTagScheduleTemplateDownload", method = RequestMethod.GET)
    public void crowdTagScheduleTemplateDownload(HttpServletResponse response) {
        aiOptionModeService.crowdTagScheduleTemplateDownload(response);
    }

    @ApiLog
    @ApiOperation(value = "地理围栏匹配结果数据导出")
    @RequestMapping(value = "/geoLocationMatchResultExport", method = RequestMethod.GET)
    @PreAuthorize("hasPermission(null,'GEOLOCATIONMATCHRESULT_EXPORT')")
    public void geoLocationMatchResultExport(HttpServletResponse response,@RequestParam("planId")Integer planId) {
        aiOptionModeService.geoLocationMatchResultExport(response,planId);
    }

    @ApiLog
    @ApiOperation(value = "TA浓度匹配结果数据导出")
    @RequestMapping(value = "/crowdTagMatchResultExport", method = RequestMethod.GET)
    @PreAuthorize("hasPermission(null,'CROWDTAGMATCHRESULT_EXPORT')")
    public void crowdTagMatchResultExport(HttpServletResponse response,@RequestParam("planId")Integer planId) {
        aiOptionModeService.crowdTagMatchResultExport(response,planId);
    }

    @ApiLog
    @ApiOperation(value = "地理围栏需求详情数据导出")
    @RequestMapping(value = "/geoLocationRequirementDetailExport", method = RequestMethod.GET)
    @PreAuthorize("hasPermission(null,'GEOLOCATIONREQUIREMENTDETAIL_EXPORT')")
    public void geoLocationRequirementDetailExport(HttpServletResponse response,@RequestParam("planId")Integer planId) {
        aiOptionModeService.geoLocationRequirementDetailExport(response,planId);
    }

    @ApiLog
    @ApiOperation(value = "TA浓度需求数据导出")
    @RequestMapping(value = "/taDensityRequirementExport", method = RequestMethod.GET)
    public void taDensityRequirementExport(HttpServletResponse response,@RequestParam("planId")Integer planId) {
        aiOptionModeService.taDensityRequirementExport(response,planId);
    }

    @ApiLog
    @ApiOperation(value = "TA浓度档期模板下载")
    @RequestMapping(value = "/taDensityScheduleTemplateDownload", method = RequestMethod.GET)
    public void taDensityScheduleTemplateDownload(HttpServletResponse response) {
        aiOptionModeService.taDensityScheduleTemplateDownload(response);
    }

    @ApiLog
    @ApiOperation(value = "ta浓度需求档期数据上传")
    @RequestMapping(value = "/taDensityScheduleUpload", method = RequestMethod.POST)
    public InternalResponse<List<String>> taDensityRequirementUpload(@RequestBody MultipartFile file, @RequestParam("planId")Integer planId) {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        List<String> responses = aiOptionModeService.taDensityScheduleUpload(planId, file, currentUser.getIdentifier());
        return InternalResponse.<List<String>>success().withBody(responses);
    }

    @ApiLog
    @ApiOperation(value = "查询档期明细列表")
    @RequestMapping(value = "/taDensityScheduleList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<TaDensityScheduleResponse>> taDensityScheduleList(@RequestBody TaDensityScheduleRequest request) {
        PageResponse<TaDensityScheduleResponse> planListResponse = aiOptionModeService.taDensityScheduleList(request);
        return InternalResponse.<PageResponse<TaDensityScheduleResponse>>success().withBody(planListResponse);
    }

    @ApiLog
    @ApiOperation(value = "导出档期明细列表")
    @RequestMapping(value = "/taDensityScheduleExport", method = RequestMethod.GET)
    public void taDensityScheduleExport(HttpServletResponse response,@RequestParam("planId")Integer planId) {
        aiOptionModeService.taDensityScheduleExport(response,planId);
    }







}
