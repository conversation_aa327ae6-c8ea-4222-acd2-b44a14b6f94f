package heran.media.management.platform.aioptionmode.mapper;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

/**
 * @Description:
 * @Author: yzj
 * @Date: 2023/11/20 16:10
 * @Version: 1.0
 */
public class AiOptionModeQuerySQLProvider {

    public String select(String selectionType,SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t1.id,t1.plan_name,t2.plan_sn,t2.group_name,t2.brand_name,t1.create_time,t1.update_time,t1.selection_type,t2.delivery_start_time,t2.delivery_end_time,t1.plan_status," +
                    "t1.medium_category_ids,t1.medium_attr_extras,t1.geo_range,t1.is_unrepeated,t1.sales_city_regions,t1.step_type, t3.nick_name creatorN<PERSON>, t5.id pack_id , t5.pack_name ");
            FROM("media_placement_selection_plan t1");
            INNER_JOIN("media_placement_base_sales_plan t2 ON t2.id = t1.base_plan_id");
            LEFT_OUTER_JOIN("management_sys_account t3 ON t3.id = t1.created_by_user");
            LEFT_OUTER_JOIN("main_crowd_plan_pack t4 ON t4.plan_id = t1.id");
            LEFT_OUTER_JOIN("media_ta_crowd_pack t5 ON t4.pack_id = t5.id");
            WHERE("t1.is_deleted = 0 AND t1.selection_type = '"+selectionType+"'");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                    }
                    if(cri.getKey().equals("keyword") && StringUtils.isNotBlank(cri.getValue())){
                        String value = cri.getValue();
                        WHERE("( t2.plan_sn LIKE '%"+value+"%' "+" OR t1.plan_name LIKE '%"+value+"%' "+" OR t2.brand_name LIKE '%"+value+"%' "+ " OR t2.group_name LIKE '%"+value+"%' ) ");
                    }
                    else if(cri.getKey().equals("planStatus") && StringUtils.isNotBlank(cri.getValue())){
                        String[] parts = cri.getValue().split(",");
                        StringBuilder result = new StringBuilder();
                        for (String part : parts) {
                            result.append("'").append(part).append("',");
                        }
                        // 删除最后一个多余的逗号
                        result.deleteCharAt(result.length() - 1);
                        WHERE("t1.plan_status IN ("+ result+")");
                    }else if(cri.getKey().equals("id") && StringUtils.isNotBlank(cri.getValue())){
                        WHERE("t1.id = "+ cri.getValue());
                    }
                    else{
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            ORDER_BY("t1.create_time DESC");
        }}.toString();
    }


    public String calendarPlacementSelect(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t1.id," +
                    "t1.plan_name," +
                    "t2.plan_sn," +
                    "t2.group_name," +
                    "t2.brand_name," +
                    "t1.selection_type," +
                    "t1.plan_status," +
                    "t1.geo_range,t1.sales_city_regions," +
                    "t1.is_unrepeated,t1.medium_attr_extras,t1.step_type," +
                    "t3.id calendar_placement_id,t3.plan_id,t3.placement_sn," +
                    "t3.media_name,t3.resource_provider,t3.resource_provider_type," +
                    "t3.province_id,t3.city_id,t3.district_id,t3.address_id," +
                    "t3.medium_lv1_category_id,t3.medium_lv2_category_id," +
                    "t3.medium_lv3_category_id,t3.pack_id,t3.pack_name," +
                    "t3.aoi_coordinates,t3.buffer_area,t3.buffer_type,t3.delivery_start_time," +
                    "t3.delivery_end_time,t3.remark,t3.extra_attrs, t3.update_time, t3.aoi_caliber, t4.stat_month," +
                    "sum(t3.aoi_hot_value) aoi_hot_value, sum(t3.vacancy_position)vacancy_position, t6.launch_start_date ");
            FROM("media_placement_selection_plan t1");
            INNER_JOIN("media_placement_base_sales_plan t2 ON t2.id = t1.base_plan_id");
            INNER_JOIN("main_calendar_placement t3 on t1.id= t3.plan_id");
            LEFT_OUTER_JOIN("selection_plan_hottype_detail t4 on t1.id= t4.plan_id");
            LEFT_OUTER_JOIN("route_selection_scheme t5 on t3.id= t5.selection_plan_id");
            LEFT_OUTER_JOIN("route_selection_scheme_line t6 on t5.id= t6.scheme_id");
            WHERE("t1.is_deleted = 0 ");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                    }
                    if(cri.getKey().equals("id") && StringUtils.isNotBlank(cri.getValue())){
                        WHERE("t1.id = "+ cri.getValue());
                    }
                    if(cri.getKey().equals("group_by_category")){
                        GROUP_BY("medium_lv3_category_id");
                    }
                }
            }
            ORDER_BY("t3.update_time DESC");
        }}.toString();
    }



    public String selectPlacementSnList(SearchCriteria criteria) {
        return new SQL() {{
            SELECT( "mcp.id, mcp.placement_sn,mcp.plan_id, mcp.media_name, mcp.resource_provider,mcp.resource_provider_type, " +
                    "mcp.province_id, mcp.city_id, mcp.district_id, mcp.address_id,  mcp.media_position,mcp.placement_cost," +
                    "mcp.medium_lv1_category_id, mcp.medium_lv2_category_id, mcp.medium_lv3_category_id, " +
                    "mcp.pack_id, mcp.pack_name, mcp.remark,mcp.extra_attrs," +
                    "mcp.delivery_start_time,mcp.delivery_end_time," +
                    "mcp.longitude,mcp.latitude, vacancy_position, mcp.update_time, mcp.buffer_area,mcp.buffer_type, " +
                    "mcp.aoi_area, mcp.aoi_coordinates,mcp.extend_aoi_coordinates,mcp.aoi_center, mcp.aoi_caliber, mcp.aoi_hot_value,mcp.ta_hot_value," +
                    "r1.code district_code,  r2.code city_code, r2.short_name city_name, " +
                    "ext.brighten, ext.mileage,ext.bus_name,ext.launch,ext.issue_amount,ext.production_amount,ext.extra_attrs placementExtraAttrs, " +
                    "CONCAT(DATE_FORMAT(delivery_start_time,'%Y/%m/%d'),'-',DATE_FORMAT(delivery_end_time,'%Y/%m/%d')) as deliveryTime,t4.stat_month, " +
                    "CONCAT(longitude,',',latitude) as longitudeAndLatitude, " +
                    " case when t4.statistical_type = 'RESIDENT_POPULATION' then '常驻 '   " +
                    "      when t4.statistical_type = 'PASSENGER_POPULATION' then '客流' end  as statistical_type " );
            FROM("main_calendar_placement mcp ");
            LEFT_OUTER_JOIN(" main_dict_region r1 on  mcp.district_id = r1.id ");
            LEFT_OUTER_JOIN(" main_dict_region r2 on  mcp.city_id = r2.id ");
            LEFT_OUTER_JOIN(" main_calendar_placement_ext ext on mcp.id = ext.calendar_placement_id ");
            LEFT_OUTER_JOIN("selection_plan_hottype_detail t4 on mcp.plan_id= t4.plan_id");
            WHERE(" 1=1 ");
            if( criteria.getCriterias() !=null && criteria.getCriterias().size()>0){
                for(SearchCriteria.Criteria cri:criteria.getCriterias()){
                    if(StringUtils.isBlank(cri.getKey())){
                        continue;
                    }
                    if(cri.getKey().equals("keyword") && StringUtils.isNotBlank(cri.getValue())){
                        String value = cri.getValue();
                        WHERE("( mcp.placement_sn LIKE '%"+value+"%' "+" OR mcp.media_name LIKE '%"+value+"%' "+" OR mcp.resource_provider LIKE '%"+value+"%' "+ " OR mcp.media_position LIKE '%"+value+"%' ) ");
                    }
                    if(cri.getKey().equals("planId") && StringUtils.isNotBlank(cri.getValue())){
                        WHERE("mcp.plan_id = "+ cri.getValue());
                    }
                    if(cri.getKey().equals("categoryId") && StringUtils.isNotBlank(cri.getValue())){
                        WHERE("mcp.medium_lv3_category_id = "+ cri.getValue());
                    }
                    if(cri.getKey().equals("cityIds") && StringUtils.isNotBlank(cri.getValue())){
                        WHERE("mcp.city_id in ("+ cri.getValue() + ") ");
                    }
                    if(cri.getKey().equals("notgeneratedAoiPoints")){
                        WHERE(" (mcp.aoi_coordinates is null or mcp.aoi_coordinates ='' ) ");
                    }
                    if (cri.getKey().equals("not_in_lv3_category_name") && StringUtils.isNotBlank(cri.getValue())){
                        LEFT_OUTER_JOIN(" main_resource_category c3 on  mcp.medium_lv3_category_id = c3.id ");
                        WHERE("c3.category_name not in ( "+ cri.getValue() + ") ");
                    }
                    if (cri.getKey().equals("groupByCity")  ){
                        GROUP_BY("mcp.city_id");
                    }
                }
            }
            ORDER_BY("mcp.update_time DESC");
        }}.toString();
    }
}
