package heran.media.management.platform.aioptionmode.po;

import heran.media.management.platform.aioptionmode.dto.MediumAttrExtrasResponse;
import heran.media.management.platform.aioptionmode.dto.MediumFormatAttrResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: 媒体形式数据
 * @Author: yzj
 * @Date: 2023/11/23 17:40
 * @Version: 1.0
 */
@Data
public class MediumFormatData implements Serializable {

    private Integer lv1Id;
    private String lv1Name;
    private Integer lv2Id;
    private String lv2Name;
    private Integer lv3Id;
    private String lv3Name;

    public List<MediumFormatAttrResponse> adaptToMediumFormatList(MediumAttrExtras attrData) {
        List<MediumFormatAttrResponse> mediumFormatList = new ArrayList<>();
        MediumFormatAttrResponse lv1MediumFormat = new MediumFormatAttrResponse();
        lv1MediumFormat.setCategoryId(this.lv1Id);
        lv1MediumFormat.setCategoryName(this.lv1Name);
        MediumFormatAttrResponse lv2MediumFormat = new MediumFormatAttrResponse();
        lv2MediumFormat.setCategoryId(this.lv2Id);
        lv2MediumFormat.setCategoryName(this.lv2Name);
        lv2MediumFormat.setParentId(this.lv1Id);
        MediumFormatAttrResponse lv3MediumFormat = new MediumFormatAttrResponse();
        lv3MediumFormat.setCategoryId(this.lv3Id);
        lv3MediumFormat.setCategoryName(this.lv3Name);
        lv3MediumFormat.setParentId(this.lv2Id);
        if (attrData!=null){
            lv3MediumFormat.setMediumAttr(new MediumAttrExtrasResponse(attrData));
        }
        mediumFormatList.add(lv1MediumFormat);
        mediumFormatList.add(lv2MediumFormat);
        mediumFormatList.add(lv3MediumFormat);
        return mediumFormatList;
    }
}
