package heran.media.management.platform.aioptionmode.error;

import heran.media.sharelib.errors.InternalException;


public class PlanAoiCoordinatesException extends InternalException {


    private String errMsg;


    public PlanAoiCoordinatesException(String errMsg) {
        this.errMsg = errMsg;
    }

    public PlanAoiCoordinatesException() {
    }

    @Override
    public String getErrorCode() {
        String code = "PLAN_STATUS_02";
        return code;
    }

    @Override
    public Object[] getParams() {
        return new String[]{errMsg};
    }

    @Override
    public String getCustomizeMessage() {
        return this.errMsg;
    }
}
