package heran.media.management.platform.common.constants;

import java.util.Arrays;
import java.util.List;

public class AppConstants {
    public static final String TOKEN_TYPE_USERINFO = "USER_INFO_TOKEN ";
    public static final String TOKEN_HEADER = "HERANMEDIA ";

    public static final String FUNCTION_WILDCARD = "*";
    /**
     * 最大上传图片数量
     */
    public static final Integer UPLOAD_COUNT = 10;

    /**
     * 验证码有效期（分钟）
     */
    public static final long CAPTCHA_EXPIRATION = 2;

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "heran-media-management-platform:captcha_codes:";

    public static final String CURRENT_LOGIN_USER_TOKEN_MAP_KEY = "heran:curLoginToken";

    public static final String AOI = "AOI";
    public static final String CBD = "CBD";

    public static final String REDIS_AREACODE_CITY_PROVINCIAL_TABLE_MAP_KEY = "heran:AREACODE_CITY_PROVINCIAL_TABLE_MAP";
    public static final String REDIS_RESOURCE_CATEGORY_TABLE_MAP_KEY = "heran:RESOURCE_CATEGORY_TABLE_MAP";

    public static final String MEDIA_SELECTION_PLAN_MENU_FIELD_ATTR_COMMON = "COMMON";
    public static final String MEDIA_SELECTION_PLAN_MENU_FIELD_ATTR_EXPAND = "EXPAND";

    public static final List<String> REGION_CODES = Arrays.asList(
            "110100",
            "120100",
            "310100",
            "500100"
    );
}
