package heran.media.management.platform.common.controller;


import heran.media.management.platform.common.domain.dto.LabelResponse;
import heran.media.management.platform.common.domain.dto.MapResourceChildrenData;
import heran.media.management.platform.common.domain.dto.ProfileTypeData;
import heran.media.management.platform.common.service.CommonService;
import heran.media.management.platform.common.utils.DownloadFileUtils;
import heran.media.sharelib.domain.db.model.main.MapResourceDict;
import heran.media.sharelib.domain.dto.InternalResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @Description: 公共Controller
 * @Author: yzj
 * @Date: 2023/11/22 16:38
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/common")
public class CommonController {

    @Resource
    private CommonService commonService;

    @ApiOperation(value = "标签列表查询")
    @RequestMapping(value = "/getLabelListByGroupLabel", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<LabelResponse>> getLabelListByGroupLabel(@Param("groupLabel") String groupLabel) {
        List<LabelResponse> crowdLabelList = commonService.getLabelListByGroupLabel(groupLabel);
        return InternalResponse.<List<LabelResponse>>success().withBody(crowdLabelList);
    }

    @ApiOperation(value = "标签树状集合")
    @RequestMapping(value = "/getLabelList", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<LabelResponse>> getLabelListByGroupLabel(@Param("categoryId") Integer categoryId) {
        List<LabelResponse> crowdLabelList = commonService.getLabelList(categoryId);
        return InternalResponse.<List<LabelResponse>>success().withBody(crowdLabelList);
    }

    @ApiOperation(value = "获取集团名称列表")
    @RequestMapping(value = "/getGroupNameList", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<String>> getGroupNameList() {
        List<String> groupNameList = commonService.getGroupNameList();
        return InternalResponse.<List<String>>success().withBody(groupNameList);
    }

    @ApiOperation(value = "获取品牌名称列表")
    @RequestMapping(value = "/getBrandNameList", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<String>> getBrandNameList() {
        List<String> brandNameList = commonService.getBrandNameList();
        return InternalResponse.<List<String>>success().withBody(brandNameList);
    }

    @ApiOperation(value = "获取数智透视 数智城市透视 洞察维度")
    @RequestMapping(value = "/getCityDimensionality", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<ProfileTypeData>> getCityDimensionality() {
        List<ProfileTypeData> dataList = commonService.getCityDimensionality();
        return InternalResponse.<List<ProfileTypeData>>success().withBody(dataList);
    }


    @ApiOperation(value = "下载指定模板")
    @RequestMapping(value = "/downloadFile", method = RequestMethod.GET)
    @ResponseBody
    public void downloadFile(HttpServletResponse resp, String fileName, String newFileName) throws IOException {
        DownloadFileUtils.downloadFile(resp, fileName, newFileName);
    }

    @ApiOperation(value = "高德拉下分类")
    @RequestMapping(value = "/getMapResourceDictDataList", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<MapResourceDict>> getMapResourceDictDataList(@Param("groupLabel") String groupLabel) {
        List<MapResourceDict> crowdLabelList = commonService.getMapResourceDictDataList(groupLabel);
        return InternalResponse.<List<MapResourceDict>>success().withBody(crowdLabelList);
    }

    @ApiOperation(value = "高德拉下分类（查所有）")
    @RequestMapping(value = "/getMapResourceDictDataListAll", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<MapResourceDict>> getMapResourceDictDataListAll() {
        List<MapResourceDict> crowdLabelList = commonService.getMapResourceDictDataListAll();
        return InternalResponse.<List<MapResourceDict>>success().withBody(crowdLabelList);
    }

    @ApiOperation(value = "高德拉下分类树结构")
    @RequestMapping(value = "/getMapResourceChildrenList", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<MapResourceChildrenData>> getMapResourceChildrenList(@Param("groupLabel") String groupLabel) {
        List<MapResourceChildrenData> crowdLabelList = commonService.getMapResourceChildrenList(groupLabel);
        return InternalResponse.<List<MapResourceChildrenData>>success().withBody(crowdLabelList);
    }

    @ApiOperation(value = "高德拉下分类树结构（查所有）")
    @RequestMapping(value = "/getMapResourceChildrenListAll", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<MapResourceChildrenData>> getMapResourceChildrenListAll() {
        List<MapResourceChildrenData> crowdLabelList = commonService.getMapResourceChildrenListAll();
        return InternalResponse.<List<MapResourceChildrenData>>success().withBody(crowdLabelList);
    }
}
