package heran.media.management.platform.common.controller;

import heran.media.management.platform.aioptionmode.error.AoiIndexNotBoundException;
import heran.media.management.platform.aioptionmode.error.GenerateBufferAreaException;
import heran.media.management.platform.aioptionmode.error.PlanNotExistException;
import heran.media.management.platform.aioptionmode.error.PlanStatusException;
import heran.media.management.platform.effectestimate.error.OrderEffectEstimateGroupImageImportValidateException;
import heran.media.management.platform.main.error.MediaPlacementImportValidateException;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.errors.InternalException;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@ControllerAdvice
public class BaseExceptionController {
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public InternalResponse onException(Exception e, HttpServletRequest request) {
        log.error("Error occurred while execute {} ", request.getRequestURI(), e);
        return InternalResponse.fail();
    }

    @ExceptionHandler(value = AccessDeniedException.class)
    @ResponseBody
    public InternalResponse onAccessException(Exception e, HttpServletRequest request) {
        log.error("Error occurred while execute {} ", request.getRequestURI(), e);
        return InternalResponse.fail("10");
    }

    @ExceptionHandler(value = InternalException.class)
    @ResponseBody
    public InternalResponse onInternalException(InternalException ie, HttpServletRequest request) {
        log.warn("Internal error occurred while execute {} with errorCode={}, msg={}", request.getRequestURI(), ie.getErrorCode(), ie.getCustomizeMessage());
        return ie.getParams() == null ? InternalResponse.fail(ie.getErrorCode()) : InternalResponse.fail(ie.getErrorCode(), ie.getParams());
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public InternalResponse onMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        log.warn("Parameter error occurred while execute {} with msg={}", request.getRequestURI(), e.getAllErrors().get(0).getDefaultMessage());
        return InternalResponse.fail("04", e.getAllErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(value = OrderEffectEstimateGroupImageImportValidateException.class)
    @ResponseBody
    public InternalResponse onMethodArgumentNotValidException(OrderEffectEstimateGroupImageImportValidateException e, HttpServletRequest request) {
        log.warn("Parameter error occurred while execute {} with msg={}", request.getRequestURI(), e.getCustomizeMessage());
        return InternalResponse.fail("03",e.getCustomizeMessage());
    }

    @ExceptionHandler(value = MediaPlacementImportValidateException.class)
    @ResponseBody
    public InternalResponse onMethodArgumentNotValidException(MediaPlacementImportValidateException e, HttpServletRequest request) {
        log.warn("Parameter error occurred while execute {} with msg={}", request.getRequestURI(), e.getCustomizeMessage());
        return InternalResponse.fail("03",e.getCustomizeMessage());
    }

    @ExceptionHandler(value = DuplicateKeyException.class)
    @ResponseBody
    public InternalResponse onConstraintViolationException(DuplicateKeyException e, HttpServletRequest request) {
        log.warn("Duplicated data error occurred while execute {} with msg={}", request.getRequestURI(), e.getCause().getLocalizedMessage());
        return InternalResponse.fail("05");
    }

    @ExceptionHandler(value = DataIntegrityViolationException.class)
    @ResponseBody
    public InternalResponse onDataIntegrityViolationException(DataIntegrityViolationException e, HttpServletRequest request) {
        String message = e.getCause().getLocalizedMessage();
        log.warn("Foreign constaint error occurred while execute {} with msg={}", request.getRequestURI(), message);
        if (message.contains("doesn't have a default value")) {
            InternalResponse.fail("07");
        }
        return InternalResponse.fail("06");
    }

    @ExceptionHandler(value = MaxUploadSizeExceededException.class)
    @ResponseBody
    public InternalResponse maxUploadSizeExceededException(MaxUploadSizeExceededException e, HttpServletRequest request) {
        log.warn("File upload at execution time A single file is larger than 10MB {}", request.getRequestURI());
        return InternalResponse.fail("09");
    }

    @ExceptionHandler(value = PlanNotExistException.class)
    @ResponseBody
    public InternalResponse PlanNotExistException(PlanNotExistException e, HttpServletRequest request) {
        log.warn("未查询到方案数据： {}", request.getRequestURI());
        return InternalResponse.fail(e.getErrorCode() );
    }


    @ExceptionHandler(value = AoiIndexNotBoundException.class)
    @ResponseBody
    public InternalResponse AoiIndexNotBoundException(AoiIndexNotBoundException e, HttpServletRequest request) {
        log.warn("热力值指标【口径，月份】未绑定： {}", request.getRequestURI());
        return InternalResponse.fail(e.getErrorCode());
    }

    @ExceptionHandler(value = PlanStatusException.class)
    @ResponseBody
    public InternalResponse PlanStatusException(PlanStatusException e, HttpServletRequest request) {
        log.warn("方案状态 {}", request.getRequestURI());
        return InternalResponse.fail(e.getErrorCode());
    }

}
