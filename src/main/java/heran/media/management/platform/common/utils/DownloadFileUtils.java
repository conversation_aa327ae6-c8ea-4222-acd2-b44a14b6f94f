package heran.media.management.platform.common.utils;

import com.google.gson.Gson;
import heran.media.management.platform.common.domain.dto.TableBlock;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.sharelib.domain.dto.InternalResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static heran.media.management.platform.common.utils.xls.XLSWriter.getExportFileName;
import static heran.media.management.platform.common.utils.xls.XLSWriter.getExportTxtFileName;

@Slf4j
public class DownloadFileUtils {
    /**
     * 设置Response
     *
     * @param resp         response
     * @param files        需要返回下载的文件列表
     * @param respFileName 返回名称，不需要后缀
     * @throws IOException ioException
     */
    public static void setFileResponse(HttpServletResponse resp, List<File> files, String respFileName) throws IOException {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        OutputStream outputStream = null;
        try {
            outputStream = resp.getOutputStream();
            if (files.isEmpty()) {
                outputStream.write(new Gson().toJson(internalResponse).getBytes(StandardCharsets.UTF_8));
            } else {
                File file = heran.media.management.platform.common.utils.FileUtils.compressFile(files);
                String fileName = file.getName();
                //这行代码会将整个Excel文件（可能几百MB）一次性读入内存，转换为字节数组。对于百万级数据的Excel文件，这会导致：文件大小巨大：百万行数据可能产生几百MB的Excel文件
                byte[] by = FileUtils.readFileToByteArray(file);
                respFileName = StringUtils.isNotBlank(respFileName) ? URLEncoder.encode(getExportFileName(respFileName), "UTF-8") : fileName;
                resp.setCharacterEncoding("utf-8");
                resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                resp.setHeader("Content-Disposition", "attachment; filename=" + respFileName);

                try {
                    outputStream.write(by);
                } catch (org.apache.catalina.connector.ClientAbortException e) {
                    log.info("客户端断开连接，文件下载中止: {}", respFileName);
                    return; // 直接返回，不需要继续处理
                } catch (IOException e) {
                    if (isClientAbortException(e)) {
                        log.info("客户端断开连接，文件下载中止: {}", respFileName);
                        return;
                    }
                    throw e;
                }

                FileUtils.forceDelete(file);
            }
        } catch (org.apache.catalina.connector.ClientAbortException e) {
            log.info("客户端断开连接，文件下载中止: {}", respFileName);
        } catch (IOException e) {
            if (isClientAbortException(e)) {
                log.info("客户端断开连接，文件下载中止: {}", respFileName);
            } else {
                throw e;
            }
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (org.apache.catalina.connector.ClientAbortException e) {
                    log.debug("客户端断开连接，关闭输出流时发生异常: {}", respFileName);
                } catch (IOException e) {
                    if (isClientAbortException(e)) {
                        log.debug("客户端断开连接，关闭输出流时发生异常: {}", respFileName);
                    } else {
                        log.warn("关闭输出流时发生异常", e);
                    }
                }
            }
        }
    }


    public static void setTxtFileResponse(HttpServletResponse resp, List<File> files, String respFileName) throws IOException {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        OutputStream outputStream = null;
        try {
            outputStream = resp.getOutputStream();
            if (files.isEmpty()) {
                outputStream.write(new Gson().toJson(internalResponse).getBytes(StandardCharsets.UTF_8));
            } else {
                File file = heran.media.management.platform.common.utils.FileUtils.compressFile(files);
                String fileName = file.getName();
                byte[] by = FileUtils.readFileToByteArray(file);
                respFileName = StringUtils.isNotBlank(respFileName) ? URLEncoder.encode(getExportTxtFileName(respFileName), "UTF-8") : fileName;
                resp.setCharacterEncoding("utf-8");
                resp.setContentType("text/plain;charset=UTF-8");
                resp.setHeader("Content-Disposition", "attachment; filename=" + respFileName);

                try {
                    outputStream.write(by);
                } catch (org.apache.catalina.connector.ClientAbortException e) {
                    log.info("客户端断开连接，文件下载中止: {}", respFileName);
                    return;
                } catch (IOException e) {
                    if (isClientAbortException(e)) {
                        log.info("客户端断开连接，文件下载中止: {}", respFileName);
                        return;
                    }
                    throw e;
                }

                FileUtils.forceDelete(file);
            }
        } catch (org.apache.catalina.connector.ClientAbortException e) {
            log.info("客户端断开连接，文件下载中止: {}", respFileName);
        } catch (IOException e) {
            if (isClientAbortException(e)) {
                log.info("客户端断开连接，文件下载中止: {}", respFileName);
            } else {
                throw e;
            }
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (org.apache.catalina.connector.ClientAbortException e) {
                    log.debug("客户端断开连接，关闭输出流时发生异常: {}", respFileName);
                } catch (IOException e) {
                    if (isClientAbortException(e)) {
                        log.debug("客户端断开连接，关闭输出流时发生异常: {}", respFileName);
                    } else {
                        log.warn("关闭输出流时发生异常", e);
                    }
                }
            }
        }
    }

    /**
     * 读取资源文件夹下的文件并下载下来
     *
     * @param fileName 文件名称
     * @throws IOException 异常处理
     */
    public static void downloadFile(HttpServletResponse response, String fileName, String newFileName) throws IOException {
        // 构建文件路径
        String filePath = "download/" + fileName + ".xlsx";
        // 从resources目录获取文件
        Resource resource = new ClassPathResource(filePath);
        if (!resource.exists()) {
            log.info("下载得资源文件不存在！");
            return;
        }
        // 设置响应头信息
        String encodedFileName = URLEncoder.encode(XLSWriter.getExportFileName(newFileName), "UTF-8");
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        //response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + encodedFileName);

        // 将文件内容写入响应体
        try (InputStream inputStream = resource.getInputStream();
             OutputStream outputStream = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                try {
                    outputStream.write(buffer, 0, bytesRead);
                } catch (org.apache.catalina.connector.ClientAbortException e) {
                    log.info("客户端断开连接，文件下载中止: {}", newFileName);
                    return;
                } catch (IOException e) {
                    if (isClientAbortException(e)) {
                        log.info("客户端断开连接，文件下载中止: {}", newFileName);
                        return;
                    }
                    throw e;
                }
            }
        } catch (org.apache.catalina.connector.ClientAbortException e) {
            log.info("客户端断开连接，文件下载中止: {}", newFileName);
        } catch (IOException e) {
            if (isClientAbortException(e)) {
                log.info("客户端断开连接，文件下载中止: {}", newFileName);
            } else {
                throw e;
            }
        }
    }

    public static void exportMultiTableToSheet(Sheet sheet, List<TableBlock> blocks) {
        int currentRow = 0;
        for (TableBlock block : blocks) {
            // 写标题（如：用户信息）
            Row titleRow = sheet.createRow(currentRow++);
            titleRow.createCell(0).setCellValue(block.getTitle());

            // 写表头
            Row headerRow = sheet.createRow(currentRow++);
            List<String> headers = block.getHeaders();
            for (int i = 0; i < headers.size(); i++) {
                headerRow.createCell(i).setCellValue(headers.get(i));
            }
            // 写数据
            for (List<String> rowData : block.getData()) {
                Row row = sheet.createRow(currentRow++);
                for (int i = 0; i < rowData.size(); i++) {
                    row.createCell(i).setCellValue(rowData.get(i));
                }
            }
            // 留空一行
            currentRow++;
        }
    }

    /**
     * 设置Response（支持大文件）
     *
     * @param resp         response
     * @param files        需要返回下载的文件列表
     * @param respFileName 返回名称，不需要后缀
     * @throws IOException ioException
     */
    public static void setLongFileResponse(HttpServletResponse resp, List<File> files, String respFileName) throws IOException {
        InternalResponse<Void> internalResponse = InternalResponse.success();

        if (files.isEmpty()) {
            try (OutputStream outputStream = resp.getOutputStream()) {
                outputStream.write(new Gson().toJson(internalResponse).getBytes(StandardCharsets.UTF_8));
            }
            return;
        }

        File file = heran.media.management.platform.common.utils.FileUtils.compressFile(files);

        try {
            // 设置响应头
            respFileName = StringUtils.isNotBlank(respFileName) ?
                    URLEncoder.encode(getExportFileName(respFileName), "UTF-8") : file.getName();
            resp.setCharacterEncoding("utf-8");
            resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            resp.setHeader("Content-Disposition", "attachment; filename=" + respFileName);

            // 设置文件大小（可选）
            resp.setContentLengthLong(file.length());

            // 流式传输
            try {
                streamFileToResponse(file, resp);
            } catch (org.apache.catalina.connector.ClientAbortException e) {
                // 客户端断开连接，记录信息日志
                log.info("客户端断开连接，文件下载中止: {}", respFileName);
                // 不需要抛出异常，这是正常情况
            } catch (IOException e) {
                // 检查是否是客户端断开连接导致的IOException
                if (isClientAbortException(e)) {
                    log.info("客户端断开连接，文件下载中止: {}", respFileName);
                } else {
                    // 其他IO异常继续抛出
                    throw e;
                }
            }

        } finally {
            FileUtils.forceDelete(file);
        }
    }

    /**
     * 流式传输文件到响应
     */
    private static void streamFileToResponse(File file, HttpServletResponse resp) throws IOException {
        try (FileInputStream fileInputStream = new FileInputStream(file);
             OutputStream outputStream = resp.getOutputStream()) {

            byte[] buffer = new byte[16384];
            int bytesRead;
            long totalBytes = 0;

            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                try {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;

                    // 可选：添加进度日志
                    if (totalBytes % (1024 * 1024) == 0) {
                        log.debug("已传输: {} MB", totalBytes / (1024 * 1024));
                    }
                } catch (org.apache.catalina.connector.ClientAbortException e) {
                    // 客户端断开连接，这是正常情况，记录信息日志即可
                    log.info("客户端断开连接，文件传输中止。已传输: {} MB", totalBytes / (1024 * 1024));
                    return; // 直接返回，不需要继续传输
                } catch (IOException e) {
                    // 检查是否是客户端断开连接导致的IOException
                    if (isClientAbortException(e)) {
                        log.info("客户端断开连接，文件传输中止。已传输: {} MB", totalBytes / (1024 * 1024));
                        return;
                    }
                    // 其他IO异常继续抛出
                    throw e;
                }
            }

            try {
                outputStream.flush();
            } catch (org.apache.catalina.connector.ClientAbortException e) {
                log.info("客户端断开连接，文件传输完成但flush失败。已传输: {} MB", totalBytes / (1024 * 1024));
            } catch (IOException e) {
                if (isClientAbortException(e)) {
                    log.info("客户端断开连接，文件传输完成但flush失败。已传输: {} MB", totalBytes / (1024 * 1024));
                } else {
                    throw e;
                }
            }
        }
    }

    /**
     * 检查异常是否是客户端断开连接导致的
     */
    private static boolean isClientAbortException(Throwable e) {
        if (e == null) {
            return false;
        }

        // 检查异常类型
        if (e instanceof org.apache.catalina.connector.ClientAbortException) {
            return true;
        }

        // 检查异常消息中是否包含客户端断开连接的关键词
        String message = e.getMessage();
        if (message != null) {
            message = message.toLowerCase();
            return message.contains("你的主机中的软件中止了一个已建立的连接") ||
                   message.contains("connection reset") ||
                   message.contains("broken pipe") ||
                   message.contains("connection aborted") ||
                   message.contains("远程主机强迫关闭了一个现有的连接");
        }

        // 检查根本原因
        return isClientAbortException(e.getCause());
    }


}
