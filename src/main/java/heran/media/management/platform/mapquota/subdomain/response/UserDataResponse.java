package heran.media.management.platform.mapquota.subdomain.response;

import heran.media.management.platform.mapquota.subdomain.dto.UserData;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserDataResponse {
    @ApiModelProperty("用户id")
    private Integer userId;
    @ApiModelProperty("用户名称")
    private String userName;

    public UserDataResponse(UserData data){
        this.userId = data.getUserId();
        this.userName = data.getUserName();
    }
}
