package heran.media.management.platform.mapquota.controller;

import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.utils.DownloadFileUtils;
import heran.media.management.platform.mapquota.service.ApiMapQuotaDetailService;
import heran.media.management.platform.mapquota.subdomain.response.ApiMapQuotaDetailListDataResponse;
import heran.media.management.platform.mapquota.subdomain.request.QuotaRequest;
import heran.media.management.platform.mapquota.subdomain.response.ApiMapQuotaListDataResponse;
import heran.media.management.platform.mapquota.subdomain.response.RequestNameResponse;
import heran.media.management.platform.mapquota.subdomain.response.UserDataResponse;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/api_map_quota_detail")
public class ApiMapQuotaDetailController {

    @Resource
    private ApiMapQuotaDetailService apiMapQuotaDetailService;

    @ApiLog(storageDescription = "配额管理列表数据 （请求日志）")
    @ApiOperation(value = "配额管理列表数据", notes = "查询字段可包含【userId(用户id),questName(接口名称 传code),platform(平台),requestTime(请求时间)】")
    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<ApiMapQuotaDetailListDataResponse>> list(@Valid @RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        PageResponse<ApiMapQuotaDetailListDataResponse> pageResponse = apiMapQuotaDetailService.list(criteria);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog(storageDescription = "获取请求接口下拉数据")
    @ApiOperation(value = "获取请求接口下拉数据", notes = "获取请求接口下拉数据")
    @RequestMapping(value = "/getRequestName", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<List<RequestNameResponse>> getRequestName() {
        InternalResponse internalResponse = null;
        List<RequestNameResponse> requestName = apiMapQuotaDetailService.getRequestName();
        internalResponse = InternalResponse.success().withBody(requestName);
        return internalResponse;
    }

    @ApiOperation(value = "获取用户下拉框")
    @ApiLog(storageDescription = "获取用户下拉框")
    @RequestMapping(value = "/getUserData", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse<List<UserDataResponse>> getUserData(@RequestParam(value = "userName", required = false) String userName) {
        InternalResponse internalResponse = null;
        List<UserDataResponse> userData = apiMapQuotaDetailService.getUserData(userName);
        internalResponse = InternalResponse.success().withBody(userData);
        return internalResponse;
    }

    @ApiOperation(value = "导出配额使用情况 （请求日志）")
    @ApiLog(printOutputs = false, storage = true, storageDescription = "配额使用情况导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void export(HttpServletResponse resp,
                       @RequestParam(value = "exportAll", required = false) Boolean exportAll,
                       @RequestBody SearchCriteria searchCriteria) throws Exception {
        exportAll = exportAll != null && exportAll;
        File file = apiMapQuotaDetailService.exportData(searchCriteria, exportAll);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "配额使用情况信息");
    }

    @ApiLog(storageDescription = "消耗总配额")
    @ApiOperation(value = "消耗总配额", notes = "查询字段可包含【statTime(开始时间)，endTime(结束时间)】")
    @RequestMapping(value = "/getQuotaCount", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<Integer> getQuotaCount(@Valid @RequestBody QuotaRequest quotaRequest) {
        InternalResponse internalResponse = null;
        Integer quotaCount = apiMapQuotaDetailService.getQuotaCount(quotaRequest);
        internalResponse = InternalResponse.success().withBody(quotaCount);
        return internalResponse;
    }

    @ApiLog(storageDescription = "每月汇总列表")
    @ApiOperation(value = "每月汇总列表", notes = "查询字段可包含【questName(接口名称 传code),requestTime(请求时间)】")
    @RequestMapping(value = "/getApiMapQuotaMonthData", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<ApiMapQuotaListDataResponse>> getApiMapQuotaMonthData(@Valid @RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        PageResponse<ApiMapQuotaListDataResponse> pageResponse = apiMapQuotaDetailService.getApiMapQuotaMonthData(criteria);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiOperation(value = "导出每月汇总配额")
    @ApiLog(printOutputs = false, storage = true, storageDescription = "每月汇总配额导出")
    @RequestMapping(value = "/exportApiMapQuotaMonthData", method = RequestMethod.POST, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportApiMapQuotaMonthData(HttpServletResponse resp,
                                           @RequestParam(value = "exportAll", required = false) Boolean exportAll,
                                           @RequestBody SearchCriteria searchCriteria) throws Exception {
        exportAll = exportAll != null && exportAll;
        File file = apiMapQuotaDetailService.exportApiMapQuotaMonthData(searchCriteria, exportAll);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "每月汇总配额信息");
    }

}
