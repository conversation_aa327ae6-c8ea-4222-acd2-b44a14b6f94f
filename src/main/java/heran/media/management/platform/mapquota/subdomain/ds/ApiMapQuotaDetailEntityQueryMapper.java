package heran.media.management.platform.mapquota.subdomain.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.mapquota.subdomain.dto.ApiMapQuotaDetailListData;
import heran.media.management.platform.mapquota.subdomain.dto.ApiMapQuotaMonthData;
import heran.media.management.platform.mapquota.subdomain.dto.UserData;
import heran.media.management.platform.mapquota.subdomain.request.QuotaRequest;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ApiMapQuotaDetailEntityQueryMapper {

    @SelectProvider(type = ApiMapQuotaDetailEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "userName", column = "user_name"),
            @Result(property = "platform", column = "platform"),
            @Result(property = "questName", column = "quest_name"),
            @Result(property = "type", column = "type"),
            @Result(property = "typeInfoName", column = "type_info_name"),
            @Result(property = "statisticalType", column = "statistical_type"),
            @Result(property = "statMonth", column = "stat_month"),
            @Result(property = "dimension", column = "dimension"),
            @Result(property = "packId", column = "pack_id"),
            @Result(property = "packName", column = "pack_name"),
            @Result(property = "gridSize", column = "grid_size"),
            @Result(property = "taskId", column = "task_id"),
            @Result(property = "chargeCnt", column = "charge_cnt"),
            @Result(property = "returnedValue", column = "returned_value"),
            @Result(property = "quota", column = "quota"),
            @Result(property = "requestTime", column = "request_time"),
    }
    )
    List<ApiMapQuotaDetailListData> list(SearchCriteria criteria);

    @Select({
            "<script>",
            "SELECT id userId, user_name FROM management_sys_account",
            "<where>",
            "  <if test='userName != null and userName != \"\"'>",
            "    user_name LIKE CONCAT('%', #{userName}, '%')",
            "  </if>",
            "</where>",
            "</script>"
    })
    List<UserData> getUserData(@Param("userName") String userName);


    @SelectProvider(type = ApiMapQuotaDetailEntityQuerySQLProvider.class, method = "getQuotaCount")
    Integer getQuotaCount(QuotaRequest quotaRequest);


    @SelectProvider(type = ApiMapQuotaDetailEntityQuerySQLProvider.class, method = "getApiMapQuotaMonthData")
    @Results(value = {
            @Result(property = "questName", column = "quest_name"),
            @Result(property = "statMonth", column = "statMonth"),
            @Result(property = "quota", column = "total_quota"),
    }
    )
    List<ApiMapQuotaMonthData> getApiMapQuotaMonthData(SearchCriteria criteria);

}
