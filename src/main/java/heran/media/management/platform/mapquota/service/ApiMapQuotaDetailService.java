package heran.media.management.platform.mapquota.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import heran.media.management.platform.common.db.mapper.CommunalMapper;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.management.platform.mapquota.subdomain.bo.XlsApiMapQuotaDetailListData;
import heran.media.management.platform.mapquota.subdomain.bo.XlsApiMapQuotaMonthData;
import heran.media.management.platform.mapquota.subdomain.ds.ApiMapQuotaDetailEntityQueryMapper;
import heran.media.management.platform.mapquota.subdomain.dto.ApiMapQuotaDetailListData;
import heran.media.management.platform.mapquota.subdomain.dto.ApiMapQuotaMonthData;
import heran.media.management.platform.mapquota.subdomain.dto.UserData;
import heran.media.management.platform.mapquota.subdomain.enums.ApiMapNameType;
import heran.media.management.platform.mapquota.subdomain.response.ApiMapQuotaDetailListDataResponse;
import heran.media.management.platform.mapquota.subdomain.request.QuotaRequest;
import heran.media.management.platform.mapquota.subdomain.response.ApiMapQuotaListDataResponse;
import heran.media.management.platform.mapquota.subdomain.response.RequestNameResponse;
import heran.media.management.platform.mapquota.subdomain.response.UserDataResponse;
import heran.media.sharelib.domain.db.mapper.main.ApiMapQuotaDetailMapper;
import heran.media.sharelib.domain.db.mapper.main.PoiMapResourceMapper;
import heran.media.sharelib.domain.db.model.main.MapResourceDict;
import heran.media.sharelib.domain.db.model.main.PoiMapResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static heran.media.management.platform.common.utils.xls.XLSWriter.getExportFileName;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ApiMapQuotaDetailService {

    private static int EXPORT_START_PAGE = 1;
    private static int EXPORT_PAGE_LIMIT = 5000;
    private static int EXPORT_LIMIT_LOOP_COUNT = 20;

    private static final String CACHE_KEY = "poiMap";
    private static final String ALL_BASE = "ALL_BASE";

    @Value("${path.export}")
    private String tempDataPath;

    private static final Cache<String, Map<String, String>> POI_MAP_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS)
            .maximumSize(1)
            .build();

    @Resource
    private ApiMapQuotaDetailMapper apiMapQuotaDetailMapper;
    @Resource
    private PoiMapResourceMapper poiMapResourceMapper;
    @Resource
    private CommunalMapper communalMapper;
    @Resource
    private ApiMapQuotaDetailEntityQueryMapper apiMapQuotaDetailEntityQueryMapper;


    public PageResponse<ApiMapQuotaDetailListDataResponse> list(SearchCriteria criteria) {
        Page<Object> page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<ApiMapQuotaDetailListData> searchResult = apiMapQuotaDetailEntityQueryMapper.list(criteria);
        Map<String, String> poiMapResource = getPoiMapResource();
        Map<String, String> mapResourceDict = getMapResourceDict();
        PageResponse<ApiMapQuotaDetailListDataResponse> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult.stream().map(d -> new ApiMapQuotaDetailListDataResponse(d, poiMapResource, mapResourceDict)).collect(Collectors.toList()));
        return pageResponse;
    }


    private Map<String, String> getPoiMapResource() {
        Map<String, String> map = POI_MAP_CACHE.getIfPresent(CACHE_KEY);
        if (map == null) {
            List<PoiMapResource> all = poiMapResourceMapper.getAll();
            map = all.stream()
                    .collect(Collectors.toMap(
                            PoiMapResource::getPoiCode,
                            PoiMapResource::getPoiName,
                            (v1, v2) -> v1
                    ));

            POI_MAP_CACHE.put(CACHE_KEY, map);
        }
        return map;
    }

    private Map<String, String> getMapResourceDict() {
        Map<String, String> map = POI_MAP_CACHE.getIfPresent(ALL_BASE);
        if (map == null) {
            List<MapResourceDict> all = communalMapper.getMapResourceDictDataList(ALL_BASE);
            map = all.stream()
                    .collect(Collectors.toMap(MapResourceDict::getDictCode, MapResourceDict::getDictName));
            POI_MAP_CACHE.put(ALL_BASE, map);
        }
        return map;
    }

    public List<RequestNameResponse> getRequestName() {
        ApiMapNameType[] values = ApiMapNameType.values();
        return Arrays.stream(values).map(RequestNameResponse::new).collect(Collectors.toList());
    }

    public List<UserDataResponse> getUserData(String userName) {
        List<UserData> userData = apiMapQuotaDetailEntityQueryMapper.getUserData(userName);
        return userData.stream().map(UserDataResponse::new).collect(Collectors.toList());
    }


    public File exportData(SearchCriteria criteria, boolean exportAll) throws IOException {
        if (exportAll) {
            criteria.setPage(EXPORT_START_PAGE);
            criteria.setLimit(EXPORT_PAGE_LIMIT);
        }
        int startPage = criteria.getPage();
        int pageLimit = criteria.getLimit();

        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        String fileName = getExportFileName(tempDataPath, "ApiMapQuotaDetailListDataResponse");
        File newFile = new File(fileName);

        Map<String, String> poiMapResource = getPoiMapResource();
        Map<String, String> mapResourceDict = getMapResourceDict();

        PageHelper.startPage(startPage, pageLimit, false);
        List<ApiMapQuotaDetailListData> queryResult = apiMapQuotaDetailEntityQueryMapper.list(criteria);
        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), XlsApiMapQuotaDetailListData.class);
        while (!queryResult.isEmpty()) {
            List<XlsApiMapQuotaDetailListData> appCallHistories = queryResult.stream().map(d -> new XlsApiMapQuotaDetailListData(d, poiMapResource, mapResourceDict)).collect(Collectors.toList());
            workbook = XLSWriter.appendLineFromObject("sheet1", appCallHistories, XlsApiMapQuotaDetailListData.class, workbook);
            if (exportAll) {
                if (startPage * pageLimit >= EXPORT_LIMIT_LOOP_COUNT * EXPORT_PAGE_LIMIT) {
                    log.warn("导出数据超过可导出限制（不符合实时导出场景），如需变更自行更改");
                    break;
                }
                startPage++;
                PageHelper.startPage(startPage, pageLimit, false);
                queryResult = apiMapQuotaDetailEntityQueryMapper.list(criteria);
                continue;
            }
            break;
        }

        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }

    public Integer getQuotaCount(QuotaRequest quotaRequest) {
        return apiMapQuotaDetailEntityQueryMapper.getQuotaCount(quotaRequest);
    }


    public PageResponse<ApiMapQuotaListDataResponse> getApiMapQuotaMonthData(SearchCriteria criteria) {
        Page<Object> page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<ApiMapQuotaMonthData> searchResult = apiMapQuotaDetailEntityQueryMapper.getApiMapQuotaMonthData(criteria);
        PageResponse<ApiMapQuotaListDataResponse> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult.stream().map(ApiMapQuotaListDataResponse::new).collect(Collectors.toList()));
        return pageResponse;
    }


    public File exportApiMapQuotaMonthData(SearchCriteria criteria, boolean exportAll) throws IOException {
        if (exportAll) {
            criteria.setPage(EXPORT_START_PAGE);
            criteria.setLimit(EXPORT_PAGE_LIMIT);
        }
        int startPage = criteria.getPage();
        int pageLimit = criteria.getLimit();

        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        String fileName = getExportFileName(tempDataPath, "ApiMapQuotaMonthData");
        File newFile = new File(fileName);

        PageHelper.startPage(startPage, pageLimit, false);
        List<ApiMapQuotaMonthData> queryResult = apiMapQuotaDetailEntityQueryMapper.getApiMapQuotaMonthData(criteria);
        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), XlsApiMapQuotaMonthData.class);
        while (!queryResult.isEmpty()) {
            List<XlsApiMapQuotaMonthData> appCallHistories = queryResult.stream().map(XlsApiMapQuotaMonthData::new).collect(Collectors.toList());
            workbook = XLSWriter.appendLineFromObject("sheet1", appCallHistories, XlsApiMapQuotaMonthData.class, workbook);
            if (exportAll) {
                if (startPage * pageLimit >= EXPORT_LIMIT_LOOP_COUNT * EXPORT_PAGE_LIMIT) {
                    log.warn("导出数据超过可导出限制（不符合实时导出场景），如需变更自行更改");
                    break;
                }
                startPage++;
                PageHelper.startPage(startPage, pageLimit, false);
                queryResult = apiMapQuotaDetailEntityQueryMapper.getApiMapQuotaMonthData(criteria);
                continue;
            }
            break;
        }

        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }


}
