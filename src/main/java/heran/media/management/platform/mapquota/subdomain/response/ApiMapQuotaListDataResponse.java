package heran.media.management.platform.mapquota.subdomain.response;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.mapquota.subdomain.dto.ApiMapQuotaMonthData;
import heran.media.sharelib.domain.bo.ApiMapBizType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiMapQuotaListDataResponse {
    @ApiModelProperty("请求月份")
    private String statMonth;
    @ApiModelProperty("请求接口")
    private String questName;
    @ApiModelProperty("消费配额")
    private Integer quota;

    public ApiMapQuotaListDataResponse(ApiMapQuotaMonthData data){
        if (StringUtils.isNotEmpty(data.getStatMonth())) {
            this.statMonth = convertWithDate(data.getStatMonth());
        }
        this.questName = ApiMapBizType.valueOf(data.getQuestName()).getName();
        this.quota = data.getQuota();
    }

    public String convertWithDate(String input) {
        try {
            DateTimeFormatter parser = DateTimeFormatter.ofPattern("yyyyMM");
            YearMonth ym = YearMonth.parse(input, parser);
            return ym.getYear() + "年" + ym.getMonthValue() + "月";
        } catch (Exception e) {
            return "非法格式";
        }
    }
}
