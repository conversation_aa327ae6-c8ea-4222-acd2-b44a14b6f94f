package heran.media.management.platform.mapquota.subdomain.response;

import heran.media.management.platform.mapquota.subdomain.enums.ApiMapNameType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RequestNameResponse {
    @ApiModelProperty("接口类型")
    private String code;
    @ApiModelProperty("接口名称")
    private String name;

    public RequestNameResponse(ApiMapNameType apiMapNameType){
        this.code = apiMapNameType.getCode();
        this.name = apiMapNameType.getName();
    }
}
