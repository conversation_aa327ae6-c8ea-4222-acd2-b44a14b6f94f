package heran.media.management.platform.mapquota.subdomain.bo;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.utils.xls.annotation.XlsField;
import heran.media.management.platform.mapquota.subdomain.dto.ApiMapQuotaDetailListData;
import heran.media.sharelib.domain.bo.AllStatisticalType;
import heran.media.sharelib.domain.bo.ApiMapBizType;
import heran.media.sharelib.domain.bo.MainRoleType;
import heran.media.sharelib.domain.bo.MapType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XlsApiMapQuotaDetailListData {
    @XlsField(title = "用户名称", columnIndex = 0)
    public String userName;
    @XlsField(title = "请求接口", columnIndex = 1)
    private String questName;
    @XlsField(title = "调用端", columnIndex = 2)
    private String platform;
    @XlsField(title = "区域类型", columnIndex = 3)
    private String type;
    @XlsField(title = "区域名称", columnIndex = 4)
    private String typeInfoName;
    @XlsField(title = "人口口径", columnIndex = 5)
    private String statisticalType;
    @XlsField(title = "查询时间", columnIndex = 6)
    private String statMonth;
    @XlsField(title = "洞察维度", columnIndex = 7)
    private String dimension;
    @XlsField(title = "人群包名称", columnIndex = 8)
    private String packName;
    @XlsField(title = "网格尺寸", columnIndex = 9)
    private Integer gridSize;
    @XlsField(title = "TA任务ID", columnIndex = 10)
    private String taskId;
    @XlsField(title = "chargeCnt", columnIndex = 11)
    private Integer chargeCnt;
    @XlsField(title = "返回值", columnIndex = 12)
    private Boolean returnedValue;
    @XlsField(title = "消费配额", columnIndex = 13)
    private Integer quota;
    @XlsField(title = "请求时间", columnIndex = 14)
    private Date requestTime;

    public XlsApiMapQuotaDetailListData(ApiMapQuotaDetailListData data, Map<String, String> poiMap, Map<String, String> mapResourceDict) {
        this.userName = data.getUserName();
        this.platform = MainRoleType.valueOf(data.getPlatform()).getDesc();
        this.questName = ApiMapBizType.valueOf(data.getQuestName()).getName();
        MapType mapType = MapType.getByCode(data.getType());
        if (mapType != null) {
            this.type = mapType.getName();
        }
        this.typeInfoName = data.getTypeInfoName();
        if (StringUtils.isNotEmpty(data.getStatisticalType())) {
            AllStatisticalType allStatisticalType = AllStatisticalType.valueOf(data.getStatisticalType());
            this.statisticalType = allStatisticalType.getName();
        }
        if (StringUtils.isNotEmpty(data.getStatMonth())) {
            this.statMonth = convertWithDate(data.getStatMonth());
        }
        String dataDimension = data.getDimension();
        if (StringUtils.isNotEmpty(dataDimension)) {
            List<String> list = Arrays.asList(dataDimension.split(","));
            List<String> collect;
            if (data.getQuestName().equals(ApiMapBizType.QUERY_INDUSTRY_POI.name()) || data.getQuestName().equals(ApiMapBizType.QUERY_BRAND_POI.name())) {
                collect = list.stream().map(poiMap::get).collect(Collectors.toList());
            } else {
                collect = list.stream().map(mapResourceDict::get).collect(Collectors.toList());
            }
            this.dimension = String.join(",", collect);
        }
        this.packName = data.getPackName();
        this.gridSize = data.getGridSize();
        this.taskId = data.getTaskId();
        this.chargeCnt = data.getChargeCnt();
        this.returnedValue = data.getReturnedValue();
        this.quota = data.getQuota();
        this.requestTime = data.getRequestTime();
    }

    public String convertWithDate(String input) {
        try {
            DateTimeFormatter parser = DateTimeFormatter.ofPattern("yyyyMM");
            YearMonth ym = YearMonth.parse(input, parser);
            return ym.getYear() + "年" + ym.getMonthValue() + "月";
        } catch (Exception e) {
            return "非法格式";
        }
    }
}
