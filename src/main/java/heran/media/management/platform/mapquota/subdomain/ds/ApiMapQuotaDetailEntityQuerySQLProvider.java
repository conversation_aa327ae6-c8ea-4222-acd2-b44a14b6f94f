package heran.media.management.platform.mapquota.subdomain.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.mapquota.subdomain.request.QuotaRequest;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 */
public class ApiMapQuotaDetailEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t2.user_name,t1.quest_name,t1.platform,t1.type,t1.type_info_name,t1.statistical_type,t1.stat_month,t1.dimension,t1.pack_id,pack_name,t1.grid_size,t1.task_id,t1.charge_cnt,t1.returned_value,t1.quota,t1.request_time");
            FROM("api_map_quota_detail t1");
            LEFT_OUTER_JOIN("management_sys_account t2 ON t1.created_by_user = t2.id");

            Boolean sort = false;
            String sortValue = null;

            if (criteria.getCriterias() != null && !criteria.getCriterias().isEmpty()) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("t1.id = " + cri.getValue());
                    } else if ("userId".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t2.id = " + cri.getValue());
                    } else if ("questName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.quest_name = '" + cri.getValue() + "'");
                    } else if ("platform".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.platform = '" + cri.getValue() + "'");
                    } else if ("requestTimeSort".equals(cri.getKey()) && cri.getValue() != null) {
                        sort = true;
                        sortValue = "t1.request_time " + cri.getValue();
                    } else if ("quotaSort".equals(cri.getKey()) && cri.getValue() != null) {
                        sort = true;
                        sortValue = "t1.quota " + cri.getValue();
                    } else if ("requestTime".equals(cri.getKey())) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("t1.request_time between '" + cri.getMinValue() + "' AND '" + cri.getMaxValue() + "'");
                        } else if (cri.getMinValue() != null) {
                            WHERE("t1.request_time >= '" + cri.getMinValue() + "'");
                        } else if (cri.getMaxValue() != null) {
                            WHERE("t1.request_time <= '" + cri.getMaxValue() + "'");
                        } else if (cri.getValue() != null) {
                            WHERE("t1.request_time = '" + cri.getValue() + "'");
                        }
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            if (sort) {
                ORDER_BY(sortValue);
            } else {
                ORDER_BY("t1.request_time desc");
            }
        }}.toString();
    }


    public String getQuotaCount(QuotaRequest quotaRequest) {
        return new SQL() {{
            SELECT("SUM(quota)");
            FROM("api_map_quota_detail");
            if (StringUtils.isNotEmpty(quotaRequest.getStartTime()) && StringUtils.isNotEmpty(quotaRequest.getEndTime())) {
                WHERE("request_time between '" + quotaRequest.getStartTime() + "' AND '" + quotaRequest.getEndTime() + "'");
            }
        }}.toString();
    }

    public String getApiMapQuotaMonthData(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("DATE_FORMAT(request_time, '%Y%m') statMonth, quest_name,SUM(quota) AS total_quota");
            FROM("api_map_quota_detail");
            Boolean sort = false;
            String sortValue = null;
            if (criteria.getCriterias() != null && !criteria.getCriterias().isEmpty()) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("id = " + cri.getValue());
                    } else if ("questName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("quest_name = '" + cri.getValue() + "'");
                    } else if ("statMonthSort".equals(cri.getKey()) && cri.getValue() != null) {
                        sort = true;
                        sortValue = cri.getValue();
                    } else if ("requestTime".equals(cri.getKey())) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("request_time between '" + cri.getMinValue() + "' AND '" + cri.getMaxValue() + "'");
                        }
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            GROUP_BY("DATE_FORMAT(request_time, '%Y-%m'),quest_name");
            //排序
            if (sort) {
                ORDER_BY("statMonth " + sortValue);
            }
        }}.toString();
    }

}
