package heran.media.management.platform.mapquota.subdomain.response;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.mapquota.subdomain.dto.ApiMapQuotaDetailListData;
import heran.media.sharelib.domain.bo.AllStatisticalType;
import heran.media.sharelib.domain.bo.ApiMapBizType;
import heran.media.sharelib.domain.bo.MapType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class ApiMapQuotaDetailListDataResponse {
    @ApiModelProperty("用户名称")
    public String userName;
    @ApiModelProperty("平台")
    private String platform;
    @ApiModelProperty("接口名称")
    private String questName;
    @ApiModelProperty("区域类型")
    private String type;
    @ApiModelProperty("区域名称")
    private String typeInfoName;
    @ApiModelProperty("人口口径")
    private String statisticalType;
    @ApiModelProperty("查询时间")
    private String statMonth;
    @ApiModelProperty("洞察维度")
    private String dimension;
    @ApiModelProperty("人群包ID")
    private Integer packId;
    @ApiModelProperty("人群包名称")
    private String packName;
    @ApiModelProperty("网格大小")
    private Integer gridSize;
    @ApiModelProperty("高德任务id")
    private String taskId;
    @ApiModelProperty("非业务id")
    private Integer chargeCnt;
    @ApiModelProperty("返回值")
    private Boolean returnedValue;
    @ApiModelProperty("消费配额")
    private Integer quota;
    @ApiModelProperty("请求时间")
    private Date requestTime;

    public ApiMapQuotaDetailListDataResponse(ApiMapQuotaDetailListData data, Map<String, String> poiMap, Map<String, String> mapResourceDict) {
        this.userName = data.getUserName();
        this.platform = data.getPlatform();
        this.questName = ApiMapBizType.valueOf(data.getQuestName()).getName();
        MapType mapType = MapType.getByCode(data.getType());
        if (mapType != null) {
            this.type = mapType.getName();
        }
        this.typeInfoName = data.getTypeInfoName();
        if (StringUtils.isNotEmpty(data.getStatisticalType())) {
            AllStatisticalType allStatisticalType = AllStatisticalType.valueOf(data.getStatisticalType());
            this.statisticalType = allStatisticalType.getName();
        }
        if (StringUtils.isNotEmpty(data.getStatMonth())){
            this.statMonth = convertWithDate(data.getStatMonth());
        }
        String dataDimension = data.getDimension();
        if (StringUtils.isNotEmpty(dataDimension)) {
            List<String> list = Arrays.asList(dataDimension.split(","));
            List<String> collect;
            if (data.getQuestName().equals(ApiMapBizType.QUERY_INDUSTRY_POI.name()) || data.getQuestName().equals(ApiMapBizType.QUERY_BRAND_POI.name())) {
                collect = list.stream().map(poiMap::get).collect(Collectors.toList());
            } else {
                collect = list.stream().map(mapResourceDict::get).collect(Collectors.toList());
            }
            this.dimension = String.join(",", collect);
        }
        this.packId = data.getPackId();
        this.packName = data.getPackName();
        this.gridSize = data.getGridSize();
        this.taskId = data.getTaskId();
        this.chargeCnt = data.getChargeCnt();
        this.returnedValue = data.getReturnedValue();
        this.quota = data.getQuota();
        this.requestTime = data.getRequestTime();
    }


    public String convertWithDate(String input) {
        try {
            DateTimeFormatter parser = DateTimeFormatter.ofPattern("yyyyMM");
            YearMonth ym = YearMonth.parse(input, parser);
            return ym.getYear() + "年" + ym.getMonthValue() + "月";
        } catch (Exception e) {
            return "非法格式";
        }
    }
}
