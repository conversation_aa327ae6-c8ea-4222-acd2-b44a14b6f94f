package heran.media.management.platform.mapquota.subdomain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiMapQuotaDetailListData {
    public String userName;
    private String platform;
    private String questName;
    private String type;
    private String typeInfoName;
    private String statisticalType;
    private String statMonth;
    private String dimension;
    private Integer packId;
    private String packName;
    private Integer gridSize;
    private String taskId;
    private Integer chargeCnt;
    private Boolean returnedValue;
    private Integer quota;
    private Date requestTime;
}
