package heran.media.management.platform.mapquota.subdomain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum ApiMapNameType {

    RESIDENT_THROUGH_CLEARLY("常驻/活跃人口洞察", "RESIDENT_THROUGH_CLEARLY"),
    PASSENGER_FLOW_THROUGH_CLEARLY("月均客流洞察", "PASSENGER_FLOW_THROUGH_CLEARLY"),
    PASSENGER_FLOW_THROUGH_CLEARLY_CAR("月均车流洞察", "PASSENGER_FLOW_THROUGH_CLEARLY_CAR"),
    MONTHLY_FLOW_HEAT_MAP("月均客流热力图", "MONTHLY_FLOW_HEAT_MAP"),
    RESIDENT_HEAT_MAP("常驻活跃人口热力图", "RESIDENT_HEAT_MAP"),
    QUERY_HEAT("TA热力", "QUERY_HEAT"),
    QUERY_INSIGHT("TA洞察", "QUERY_INSIGHT"),
    QUERY_INDUSTRY_POI("行业POI小类明细 619", "QUERY_INDUSTRY_POI"),
    QUERY_BRAND_POI("品牌POI 625", "QUERY_BRAND_POI");

    private String name;
    private String code;

}
