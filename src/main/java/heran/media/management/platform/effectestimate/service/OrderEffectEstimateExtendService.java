package heran.media.management.platform.effectestimate.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.GsonBuilder;
import heran.media.management.platform.appanalyse.po.ComponentAttrContent;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.common.utils.StringMapUtils;
import heran.media.management.platform.effectestimate.subdomian.bo.OrderEffectEstimateExtendDetailData;
import heran.media.management.platform.effectestimate.subdomian.bo.OrderEffectEstimateExtendListData;
import heran.media.management.platform.effectestimate.subdomian.ds.OrderEffectEstimateExtendMapper;
import heran.media.management.platform.effectestimate.subdomian.ds.OrderEffectEstimateGroupImageExternalEntityQueryMapper;
import heran.media.management.platform.effectestimate.subdomian.dto.*;
import heran.media.sharelib.domain.db.mapper.main.*;
import heran.media.sharelib.domain.db.model.main.*;
import heran.media.sharelib.utils.upload.AliYunOssRedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 全域效果评估扩展版Service
 * @Author: yzj
 * @Date: 2024/1/26 16:14
 * @Version: 1.0
 */
@Slf4j
@Service
public class OrderEffectEstimateExtendService extends BaseCallServiceImpl {
    private static final int EXPORT_START_PAGE = 1;
    private static final int EXPORT_PAGE_LIMIT = 5000;
    private static final int EXPORT_LIMIT_LOOP_COUNT = 20;

    private static final String CUSTOM_IMAGE = "CUSTOM_IMAGE";
    private static final String TITLE = "TITLE";
    private static final String APPRAISAL = "APPRAISAL";

    @Resource
    private OrderEffectEstimateExtendMapper extendMapper;
    @Resource
    private OrderEffectEstimateEcomBehaviorMapper orderEffectEstimateEcomBehaviorMapper;
    @Resource
    private MainAttachementMapper mainAttachementMapper;
    @Resource
    private OrderEffectEstimateKeyIndicatorsGroupMapper groupMapper;
    @Resource
    private OrderEffectEstimateKeyIndicatorsMapper indicatorsMapper;
    @Resource
    private AliYunOssRedisUtils aliYunOssRedisUtils;
    @Resource
    private OrderEffectEstimateGroupImageExternalEntityQueryMapper orderEffectEstimateGroupImageExternalEntityQueryMapper;
    @Resource
    private OrderEffectEstimateGroupImageMapper orderEffectEstimateGroupImageMapper;
    @Resource
    private MediaOrderMapper orderMapper;
    @Resource
    private OrderEffectEstimateBaseManLandMapper orderEffectEstimateBaseManLandMapper;
    @Resource
    private OrderEffectEstimateBaseSampleResultMapper orderEffectEstimateBaseSampleResultMapper;
    @Resource
    private OrderEffectBusReportCollectResultMapper orderEffectBusReportCollectResultMapper;
    @Resource
    private OrderEffectBusProgrammeLineMapper orderEffectBusProgrammeLineMapper;


    public PageResponse<OrderEffectEstimateExtendResponse> list(SearchCriteria criteria) {
        verifySearchCri(criteria, OrderEffectEstimateExtendResponse.class);
        //此分页为entity分页，如果有其他查询自行在前面增加不要导致以下分页失效
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<OrderEffectEstimateExtendListData> searchResult = extendMapper.list(criteria);
        PageResponse<OrderEffectEstimateExtendResponse> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        if (CollectionUtils.isEmpty(searchResult)) {
            return pageResponse;
        }
        pageResponse.setResults(searchResult.stream().map(OrderEffectEstimateExtendResponse::new).collect(Collectors.toList()));
        return pageResponse;
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(Integer identifier, OrderEffectEstimateExtendRequest request) {
        Integer orderId = request.getOrderId();
        deleteDetail(request);
        for (OrderEffectEstimateExtendGroup extendGroup : request.getIndicatorGroupList()) {
            OrderEffectEstimateKeyIndicatorsGroup group = extendGroup.adaptToPo();
            group.setOrderId(orderId);
            setCreatorInfo(identifier, group);
            setUpdaterInfo(identifier, group);
            groupMapper.insertUpdateEntity(group);
            List<OrderEffectEstimateKeyIndicators> indicatorList = extendGroup.getIndicatorList().stream()
                    .map(s -> {
                        OrderEffectEstimateKeyIndicators indicator = s.adaptToPo();
                        indicator.setKeyIndicatorsGroupId(group.getId());
                        setCreatorInfo(identifier, indicator);
                        setUpdaterInfo(identifier, indicator);
                        return indicator;
                    }).collect(Collectors.toList());
            indicatorsMapper.insertUpdateEntities(indicatorList);
        }
        //新增或编辑TA电商行为
        insertUpdateBehavior(identifier, orderId, request.getBehaviorData());
    }

    private void deleteDetail(OrderEffectEstimateExtendRequest request) {
        List<OrderEffectEstimateExtendDetailData> extendDetailDataList = extendMapper.getExtendDetailDataList(request.getOrderId());
        Set<Integer> needDeleteGroupIdList = new HashSet<>();
        List<Integer> needDeleteIndicatorIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(extendDetailDataList)) {
            for (OrderEffectEstimateExtendDetailData detailData : extendDetailDataList) {
                needDeleteGroupIdList.add(detailData.getGroupId());
                needDeleteIndicatorIdList.add(detailData.getIndicatorId());
            }
        }
        Set<Integer> inputGroupIdList = request.getIndicatorGroupList().stream()
                .map(OrderEffectEstimateExtendGroup::getId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        List<Integer> inputIndicatorIdList = request.getIndicatorGroupList().stream()
                .flatMap(s -> s.getIndicatorList().stream())
                .map(ExtendIndicator::getId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        needDeleteGroupIdList.removeAll(inputGroupIdList);
        needDeleteIndicatorIdList.removeAll(inputIndicatorIdList);
        if (CollectionUtils.isNotEmpty(needDeleteGroupIdList)) {
            groupMapper.deleteByIds(new ArrayList<>(needDeleteGroupIdList));
        }
        if (CollectionUtils.isNotEmpty(needDeleteIndicatorIdList)) {
            indicatorsMapper.deleteByIds(needDeleteIndicatorIdList);
        }
    }


    private void insertUpdateBehavior(Integer userKey, Integer orderId, UpDateOrderEffectEstimateBehavior behaviorData) {
        OrderEffectEstimateEcomBehavior behavior = orderEffectEstimateEcomBehaviorMapper.getOrderEffectEstimateEcomBehaviorByOrderId(orderId);
        Integer existImageId = null;
        String existTitle = null;
        if (behavior != null) {
            Map<String, String> componentContentMap = StringMapUtils.stringGetMap(behavior.getCompentContent());
            existImageId = Integer.valueOf(componentContentMap.get(CUSTOM_IMAGE));
            existTitle = String.valueOf(componentContentMap.get(TITLE));
        }
        if (behaviorData != null) {
            Integer inputImageId = behaviorData.getAttachmentBehaviorId();
            if (inputImageId.equals(existImageId) && existTitle.equals(behaviorData.getTitle())) {
                return;
            }
            if (existImageId != null && !inputImageId.equals(existImageId)) {
                mainAttachementMapper.deleteByIdLogically(userKey, Long.valueOf(existImageId));
            }
            ArrayList<ComponentAttrContent> list = new ArrayList<>();
            ComponentAttrContent imageId = new ComponentAttrContent(CUSTOM_IMAGE, String.valueOf(inputImageId));
            ComponentAttrContent title = new ComponentAttrContent(TITLE, behaviorData.getTitle());
            list.add(imageId);
            list.add(title);
            String jsonContent = new GsonBuilder().serializeNulls().create().toJson(list);
            insertUpdateBehavior(behaviorData.getId(), orderId, userKey, jsonContent);
        } else {
            if (existImageId != null) {
                orderEffectEstimateEcomBehaviorMapper.deleteByIdEX(behavior.getId());
                mainAttachementMapper.deleteByIdLogically(userKey, Long.valueOf(existImageId));
            }
        }
    }

    private void insertUpdateBehavior(Integer id, Integer orderId, Integer userKey, String jsonContent) {
        OrderEffectEstimateEcomBehavior behavior = new OrderEffectEstimateEcomBehavior();
        behavior.setId(id);
        behavior.setCompentContent(jsonContent);
        behavior.setOrderId(orderId);
        behavior.setDisplayTitle(CUSTOM_IMAGE);
        behavior.setComponentId(20);
        behavior.setDisplayPriority(1);
        setCreatorInfo(userKey, behavior);
        setUpdaterInfo(userKey, behavior);
        behavior.setCreateTime(new Date());
        behavior.setUpdateTime(new Date());
        orderEffectEstimateEcomBehaviorMapper.insertUpdateEntity(behavior);
    }

    public OrderEffectEstimateExtendDetailResponse getDetail(Integer orderId) {
        List<OrderEffectEstimateExtendDetailData> extendDetailDataList = extendMapper.getExtendDetailDataList(orderId);
        if (CollectionUtils.isEmpty(extendDetailDataList)) {
            return null;
        }
        Map<Integer, List<OrderEffectEstimateExtendDetailData>> groupByGroupId = extendDetailDataList.stream()
                .collect(Collectors.groupingBy(OrderEffectEstimateExtendDetailData::getGroupId));
        List<OrderEffectEstimateExtendGroup> indicatorGroupList = new ArrayList<>();
        for (Map.Entry<Integer, List<OrderEffectEstimateExtendDetailData>> entry : groupByGroupId.entrySet()) {
            OrderEffectEstimateExtendGroup group = new OrderEffectEstimateExtendGroup(entry.getValue().get(0));
            group.setIndicatorList(entry.getValue().stream().map(ExtendIndicator::new).sorted(Comparator.comparing(ExtendIndicator::getPriority)).collect(Collectors.toList()));
            indicatorGroupList.add(group);
        }
        OrderEffectEstimateExtendDetailResponse response = new OrderEffectEstimateExtendDetailResponse();
        response.setOrderId(orderId);
        OrderEffectEstimateEcomBehavior behavior = orderEffectEstimateEcomBehaviorMapper.getOrderEffectEstimateEcomBehaviorByOrderId(orderId);
        if (behavior != null) {
            Map<String, String> componentContentMap = StringMapUtils.stringGetMap(behavior.getCompentContent());
            String title = componentContentMap.get(TITLE);
            Long imageId = Long.parseLong(componentContentMap.get(CUSTOM_IMAGE));
            MainAttachement attachment = mainAttachementMapper.getByIdEX(imageId);
            if (attachment != null) {
                aliYunOssRedisUtils.setTempUrl(attachment);
                response.setBehaviorData(new OrderEffectEstimateBehaviorData(behavior.getId(), attachment.getId().intValue(), attachment.getUrl(), title));
            }
        }
        indicatorGroupList.sort(Comparator.comparing(OrderEffectEstimateExtendGroup::getPriority));
        response.setIndicatorGroupList(indicatorGroupList);
        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer identifier, Integer orderId) {
        //删除指标数据
        List<OrderEffectEstimateKeyIndicatorsGroup> groupList = groupMapper.getByOrderId(orderId);
        if (CollectionUtils.isNotEmpty(groupList)) {
            List<Integer> groupIdList = groupList.stream().map(OrderEffectEstimateKeyIndicatorsGroup::getId).collect(Collectors.toList());
            indicatorsMapper.deleteByGroupIdList(groupIdList);
            groupMapper.deleteByIds(groupIdList);
        }
        //删除属性数据
        deleteOrderEffectEstimateGroupImage(orderId);
        //删除TA电商行为数据
        OrderEffectEstimateEcomBehavior behavior = orderEffectEstimateEcomBehaviorMapper.getOrderEffectEstimateEcomBehaviorByOrderId(orderId);
        if (behavior != null) {
            orderEffectEstimateEcomBehaviorMapper.deleteByIdEX(behavior.getId());
            Map<String, String> componentContentMap = StringMapUtils.stringGetMap(behavior.getCompentContent());
            Long imageId = Long.parseLong(componentContentMap.get(CUSTOM_IMAGE));
            mainAttachementMapper.deleteByIdLogically(identifier, imageId);
        }
        //删除订单下的 kpi 汇总数据
        deleteLand(orderId);
        orderMapper.updateOrderEffectEvaluationToNull(orderId);
    }

    public void deleteLand(Integer orderId) {
        //删除指标的汇总数据
        orderEffectEstimateBaseManLandMapper.deleteByOrderId(orderId, APPRAISAL);
        //删除曝光数据的汇总数据
        orderEffectEstimateBaseSampleResultMapper.deleteByOrderId(orderId);
        //删除公交的汇总数据
        orderEffectBusReportCollectResultMapper.deleteByOrderId(orderId);
        //删除途径行政区线路数据
        orderEffectBusProgrammeLineMapper.deleteByOrderId(orderId);
    }

    /**
     * 删除 基础属性 可选属性
     *
     * @param orderId 订单id
     */
    private void deleteOrderEffectEstimateGroupImage(Integer orderId) {
        //通过关键指标关联的 订单id 去获取下面的基础属性 和 可选属性
        OrderEffectEstimateGroupImage groupImage = orderEffectEstimateGroupImageMapper.getOrderEffectEstimateGroupImageByOrderId(orderId);
        if (groupImage != null) {
            //通过基础属性id 获取到下面的可选属性数据
            List<Integer> externalIds = orderEffectEstimateGroupImageExternalEntityQueryMapper.getIdByGroupImageId(groupImage.getId());
            //删除可选属性
            orderEffectEstimateGroupImageExternalEntityQueryMapper.batchDeleteById(externalIds);
            //删除基本属性
            orderEffectEstimateGroupImageMapper.deleteByIdEX(groupImage.getId());
        }
    }


    public void exportData(SearchCriteria criteria, Boolean exportAll, HttpServletResponse response) {
        verifySearchCri(criteria, OrderEffectEstimateExtendResponse.class);
        List<String> exportAllIndicatorNameList = new ArrayList<>();
        exportAllIndicatorNameList.add("订单ID");
        exportAllIndicatorNameList.add("订单名称");
        if (exportAll) {
            criteria.setPage(EXPORT_START_PAGE);
            criteria.setLimit(EXPORT_PAGE_LIMIT);
            List<OrderEffectEstimateExtendListData> listData = extendMapper.list(criteria);
            if (CollectionUtils.isNotEmpty(listData)) {
                List<Integer> idList = listData.stream().map(OrderEffectEstimateExtendListData::getOrderId).collect(Collectors.toList());
                List<String> exportAllName = extendMapper.getExportAllNameByOrderIdList(idList);
                if (CollectionUtils.isNotEmpty(exportAllName)) {
                    exportAllIndicatorNameList.addAll(exportAllName);
                }
            }
        }

        int startPage = criteria.getPage();
        int pageLimit = criteria.getLimit();
        PageHelper.startPage(startPage, pageLimit, false);

        List<OrderEffectEstimateExtendListData> queryResult = extendMapper.list(criteria);

        // detailDataList
        List<OrderEffectEstimateExtendDetailData> detailDataList = new ArrayList<>();

        while (!queryResult.isEmpty()) {
            List<Integer> orderIdList = queryResult.stream().map(OrderEffectEstimateExtendListData::getOrderId).collect(Collectors.toList());
            List<OrderEffectEstimateExtendDetailData> batchDetailDataList = extendMapper.getExtendDetailDataListBatch(orderIdList);
            detailDataList.addAll(batchDetailDataList);
            if (exportAll) {
                if (startPage * pageLimit >= EXPORT_LIMIT_LOOP_COUNT * EXPORT_PAGE_LIMIT) {
                    log.warn("导出数据超过可导出限制（不符合实时导出场景），如需变更自行更改");
                    break;
                }
                startPage++;
                PageHelper.startPage(startPage, pageLimit, false);
                queryResult = extendMapper.list(criteria);
            } else {
                Set<String> indicatorNameList = detailDataList.stream().map(OrderEffectEstimateExtendDetailData::getKeyIndicatorsName).collect(Collectors.toSet());
                exportAllIndicatorNameList.addAll(indicatorNameList);
                break;
            }
        }
        Map<String, List<OrderEffectEstimateExtendDetailData>> groupByOrderSn = detailDataList.stream().collect(Collectors.groupingBy(OrderEffectEstimateExtendDetailData::getOrderSn));
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("全域效果评估拓展版指标信息", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");
            // 使用 EasyExcel 写入 Excel 数据到响应流
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
            try {
                //写入表头
                excelWriter.write(Collections.singletonList(exportAllIndicatorNameList), EasyExcel.writerSheet("sheet1").build());
                // 分批写入
                // 指定每批次写入的数据量
                int batchSize = 1000;
                List<List<Object>> data = new ArrayList<>();
                List<List<OrderEffectEstimateExtendDetailData>> values = new ArrayList<>(groupByOrderSn.values());
                for (int i = 0; i < groupByOrderSn.values().size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, values.size());
                    List<List<OrderEffectEstimateExtendDetailData>> batchData = values.subList(i, endIndex);
                    prepareData(exportAllIndicatorNameList, data, batchData, groupByOrderSn);
                    excelWriter.write(data, EasyExcel.writerSheet("sheet1").build());
                    // 清空 data，准备下一批数据
                    data.clear();
                }
            } finally {
                // 完成写入操作
                excelWriter.finish();
            }
        } catch (IOException e) {
            log.error("OrderEffectEstimateExtendService exportData fail with error=", e);
        }
    }

    private void prepareData(List<String> headerList, List<List<Object>> data, List<List<OrderEffectEstimateExtendDetailData>> dataList, Map<String, List<OrderEffectEstimateExtendDetailData>> groupByOrderSn) {
        // 遍历数据填充行
        Set<String> orderSnList = dataList.stream()
                .flatMap(List::stream)
                .map(OrderEffectEstimateExtendDetailData::getOrderSn)
                .collect(Collectors.toSet());
        for (String orderSn : orderSnList) {
            List<Object> row = Lists.newArrayList();
            row.add(orderSn);

            List<OrderEffectEstimateExtendDetailData> detailDataList = groupByOrderSn.get(orderSn);
            if (CollectionUtils.isEmpty(detailDataList)) {
                continue;
            }

            // 获取订单名称
            for (OrderEffectEstimateExtendDetailData detailData : detailDataList) {
                row.add(detailData.getOrderName());
                break;
            }

            // 动态添加指标值到行
            for (String indicatorName : headerList) {
                boolean found = false;
                if ("订单ID".equals(indicatorName) || "订单名称".equals(indicatorName)) {
                    continue;
                }
                for (OrderEffectEstimateExtendDetailData indicatorData : detailDataList) {
                    if (indicatorName.equals(indicatorData.getKeyIndicatorsName())) {
                        row.add(indicatorData.getKeyIndicatorsValue());
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    row.add(null);
                }
            }

            data.add(row);
        }
    }

}
