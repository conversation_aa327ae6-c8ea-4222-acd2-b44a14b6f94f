package heran.media.management.platform.effectestimate.subdomian.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.effectestimate.subdomian.bo.OrderEffectEstimateExtendDetailData;
import heran.media.management.platform.effectestimate.subdomian.bo.OrderEffectEstimateExtendListData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @Description: 全与效果评估扩展版Mapper
 * @Author: yzj
 * @Date: 2024/1/26 16:25
 * @Version: 1.0
 */
@Mapper
public interface OrderEffectEstimateExtendMapper {

    @SelectProvider(type = OrderEffectEstimateExtendQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "orderId", column = "id"),
            @Result(property = "orderSn", column = "order_sn"),
            @Result(property = "orderName", column = "order_name"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "orderEffectEvaluation", column = "order_effect_evaluation"),
            @Result(property = "thirdPartyEvaluation", column = "third_party_evaluation"),
            @Result(property = "thirdPartyEvaluationAttachmentId", column = "third_party_evaluation_attachment_id"),
            @Result(property = "attrUpdateTime", column = "attrUpdateTime"),
            @Result(property = "isUploadAttrData", column = "is_upload_attr_data"),
            @Result(property = "isUploadBehaviorData", column = "is_upload_behavior_data"),
            @Result(property = "isUploadKeyIndicator", column = "is_upload_key_indicator"),
            @Result(property = "thirdUpdateTime", column = "thirdUpdateTime"),
            @Result(property = "taUpdateTime", column = "taUpdateTime"),
            @Result(property = "deliveryStartTime", column = "deliveryStartTime"),
            @Result(property = "deliveryEndTime", column = "deliveryEndTime"),
    }
    )
    List<OrderEffectEstimateExtendListData> list(SearchCriteria criteria);

    @Select("SELECT\n" +
            "t1.order_id,\n" +
            "t1.id groupId,\n" +
            "t1.title,\n" +
            "t1.priority groupPriority,\n" +
            "t2.id indicatorId,\n" +
            "t2.priority indicatorPriority,\n" +
            "t2.key_indicators_name,\n" +
            "t2.key_indicators_value,\n" +
            "t2.unit,\n" +
            "t2.remark \n" +
            "FROM\n" +
            "order_effect_estimate_key_indicators_group t1\n" +
            "INNER JOIN order_effect_estimate_key_indicators t2 ON t1.id = t2.key_indicators_group_id \n" +
            "WHERE\n" +
            "t1.order_id =#{orderId}")
    @Results(id = "extendDetailData-mapping",value = {
            @Result(property = "orderId", column = "order_id"),
            @Result(property = "orderName", column = "order_name"),
            @Result(property = "orderSn", column = "order_sn"),
            @Result(property = "groupId", column = "groupId"),
            @Result(property = "title", column = "title"),
            @Result(property = "groupPriority", column = "groupPriority"),
            @Result(property = "indicatorId", column = "indicatorId"),
            @Result(property = "keyIndicatorsName", column = "key_indicators_name"),
            @Result(property = "keyIndicatorsValue", column = "key_indicators_value"),
            @Result(property = "unit", column = "unit"),
            @Result(property = "remark", column = "remark")
    }
    )
    List<OrderEffectEstimateExtendDetailData> getExtendDetailDataList(@Param("orderId")Integer orderId);


    @Select("<script>" +
            "SELECT\n" +
            "t1.order_id,\n" +
            "t1.id groupId,\n" +
            "t1.title,\n" +
            "t1.priority groupPriority,\n" +
            "t2.id indicatorId,\n" +
            "t2.priority indicatorPriority,\n" +
            "t2.key_indicators_name,\n" +
            "t2.key_indicators_value,\n" +
            "t2.unit,\n" +
            "t2.remark, \n" +
            "t3.order_sn, \n" +
            "t3.order_name \n" +
            "FROM\n" +
            "order_effect_estimate_key_indicators_group t1\n" +
            "INNER JOIN order_effect_estimate_key_indicators t2 ON t1.id = t2.key_indicators_group_id \n" +
            "INNER JOIN media_order t3 ON t1.order_id = t3.id \n" +
            "WHERE\n" +
            "t3.is_deleted = 0 AND t1.order_id IN " +
            "<foreach collection='orderIds' item='orderId' open='(' separator=',' close=')'>" +
            "#{orderId}" +
            "</foreach>" +
            "</script>")
    @ResultMap("extendDetailData-mapping")
    List<OrderEffectEstimateExtendDetailData> getExtendDetailDataListBatch(@Param("orderIds") List<Integer> orderIds);

    @Select({
            "<script>",
            "SELECT DISTINCT(t3.key_indicators_name)",
            "FROM media_order t1",
            "INNER JOIN order_effect_estimate_key_indicators_group t2 ON t1.id = t2.order_id",
            "INNER JOIN order_effect_estimate_key_indicators t3 ON t2.id = t3.key_indicators_group_id",
            "WHERE t1.is_deleted = 0 AND t1.id IN",
            "<foreach item='orderId' collection='orderIds' open='(' separator=',' close=')'>",
            "#{orderId}",
            "</foreach>",
            "</script>"
    })
    List<String> getExportAllNameByOrderIdList(@Param("orderIds") List<Integer> orderIds);

}
