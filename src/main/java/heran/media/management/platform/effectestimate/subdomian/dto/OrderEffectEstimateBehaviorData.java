package heran.media.management.platform.effectestimate.subdomian.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderEffectEstimateBehaviorData {
    @ApiModelProperty(value = "主键", required = false, example = "230")
    private Integer id;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty(value = "附件id")
    private Integer attachmentBehaviorId;
    @ApiModelProperty(value = "图片访问地址")
    private String attachmentUrl;

    public OrderEffectEstimateBehaviorData() {

    }

    public OrderEffectEstimateBehaviorData(Integer id, Integer attachmentBehaviorId, String attachmentUrl,String title) {
        this.id = id;
        this.attachmentBehaviorId = attachmentBehaviorId;
        this.attachmentUrl = attachmentUrl;
        this.title = title;
    }
}
