package heran.media.management.platform.effectestimate.subdomian.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.management.platform.effectestimate.subdomian.bo.OrderEffectEstimateExtendListData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class OrderEffectEstimateExtendResponse implements Serializable {

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "订单名称")
    private String orderName;

    @ApiModelProperty(value = "类型列表")
    private List<String> typeList;

    @ApiModelProperty(value = "是否上传第三方效果评估")
    private Boolean isUploadThirdPartyEvaluation = false;

    @ApiModelProperty(value = "是否上传属性数据")
    private Boolean isUploadAttrData;

    @ApiModelProperty(value = "是否上传TA电商行为")
    private Boolean isUploadBehaviorData;

    @ApiModelProperty(value = "是否上传关键指标")
    private Boolean isUploadKeyIndicator;

    @ApiModelProperty(value = "行记录创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("计划投放开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty("计划投放结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryEndTime;



    public OrderEffectEstimateExtendResponse() {
    }

    public OrderEffectEstimateExtendResponse(OrderEffectEstimateExtendListData data) {
        this.orderId = data.getOrderId();
        this.orderSn = data.getOrderSn();
        this.orderName = data.getOrderName();
        this.typeList = new ArrayList<>();
        if (data.getOrderEffectEvaluation()!=null){
            this.typeList.add(data.getOrderEffectEvaluation());
        }
        if (data.getThirdPartyEvaluation()!=null){
            this.typeList.add(data.getThirdPartyEvaluation());
        }
        if (data.getThirdPartyEvaluationAttachmentId()!=null){
            this.isUploadThirdPartyEvaluation = true;
        }
        this.isUploadAttrData = data.getIsUploadAttrData();
        this.isUploadBehaviorData = data.getIsUploadBehaviorData();
        this.isUploadKeyIndicator = data.getIsUploadKeyIndicator();
        this.deliveryStartTime = data.getDeliveryStartTime();
        this.deliveryEndTime = data.getDeliveryEndTime();
        this.createTime = data.getCreateTime();
        this.updateTime = Collections.max(Stream.of(data.getUpdateTime(),data.getAttrUpdateTime(),data.getTaUpdateTime(),data.getThirdUpdateTime())
                .filter(Objects::nonNull).collect(Collectors.toList()));
    }
}
