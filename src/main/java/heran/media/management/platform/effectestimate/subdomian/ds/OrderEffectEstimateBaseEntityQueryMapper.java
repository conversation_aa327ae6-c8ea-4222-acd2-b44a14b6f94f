package heran.media.management.platform.effectestimate.subdomian.ds;

import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.effectestimate.subdomian.dto.ManLandDataListResponse;
import heran.media.management.platform.effectestimate.subdomian.dto.TOrderEffectEstimateBase;
import heran.media.management.platform.effectestimate.subdomian.dto.ValidateOrderId;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBase;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderEffectEstimateBaseEntityQueryMapper {
    /**
     * 列表
     *
     * @param criteria 查询条件
     * @return List<OrderEffectEstimateBase>
     */
    @SelectProvider(type = OrderEffectEstimateBaseEntityQuerySQLProvider.class, method = "select")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "orderId", column = "order_id"),
            @Result(property = "orderSn", column = "order_sn"),
            @Result(property = "orderName", column = "order_name"),
            @Result(property = "pv", column = "pv"),
            @Result(property = "uv", column = "uv"),
            @Result(property = "cpm", column = "cpm"),
            @Result(property = "cpuv", column = "cpuv"),
            @Result(property = "tauv", column = "tauv"),
            @Result(property = "epcc", column = "epcc"),
            @Result(property = "taReach", column = "ta_reach"),
            @Result(property = "isUploadAttrData", column = "is_upload_attr_data"),
            @Result(property = "isUploadBehaviorData", column = "is_upload_behavior_data"),
            @Result(property = "isThirdPartyEvaluationAttachmentId", column = "is_third_party_evaluation_attachment_id"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "orderEffectEvaluation", column = "order_effect_evaluation"),
            @Result(property = "thirdPartyEvaluation", column = "third_party_evaluation"),
            @Result(property = "deliveryStartTime", column = "deliveryStartTime"),
            @Result(property = "deliveryEndTime", column = "deliveryEndTime"),
    }
    )
    List<TOrderEffectEstimateBase> list(SearchCriteria criteria);


    /**
     * 批量新增
     *
     * @param list 要新增的数据
     */
    @Insert({
            "<script>",
            "INSERT INTO order_effect_estimate_base (order_id, pv, uv, cpm, cpuv, tauv, epcc, cdi, ppi, bsi, pagi, iti, pai, ta_reach, creator, updater, created_by_user, updated_by_user, create_time, update_time) VALUES ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.orderId}, #{item.pv}, #{item.uv}, #{item.cpm}, #{item.cpuv}, #{item.tauv}, #{item.epcc}, #{item.cdi}, #{item.ppi}, #{item.bsi}, #{item.pagi}, #{item.iti}, #{item.pai}, #{item.taReach}, #{item.creator}, #{item.updater}, #{item.createdByUser}, #{item.updatedByUser}, #{item.createTime}, #{item.updateTime})",
            "</foreach>",
            "ON DUPLICATE KEY UPDATE",
            "pv = VALUES(pv), uv = VALUES(uv), cpm = VALUES(cpm), cpuv = VALUES(cpuv), tauv = VALUES(tauv), epcc = VALUES(epcc), cdi = VALUES(cdi), ppi = VALUES(ppi), bsi = VALUES(bsi), pagi = VALUES(pagi), iti = VALUES(iti), pai = VALUES(pai), ta_reach = VALUES(ta_reach), updater = VALUES(updater), updated_by_user = VALUES(updated_by_user), update_time = VALUES(update_time)",
            "</script>"
    })
    void batchInsertOrUpdate(List<OrderEffectEstimateBase> list);

    /**
     * 获取到订单数据
     *
     * @param orderScs 订单编号数组
     * @return List<ValidateOrderId>
     */
    @Select({
            "SELECT id, order_sn FROM media_order",
            "WHERE order_sn IN",
            "<foreach collection='orderSns' item='orderSn' open='(' separator=',' close=')'>",
            "#{orderSn}",
            "</foreach>"
    })
    List<ValidateOrderId> validateOrderIds(List<String> orderScs);

    /**
     * 获取全部的订单
     *
     * @return List<ValidateOrderId>
     */
    @Select("SELECT id, order_sn,order_effect_evaluation,third_party_evaluation FROM media_order WHERE is_deleted = false")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "orderSn", column = "order_sn"),
            @Result(property = "orderEffectEvaluation", column = "order_effect_evaluation"),
            @Result(property = "thirdPartyEvaluation", column = "third_party_evaluation")
    }
    )
    List<ValidateOrderId> mediaOrderAll();

    @Update("update media_order set third_party_estimate_update_time = #{updateTime} where id = #{orderId}")
    int updateThirdPartyEstimateUpdateTime(@Param("updateTime") Date updateTime, @Param("orderId") Integer orderId);

    /**
     * 全域效果评估人地数据
     *
     * @param criteria SearchCriteria
     * @return List<ManLandDataListResponse>
     */
    @SelectProvider(type = OrderEffectEstimateBaseEntityQuerySQLProvider.class, method = "getManLandDataList")
    @Results(value = {
            @Result(property = "orderId", column = "order_id"),
            @Result(property = "orderSn", column = "order_sn"),
            @Result(property = "orderName", column = "order_name"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "deliveryStartTime", column = "delivery_start_time"),
            @Result(property = "deliveryEndTime", column = "delivery_end_time"),
    }
    )
    List<ManLandDataListResponse> getManLandDataList(SearchCriteria criteria);
}
