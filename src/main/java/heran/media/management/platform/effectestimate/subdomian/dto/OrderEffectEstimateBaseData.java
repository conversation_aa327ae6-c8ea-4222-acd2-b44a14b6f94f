package heran.media.management.platform.effectestimate.subdomian.dto;

import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class OrderEffectEstimateBaseData {
    @ApiModelProperty(value = "主键")
    private Integer id;
    @ApiModelProperty(value = "整体曝光人次")
    private String pv;
    @ApiModelProperty(value = "整体曝光人数")
    private String uv;
    @ApiModelProperty(value = "千人成本")
    private String cpm;
    @ApiModelProperty(value = "单UV曝光成本")
    private String cpuv;
    @ApiModelProperty(value = "曝光潜客人数")
    private String tauv;
    @ApiModelProperty(value = "曝光潜客浓度")
    private String epcc;
    @ApiModelProperty(value = "潜客城市参透率")
    private String taReach;
    @ApiModelProperty(value = "广告平均触达频次")
    private String af;
    @ApiModelProperty(value = "单人TA成本")
    private String cpta;

    public OrderEffectEstimateBaseData(OrderEffectEstimateBase orderEffectEstimateBase) {
        this.id = orderEffectEstimateBase.getId();
        this.pv = orderEffectEstimateBase.getPv();
        this.uv = orderEffectEstimateBase.getUv();
        this.cpm = orderEffectEstimateBase.getCpm();
        this.cpuv = orderEffectEstimateBase.getCpuv();
        this.tauv = orderEffectEstimateBase.getTauv();
        this.epcc = orderEffectEstimateBase.getEpcc();
        this.taReach = orderEffectEstimateBase.getTaReach();
        this.af = orderEffectEstimateBase.getAf();
        this.cpta = orderEffectEstimateBase.getCpta();
    }

    public OrderEffectEstimateBase adaToPo(String appName) {
        OrderEffectEstimateBase estimateBase = new OrderEffectEstimateBase();
        estimateBase.setId(id);
        estimateBase.setPv(pv);
        estimateBase.setUv(uv);
        estimateBase.setCpm(cpm);
        estimateBase.setCpuv(cpuv);
        estimateBase.setTauv(tauv);
        estimateBase.setEpcc(epcc);
        estimateBase.setTaReach(taReach);
        estimateBase.setAf(af);
        estimateBase.setCpta(cpta);
        estimateBase.setUpdateTime(new Date());
        estimateBase.setUpdater(appName);
        return estimateBase;
    }
}
