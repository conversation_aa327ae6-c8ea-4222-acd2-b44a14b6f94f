package heran.media.management.platform.effectestimate.subdomian.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBase;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.ToString;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TChangebleOrderEffectEstimateBase implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "230")
    private Integer id;

	@ApiModelProperty(value = "订单ID", required = true, example = "168")
    @NotNull(message = "订单ID不能为空")
    private Integer orderId;

	@ApiModelProperty(value = "整体曝光人次", required = true, example = "整体曝光人次")
    @Size(max=32, message = "整体曝光人次长度不能超出32位")
    @NotBlank(message = "整体曝光人次不能为空")
    private String pv;

	@ApiModelProperty(value = "整体曝光人数", required = true, example = "整体曝光人数")
    @Size(max=32, message = "整体曝光人数长度不能超出32位")
    @NotBlank(message = "整体曝光人数不能为空")
    private String uv;

	@ApiModelProperty(value = "千人成本", required = true, example = "千人成本")
    @Size(max=32, message = "千人成本长度不能超出32位")
    @NotBlank(message = "千人成本不能为空")
    private String cpm;

	@ApiModelProperty(value = "单UV曝光成本", required = true, example = "单UV曝光成本")
    @Size(max=32, message = "单UV曝光成本长度不能超出32位")
    @NotBlank(message = "单UV曝光成本不能为空")
    private String cpuv;

	@ApiModelProperty(value = "曝光潜客人数", required = true, example = "曝光潜客人数")
    @Size(max=32, message = "曝光潜客人数长度不能超出32位")
    @NotBlank(message = "曝光潜客人数不能为空")
    private String tauv;

	@ApiModelProperty(value = "曝光潜客浓度", required = true, example = "曝光潜客浓度")
    @Size(max=32, message = "曝光潜客浓度长度不能超出32位")
    @NotBlank(message = "曝光潜客浓度不能为空")
    private String epcc;

	@ApiModelProperty(value = "潜客城市参透率", required = true, example = "潜客城市参透率")
    @Size(max=32, message = "潜客城市参透率长度不能超出32位")
    @NotBlank(message = "潜客城市参透率不能为空")
    private String taReach;

	@ApiModelProperty(value = "认知加深指数", required = false, example = "认知加深指数")
    @Size(max=32, message = "认知加深指数长度不能超出32位")
    private String cdi;

	@ApiModelProperty(value = "购买倾向指数", required = false, example = "购买倾向指数")
    @Size(max=32, message = "购买倾向指数长度不能超出32位")
    private String ppi;

	@ApiModelProperty(value = "品牌搜索指数", required = false, example = "品牌搜索指数")
    @Size(max=32, message = "品牌搜索指数长度不能超出32位")
    private String bsi;

	@ApiModelProperty(value = "人群资产增长指数", required = false, example = "人群资产增长指数")
    @Size(max=32, message = "人群资产增长指数长度不能超出32位")
    private String pagi;

	@ApiModelProperty(value = "兴趣流转指数", required = false, example = "兴趣流转指数")
    @Size(max=32, message = "兴趣流转指数长度不能超出32位")
    private String iti;

	@ApiModelProperty(value = "购买行动指数", required = false, example = "购买行动指数")
    @Size(max=32, message = "购买行动指数长度不能超出32位")
    private String pai;



    public TChangebleOrderEffectEstimateBase() {}

    public OrderEffectEstimateBase adapToPO() {
        OrderEffectEstimateBase orderEffectEstimateBase = new OrderEffectEstimateBase();
        orderEffectEstimateBase.setId(id);
	orderEffectEstimateBase.setOrderId(orderId);
	orderEffectEstimateBase.setPv(pv);
	orderEffectEstimateBase.setUv(uv);
	orderEffectEstimateBase.setCpm(cpm);
	orderEffectEstimateBase.setCpuv(cpuv);
	orderEffectEstimateBase.setTauv(tauv);
	orderEffectEstimateBase.setEpcc(epcc);
	orderEffectEstimateBase.setTaReach(taReach);
	orderEffectEstimateBase.setAf(cdi);
	orderEffectEstimateBase.setCpta(ppi);
	orderEffectEstimateBase.setBsi(bsi);
	orderEffectEstimateBase.setPagi(pagi);
	orderEffectEstimateBase.setIti(iti);
	orderEffectEstimateBase.setPai(pai);

        return orderEffectEstimateBase;
    }
}
