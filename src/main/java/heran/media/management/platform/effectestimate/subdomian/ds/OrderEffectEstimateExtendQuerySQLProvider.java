package heran.media.management.platform.effectestimate.subdomian.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class OrderEffectEstimateExtendQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t1.id,\n" +
                    "t1.order_sn,\n" +
                    "t1.order_name,\n" +
                    "t1.order_effect_evaluation,\n" +
                    "t1.third_party_evaluation,\n" +
                    "t1.third_party_evaluation_attachment_id,\n" +
                    "t1.third_party_estimate_create_time create_time,\n" +
                    "t1.third_party_estimate_update_time thirdUpdateTime,\n" +
                    "IFNULL(MAX( t3.update_time ),t1.third_party_estimate_create_time) update_time, \n" +
                    "CASE WHEN t4.order_id IS NOT NULL THEN TRUE ELSE FALSE END AS is_upload_attr_data," +
                    "CASE WHEN t5.order_id IS NOT NULL THEN TRUE ELSE FALSE END AS is_upload_behavior_data," +
                    "CASE WHEN t3.id IS NOT NULL THEN TRUE ELSE FALSE END AS is_upload_key_indicator," +
                    "t4.update_time attrUpdateTime,\n" +
                    "t6.delivery_start_time as deliveryStartTime,\n" +
                    "t6.delivery_end_time as deliveryEndTime,\n" +
                    "t5.update_time taUpdateTime\n");
            FROM("media_order t1");
            LEFT_OUTER_JOIN("order_effect_estimate_key_indicators_group t2 ON t1.id = t2.order_id");
            LEFT_OUTER_JOIN("order_effect_estimate_key_indicators t3 ON t2.id = t3.key_indicators_group_id");
            LEFT_OUTER_JOIN("order_effect_estimate_group_image t4 ON t1.id = t4.order_id");
            LEFT_OUTER_JOIN("order_effect_estimate_ecom_behavior t5 ON t1.id = t5.order_id");
            LEFT_OUTER_JOIN("media_placement_base_sales_plan t6 ON t6.id = t1.base_plan_id");
            WHERE("t1.is_deleted = 0 AND t1.order_effect_evaluation = '投后全域效果评估-拓展版'");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("orderId".equals(cri.getKey())) {
                        WHERE("t1.id = " + cri.getValue());
                    } else if ("orderSn".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.order_sn like '%" + cri.getValue() + "%'");
                    } else if ("orderName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.order_name like '%" + cri.getValue() + "%'");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            GROUP_BY("t1.id");
            ORDER_BY("update_time desc");
        }}.toString();
    }
}
