package heran.media.management.platform.effectestimate.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import heran.media.management.platform.appanalyse.po.ComponentAttrContent;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.service.BaseCallServiceImpl;
import heran.media.management.platform.common.utils.ExceptionMessageUtils;
import heran.media.management.platform.common.utils.StringMapUtils;
import heran.media.management.platform.common.utils.xls.XLSReader;
import heran.media.management.platform.common.utils.xls.XLSWriter;
import heran.media.management.platform.effectestimate.subdomian.bo.XlsOrderEffectEstimateBase;
import heran.media.management.platform.effectestimate.subdomian.ds.OrderEffectEstimateBaseEntityQueryMapper;
import heran.media.management.platform.effectestimate.subdomian.ds.OrderEffectEstimateGroupImageExternalEntityQueryMapper;
import heran.media.management.platform.effectestimate.subdomian.dto.*;
import heran.media.sharelib.common.ObjectErrorInfo;
import heran.media.sharelib.domain.db.mapper.main.*;
import heran.media.sharelib.domain.db.model.main.*;
import heran.media.sharelib.utils.upload.AliYunOssRedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static heran.media.management.platform.common.utils.xls.XLSWriter.getExportFileName;

@Slf4j
@Service
@RefreshScope
public class OrderEffectEstimateBaseService extends BaseCallServiceImpl {
    private static int EXPORT_START_PAGE = 1;
    private static int EXPORT_PAGE_LIMIT = 5000;
    private static int EXPORT_LIMIT_LOOP_COUNT = 20;

    private static final String CUSTOM_IMAGE = "CUSTOM_IMAGE";
    private static final String TITLE = "TITLE";


    @Resource
    private OrderEffectEstimateBaseEntityQueryMapper orderEffectEstimateBaseEntityQueryMapper;
    @Resource
    private OrderEffectEstimateGroupImageExternalEntityQueryMapper orderEffectEstimateGroupImageExternalEntityQueryMapper;
    @Resource
    private OrderEffectEstimateBaseMapper orderEffectEstimateBaseMapper;
    @Resource
    private OrderEffectEstimateEcomBehaviorMapper orderEffectEstimateEcomBehaviorMapper;
    @Resource
    private OrderEffectEstimateGroupImageMapper orderEffectEstimateGroupImageMapper;
    @Resource
    private MediaOrderMapper mediaOrderMapper;
    @Resource
    private AliYunOssRedisUtils aliYunOssRedisUtils;
    @Resource
    private MainAttachementMapper mainAttachementMapper;
    @Resource
    private OrderEffectEstimateKeyIndicatorsGroupMapper orderEffectEstimateKeyIndicatorsGroupMapper;
    @Resource
    private OrderEffectEstimateKeyIndicatorsMapper orderEffectEstimateKeyIndicatorsMapper;


    @Value("${path.upload}")
    private String userFilePath;

    @Value("${path.export}")
    private String tempDataPath;

    @Value("${spring.application.name}")
    protected String appName;


    public PageResponse<TOrderEffectEstimateBase> list(SearchCriteria criteria) {
        verifySearchCri(criteria, TOrderEffectEstimateBase.class);
        //此分页为entity分页，如果有其他查询自行在前面增加不要导致以下分页失效
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<TOrderEffectEstimateBase> searchResult = orderEffectEstimateBaseEntityQueryMapper.list(criteria);
        PageResponse<TOrderEffectEstimateBase> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult);
        return pageResponse;
    }


    public TOrderEffectEstimateBase getById(Integer id) {
        TOrderEffectEstimateBase torderEffectEstimateBase = null;
        OrderEffectEstimateBase orderEffectEstimateBase = orderEffectEstimateBaseMapper.getByIdEX(id);
        if (orderEffectEstimateBase != null) {
            torderEffectEstimateBase = new TOrderEffectEstimateBase(orderEffectEstimateBase);
            MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(orderEffectEstimateBase.getOrderId());
            if (mediaOrder != null) {
                torderEffectEstimateBase.setOrderId(mediaOrder.getId());
                torderEffectEstimateBase.setOrderSn(mediaOrder.getOrderSn());
                torderEffectEstimateBase.setOrderName(mediaOrder.getOrderName());
            }
        }
        return torderEffectEstimateBase;
    }

    /**
     * 通过订单id 获取到修改时回显数据
     *
     * @param orderId 订单id
     * @return OrderEffectEstimateBaseDetail
     */
    public OrderEffectEstimateBaseDetail getOrderEffectEstimateBaseByOrderId(Integer orderId) {
        //关键指标
        OrderEffectEstimateBase orderEffectEstimateBase = orderEffectEstimateBaseMapper.getOrderEffectEstimateBaseByOrderId(orderId);
        if (orderEffectEstimateBase == null) {
            return null;
        }
        //构建返回对象
        OrderEffectEstimateBaseDetail detail = new OrderEffectEstimateBaseDetail();
        detail.setOrderId(orderId);
        //封装 关键指标返回数据
        OrderEffectEstimateBaseData orderEffectEstimateBaseData = new OrderEffectEstimateBaseData(orderEffectEstimateBase);
        detail.setEstimateBase(orderEffectEstimateBaseData);
        //TA电商行为
        OrderEffectEstimateEcomBehavior behavior = orderEffectEstimateEcomBehaviorMapper.getOrderEffectEstimateEcomBehaviorByOrderId(orderId);
        if (behavior == null) {
            return detail;
        }
        //封装 TA电商行为 返回数据
        OrderEffectEstimateBehaviorData behaviorData = new OrderEffectEstimateBehaviorData();
        behaviorData.setId(behavior.getId());
        //解析组件文件内容（暂时定死的 只有附件组件 直接获取）
        Map<String, String> componentContentMap = StringMapUtils.stringGetMap(behavior.getCompentContent());
        if (componentContentMap.isEmpty()) {
            return detail;
        }
        buildOrderEffectEstimateBehaviorData(behaviorData, componentContentMap);
        //设置返回对象
        detail.setBehaviorData(behaviorData);
        return detail;
    }

    public File exportData(SearchCriteria criteria, boolean exportAll) throws IOException, IllegalAccessException {
        if (exportAll) {
            criteria.setPage(EXPORT_START_PAGE);
            criteria.setLimit(EXPORT_PAGE_LIMIT);
        }
        int startPage = criteria.getPage();
        int pageLimit = criteria.getLimit();

        File dirs = new File(tempDataPath);
        if (!dirs.exists()) {
            dirs.mkdirs();
        }
        verifySearchCri(criteria, TOrderEffectEstimateBase.class);
        String fileName = getExportFileName(tempDataPath, "OrderEffectEstimateBaseData");
        File newFile = new File(fileName);

        PageHelper.startPage(startPage, pageLimit, false);
        List<TOrderEffectEstimateBase> queryResult = orderEffectEstimateBaseEntityQueryMapper.list(criteria);
        Workbook workbook = XLSWriter.appendLineFromObject(newFile, "sheet1", new LinkedList(), XlsOrderEffectEstimateBase.class);
        while (!queryResult.isEmpty()) {
            List<XlsOrderEffectEstimateBase> appCallHistories = queryResult.stream().map(XlsOrderEffectEstimateBase::new).collect(Collectors.toList());
            workbook = XLSWriter.appendLineFromObject("sheet1", appCallHistories, XlsOrderEffectEstimateBase.class, workbook);
            if (exportAll) {
                if (startPage * pageLimit >= EXPORT_LIMIT_LOOP_COUNT * EXPORT_PAGE_LIMIT) {
                    log.warn("导出数据超过可导出限制（不符合实时导出场景），如需变更自行更改");
                    break;
                }
                startPage++;
                PageHelper.startPage(startPage, pageLimit, false);
                queryResult = orderEffectEstimateBaseEntityQueryMapper.list(criteria);
                continue;
            }
            break;
        }
        FileOutputStream outputStream = new FileOutputStream(newFile);
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        return newFile;
    }

    public List<File> importData(Integer userKey, MultipartFile[] files) throws Exception {
        List<File> fileList = new ArrayList<>(files.length);
        for (MultipartFile importedFile : files) {
            String postfix = "";
            if (importedFile.getOriginalFilename().toUpperCase().endsWith("XLSX")) {
                postfix = ".xlsx";
            } else {
                postfix = ".xls";
            }

            File folder = new File(userFilePath);
            if (!folder.exists()) {
                folder.mkdirs();
            }
            File uploadedFile = new File(folder.getAbsolutePath() + "/" + UUID.randomUUID() + postfix);
            importedFile.transferTo(uploadedFile);
            List<XlsOrderEffectEstimateBase> xlsOrderEffectEstimateBases = XLSReader.readLineAsObject(uploadedFile, "sheet1", 1,
                    XlsOrderEffectEstimateBase.class);
            if (xlsOrderEffectEstimateBases.size() == 0) {
                continue;
            }

            List<OrderEffectEstimateBase> xlsDatas =
                    xlsOrderEffectEstimateBases.stream().map(p -> p.adapToPO(userKey)).collect(Collectors.toList());
            Integer failCount = 0, sucCount = 0;
            List<ObjectErrorInfo> failList = new ArrayList<>();
            for (int i = 0; i < xlsDatas.size(); i++) {
                OrderEffectEstimateBase orderEffectEstimateBase = xlsDatas.get(i);
                try {
                    setCreatorInfo(userKey, orderEffectEstimateBase);
                    setUpdaterInfo(userKey, orderEffectEstimateBase);
                    orderEffectEstimateBaseMapper.insertUpdateEntity(orderEffectEstimateBase);
                    sucCount++;
                } catch (Exception e) {
                    log.error("Error for user import with data=【{}】 and exception", new Gson().toJson(orderEffectEstimateBase), e);
                    failCount++;
                    failList.add(
                            new ObjectErrorInfo(xlsOrderEffectEstimateBases.get(i), ExceptionMessageUtils.getExceptionErrorMessage(e)));
                }
            }
            if (!failList.isEmpty()) {
                fileList.add(XLSWriter.writeFailExcel(uploadedFile,
                        new File(folder.getAbsolutePath() + "/" + UUID.randomUUID() + postfix), failList));
            }
            log.info("Success count={}, Fail count={}", sucCount, failCount);
        }
        return fileList;
    }

    public void create(Integer userKey, OrderEffectEstimateBase orderEffectEstimateBase) {
        orderEffectEstimateBase.setId(null);
        setCreatorInfo(userKey, orderEffectEstimateBase);
        setUpdaterInfo(userKey, orderEffectEstimateBase);
        orderEffectEstimateBaseMapper.insert(orderEffectEstimateBase);
    }


    public void modify(Integer userKey, TChangebleOrderEffectEstimateBase torderEffectEstimateBase) {
        OrderEffectEstimateBase orderEffectEstimateBase = torderEffectEstimateBase.adapToPO();
        orderEffectEstimateBase.setUpdatedByUser(userKey);
        setUpdaterInfo(userKey, orderEffectEstimateBase);
        orderEffectEstimateBaseMapper.updateByEntity(orderEffectEstimateBase);
    }

    /**
     * 全域效果评估修改数据
     *
     * @param updateData 要修改的数据
     */
    public void modifyOrderEffectEstimateBase(Integer userKey, UpDateOrderEffectEstimateBase updateData) {
        if (updateData == null) {
            return;
        }
        //修改关键指标
        OrderEffectEstimateBase base = updateEstimateBase(updateData.getOrderId(), updateData.getEstimateBase());
        //编辑TA电商行为
        updateBehavior(userKey, updateData.getOrderId(), updateData.getBehaviorData(), base);
    }

    /**
     * 修改关键指标
     *
     * @param orderId      订单id
     * @param estimateBase 数据
     */
    private OrderEffectEstimateBase updateEstimateBase(Integer orderId, OrderEffectEstimateBaseData estimateBase) {
        if (estimateBase != null) {
            OrderEffectEstimateBase orderEffectEstimateBase = estimateBase.adaToPo(appName);
            orderEffectEstimateBase.setOrderId(orderId);
            orderEffectEstimateBaseMapper.updateByEntity(orderEffectEstimateBase);
            return orderEffectEstimateBase;
        }
        return null;
    }

    /**
     * 修改 or 添加 TA电商行为
     *
     * @param userKey      用户id
     * @param orderId      订单id
     * @param behaviorData 电商行为数据
     */
    public void updateBehavior(Integer userKey, Integer orderId, UpDateOrderEffectEstimateBehavior behaviorData, OrderEffectEstimateBase base) {
        OrderEffectEstimateEcomBehavior behavior = orderEffectEstimateEcomBehaviorMapper.getOrderEffectEstimateEcomBehaviorByOrderId(orderId);
        if (behaviorData == null || behaviorData.getAttachmentBehaviorId() == null) {
            handleNullBehaviorData(userKey, orderId, base, behavior);
            return;
        }
        ArrayList<ComponentAttrContent> list = new ArrayList<>();
        ComponentAttrContent componentAttrContent = new ComponentAttrContent(CUSTOM_IMAGE, String.valueOf(behaviorData.getAttachmentBehaviorId()));
        ComponentAttrContent title = new ComponentAttrContent(TITLE, String.valueOf(behaviorData.getTitle()));
        list.add(componentAttrContent);
        list.add(title);
        String jsonContent = new GsonBuilder().serializeNulls().create().toJson(list);
        if (behavior == null) {
            handleNullBehavior(orderId, userKey, jsonContent, base);
            return;
        }
        handleNonNullOriginalBehavior(behaviorData.getAttachmentBehaviorId(), jsonContent, behavior, userKey, base);
    }

    /**
     * 校验设置的图片是否为 null
     *
     * @param orderId  订单id
     * @param base     OrderEffectEstimateBase
     * @param behavior OrderEffectEstimateEcomBehavior
     */
    private void handleNullBehaviorData(Integer userKey, Integer orderId, OrderEffectEstimateBase base, OrderEffectEstimateEcomBehavior behavior) {
        if (behavior != null) {
            updateOrderEffectEstimateBaseUpdateTime(base);
            this.deleteOrderEffectEstimateEcomBehavior(userKey, orderId);
        }
    }

    /**
     * 处理behavior为null的情况
     *
     * @param orderId     订单id
     * @param userKey     用户id
     * @param jsonContent jsonContent
     * @param base        OrderEffectEstimateBase
     */
    private void handleNullBehavior(Integer orderId, Integer userKey, String jsonContent, OrderEffectEstimateBase base) {
        insertNewBehavior(orderId, userKey, jsonContent);
        updateOrderEffectEstimateBaseUpdateTime(base);
    }

    /**
     * 处理有没有修改图片
     *
     * @param attachmentBehaviorId 修改的图片id
     * @param jsonContent          修改图片的json
     * @param behavior             要修改的数据
     * @param userKey              用户id
     * @param base                 OrderEffectEstimateBase
     */
    private void handleNonNullOriginalBehavior(Integer attachmentBehaviorId, String jsonContent, OrderEffectEstimateEcomBehavior behavior, Integer userKey, OrderEffectEstimateBase base) {
        if (!jsonContent.equals(behavior.getCompentContent())) {
            Map<String, String> componentContentMap = StringMapUtils.stringGetMap(behavior.getCompentContent());
            Integer imageId = Integer.valueOf(componentContentMap.get(CUSTOM_IMAGE));
            if (!imageId.equals(attachmentBehaviorId)) {
                mainAttachementMapper.deleteByIdLogically(userKey, Long.valueOf(imageId));
            }
            behavior.setCompentContent(jsonContent);
            orderEffectEstimateEcomBehaviorMapper.updateByEntity(behavior);
            updateOrderEffectEstimateBaseUpdateTime(base);
        }
    }

    /**
     * 修改 修改时间
     *
     * @param base OrderEffectEstimateBase
     */
    private void updateOrderEffectEstimateBaseUpdateTime(OrderEffectEstimateBase base) {
        //要修改关键属性的修改时间
        if (base != null) {
            base.setUpdateTime(new Date());
            orderEffectEstimateBaseMapper.updateByEntity(base);
        }
    }

    /**
     * 添加 TA电商行为
     *
     * @param orderId     订单id
     * @param userKey     用户id
     * @param jsonContent 内容
     */
    private void insertNewBehavior(Integer orderId, Integer userKey, String jsonContent) {
        OrderEffectEstimateEcomBehavior behavior = new OrderEffectEstimateEcomBehavior();
        behavior.setCompentContent(jsonContent);
        behavior.setOrderId(orderId);
        behavior.setDisplayTitle(CUSTOM_IMAGE);
        behavior.setComponentId(20);
        behavior.setDisplayPriority(1);
        setCreatorInfo(userKey, behavior);
        setUpdaterInfo(userKey, behavior);
        behavior.setCreateTime(new Date());
        behavior.setUpdateTime(new Date());
        orderEffectEstimateEcomBehaviorMapper.insertUpdateEntity(behavior);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer userKey, Integer orderId) {
        //删除基本数据可选数据
        deleteOrderEffectEstimateGroupImage(orderId);
        //删除TA电商行为数据
        deleteOrderEffectEstimateEcomBehavior(userKey, orderId);
        //删除拓展数据
        deleteIndicator(orderId);
        //将全域评估 与 第三方评估 设置成null
        MediaOrder mediaOrder = mediaOrderMapper.getByIdFilterIsDeleted(orderId);
        if (mediaOrder != null) {
            mediaOrder.setOrderEffectEvaluation(null);
            mediaOrder.setThirdPartyEvaluation(null);
            mediaOrderMapper.updateByEntity(mediaOrder);
        }
        //删除关键指标
        OrderEffectEstimateBase effectEstimateBase = orderEffectEstimateBaseMapper.getOrderEffectEstimateBaseByOrderId(orderId);
        if (effectEstimateBase == null) {
            return;
        }
        orderEffectEstimateBaseMapper.deleteByIdEX(effectEstimateBase.getId());
    }

    /**
     * 删除扩展数据
     *
     * @param orderId 订单id
     */
    private void deleteIndicator(Integer orderId) {
        List<Integer> groupIds = orderEffectEstimateKeyIndicatorsGroupMapper.getIndicatorsGroupIdByOrderId(orderId);
        if (groupIds.isEmpty()) {
            return;
        }
        for (Integer groupId : groupIds) {
            orderEffectEstimateKeyIndicatorsMapper.deleteByKeyIndicatorsGroupId(groupId);
            orderEffectEstimateKeyIndicatorsGroupMapper.deleteByIdEX(groupId);
        }
    }

    /**
     * 删除 基础属性 可选属性
     *
     * @param orderId 订单id
     */
    private void deleteOrderEffectEstimateGroupImage(Integer orderId) {
        //通过关键指标关联的 订单id 去获取下面的基础属性 和 可选属性
        OrderEffectEstimateGroupImage groupImage = orderEffectEstimateGroupImageMapper.getOrderEffectEstimateGroupImageByOrderId(orderId);
        if (groupImage != null) {
            //通过基础属性id 获取到下面的可选属性数据
            List<Integer> externalIds = orderEffectEstimateGroupImageExternalEntityQueryMapper.getIdByGroupImageId(groupImage.getId());
            //删除可选属性
            if (!externalIds.isEmpty()) {
                orderEffectEstimateGroupImageExternalEntityQueryMapper.batchDeleteById(externalIds);
            }
            //删除基本属性
            orderEffectEstimateGroupImageMapper.deleteByIdEX(groupImage.getId());
        }
    }

    /**
     * 删除TA电商行为数据
     *
     * @param userKey
     * @param orderId 订单id
     */
    private void deleteOrderEffectEstimateEcomBehavior(Integer userKey, Integer orderId) {
        OrderEffectEstimateEcomBehavior behavior = orderEffectEstimateEcomBehaviorMapper.getOrderEffectEstimateEcomBehaviorByOrderId(orderId);
        if (behavior != null) {
            //要删除图片附件
            if (StringUtils.isNotEmpty(behavior.getCompentContent())) {
                Map<String, String> componentContentMap = StringMapUtils.stringGetMap(behavior.getCompentContent());
                Long imageId = Long.valueOf(componentContentMap.get(CUSTOM_IMAGE));
                mainAttachementMapper.deleteByIdLogically(userKey, imageId);
            }
            orderEffectEstimateEcomBehaviorMapper.deleteByIdEX(behavior.getId());
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void batchInsertOrUpdate(List<OrderEffectEstimateBase> list) {
        if (list.isEmpty()) {
            return;
        }
        orderEffectEstimateBaseEntityQueryMapper.batchInsertOrUpdate(list);
    }

    /**
     * 设置第三方评估报告 pdf
     *
     * @param data 上传数据
     */
    public void setThirdPartyBriefAttachment(SetThirdPartyBriefAttachmentData data) {
        MediaOrder mediaOrder = mediaOrderMapper.getByIdEX(data.getOrderId());
        //判断当前订单是否存在 该订单是否选择了第三方评估
        if (mediaOrder == null || StringUtils.isEmpty(mediaOrder.getThirdPartyEvaluation())) {
            return;
        }
        mediaOrder.setThirdPartyEvaluationAttachmentId(data.getAttachmentId());
        mediaOrderMapper.updateByEntity(mediaOrder);
        orderEffectEstimateBaseEntityQueryMapper.updateThirdPartyEstimateUpdateTime(new Date(), data.getOrderId());
    }

    /**
     * 人地效果评估列表
     *
     * @param criteria SearchCriteria
     * @return PageResponse<ManLandDataListResponse>
     */
    public PageResponse<ManLandDataListResponse> getManLandDataList(SearchCriteria criteria) {
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<ManLandDataListResponse> searchResult = orderEffectEstimateBaseEntityQueryMapper.getManLandDataList(criteria);
        PageResponse<ManLandDataListResponse> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(searchResult);
        return pageResponse;
    }

    /**
     * 人地数据天假TA电商数据
     *
     * @param userKey 用户id
     * @param request 请求参数
     */
    public void saveOrderEffectEstimateBehavior(Integer userKey, SaveOrderEffectEstimateBehaviorRequest request) {
        OrderEffectEstimateEcomBehavior behavior = orderEffectEstimateEcomBehaviorMapper.getOrderEffectEstimateEcomBehaviorByOrderId(request.getOrderId());

        List<ComponentAttrContent> list = Arrays.asList(new ComponentAttrContent(CUSTOM_IMAGE, String.valueOf(request.getAttachmentBehaviorId())), new ComponentAttrContent(TITLE, String.valueOf(request.getTitle())));
        String jsonContent = new GsonBuilder().serializeNulls().create().toJson(list);

        if (behavior != null) {
            if (!behavior.getCompentContent().equals(jsonContent)) {
                Map<String, String> componentContentMap = StringMapUtils.stringGetMap(behavior.getCompentContent());
                Integer imageId = Integer.valueOf(componentContentMap.get(CUSTOM_IMAGE));
                if (!imageId.equals(request.getAttachmentBehaviorId())) {
                    mainAttachementMapper.deleteByIdLogically(userKey, Long.valueOf(imageId));
                }
                behavior.setCompentContent(jsonContent);
                orderEffectEstimateEcomBehaviorMapper.updateByEntity(behavior);
            }
        } else {
            insertNewBehavior(request.getOrderId(), userKey, jsonContent);
        }
    }

    /**
     * 回显TA电商行为
     *
     * @param orderId 订单id
     * @return OrderEffectEstimateBehaviorData
     */
    public OrderEffectEstimateBehaviorData getOrderEffectEstimateBehaviorData(Integer orderId) {
        //封装 TA电商行为 返回数据
        OrderEffectEstimateBehaviorData behaviorData = new OrderEffectEstimateBehaviorData();
        //TA电商行为
        OrderEffectEstimateEcomBehavior behavior = orderEffectEstimateEcomBehaviorMapper.getOrderEffectEstimateEcomBehaviorByOrderId(orderId);
        if (behavior == null) {
            return behaviorData;
        }
        behaviorData.setId(behavior.getId());
        //解析组件文件内容（暂时定死的 只有附件组件 直接获取）
        if (StringUtils.isNotEmpty(behavior.getCompentContent())) {
            Map<String, String> componentContentMap = StringMapUtils.stringGetMap(behavior.getCompentContent());
            buildOrderEffectEstimateBehaviorData(behaviorData, componentContentMap);
        }
        return behaviorData;
    }

    private void buildOrderEffectEstimateBehaviorData(OrderEffectEstimateBehaviorData behaviorData, Map<String, String> componentContentMap) {
        if (componentContentMap.isEmpty()) {
            return;
        }
        //附件id
        Long imageId = Long.parseLong(componentContentMap.get(CUSTOM_IMAGE));
        String title = componentContentMap.get(TITLE);
        behaviorData.setAttachmentBehaviorId(imageId.intValue());
        behaviorData.setTitle(title);
        //获取附件url
        MainAttachement attachment = mainAttachementMapper.getByIdEX(imageId);
        if (attachment != null) {
            aliYunOssRedisUtils.setTempUrl(attachment);
            behaviorData.setAttachmentUrl(attachment.getUrl());
        }
    }


}
