package heran.media.management.platform.effectestimate.subdomian.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import heran.media.sharelib.domain.db.model.main.OrderEffectEstimateBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class TOrderEffectEstimateBase implements Serializable {

    @ApiModelProperty(value = "主键", required = false, example = "91")
    private Integer id;

    @ApiModelProperty(value = "订单id", required = true, example = "225")
    @NotNull(message = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "订单编号", required = true, example = "225")
    @NotNull(message = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "订单名称", required = true, example = "225")
    @NotNull(message = "订单名称")
    private String orderName;

    @ApiModelProperty(value = "整体曝光人次", required = true, example = "整体曝光人次")
    @Size(max = 32, message = "整体曝光人次长度不能超出32位")
    @NotBlank(message = "整体曝光人次不能为空")
    private String pv;

    @ApiModelProperty(value = "整体曝光人数", required = true, example = "整体曝光人数")
    @Size(max = 32, message = "整体曝光人数长度不能超出32位")
    @NotBlank(message = "整体曝光人数不能为空")
    private String uv;

    @ApiModelProperty(value = "千人成本", required = true, example = "千人成本")
    @Size(max = 32, message = "千人成本长度不能超出32位")
    @NotBlank(message = "千人成本不能为空")
    private String cpm;

    @ApiModelProperty(value = "单UV曝光成本", required = true, example = "单UV曝光成本")
    @Size(max = 32, message = "单UV曝光成本长度不能超出32位")
    @NotBlank(message = "单UV曝光成本不能为空")
    private String cpuv;

    @ApiModelProperty(value = "曝光潜客人数", required = true, example = "曝光潜客人数")
    @Size(max = 32, message = "曝光潜客人数长度不能超出32位")
    @NotBlank(message = "曝光潜客人数不能为空")
    private String tauv;

    @ApiModelProperty(value = "曝光潜客浓度", required = true, example = "曝光潜客浓度")
    @Size(max = 32, message = "曝光潜客浓度长度不能超出32位")
    @NotBlank(message = "曝光潜客浓度不能为空")
    private String epcc;

    @ApiModelProperty(value = "潜客城市参透率", required = true, example = "潜客城市参透率")
    @Size(max = 32, message = "潜客城市参透率长度不能超出32位")
    @NotBlank(message = "潜客城市参透率不能为空")
    private String taReach;

    @ApiModelProperty(value = "是否上传属性数据", required = true, example = "225")
    private Boolean isUploadAttrData;

    @ApiModelProperty(value = "是否上传TA电商行为", required = true, example = "225")
    private Boolean isUploadBehaviorData;

    @ApiModelProperty(value = "是否上传全域评估第三方附件", required = true, example = "225")
    private Boolean isThirdPartyEvaluationAttachmentId;

    @ApiModelProperty(value = "行记录创建时间", required = false, example = "")
    private Date createTime;

    @ApiModelProperty(value = "行记录更新时间", required = false, example = "")
    private Date updateTime;

    @ApiModelProperty("全域效果评估")
    private String orderEffectEvaluation;

    @ApiModelProperty("第三方效果评估")
    private String thirdPartyEvaluation;

    @ApiModelProperty("计划投放开始时间")
    private Date deliveryStartTime;

    @ApiModelProperty("计划投放结束时间")
    private Date deliveryEndTime;

    public TOrderEffectEstimateBase() {
    }

    public TOrderEffectEstimateBase(OrderEffectEstimateBase orderEffectEstimateBase) {
        this.id = orderEffectEstimateBase.getId();
        this.pv = orderEffectEstimateBase.getPv();
        this.uv = orderEffectEstimateBase.getUv();
        this.cpm = orderEffectEstimateBase.getCpm();
        this.cpuv = orderEffectEstimateBase.getCpuv();
        this.tauv = orderEffectEstimateBase.getTauv();
        this.epcc = orderEffectEstimateBase.getEpcc();
        this.taReach = orderEffectEstimateBase.getTaReach();
        this.createTime = orderEffectEstimateBase.getCreateTime();
        this.updateTime = orderEffectEstimateBase.getUpdateTime();

    }
}
