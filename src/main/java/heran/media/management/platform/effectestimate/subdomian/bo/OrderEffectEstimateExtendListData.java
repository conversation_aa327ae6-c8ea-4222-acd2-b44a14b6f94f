package heran.media.management.platform.effectestimate.subdomian.bo;

import lombok.Data;

import java.util.Date;

/**
 * @Description: 扩展版列表数据
 * @Author: yzj
 * @Date: 2024/1/26 17:22
 * @Version: 1.0
 */
@Data
public class OrderEffectEstimateExtendListData {

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 版本类型
     */
    private String orderEffectEvaluation;

    /**
     * 第三方效果评估
     */
    private String thirdPartyEvaluation;

    /**
     * 第三方效果评估
     */
    private String thirdPartyEvaluationAttachmentId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 上传属性更新时间
     */
    private Date attrUpdateTime;
    /**
     * TA电商行为更新时间
     */
    private Date taUpdateTime;
    /**
     * 三方评估更新时间
     */
    private Date thirdUpdateTime;
    /**
     * 是否上传人群画像数据
     */
    private Boolean isUploadAttrData;
    /**
     * 是否上传电商行为
     */
    private Boolean isUploadBehaviorData;
    /**
     * 是否上传关键指标
     */
    private Boolean isUploadKeyIndicator;

    /**
     * 计划投放开始时间
     */
    private Date deliveryStartTime;

    /**
     * 计划投放结束时间
     */
    private Date deliveryEndTime;

}
