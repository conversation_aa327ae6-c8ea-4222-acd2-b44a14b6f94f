package heran.media.management.platform.effectestimate.subdomian.ds;

import com.alibaba.nacos.common.utils.StringUtils;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import org.apache.ibatis.jdbc.SQL;

public class OrderEffectEstimateBaseEntityQuerySQLProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("oeeb.id," +
                    "oeeb.order_id," +
                    "mo.order_sn," +
                    "mo.order_name," +
                    "oeeb.pv," +
                    "oeeb.cpm," +
                    "oeeb.uv," +
                    "oeeb.cpuv," +
                    "oeeb.tauv," +
                    "oeeb.epcc," +
                    "oeeb.ta_reach," +
                    "CASE WHEN t3.order_id IS NOT NULL THEN TRUE ELSE FALSE END AS is_upload_attr_data," +
                    "CASE WHEN t4.order_id IS NOT NULL THEN TRUE ELSE FALSE END AS is_upload_behavior_data," +
                    "CASE WHEN mo.third_party_evaluation_attachment_id IS NOT NULL THEN TRUE ELSE FALSE END AS is_third_party_evaluation_attachment_id," +
                    "mo.order_effect_evaluation," +
                    "mo.third_party_evaluation," +
                    "t5.delivery_start_time as deliveryStartTime," +
                    "t5.delivery_end_time as deliveryEndTime," +
                    "oeeb.create_time," +
                    "oeeb.update_time");
            FROM("order_effect_estimate_base as oeeb");
            JOIN("media_order as mo on mo.id = oeeb.order_id");
            LEFT_OUTER_JOIN("order_effect_estimate_group_image t3 on oeeb.order_id = t3.order_id");
            LEFT_OUTER_JOIN("order_effect_estimate_ecom_behavior t4 on oeeb.order_id = t4.order_id");
            LEFT_OUTER_JOIN("media_placement_base_sales_plan t5 ON t5.id = mo.base_plan_id");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("id = " + cri.getValue());
                    } else if ("orderSn".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("mo.order_sn like '%" + cri.getValue() + "%'");
                    } else if ("orderName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("mo.order_name like '%" + cri.getValue() + "%'");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            ORDER_BY("oeeb.update_time desc");
        }}.toString();
    }

    public String getManLandDataList(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t1.id order_id,t1.order_name,t1.order_sn,t2.delivery_start_time,t2.delivery_end_time,t1.update_time,t1.bus_evaluation");
            FROM("media_order t1");
            LEFT_OUTER_JOIN("media_placement_base_sales_plan t2 ON t2.id = t1.base_plan_id");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if ("id".equals(cri.getKey())) {
                        WHERE("id = " + cri.getValue());
                    } else if ("orderSn".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.order_sn like '%" + cri.getValue() + "%'");
                    } else if ("orderName".equals(cri.getKey()) && cri.getValue() != null) {
                        WHERE("t1.order_name like '%" + cri.getValue() + "%'");
                    } else {
                        throw new RuntimeException("Not supported properties");
                    }
                }
            }
            WHERE("t1.order_effect_evaluation = '投后全域效果评估-人地版' AND t1.is_deleted = 0");
            ORDER_BY("t1.update_time desc");
        }}.toString();
    }
}
