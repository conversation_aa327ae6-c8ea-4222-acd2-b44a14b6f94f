package heran.media.management.platform.effectestimate.controller;

import com.alibaba.excel.EasyExcel;
import heran.media.management.platform.common.domain.dto.PageResponse;
import heran.media.management.platform.common.domain.dto.SearchCriteria;
import heran.media.management.platform.common.service.CommonService;
import heran.media.management.platform.common.utils.DownloadFileUtils;
import heran.media.management.platform.effectestimate.listener.EasyExcelOrderEffectEstimateBaseDataListener;
import heran.media.management.platform.effectestimate.service.OrderEffectEstimateBaseService;
import heran.media.management.platform.effectestimate.subdomian.bo.EasyXlsOrderEffectEstimateBase;
import heran.media.management.platform.effectestimate.subdomian.ds.OrderEffectEstimateBaseEntityQueryMapper;
import heran.media.management.platform.effectestimate.subdomian.dto.*;
import heran.media.sharelib.domain.bo.AuthUserInfo;
import heran.media.sharelib.domain.dto.InternalResponse;
import heran.media.sharelib.errors.QueryParamErrorException;
import heran.media.sharelib.utils.AuthUtils;
import heran.media.sharelib.utils.log.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 */
@ApiOperation(value = "全域效果评估关键指标接口")
@Slf4j
@RestController
@RequestMapping("/order-effect-estimate-base")
public class OrderEffectEstimateBaseController {

    private final OrderEffectEstimateBaseService orderEffectEstimateBaseService;
    private final OrderEffectEstimateBaseEntityQueryMapper orderEffectEstimateBaseEntityQueryMapper;
    private final CommonService commonService;

    public OrderEffectEstimateBaseController(OrderEffectEstimateBaseService orderEffectEstimateBaseService,
                                             OrderEffectEstimateBaseEntityQueryMapper orderEffectEstimateBaseEntityQueryMapper, CommonService commonService) {
        this.orderEffectEstimateBaseService = orderEffectEstimateBaseService;
        this.orderEffectEstimateBaseEntityQueryMapper = orderEffectEstimateBaseEntityQueryMapper;
        this.commonService = commonService;
    }

    @Value("${spring.application.name}")
    public String appName;

    @ApiLog(storage = true, storageDescription = "全域效果评估关键指标列表查询")
    @ApiOperation(value = "全域效果评估关键指标列表查询", notes = "查询字段可包含【orderName(订单名称),orderSn(订单编号)】")
    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<TOrderEffectEstimateBase>> list(@Valid @RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        PageResponse<TOrderEffectEstimateBase> pageResponse = orderEffectEstimateBaseService.list(criteria);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog(storageDescription = "单个全域效果评估关键指标查询")
    @ApiOperation(value = "单个全域效果评估关键指标查询", notes = "根据id获取对象详情")
    @RequestMapping(value = "/getById/{id}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<TOrderEffectEstimateBase> getById(@PathVariable Integer id) {
        InternalResponse internalResponse = null;
        TOrderEffectEstimateBase torderEffectEstimateBase = orderEffectEstimateBaseService.getById(id);
        internalResponse = InternalResponse.success().withBody(torderEffectEstimateBase);
        return internalResponse;
    }

    @ApiLog(storageDescription = "全域效果评估编辑回显接口")
    @ApiOperation(value = "全域效果评估编辑回显接口", notes = "根据订单id获取对象详情")
    @RequestMapping(value = "/getOrderEffectEstimateBaseByOrderId/{orderId}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public InternalResponse<OrderEffectEstimateBaseDetail> getOrderEffectEstimateBaseByOrderId(@PathVariable Integer orderId) {
        InternalResponse internalResponse = null;
        OrderEffectEstimateBaseDetail orderEffectEstimateBaseDetail = orderEffectEstimateBaseService.getOrderEffectEstimateBaseByOrderId(orderId);
        internalResponse = InternalResponse.success().withBody(orderEffectEstimateBaseDetail);
        return internalResponse;
    }

    @ApiOperation(value = "根据模板导入数据，模板可在导出功能查看", response = InternalResponse.class)
    @ApiLog(printInputs = false, storage = true, storageDescription = "全域效果评估关键指标导入")
    @RequestMapping(value = "/import", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public void importData(@RequestPart("files") MultipartFile[] files, HttpServletResponse resp) throws Exception {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        List<File> fileList = orderEffectEstimateBaseService.importData(currentUser.getIdentifier(), files);
        DownloadFileUtils.setFileResponse(resp, fileList, "失败列表");
    }

    @ApiOperation(value = "根据条件导出所有记录，现在5W")
    @ApiLog(printOutputs = false, storage = true, storageDescription = "全域效果评估关键指标导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void export(HttpServletResponse resp,
                       @RequestParam(value = "exportAll", required = false) Boolean exportAll,
                       @RequestBody SearchCriteria searchCriteria) throws Exception {
        exportAll = exportAll != null && exportAll;
        checkExportPageLimit(searchCriteria, exportAll);
        File file = orderEffectEstimateBaseService.exportData(searchCriteria, exportAll);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "全域效果评估关键指标信息");
    }

    @ApiOperation(value = "新建全域效果评估关键指标")
    @ApiLog(storage = true, storageDescription = "新建全域效果评估关键指标")
    @RequestMapping(value = "/create", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse create(@Valid @RequestBody TChangebleOrderEffectEstimateBase torderEffectEstimateBase) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        orderEffectEstimateBaseService.create(currentUser.getIdentifier(), torderEffectEstimateBase.adapToPO());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "修改全域效果评估关键指标")
    @ApiLog(storage = true, storageDescription = "修改全域效果评估关键指标")
    @RequestMapping(value = "/modify", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse modify(@Valid @RequestBody TChangebleOrderEffectEstimateBase torderEffectEstimateBase) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        orderEffectEstimateBaseService.modify(currentUser.getIdentifier(), torderEffectEstimateBase);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "修改全域效果评估关键指标（改版后）")
    @ApiLog(storage = true, storageDescription = "修改全域效果评估关键指标（改版后）")
    @RequestMapping(value = "/modifyOrderEffectEstimateBase", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse modifyOrderEffectEstimateBase(@Valid @RequestBody UpDateOrderEffectEstimateBase upDateOrderEffectEstimateBase) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        orderEffectEstimateBaseService.modifyOrderEffectEstimateBase(currentUser.getIdentifier(), upDateOrderEffectEstimateBase);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "删除全域效果评估关键指标")
    @ApiLog(storage = true, storageDescription = "删除全域效果评估关键指标")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @ResponseBody
    public InternalResponse delete(@RequestParam("id") Integer id) {
        InternalResponse internalResponse = null;
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        TOrderEffectEstimateBase base = orderEffectEstimateBaseService.getById(id);
        orderEffectEstimateBaseService.delete(currentUser.getIdentifier(), base.getOrderId());
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiOperation(value = "全域效果评估关键指标导入", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/importOrderEffectEstimateBase", method = RequestMethod.POST)
    public void importOrderEffectEstimateBase(@RequestPart("file") MultipartFile file) throws Exception {
        AuthUserInfo currentUser = AuthUtils.getUserInfo();
        EasyExcel.read(file.getInputStream(),
                EasyXlsOrderEffectEstimateBase.class,
                new EasyExcelOrderEffectEstimateBaseDataListener(orderEffectEstimateBaseService, orderEffectEstimateBaseEntityQueryMapper, currentUser.getIdentifier(), appName)).sheet().doRead();
    }

    @ApiOperation(value = "下载模板", response = InternalResponse.class)
    @ApiLog
    @RequestMapping(value = "/downloadTemplate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadTemplate(HttpServletResponse resp) throws Exception {
        File file = commonService.generalTemplate("关键指标模板", EasyXlsOrderEffectEstimateBase.class);
        //初始化数据
//        EasyXlsOrderEffectEstimateBase base = new EasyXlsOrderEffectEstimateBase();
//        base.setOrderSc("HXX2024055018");
//        base.setOrderName("不要导入词条数据");
//        base.setPv("1");
//        base.setCpm("1");
//        base.setUv("1");
//        base.setCpuv("1");
//        base.setEpcc("1");
//        base.setTaReach("1");
//        ArrayList<EasyXlsOrderEffectEstimateBase> estimateBases = Lists.newArrayList(base);
//        File file = commonService.generalTemplate("关键指标模板",EasyXlsOrderEffectEstimateBase.class, estimateBases);
        DownloadFileUtils.setFileResponse(resp, Collections.singletonList(file), "关键指标模板");
    }

    @ApiOperation(value = "设置第三方评估pdf")
    @ApiLog
    @RequestMapping(value = "/setThirdPartyBriefAttachment", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse setThirdPartyBriefAttachment(@Valid @RequestBody SetThirdPartyBriefAttachmentData data) {
        InternalResponse internalResponse = null;
        orderEffectEstimateBaseService.setThirdPartyBriefAttachment(data);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }


    @ApiLog(storageDescription = "全域效果评估关键指标列表查询")
    @ApiOperation(value = "全域效果评估关键指标列表查询", notes = "查询字段可包含【orderName(订单名称),orderSn(订单编号)】")
    @RequestMapping(value = "/getManLandDataList", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<ManLandDataListResponse>> getManLandDataList(@Valid @RequestBody SearchCriteria criteria) {
        InternalResponse internalResponse = null;
        PageResponse<ManLandDataListResponse> pageResponse = orderEffectEstimateBaseService.getManLandDataList(criteria);
        internalResponse = InternalResponse.success().withBody(pageResponse);
        return internalResponse;
    }


    private void checkExportPageLimit(SearchCriteria searchCriteria, Boolean exportAll) {
        if (!exportAll) {
            if (searchCriteria.getPage() == null || searchCriteria.getLimit() == null) {
                throw new QueryParamErrorException();
            }
        }
    }
}
