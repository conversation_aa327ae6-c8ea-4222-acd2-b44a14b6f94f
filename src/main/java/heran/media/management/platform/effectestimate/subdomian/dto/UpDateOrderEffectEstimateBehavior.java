package heran.media.management.platform.effectestimate.subdomian.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UpDateOrderEffectEstimateBehavior {
    @ApiModelProperty(value = "主键")
    private Integer id;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty(value = "附件id")
    private Integer attachmentBehaviorId;
}
