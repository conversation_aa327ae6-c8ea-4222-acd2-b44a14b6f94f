package heran.media.management.platform.mediaresource.nationwidemedia.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import heran.media.management.platform.common.domain.dto.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class AoiCodeListRequest extends PageRequest {

    @ApiModelProperty("城市编码列表")
    private List<String> codeList;
    @ApiModelProperty("引用媒介商圈")
    private Integer quoteCbd;
    @ApiModelProperty("标签列表")
    private List<Integer> tagIdList;
}
