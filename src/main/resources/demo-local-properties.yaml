spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************
    username: opworker
    password: <PERSON><PERSON>(tfj1ow/A/igqsiQskOTcH8um6xvLyDiv)
    tomcat:
      max-wait: 10000
      max-idle: 30
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 50MB
  session:
    store-type: redis
  redis:
    host: redis-aa800e1e-df86-4a33-8e83-5bc404f89bb2.dcs.huaweicloud.com
    database: 9
rest:
  call:
    pool:
      max-total: 5000
      max-per-route: 5000
      validate-after-inactivity: 5000
    connect:
      timeout: 5000
    read:
      timeout: 5000
  http:
    timeout: 5000
security:
  enable:
    appauth:
      check: false
kaptcha:
  border: "yes"
  border.color: 105,179,90
  textproducer:
    font.color: blue
    font.size: 30
    char.length: 4
  image:
    width: 110
    height: 40
  session.key: "heranmedia-management-platform:kaptcha:value"
# 阿里云文件上传配置
aliyun:
  accessKeyId: LTAI5tBikVuNz2MNkmwDZaek
  accessKeySecret: ******************************
  ossBucket: cs-uat-oss
  endpoint: https://oss-cn-zhangjiakou.aliyuncs.com
  #一周 单位秒
  temp-url-expiration: 604800
  path:
    main-marketing-case: heranmedia/data/main-marketing-case/imgs/
    main-marketing-case-file: heranmedia/data/main-marketing-case/file/
    medium-analyse: heranmedia/data/medium-analyse/imgs/
    creative-app: heranmedia/data/creative-app/imgs/
    creative-app-videos: heranmedia/data/creative-app/videos/
    nationwide-media: heranmedia/data/nationwide-media/imgs/
    city-nationwide-media-files: heranmedia/data/city-nationwide-media/files/
    city-nationwide-media: heranmedia/data/city-nationwide-media/imgs/
    business-area-media: heranmedia/data/business-area-media/imgs/
    media-order: heranmedia/data/media-order/file/
    media-order-pdf: heranmedia/data/media-order/pdf/
    media-order-imgs: heranmedia/data/media-order/imgs/
    main-media-placement: heranmedia/data/main-media-placement/imgs/
    ai-option-plan-excel: heranmedia/data/ai-option-plan/excel/
    attachment-behavior: heranmedia/data/attachment-behavior/imgs/
    devilver-screen: heranmedia/data/devilver-screen/imgs/
    devilver-brand: heranmedia/data/devilver-brand/imgs/
    media-effect-estimate-base-pdf: heranmedia/data/effect-estimate-base/pdf/
    login-picture: heranmedia/data/login-picture/imgs/
    selection-mode: heranmedia/data/selection-mode/files/
    plan-task-excel: heranmedia/data/ai-option-plan/task/excel/
# 上传目标 1.oss 2.服务器上传
upload:
  target: 1
  # 图片限制大小 单位 MB
  file-size: 10
  file-video-size: 50
path:
  upload: /opt/data
  export: /opt/data
  batchImages: opt/data/main-media-placement/file
redis:
  prefix:
    tempImageUrl: "heran:tempImageUrl:"
    pieData: "heran:nationwide:nationwideMedia:"
    nationwideMap: "heran:nationwide:mapData:"
    histogram: "heran:city:media:cityMedia:"
    cityMap: "heran:city:media:mapData:city:"
    provinceMap: "heran:city:media:mapData:province:"
    districtMap: "heran:city:media:mapData:district:"
    busData: "heran:city:city:bus:"
aMap:
  key: 8a5e4eae44ea73d63a6ac102cd7b2c5f
  host: https://restapi.amap.com
  #host: https://yapi.icyanstone.com/mock/135
  app-name: dz-amap-hr
  secret_key: acb1160a34c26059fb94945c4ef3c07c
  secret_key_lv1: d4059d3285185904a838cff38892f26b

#定时任务时间
task:
  initPlacementInfo: 0 0 1 * * ?
  initOrderPlacementInfo: 0 0 2 * * ?
  initSchedulePlacementInfo: 0 0 3 * * ?

useCDN: false
videoHost: heran-video-case.icyanstone.com

