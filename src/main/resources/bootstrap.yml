jasypt:
  encryptor:
    password: heran-media-management-platformfikMc
server:
  port: 9503
  servlet:
    context-path: /heran-media-management-platform
  tomcat:
    max-http-form-post-size: 100MB
    background-process-time: 180s
    connection-timeout: 180s
spring:
  application:
    name: heran-media-management-platform
  config:
    activate:
      on-profile: local-nacos
    additional-location: classpath:/demo-local-properties.yaml
  cloud:
    nacos:
      config:
        enabled: false
        group: howard
      discovery:
        enabled: false
logging:
  config: classpath:logback-local.xml
  level:
    heran.media.management.platform: debug
    heran.media.sharelib.domain.db.mapper: debug
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
---
jasypt:
  encryptor:
    password: heran-media-management-platformfikMc
server:
  port: 9503
  servlet:
    context-path: /heran-media-management-platform
spring:
  application:
    name: heran-media-management-platform
  cloud:
    nacos:
      server-addr: 192.168.0.205:8848
      config:
        file-extension: yaml
        namespace: heran-media
  config:
    activate:
      on-profile: demo
logging:
  config: classpath:logback-demo.xml
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
---
jasypt:
  encryptor:
    password: heran-media-management-platformfiEOF
server:
  port: 9503
  servlet:
    context-path: /heran-media-management-platform
spring:
  application:
    name: heran-media-management-platform
  cloud:
    nacos:
#      server-addr: 192.168.0.9:8848
      server-addr: nacos.tools.svc.cluster.local:8848
      discovery:
        enabled: false
      config:
        file-extension: yaml
        namespace: heran-media
        username: heran-media
        password: heran-media
  config:
    activate:
      on-profile: prod
logging:
  config: classpath:logback-prod.xml
